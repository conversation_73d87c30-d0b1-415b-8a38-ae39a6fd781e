/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
    http://spirit.sourceforge.net/

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#ifndef BOOST_SPIRIT_INCLUDE_SUPPORT_ISTREAM_ITERATOR
#define BOOST_SPIRIT_INCLUDE_SUPPORT_ISTREAM_ITERATOR

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/iterators/istream_iterator.hpp>

#endif
