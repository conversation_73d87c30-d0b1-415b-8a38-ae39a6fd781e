﻿# ifndef _sourceFlow_timeScheme_ExactJacobian_
# define _sourceFlow_timeScheme_ExactJacobian_

#include "basic/CFD/linearSystemSolver/LinearSystemSolverSelf.h"
#include "basic/CFD/linearSystemSolver/LinearSystemSolverPetsc.h"
#include "basic/common/Matrix.h"
#include "sourceFlow/timeScheme/BackEuler.h"
#include <iostream>
using namespace std;

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场GMRES时间推进类
 * 由基础时间类进行派生
 * 
 */
class ExactJacobian : public BackEuler
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    ExactJacobian(Package::FlowPackage &flowPackage_);

	/**
	* @brief 析构函数
	*/
	~ExactJacobian();

    /**
     * @brief 初始化
     * 
     * @param[in] initialType 流场初始化类型
     */
    void Initialize(const Initialization::Type &initialType);    

private:

	/**
	* @brief 初始化求解器
	*
	*/
    void InitializeSolver();

    /**
     * @brief GMRES推进
     * 
     */
    void Solve();    
	
    /**
     * @brief 流动变量更新
     * 
     */
    void Update();  

	/**
	* @brief 计算松弛系数
	*
	*/
	Scalar ComputeUnderRelaxationFactor(const Scalar &rhoTemp, const Scalar &rhoETemp);

private:
	BlockSparseMatrix *jacobian; ///< 主流Jacobian矩阵
	LinearSystemVector *resVector; ///< 主流残值矢量
	LinearSystemVector *solVector; ///< 主流解矢量
	LinearSystemSolver *systemNew; ///< 主流线性求解系统

	BlockSparseMatrix *jacobianTur; ///< 湍流Jacobian矩阵
	LinearSystemVector *resVectorTur; ///< 湍流残值矢量
	LinearSystemVector *solVectorTur; ///< 湍流解矢量
	LinearSystemSolver *systemNewTur; ///< 湍流线性求解系统
};

} // namespace Flow
} // namespace Time

# endif