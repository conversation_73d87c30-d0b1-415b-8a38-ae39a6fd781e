
namespace Eigen {

/** \page TopicCUDA Using Eigen in CUDA kernels

\b Disclaimer: this page is about an \b experimental feature in %Eigen.

Staring from CUDA 5.0, the CUDA compiler, \c nvcc, is able to properly parse %Eigen's code (almost).
A few adaptations of the %Eigen's code already allows to use some parts of %Eigen in your own CUDA kernels.
To this end you need the devel branch of %Eigen, CUDA 5.0 or greater with GCC.

Known issues:

 - \c nvcc with MS Visual Studio does not work (patch welcome)
 
 - \c nvcc with \c clang does not work (patch welcome)
 
 - \c nvcc 5.5 with gcc-4.7 (or greater) has issues with the standard \c \<limits\> header file. To workaround this, you can add the following before including any other files:
   \code
    // workaround issue between gcc >= 4.7 and cuda 5.5
    #if (defined __GNUC__) && (__GNUC__>4 || __GNUC_MINOR__>=7)
      #undef _GLIBCXX_ATOMIC_BUILTINS
      #undef _GLIBCXX_USE_INT128
    #endif
   \endcode
   
 - On 64bits system <PERSON>igen uses \c long \c int as the default type for indexes and sizes. On CUDA device, it would make sense to default to 32 bits \c int.
   However, to keep host and CUDA code compatible, this cannot be done automatically by %Eigen, and the user is thus required to define \c EIGEN_DEFAULT_DENSE_INDEX_TYPE to \c int throughout his code (or only for CUDA code if there is no interaction between host and CUDA code through %Eigen's object).

*/

}
