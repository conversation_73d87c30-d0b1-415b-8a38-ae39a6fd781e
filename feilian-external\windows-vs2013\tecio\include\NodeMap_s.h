 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <fstream>
#include <vector>
#include <boost/make_shared.hpp>
#include <boost/shared_ptr.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "MASTER.h"
#include "GLOBAL.h"
#include "basicTypes.h"
#include "fileio.h"
struct ___2730 { typedef boost::shared_ptr<___2730> Ptr; static Ptr makePtr (int32_t nodesPerCell, int64_t cellCount, int64_t maxNodeValue); static OffsetDataType_e getRawItemTypeForMaxNodeValue(int64_t maxNodeValue); int32_t ___2500; int64_t ___2392; int64_t m_maxNodeValue; ___2730(int32_t nodesPerCell, int64_t cellCount, int64_t maxNodeValue) : ___2500(nodesPerCell) , ___2392(cellCount) , m_maxNodeValue(maxNodeValue) {} virtual ~___2730() {} OffsetDataType_e getRawItemType() const { return getRawItemTypeForMaxNodeValue(m_maxNodeValue); } virtual int64_t storedValueCount() const = 0; virtual void appendValue(int64_t ___4314) = 0; virtual void ___3504(int64_t ___2865, int64_t ___4314) = 0; virtual int64_t ___4314(int64_t ___2865) const = 0; virtual void* getRawPtr() = 0; virtual void writeToFile(tecplot::___3933::FileWriterInterface& outputFile, bool ___4480) const = 0; virtual uint64_t sizeInFile(bool ___4480) const = 0; static Ptr makePtr(tecplot::___3933::___1399& inputFile, bool readASCII); protected: virtual void readValuesFromFile(tecplot::___3933::___1399& inputFile, bool readASCII) = 0; }; template <typename T> class TypedNodeMap : public ___2730 { public: TypedNodeMap(int32_t nodesPerCell, int64_t cellCount, int64_t maxNodeValue); virtual int64_t storedValueCount() const; virtual void appendValue(int64_t ___4314); virtual void ___3504(int64_t ___2865, int64_t ___4314); virtual int64_t ___4314(int64_t ___2865) const; virtual void* getRawPtr(); virtual void writeToFile(tecplot::___3933::FileWriterInterface& outputFile, bool ___4480) const; virtual uint64_t sizeInFile(bool ___4480) const; protected: virtual void readValuesFromFile(tecplot::___3933::___1399& inputFile, bool readASCII); private: std::vector<T> m_nodes; }; template<typename T> TypedNodeMap<T>::TypedNodeMap(int32_t nodesPerCell, int64_t cellCount, int64_t maxNodeValue) : ___2730(nodesPerCell, cellCount, maxNodeValue) { REQUIRE(maxNodeValue < static_cast<int64_t>(std::numeric_limits<T>::max())); } template<typename T> int64_t TypedNodeMap<T>::storedValueCount() const { return static_cast<int64_t>(m_nodes.size()); } template<typename T> void TypedNodeMap<T>::appendValue(int64_t ___4314) { REQUIRE(___4314 < std::numeric_limits<T>::max()); if (m_nodes.empty()) m_nodes.reserve(___2500 * ___2392); m_nodes.push_back(static_cast<T>(___4314)); } template<typename T> void TypedNodeMap<T>::___3504(int64_t ___2865, int64_t ___4314) { REQUIRE(___4314 < std::numeric_limits<T>::max()); if (m_nodes.empty() || m_nodes.size() <= (size_t)___2865) m_nodes.resize(___2865 + 1); m_nodes[___2865] = static_cast<T>(___4314); } template<typename T> int64_t TypedNodeMap<T>::___4314(int64_t ___2865) const { REQUIRE(0 <= ___2865 && ___2865 < static_cast<int64_t>(m_nodes.size())); return m_nodes[___2865]; } template<typename T> void* TypedNodeMap<T>::getRawPtr() { if (m_nodes.empty()) m_nodes.assign(___2500 * ___2392, 0); return &m_nodes[0]; } inline OffsetDataType_e ___2730::getRawItemTypeForMaxNodeValue(int64_t maxNodeValue) { if (maxNodeValue < std::numeric_limits<int32_t>::max()) return OffsetDataType_32Bit; else return OffsetDataType_64Bit; } inline ___2730::Ptr ___2730::makePtr(int32_t nodesPerCell, int64_t cellCount, int64_t maxNodeValue) { if (getRawItemTypeForMaxNodeValue(maxNodeValue) == OffsetDataType_32Bit) return boost::make_shared<TypedNodeMap<int32_t> >(nodesPerCell, cellCount, maxNodeValue); else return boost::make_shared<TypedNodeMap<int64_t> >(nodesPerCell, cellCount, maxNodeValue); } template<typename T> void TypedNodeMap<T>::writeToFile(tecplot::___3933::FileWriterInterface& outputFile, bool ___4480) const { tecplot::tecioszl::writeScalar(outputFile, ___2500, ___4480); tecplot::tecioszl::writeScalar(outputFile, ___2392, ___4480); tecplot::tecioszl::writeScalar(outputFile, m_maxNodeValue, ___4480); tecplot::tecioszl::writeVector(outputFile, m_nodes, ___4480); } template<typename T> uint64_t TypedNodeMap<T>::sizeInFile(bool ___4480) const { return tecplot::tecioszl::scalarSizeInFile(___2500, ___4480) + tecplot::tecioszl::scalarSizeInFile(___2392, ___4480) + tecplot::tecioszl::scalarSizeInFile(m_maxNodeValue, ___4480) + tecplot::tecioszl::vectorSizeInFile(m_nodes, ___4480); } template<typename T> void TypedNodeMap<T>::readValuesFromFile(tecplot::___3933::___1399& inputFile, bool readASCII) { tecplot::tecioszl::readVector(inputFile, m_nodes, readASCII); } inline ___2730::Ptr ___2730::makePtr(tecplot::___3933::___1399& inputFile, bool readASCII) { int32_t nodesPerCell; int64_t cellCount; int64_t maxNodeValue; tecplot::tecioszl::readScalar(inputFile, nodesPerCell, readASCII); tecplot::tecioszl::readScalar(inputFile, cellCount, readASCII); tecplot::tecioszl::readScalar(inputFile, maxNodeValue, readASCII); Ptr ___3358 = makePtr(nodesPerCell, cellCount, maxNodeValue); ___3358->readValuesFromFile(inputFile, readASCII); return ___3358;
}
