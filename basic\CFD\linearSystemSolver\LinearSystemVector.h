﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LinearSystemVector.h
//! <AUTHOR>
//! @brief 线性求解系统列向量类
//! @date 2025-6-11
//
//------------------------------修改日志----------------------------------------
//
// 2025-6-11 气动院
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_CFD_linearSystemSolver_LinearSystemVector_
#define _basic_CFD_linearSystemSolver_LinearSystemVector_

#include "basic/mesh/SubMesh.h"

#if defined(_EnablePETSC_)
#include <petsc.h>
#include <petscksp.h>
#endif

/**
 * @brief 线性求解系统列向量基类
 *
 * 抽象基类，提供线性求解系统中列向量的基本接口。
 */
class LinearSystemVector
{
public:
    /**
     * @brief 构造函数
     * 
     * @param mesh_ 网格指针
     * @param nVariable_ 每个块中的变量数目
     */
	LinearSystemVector(Mesh *mesh_, const int nVariable_)
	: mesh(mesh_), nVariable(nVariable_)
	{}

	/**
	 * @brief 构造函数
	 *
	 */
	virtual ~LinearSystemVector() {}

    /**
     * @brief 初始化向量数据
     * 
     * 由子类实现具体的初始化逻辑
     */
	virtual void Initialize() = 0;

    /**
     * @brief 将向量置零
     * 
     * 由子类实现具体的置零逻辑
     */
	virtual void SetZero() = 0;

    /**
     * @brief 获取向量中某个位置的值
     * 
     * @param localID 当地单元编号
     * @param index 向量索引位置
     * @return const Scalar 索引位置对应的数值
     */
	virtual const Scalar GetValue(const int localID, const int index) const = 0;

    /**
     * @brief 设置向量中某个位置的值
     * 
     * @param localID 当地单元编号
     * @param index 向量索引位置
     * @param value 要设置的新值
     */
	virtual void SetValue(const int localID, const int index, const Scalar &value) = 0;

protected:
	const int nVariable; ///< 每个块的每行元素数量
	Mesh *mesh; ///< 网格指针
};

/**
 * @brief 自定义线性求解系统列向量实现类
 *
 * 使用std::vector存储向量数据
 */
class LinearSystemVectorSelf : public LinearSystemVector
{
public:
    /**
     * @brief 构造函数
     * 
     * @param mesh_ 网格指针
     * @param nVariable_ 每个块中的变量数目
     */
	LinearSystemVectorSelf(Mesh *mesh_, const int nVariable_)
	: LinearSystemVector(mesh_, nVariable_)
	{}

    /**
     * @brief 析构函数
     * 
     */
     ~LinearSystemVectorSelf() {}

    /**
     * @brief 初始化向量数据
     * 
     * 根据网格的元素数量和变量数初始化向量，并将其值初始化为默认值
     */
	void Initialize()
	{
		const int elementSize = mesh->GetElementNumberAll();
		vec.resize(elementSize * nVariable);
		std::fill(vec.begin(), vec.end(), Scalar0);
	}

    /**
     * @brief 将向量置零
     * 
     * 使用std::fill将所有元素设置为默认值
     */
	void SetZero() { std::fill(vec.begin(), vec.end(), Scalar0); }

    /**
     * @brief 获取向量的引用
     * 
     * @return std::vector<Scalar>& 向量的引用
     */
	std::vector<Scalar> &GetVector() { return vec; }

    /**
     * @brief 获取向量中某个位置的值
     * 
     * @param localID 当地单元编号
     * @param index 向量索引位置
     * @return const Scalar 索引位置对应的数值
     */
	const Scalar GetValue(const int localID, const int index) const { return vec[localID * nVariable + index]; }

	/**
     * @brief 设置向量中某个位置的值
     * 
     * @param localID 当地单元编号
     * @param index 向量索引位置
     * @param value 要设置的新值
     */
	void SetValue(const int localID, const int index, const Scalar &value) { vec[localID * nVariable + index] = value; }

protected:
	std::vector<Scalar> vec; ///< 列向量数据

};

#if defined(_EnablePETSC_)
/**
 * @brief PETSc线性求解系统列向量实现类
 *
 * 使用PETSc的Vec存储向量数据
 */
class LinearSystemVectorPetsc : public LinearSystemVector
{
public:
	/**
     * @brief 构造函数
     * 
     * @param mesh_ 网格指针
     * @param nVariable_ 每个块中的变量数目
     */
	LinearSystemVectorPetsc(Mesh *mesh_, const int nVariable_)
	: LinearSystemVector(mesh_, nVariable_)
	{
		subMesh = (SubMesh*)mesh_;
	     startGlobalID = 0;
	}

     ~LinearSystemVectorPetsc() { VecDestroy(&vec); }

	/**
     * @brief 初始化向量数据
     * 
     * 创建并初始化PETSc Vec对象，并将其值初始化为默认值
     */
	void Initialize()
	{
		// 计算局部和全局数组大小
		const int elementSize = mesh->GetElementNumberReal();
		const int localSize = elementSize * nVariable;
		int globalSize = localSize;
		SumAllProcessor(globalSize, 0);
          MPIBroadcast(globalSize, 0);
          
          // 创建数组
    	     VecCreateMPI(PETSC_COMM_WORLD, localSize, globalSize, &vec); // 在给定通信域上创建向量
    	     VecSetBlockSize(vec, nVariable);
    	     VecSetFromOptions(vec);
		VecZeroEntries(vec);
		VecSetUp(vec);
          
          // 计算当地网格单元的全局起始位置
          startGlobalID = 0;
          std::vector<int> localElementSizeVector(GetMPISize());
          boost::mpi::all_gather(MPI::mpiWorld, elementSize, localElementSizeVector);
	     for (int i = 0; i < GetMPIRank(); i++) startGlobalID += localElementSizeVector[i];
	}

	/**
     * @brief 将向量置零
     * 
     * 使用PETSc的VecZeroEntries函数将所有元素设置为默认值
     */
	void SetZero() { VecZeroEntries(vec); }

	Vec &GetVector() { return vec; }

	/**
     * @brief 获取向量中某个位置的值
     * 
     * @param localID 当地单元编号
     * @param index 向量索引位置
     * @return const Scalar 索引位置对应的数值
     */
	const Scalar GetValue(const int localID, const int index) const
	{
          const int pos = localID * nVariable + index;
          PetscScalar *vectemp;
          VecGetArray(vec, &vectemp);
          return PetscRealPart(vectemp[pos]);
	}

	/**
     * @brief 设置向量中某个位置的值
     * 
     * @param localID 当地单元编号
     * @param index 向量索引位置
     * @param value 要设置的新值
     */
	void SetValue(const int localID, const int index, const Scalar &value)
	{
          const int pos = (startGlobalID + localID) * nVariable + index;
          PetscScalar temp = value;
          VecSetValues(vec, 1, &pos, &temp, INSERT_VALUES);
	}

protected:
	Vec vec; ///< 列向量数据
     PetscScalar *vectemp; ///< 列向量所存储数据指针
	SubMesh *subMesh; ///< 网格指针
	int startGlobalID; ///< 当地网格单元在全局的起始编号
};
#endif

#endif