﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file IO.h
//! <AUTHOR>
//! @brief 一般数据的读写函数
//! @date 2022-04-09
//
//------------------------------修改日志----------------------------------------
// 2022-02-22 乔龙
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_common_IO_
#define _basic_common_IO_

#include <iostream>
#include <string>
#include <vector>
#include <fstream>
#include <sstream>

#include "basic/common/Vector.h"
#include "basic/common/Tensor.h"

namespace IO
{
/**
 * @brief 输出单一数据到文件
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[in] value 数据
 * @param[in] binary 二进制标识
 */
template<class Type>
void Write(std::fstream &file, const Type &value, const bool binary = true);

/**
 * @brief 输出pair数据到文件
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[in] value 数据
 * @param[in] binary 二进制标识
 */
template<class Type>
void Write(std::fstream &file, const std::pair<Type, Type> &value, const bool binary = true);

/**
 * @brief 输出数据容器到文件
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[in] valueVector 数据容器
 * @param[in] binary 二进制标识
 * @param[in] vectorSize 容器大小
 * @note vectorSize < 0时，需输出数据容器实际大小
 * @note vectorSize >= 0时，不输出数据容器大小
 */
template<class Type>
void Write(std::fstream &file, const std::vector<Type> &valueVector, const bool binary, int vectorSize = -1);

/**
 * @brief 输出双层数据容器到文件
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[in] valueVector 数据容器
 * @param[in] binary 二进制标识
 * @param[in] vectorSize 外层容器大小
 * @note vectorSize < 0时，需输出外层容器实际大小
 * @note vectorSize >= 0时，不输出容器大小
 */
template<class Type>
void Write(std::fstream &file, const std::vector<std::vector<Type>> &valueVector, const bool binary, int vectorSize = -1);

/**
 * @brief 从文件读取单一数据
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[out] value 数据
 * @param[in] binary 二进制标识
 */
template<class Type>
void Read(std::fstream &file, Type &value, const bool binary = true);

/**
 * @brief 从文件读取pair数据
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[out] value 数据
 * @param[in] binary 二进制标识
 */
template<class Type>
void Read(std::fstream &file, std::pair<Type, Type> &value, const bool binary = true);

/**
 * @brief 从文件读取数据容器
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[out] valueVector 数据容器
 * @param[in] binary 二进制标识
 * @param[in] vectorSize 容器大小
 * @note vectorSize < 0时，需读入数据容器实际大小
 * @note vectorSize >= 0时，不读入数据容器大小
 */
template<class Type>
void Read(std::fstream &file, std::vector<Type> &valueVector, const bool binary, int vectorSize = -1);

/**
 * @brief 从文件读取双层数据容器
 * 
 * @tparam Type 数据类型
 * @param[in] file 文件对象
 * @param[out] valueVector 数据容器
 * @param[in] binary 二进制标识
 * @param[in] vectorSize 外层容器大小
 * @note vectorSize < 0时，需读入外层容器实际大小
 * @note vectorSize >= 0时，不读入容器大小
 */
template<class Type>
void Read(std::fstream &file, std::vector<std::vector<Type>> &valueVector, const bool binary, int vectorSize = -1);

}

#endif