﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/AUSMDVScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

AUSMDVScheme::AUSMDVScheme(Package::FlowPackage &data,
                           Limiter::Limiter *limiter,
                           Flux::Flow::Precondition::Precondition *precondition)
    :
    UpwindScheme(data, limiter, precondition)
{
}

NSFaceFlux AUSMDVScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    // 获取左右面值
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;
    
    // 得到面法向和面积大小 
    const Face &face = mesh->GetFace(faceID);
    const Vector &faceNormal = face.GetNormal();
    const Scalar &faceArea = face.GetArea();    

    // 计算左右面的温度、焓和声速
    const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
    const Scalar soundLeft = material.GetSoundSpeed(TLeft);
    const Scalar enthalpyLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);

    const Scalar TRight = material.GetTemperature(pRight, rhoRight);
    const Scalar soundRight = material.GetSoundSpeed(TRight);
    const Scalar enthalpyRight = Cp * TRight + 0.5 * (URight & URight);

    // 计算左右面声速值
    const Scalar velocityNormLeft = ULeft & faceNormal;
    const Scalar velocityNormRight = URight & faceNormal;

    const Vector UTildaLeft = ULeft - velocityNormLeft * faceNormal;
    const Vector UTildaRight = URight - velocityNormRight * faceNormal;

    // 计算激波开关
    const int &ownerID = face.GetOwnerID();
    const int &neighID = face.GetNeighborID();
    const Scalar currentShockSwitch = 0.5 * (shockSwitch->GetValue(ownerID) + shockSwitch->GetValue(neighID));

    Scalar pShare = 0.0;
    Scalar rhoUNormalLeft = 0.0;
    Scalar rhoUNormalRight = 0.0;
    Scalar rhoUUNormal = 0.0;
    if (currentShockSwitch != 0)
    {
        // VanLeer分裂
        const Scalar machLeft = velocityNormLeft / soundLeft;
        Scalar machPositiveVanLeer = 0.0;
        Scalar pPositiveVanLeer = 0.0;
        if (machLeft >= 1)
        {
            machPositiveVanLeer = machLeft;
            pPositiveVanLeer = 1.0;
        }
        else if (machLeft > -1)
        {
            machPositiveVanLeer = 0.25 * (machLeft + 1) * (machLeft + 1);
            pPositiveVanLeer = machPositiveVanLeer * (2.0 - machLeft);
        }

        const Scalar machRight = velocityNormRight / soundRight;
        Scalar machNegativeVanLeer = 0.0;
        Scalar pNegativeVanLeer = 0.0;
        if (machRight <= -1)
        {
            machNegativeVanLeer = machRight;
            pNegativeVanLeer = 1.0;
        }
        else if (machRight < 1)
        {
            machNegativeVanLeer = -0.25 * (machRight - 1) * (machRight - 1);
            pNegativeVanLeer = -machNegativeVanLeer * (2.0 + machRight);
        }
        pShare = currentShockSwitch * (pPositiveVanLeer * pLeft + pNegativeVanLeer * pRight);
        rhoUNormalLeft = currentShockSwitch * machPositiveVanLeer * rhoLeft * soundLeft;
        rhoUNormalRight = currentShockSwitch * machNegativeVanLeer * rhoRight * soundRight;
        rhoUUNormal = rhoUNormalLeft * velocityNormLeft + rhoUNormalRight * velocityNormRight;
    }

    // AUSMDV分裂
    const Scalar soundMax = Max(soundLeft, soundRight);
    const Scalar machMaxLeft = velocityNormLeft / soundMax;
    Scalar machPositiveAUSM = 0.0;
    Scalar pPositiveAUSM = 0.0;
    if (fabs(machMaxLeft) < 1)
    {
        pPositiveAUSM = 0.25 * (2.0 - machMaxLeft) * (machMaxLeft + 1) * (machMaxLeft + 1);
        Scalar temp = 0.5 * rhoRight * pLeft / (rhoRight * pLeft + rhoLeft * pRight);
        if (machMaxLeft >= 0)
            machPositiveAUSM = machMaxLeft + temp * (machMaxLeft - 1) * (machMaxLeft - 1);
        else
            machPositiveAUSM =               temp * (machMaxLeft + 1) * (machMaxLeft + 1);
    }
    else
    {
        if (machMaxLeft >= 0.0)
        {
            machPositiveAUSM = machMaxLeft;
            pPositiveAUSM = 1.0;
        }
        else
        {
            machPositiveAUSM = 0.0;
            pPositiveAUSM = 0.0;
        }
    }

    const Scalar machMaxRight = velocityNormRight / soundMax;
    Scalar machNegativeAUSM = 0.0;
    Scalar pNegativeAUSM = 0.0;
    if (fabs(machMaxRight) < 1.0)
    {
        pNegativeAUSM = 0.25 * (2.0 + machMaxRight) * (machMaxRight - 1) * (machMaxRight - 1);
        Scalar temp = 0.5 * rhoLeft * pRight / (rhoRight * pLeft + rhoLeft * pRight);
        if (machMaxRight >= 0)
            machNegativeAUSM =              - temp * (machMaxRight - 1) * (machMaxRight - 1);
        else
            machNegativeAUSM = machMaxRight - temp * (machMaxRight + 1) * (machMaxRight + 1);
    }
    else
    {
        if (machMaxRight >= 0.0)
        {
            machNegativeAUSM = 0.0;
            pNegativeAUSM = 0.0;
        }
        else
        {
            machNegativeAUSM = machMaxRight;
            pNegativeAUSM = 1.0;
        }
    }
    pShare += (1.0 - currentShockSwitch) * (pPositiveAUSM * pLeft + pNegativeAUSM * pRight);
    const Scalar velocityNormalPositive = (1.0 - currentShockSwitch) * machPositiveAUSM * soundMax;
    const Scalar velocityNormalNegative = (1.0 - currentShockSwitch) * machNegativeAUSM * soundMax;

    const Scalar rhoUNormal = velocityNormalPositive * rhoLeft + velocityNormalNegative * rhoRight;
    Scalar rhovnLeftTemp = 0.0;
    Scalar rhovnRightTemp = 0.0;
    if (rhoUNormal >= 0.0) rhovnLeftTemp = rhoUNormal;
    else                   rhovnRightTemp = rhoUNormal;

    rhoUNormalLeft += rhovnLeftTemp;
    rhoUNormalRight += rhovnRightTemp;

    // AUSMD/AUSMV开关
    Scalar AUSMDVSwitch;
    Scalar temp = 10.0 * fabs(pRight - pLeft) / Min(pLeft, pRight);
    if (1 <= temp) AUSMDVSwitch = 0.5;
    else           AUSMDVSwitch = 0.5 * temp;

    rhoUUNormal += (0.5 - AUSMDVSwitch) * (rhovnLeftTemp * velocityNormLeft + rhovnRightTemp * velocityNormRight);
    rhoUUNormal += (0.5 + AUSMDVSwitch) * ( rhoLeft * velocityNormLeft * velocityNormalPositive
                                          + rhoRight * velocityNormRight * velocityNormalNegative);

    // 熵修正
    const bool temp1 = ((velocityNormLeft - soundLeft) < 0) && ((velocityNormRight - soundRight) > 0);
    const bool temp2 = ((velocityNormLeft + soundLeft) < 0) && ((velocityNormRight + soundRight) > 0);
    if ((temp1 && !temp2) || (!temp1 && temp2))
    {
        if (temp1 && !temp2)
        {
            temp = 0.125 * (1.0 - currentShockSwitch) * ( (velocityNormLeft - soundLeft)
                                                 - (velocityNormRight - soundRight));
            rhoUNormalLeft -= temp * rhoLeft;
            rhoUNormalRight += temp * rhoRight;
        }
        else
        {
            temp = 0.125 * (1.0 - currentShockSwitch) * ( (velocityNormLeft + soundLeft)
                                                 - (velocityNormRight + soundRight));
            rhoUNormalLeft -= temp * rhoLeft;
            rhoUNormalRight += temp * rhoRight;
        }
    }

    // 计算面通量
    const Scalar left = rhoUNormalLeft * faceArea;
    const Scalar right = rhoUNormalRight * faceArea;
    faceFlux.massFlux = left + right;
    faceFlux.momentumFlux = left * UTildaLeft + right * UTildaRight + (rhoUUNormal + pShare) * faceNormal  * faceArea;
    faceFlux.energyFlux = left * enthalpyLeft + right * enthalpyRight;    

    return faceFlux;
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux