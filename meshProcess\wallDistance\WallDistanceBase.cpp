﻿#include "meshProcess/wallDistance/WallDistanceBase.h"

WallDistanceBase::WallDistanceBase(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_)
    : pmesh(mesh), WallBoundaryFace(wallBoundaryFace_), 
      wallDistance(mesh->v_nearWallDistance), elementSize(mesh->n_elemNum)
{}

WallDistanceBase::~WallDistanceBase()
{}

Scalar WallDistanceBase::CalculateFromElementTOFace(const int &elementID, const int &faceID, const bool &project)
//project:是否按照投影方式计算
{
    const Vector &elementCenter = pmesh->v_elem[elementID].GetCenter();
    const Face &face = WallBoundaryFace[faceID].first;
    const Vector &faceNorm = face.GetNormal();
    const Vector d = face.GetCenter() - elementCenter;
    const Scalar dist = d.Mag();
	
    if (dist < SMALL) return Scalar0;
	if((d.GetNormal() & faceNorm) < -SMALL) return -1.0;
	
    if (!project) return dist;
    if (WallBoundaryFace[faceID].second.size() == 0) return dist;

    const Scalar dNorm = d & faceNorm;
    const Node projectNode = elementCenter + dNorm *faceNorm; //计算投影点

    const auto &nodeList = WallBoundaryFace[faceID].second;
    const int nodeNum = nodeList.size();

    if (nodeNum == 2) //线段
    {
        const Node &node0 = nodeList[0];
        const Node &node1 = nodeList[1];

        if (((node0 - projectNode) & (node1 - projectNode)) < 0.0) //投影点位于线段内
        {
            return fabs(dNorm);
        }
        else //投影点位于线段外，返回距离两端点的最小值
        {
            const Vector distance0 = node0 - elementCenter;
            const Vector distance1 = node1 - elementCenter;
            return Min(distance0.Mag(), distance1.Mag());
        }
    }
    else //多面形
    {
        //计算投影点与各边构成的三角形的矢量面积
        std::vector<Vector> area(nodeNum);
        for (int k = 0; k < nodeNum; k++)
        {
            const Node &node0 = nodeList[k];
            const Node &node1 = nodeList[(k+1)%nodeNum];
            area[k] = (node1 - node0) ^ (projectNode - node1);
        }

        bool flag = true;//投影点位于多边形内部
        for (int k = 0; k < nodeNum; k++)
        {
            if ((area[k] & area[(k + 1) % nodeNum]) < 0.0) //投影点位于多边形外部
            {
                flag = false;
                break;
            }
        }

        if (flag) return fabs(dNorm); //投影点位于多边形内部
        else //投影点位于多边形外部
        {
            Scalar minDistance = INF;
            for (int k = 0; k < nodeNum; k++)
            {
                const Node &node0 = nodeList[k];
                const Scalar distance = (node0 - elementCenter).Mag();
                if (minDistance > distance)  minDistance = distance;
            }
            return minDistance;
        }
    }
}