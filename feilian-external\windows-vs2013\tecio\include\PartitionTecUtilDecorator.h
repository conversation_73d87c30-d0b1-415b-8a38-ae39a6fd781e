 #pragma once
#include "AltTecUtil.h"
#include "ThirdPartyHeadersBegin.h"
#include <boost/shared_ptr.hpp>
#include "ThirdPartyHeadersEnd.h"
namespace tecplot { namespace ___3933 { class PartitionTecUtilDecorator : public ___37 { public: PartitionTecUtilDecorator(___37& ___36, ___4636 zone); virtual ~PartitionTecUtilDecorator(); virtual void      ___3817(char** ___3855) const; virtual void      ___3827(___3839* ___3819) const; virtual ___2227 ___3832(___3839 ___3819) const; virtual char*     ___3833(___3839 ___3819, ___2227 ___3853) const; virtual ___264 ___235() const; virtual ___264 ___274(___4636 zone) const; virtual ___264 ___273(___4352 ___4336) const; virtual int32_t    ___247(___264 ___265) const; virtual void       ___243(___264 ___265, int32_t index, char** ___2685, ___90* ___4314, AuxDataType_e* type, ___372* ___3361) const; virtual ___372   ___896(void) const; virtual ___372   datasetGetTitle(char** datasetTitle) const; virtual int32_t     ___889(void) const; virtual ___3501      datasetGetRelevantZones(double solutionTimeMin, double solutionTimeMax, ___372 ignoreStaticZones) const; virtual ___4636 ___891(void) const; virtual ___4352  ___890(void) const; virtual ___4352  ___4345(char ___214) const; virtual ___372   ___4344(___4352 ___4368, char** ___4362) const; virtual int32_t     ___4343(___4352 ___4368) const; virtual ___372   ___4638(___4636 ___4658) const; virtual ___372   ___4640(___4636 ___4658) const; virtual ___372   ___4641(___4636 ___4658) const; virtual int32_t     ___4613(___4636 ___4658) const; virtual ___372   ___4614(___3501* ___1153) const; virtual void        ___4615(___4636 ___4658, ___1844& ___2715) const; virtual ___372   ___4616(___4636 ___4658, char** ___4652) const; virtual ___4636 ___4617(___4636 ___4658) const; virtual double      ___4618(___4636 ___4658) const; virtual ___1172  ___4619(___4636 ___4658) const; virtual ZoneType_e  ___4620(___4636 ___4658) const; virtual ___372   ___4353(___4352 ___4368) const; virtual ___372   varGetEnabled(___3501* enabledVars) const; virtual int32_t     solutionTimeGetNumTimeSteps() const; virtual double      solutionTimeGetMinTime() const; virtual double      solutionTimeGetMaxTime() const; virtual ___372   ___3768() const; virtual GeomID_t     ___1592(void); virtual TextID_t     ___4087(void); virtual int32_t     ___797(void); virtual ___372   ___796(___3839* ___2171, int32_t ___4453); virtual void        ___3779(char const* ___3001, ___372 ___3584, ___372 ___3579) const; virtual void        ___3778(char const* ___3001) const; virtual ___372   ___3769(int PercentDone) const; virtual void        ___3770(void) const; virtual ___372   ___1983(void) const; virtual void        ___858(void); virtual void        ___859(void); virtual ___4636 ___544(___3501 ___4684, ___4636 zone) const; virtual ___3501      ___545(___4636 zone) const; virtual ValueLocation_e ___910(___4636 ___4658, ___4352 ___4368) const; virtual ValueLocation_e ___911(___1361 ___1351) const; virtual ___372       ___913(___4636 zone, ___4352 ___4336, double* minVal, double* maxVal) const; virtual ___372       ___912(___1361 ___1351, double* minVal, double* maxVal) const; virtual FieldDataType_e ___923(___4636 ___4658, ___4352 ___4368); virtual ___1172      ___921(___4636 ___4658, ___4352 ___4368); virtual ___1361 ___918(___4636 ___4658, ___4352 ___4368); virtual ___1361 ___915(___4636 ___4658, ___4352 ___4368); virtual ___1361 ___917(___4636 ___4658, ___4352 ___4368); virtual ___1361 ___916(___4636 ___4658, ___4352 ___4368); virtual ___1361 ___924(___4636 ___4658, ___4352 ___4368); virtual double       ___909(___1361 ___1351, ___81 ___2733); virtual void         dataValueSetByRef(___1361 ___1351, ___81 ___2733, double ___4298); virtual void         ___919(___4636 ___4658, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363); virtual void         ___925(___4636 ___4658, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363); virtual ___4636  ___914(___3501 ___4684, ___4636 zone, ___4352 ___4336) const;
virtual ___3501       ___922(___4636 ___4658, ___4352 ___4368) const; virtual ___372    ___926(___4636 ___4658, ___4352 ___4368) const; virtual ___1383 ___927(___1361 ___1309); virtual ___1384 ___928(___1361 ___1309); virtual FieldDataType_e ___920(___1361 ___1352); virtual ___2727       ___867(___4636 ___4658); virtual ___2727       ___869(___4636 ___4658); virtual ___2718      ___865(___2727 ___2723, ___465 ___468, ___682 ___683); virtual void             ___870(___2727 ___2723, ___465 ___468, ___682 ___683, ___2718 ___2716); virtual OffsetDataType_e dataNodeGetRawItemType(___2727 ___2723); virtual int32_t*         dataNodeGetRawPtrByRef(___2727 ___2723); virtual int64_t*         dataNodeGetRawPtrByRef64(___2727 ___2723); virtual ___2742 dataNodeToElemMapGetReadableRef(___4636 ___4658) const; ___465              dataNodeToElemMapGetNumElems(___2742 nodeToElemMap, ___2718 ___2709) const; ___465              dataNodeToElemMapGetElem(___2742 nodeToElemMap, ___2718 ___2709, ___465 elemOffset) const; virtual FaceNeighborMode_e ___836(___4636 ___4658) const; virtual void ___837( ___1292 ___1274, ___2227 ___1144, int32_t face, int32_t ___2692, ___2227* ___2691, ___4636* ___2695) const; virtual ___372 ___835( ___1292 ___1274, ___2227 ___1144, int32_t face, ___3501 ___4) const; virtual int32_t ___838(___1292 ___1274, ___2227 ___1144, int32_t face, ___372* neighborsAreUserSpecified) const; virtual ___1292 ___839(___4636 zone) const; virtual ___372 setAddMember(___3501 set, ___3493 ___2401, ___372 showErr) const; virtual ___3501 setAlloc(___372 showErr) const; virtual void ___3484(___3501* set) const; virtual ___3493 ___3491(___3501 set, ___3493 ___2401) const; virtual ___3493 setGetPrevMember(___3501 set, ___3493 ___2401) const; virtual ___3493 setGetMemberCount(___3501 set) const; virtual ___372 ___3495(___3501 set, ___3493 ___2401) const; virtual ___372 setIsEqual(___3501 ___3477, ___3501 ___3478) const; virtual void setRemoveMember(___3501 set, ___3493 ___2401) const; virtual void ___1557(GeomID_t ___1805, int32_t ___3157, ___2227 ___3141, double* x, double* ___4583) const; virtual void ___1558(GeomID_t ___1805, ___2227 ___3141, double* x, double* ___4583) const; virtual void ___1560(GeomID_t ___1805, int32_t ___3157, ___2227 ___3141, double* x, double* ___4583, double* z) const; virtual void ___1561(GeomID_t ___1805, ___2227 ___3141, double* x, double* ___4583, double* z) const; virtual double ___1564(GeomID_t ___1805) const; virtual ArrowheadAttachment_e ___1565(GeomID_t ___1805) const; virtual double ___1566(GeomID_t ___1805) const; virtual ArrowheadStyle_e ___1567(GeomID_t ___1805) const; virtual double ___1570(GeomID_t ___1805) const; virtual int32_t ___1576(GeomID_t ___1805) const; virtual void ___1577(GeomID_t ___1805, double* ___1824, double* ___4394) const; virtual void ___1591(GeomID_t ___1805, double* ___4574, double* ___4591, double* ___4715) const; virtual Clipping_e ___1593(GeomID_t ___1805) const; virtual ___516 ___1594(GeomID_t ___1805) const; virtual DrawOrder_e ___1595(GeomID_t ___1805) const; virtual ___516 ___1596(GeomID_t ___1805) const; virtual ___372 ___1597(GeomID_t ___1805) const; virtual LinePattern_e ___1598(GeomID_t ___1805) const; virtual double ___1599(GeomID_t ___1805) const; virtual ___372 ___1600(GeomID_t ___1805, char** macroFunctionCmd) const; virtual GeomID_t ___1601(GeomID_t ___1805) const; virtual double ___1602(GeomID_t ___1805) const; virtual CoordSys_e ___1603(GeomID_t ___1805) const; virtual GeomID_t ___1604(GeomID_t ___1805) const; virtual Scope_e ___1605(GeomID_t ___1805) const; virtual GeomForm_e ___1606(GeomID_t ___1805) const; virtual ___4636 ___1607(GeomID_t ___1805) const; virtual ___372 ___1610(GeomID_t ___1805) const; virtual ___2227 ___1619(GeomID_t ___1805, int32_t ___3157) const; virtual ___2227 ___1620(GeomID_t ___1805) const; virtual ___2227 ___1626(GeomID_t ___1805) const; virtual void ___1628(GeomID_t ___1805, double* ___4458, double* ___1826) const; virtual double ___1648(GeomID_t ___1805) const; virtual ___516 ___4064(TextID_t ___4171) const; virtual ___516 ___4065(TextID_t ___4171) const; virtual double ___4066(TextID_t ___4171) const; virtual double ___4067(TextID_t ___4171) const;
virtual TextBox_e ___4068(TextID_t ___4171) const; virtual TextAnchor_e ___4084(TextID_t ___4171) const; virtual void ___4085(TextID_t ___4171, double* ___4574, double* ___4591, double* ___4715) const; virtual double ___4086(TextID_t ___4171) const; virtual Clipping_e ___4088(TextID_t ___4171) const; virtual ___516 ___4089(TextID_t ___4171) const; virtual double ___4090(TextID_t ___4171) const; virtual double ___4091(TextID_t ___4171) const; virtual ___372 ___4092(TextID_t ___4171, char** ___2330) const; virtual TextID_t ___4093(TextID_t ___4171) const; virtual CoordSys_e ___4094(TextID_t ___4171) const; virtual TextID_t ___4095(TextID_t ___4171) const; virtual Scope_e ___4096(TextID_t ___4171) const; virtual Units_e ___4097(TextID_t ___4171) const; virtual ___372 ___4098(TextID_t ___4171, char** ___4126) const; virtual char* ___4099(TextID_t ___4171) const; virtual ___372 ___4100(TextID_t ___4171) const; virtual ___372 ___4101(TextID_t ___4171) const; virtual ___4636 ___4102(TextID_t ___4171) const; virtual ___372 ___4105(TextID_t ___4171) const; virtual ___2664 ___4152(); virtual void ___4153(___2664* mutex); virtual void ___4154(___2664 mutex); virtual void ___4155(___2664 mutex); virtual void ___4156(___4160 ___2118, ___90 ___2123, ___2120 ___2119); virtual int ___4157(); virtual ___2120 ___4158(); virtual void ___4159(___2120* ___2119); virtual void ___4161(___2120 ___2119); virtual ___372    ___4304(___1361 ___1351) const; virtual ___372    ___4309(___2727 ___2723) const; virtual PlotType_e   ___1513() const; virtual int32_t   datasetGetNumPartitionFiles() const; virtual int32_t   zoneGetOwnerProcess(___4636 ___4658) const; virtual int32_t   zonePartitionGetOwnerProcess(___4636 ___4658, ___4636 partitionNum) const; virtual ___372 zoneIsPartitioned(___4636 ___4658) const; virtual ___4636 zoneGetNumPartitions(___4636 ___4658) const; virtual void zonePartitionGetIJK(___4636 ___4658, ___4636 partitionNum, ___1844& ___1861) const; virtual void zonePartitionGetIJKOffset(___4636 ___4658, ___4636 partitionNum, ___1844& ___1862) const; virtual ___372 dataValueGetMinMaxByZonePartitionVar(___4636 ___4658, ___4636 partitionNum, ___4352 ___4336, double* minVal, double* maxVal) const; virtual ___1361 dataValuePartitionGetReadableNLRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const; virtual ___1361 dataValuePartitionGetReadableCCRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const; virtual ___1361 dataValuePartitionGetReadableNativeRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const; virtual ___1361 dataValuePartitionGetReadableDerivedRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const; virtual ___1361 dataValuePartitionGetWritableNativeRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const; virtual void         dataValuePartitionGetReadableRawPtr(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363) const; virtual void         dataValuePartitionGetWritableRawPtr(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363) const; virtual ___2727 dataNodePartitionGetReadableRef(___4636 ___4658, ___4636 partitionNum) const; virtual ___2727 dataNodePartitionGetWritableRef(___4636 ___4658, ___4636 partitionNum) const; virtual ___2742 dataNodeToElemMapPartitionGetReadableRef(___4636 ___4658, ___4636 partitionNum) const; virtual GhostInfo_pa zoneGhostNodeInfoGetRef(___4636 ___4658) const; virtual GhostInfo_pa zoneGhostCellInfoGetRef(___4636 ___4658) const; virtual GhostInfo_pa zonePartitionGhostNodeInfoGetRef(___4636 ___4658, ___4636 partitionNum) const; virtual GhostInfo_pa zonePartitionGhostCellInfoGetRef(___4636 ___4658, ___4636 partitionNum) const; virtual ___81               ghostInfoGetNumItemsByRef(GhostInfo_pa ghostInfo) const; virtual ___81               ghostInfoGetItemByRef(GhostInfo_pa ghostInfo, ___81 itemNum) const; virtual ___2090::___2980 ghostInfoGetNeighborByRef(GhostInfo_pa ghostInfo, ___81 itemNum) const; virtual ___81               ghostInfoGetNeighborItemByRef(GhostInfo_pa ghostInfo, ___81 itemNum) const; private: ___37&       ___2337; ___4636 const m_zoneNum; }; }}
