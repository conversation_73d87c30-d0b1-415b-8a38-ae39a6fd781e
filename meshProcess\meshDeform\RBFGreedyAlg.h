﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshDeform.h
//! <AUTHOR>
//! @brief 基于贪心算法的RBFs类
//! @date 2022-02-14
//
//------------------------------修改标志----------------------------------------
//------------------------------------------------------------------------------
#ifndef _specialModule_meshDeform_RBFGreedyAlg_
#define _specialModule_meshDeform_RBFGreedyAlg_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>

#include "basic/mesh/MeshSupport.h"
#include "basic/common/Vector.h"
#include "basic/common/AllocateArray.h"
#include "basic/common/InterpolationTools.h"

// 求解器类型枚举
enum class RBFSolverType {
    TRADITIONAL_GAUSS,    // 传统高斯消元
    GREEDY_ALGORITHM,     // 贪心算法
    GREEDY_IMPROVED,      // 改进贪心算法
    MKL_SERIAL,          // 串行LAPACK求解（不再依赖MKL）
    MKL_PARALLEL,        // 分布式ScaLAPACK并行求解
    MEMORY_OPTIMIZED     // 内存优化算法
};

// 简化的求解器配置结构
struct RBFSolverConfig {
    RBFSolverType solverType = RBFSolverType::TRADITIONAL_GAUSS;
    int serialThreshold = 5000;        // 串行/分布式求解阈值
    int skipInterval = 3;              // 缩减时每组中选取的节点数
    int maxNodes = 50000;              // 缩减时的最大节点数
    int groupSize = 10;                // 每组的节点数（可配置）
    bool enableMKL = false;            // 是否启用MKL
    bool enableParallel = false;       // 是否启用并行

    // 内存控制参数
    bool enableMemoryControl = true;    // 是否启用内存监控
    double memorySafetyFactor = 0.8;    // 内存安全系数（使用可用内存的80%）
    size_t minMemoryMB = 100;          // 最小内存需求（MB）
    int minNodes = 100;                // 最小节点数

    // 构造函数，根据编译选项设置默认值
    RBFSolverConfig() {
#if defined(_EnableMKL_)
        enableMKL = true;
#endif
#if defined(_BaseParallelMPI_)
        enableParallel = true;
#endif
    }
};

class RBFGreedyAlg
{
public:
	RBFGreedyAlg(std::vector<Node> &v_wallNode_,std::vector<Node> &v_wallNode_deform_,Scalar &R_ );

	// 析构函数
	~RBFGreedyAlg();

	//直接使用贪心算法求权重系数
	std::vector<Vector> DirectGreedyAlg();

	//基于贪心方法的空间递减近似算法求权重系数
	std::vector<Vector> GreedyAlgImp();

	//贪心方法
	void GreedyAlgorithm();

	//直接求权重系数
	std::vector<Vector> DirectRBFAlg();

	//内存优化的RBF算法：通过分组节点选取降低矩阵规模，使用ScaLAPACK求解稠密系统
	std::vector<Vector> MemoryOptimizedRBFAlg(int skipInterval = 3, int maxNodes = 5000, int parallelThreshold = 5000);

	//智能节点选择策略：保证均匀性和自适应调整
	std::vector<int> SelectNodeSubset(int skipInterval, int maxNodes, int groupSize = 10);

	//使用ScaLAPACK求解稠密RBF系统
	std::vector<Vector> SolveRBFWithScaLAPACK(const std::vector<int>& selectedIndices, int parallelThreshold = 5000);

	//串行LAPACK求解（使用标准LAPACK，不依赖MKL）
	void SolveRBFWithSerialLAPACK(const std::vector<std::vector<Scalar>>& matrix,
	                              const std::vector<Vector>& rhs,
	                              std::vector<Vector>& solution);

	//分布式RBF矩阵构建和ScaLAPACK求解（内存优化版本）
	void SolveRBFWithDistributedScaLAPACK(const std::vector<int>& selectedIndices,
	                                       std::vector<Vector>& solution);

	//智能求解器选择：根据配置自动选择最优求解策略
	std::vector<Vector> SolveWithOptimalStrategy(const RBFSolverConfig& config);

	//内存监控和自适应节点选择
	std::vector<int> SelectNodeSubsetWithMemoryControl(int& skipInterval, int& maxNodes, int groupSize = 10);

	//获取当前系统可用内存（MB）
	size_t GetAvailableMemoryMB();

	//估算RBF矩阵所需内存（MB）
	size_t EstimateRBFMatrixMemoryMB(int nodeCount);

	//检查内存是否足够
	bool CheckMemoryAvailability(int nodeCount, double safetyFactor = 0.8);

	void rbfGreedy();

private:

	// 壁面变形向量
	std::vector<Vector> v_wallDelta_all;

	// 壁面节点坐标
	std::vector<Vector> &v_wallNode;

	// 变形后壁面节点坐标
	std::vector<Vector> &v_wallNode_deform;

	//插值矩阵
	std::vector<std::vector<Scalar>> Matrix_all;

	//全局权重系数
	std::vector<Vector> v_weightM;

	std::vector<Vector> v_wallDelta;

	//壁面节点数量
	int n_wallNode;

	//限制
	Scalar limx;

	Scalar R;

	int cycle ;


};

#endif