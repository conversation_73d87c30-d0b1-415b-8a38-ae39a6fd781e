﻿#include "feilian-specialmodule/particle/property/PhysicalProperty.h"

namespace Particle
{
PhysicalProperty::PhysicalProperty()
{
    particleTypeNumber = 0;
    wallTypeNumber  = 0;

    particlePurePropertyFlag  = false;
    binaryPropertyPPFlag  = false;
    binaryPropertyPWFlag   = false;

    relativeVelocity = 1.0;
}

void PhysicalProperty::SetParticleProperty(DistributionProperty *PSDP)
{
    int num_prop = PSDP->GetPropertyTypeNumber();
    this->particleTypeNumber = num_prop;
    this->particlePureProperty.resize(num_prop);
    
    for(int i = 0; i < num_prop; ++i)
    {
        this->particlePureProperty[i] = PSDP->GetPureProperty(i);
        this->particlePureProperty[i].Calculate();
    }

    this->particlePurePropertyFlag = true;
}

void PhysicalProperty::SetWallTypeNumber(const int &wallTypeNumber_)
{
    this->wallTypeNumber = wallTypeNumber_;
    this->wallPureProperty.resize(this->wallTypeNumber);
}

void PhysicalProperty::SetWallProperty(const int &wallTypeID, const PureProperty &pureProperty)
{
	this->wallPureProperty[wallTypeID].SetYoung(pureProperty.GetYoungMod());
	this->wallPureProperty[wallTypeID].SetShear(pureProperty.GetShearMod());
	this->wallPureProperty[wallTypeID].SetYeild(pureProperty.GetYeildStress());
	this->wallPureProperty[wallTypeID].SetPratio(pureProperty.GetPoissonsRatio());
	this->wallPureProperty[wallTypeID].SetKappa(pureProperty.GetKappa());
	this->wallPureProperty[wallTypeID].SetRad(10000000.0);
	this->wallPureProperty[wallTypeID].SetDensity(10000000.0);
	this->wallPureProperty[wallTypeID].SetMass(10000000.0);
	this->wallPureProperty[wallTypeID].SetVol(10000000.0);
	this->wallPureProperty[wallTypeID].SetInertia(10000000.0);
}

void PhysicalProperty::SetNonWallProperty( const int &wallTypeID, const PureProperty &pureProperty)
{
	this->wallPureProperty[wallTypeID].SetYoung(pureProperty.GetYoungMod());
	this->wallPureProperty[wallTypeID].SetShear(pureProperty.GetShearMod());
	this->wallPureProperty[wallTypeID].SetYeild(pureProperty.GetYeildStress());
	this->wallPureProperty[wallTypeID].SetPratio(pureProperty.GetPoissonsRatio());
	this->wallPureProperty[wallTypeID].SetKappa(pureProperty.GetKappa());
	this->wallPureProperty[wallTypeID].SetRad(SMALL);
	this->wallPureProperty[wallTypeID].SetDensity(SMALL);
	this->wallPureProperty[wallTypeID].SetMass(SMALL);
	this->wallPureProperty[wallTypeID].SetVol(SMALL);
	this->wallPureProperty[wallTypeID].SetInertia(SMALL);
}

void PhysicalProperty::SetBinaryPropertyPP(const Configure::Particle::ParticleConfigure &DEM_opt)
{
    this->relativeVelocity = DEM_opt.binaryParasPP.relativeVelocity;
    
    if( !this->particlePurePropertyFlag )
        FatalError("SetBinaryPropertyPP: 需先指定颗粒材料属性");   
    
    const int num = this->GetParticleTypeNumber();
    this->binaryPropertyPP.resize(num);
    for(int i = 0; i < num; ++i) this->binaryPropertyPP[i].resize(num);
    
    for(int i = 0; i < num; ++i) 
    {
        for(int j = 0; j < num; ++j)
        {
            PureProperty &pari = this->particlePureProperty[i];
            PureProperty &parj = this->particlePureProperty[i];
            
            const Scalar &mu = DEM_opt.binaryParasPP.mu;
            const Scalar &mur = DEM_opt.binaryParasPP.mur;
            const Scalar &en = DEM_opt.binaryParasPP.en;
            const Scalar &et = DEM_opt.binaryParasPP.et;
            this->binaryPropertyPP[i][j] = this->CalculateBinaryProperty( pari, parj, mu, mur, en, et, DEM_opt.contact.contactForceType );
        }
    }
    
    this->binaryPropertyPPFlag = true;
}

void PhysicalProperty::SetBinaryPropertyPW(const Configure::Particle::ParticleConfigure &DEM_opt)
{
    this->relativeVelocity = DEM_opt.binaryParasPW.relativeVelocity;
    
    if( !this->particlePurePropertyFlag )
        FatalError("SetBinaryPropertyPW: 需先指定颗粒材料属性");
    
    int num = this->GetParticleTypeNumber();
    int numw = this->GetWallTypeNumber();
    this->binaryPropertyPW.resize(num);
    for(int i = 0; i < num; ++i) this->binaryPropertyPW[i].resize(numw);
    
    for(int i = 0; i < num; ++i) 
    {
        for(int j = 0; j < numw; ++j)
        {
            PureProperty &pari = this->particlePureProperty[i];
            PureProperty &wall = this->wallPureProperty[j];
            
            const Scalar &mu = DEM_opt.binaryParasPW.mu;
            const Scalar &mur = DEM_opt.binaryParasPW.mur;
            const Scalar &en = DEM_opt.binaryParasPW.en;
            const Scalar &et = DEM_opt.binaryParasPW.et;

            this->binaryPropertyPW[i][j] = this->CalculateBinaryProperty( pari, wall , mu, mur, en, et, DEM_opt.contact.contactForceType );
        }
    }
    
    this->binaryPropertyPWFlag = true;
}

BinaryProperty PhysicalProperty::CalculateBinaryProperty(const PureProperty &pari, const PureProperty &parj,
                                                    const Scalar &mu, const Scalar &mur, const Scalar &en, const Scalar &et,
                                                    const Configure::Particle::ContactForceType &contactForceType )
{
    BinaryProperty Bnry;
    Bnry.Reff = 1.0/(1.0/pari.GetRad() + 1.0/parj.GetRad());
    Bnry.meff = 1.0/(1.0/pari.GetMass() + 1.0/parj.GetMass());
    Bnry.Yeff = 1.0/( (1.0-pari.GetPoissonsRatio()*pari.GetPoissonsRatio()) / pari.GetYoungMod()
                   +  (1.0-parj.GetPoissonsRatio()*parj.GetPoissonsRatio())/parj.GetYoungMod() );
    Bnry.Geff = 1.0/( (2.0-pari.GetPoissonsRatio())/pari.GetShearMod()
                    + (2.0-parj.GetPoissonsRatio())/parj.GetShearMod()  );
    Bnry.kn = 1.2024 * pow(sqrt(Bnry.meff) * Bnry.Yeff*Bnry.Yeff * Bnry.Reff * this->relativeVelocity, 2.0/5.0);
        
    Scalar kappa = ( (1.0 - pari.GetPoissonsRatio())/pari.GetShearMod() + (1.0 - parj.GetPoissonsRatio())/parj.GetShearMod() )
                 / ( (1.0 - 0.5*pari.GetPoissonsRatio())/pari.GetShearMod() + (1.0 - 0.5*parj.GetPoissonsRatio())/parj.GetShearMod() );
    Bnry.kt = kappa*Bnry.kn;

    Bnry.en = en;
    Bnry.et = et;
    Bnry.fric = mu;
    Bnry.roll_fric = mur;
    
    Bnry.dmp_n = CalculateDampingNormal(Bnry , contactForceType);
    Bnry.dmp_t = CalculateDampingTangential(Bnry , contactForceType);

    return Bnry;
}

Scalar PhysicalProperty::CalculateDampingNormal( const BinaryProperty &Bnry, const Configure::Particle::ContactForceType &contactForceType )
{
    switch(contactForceType)
    {
        case Configure::Particle::ContactForceType::LINEAR_NOLIMITED:
        case Configure::Particle::ContactForceType::LINEAR_LIMITED:
        {
            return -2.0 * log(Bnry.en) * sqrt(Bnry.meff*Bnry.kn) / sqrt( pow(log(Bnry.en), 2) + PI*PI);
		    break;
        }
        
        case Configure::Particle::ContactForceType::NONLINEAR_NOLIMITED:
        case Configure::Particle::ContactForceType::NONLINEAR_LIMITED:
        {
            Scalar K_hertz = 4.0/3.0*Bnry.Yeff*sqrt(Bnry.Reff); 
            return -2.2664 * log(Bnry.en) * sqrt(Bnry.meff*K_hertz) / sqrt( pow(log(Bnry.en), 2) + 10.1354);
        }
        default:
        {
            FatalError("CalculateDampingNormal: 不支持此类法向阻尼计算方法");
            return Bnry.dmp_n;
        }
    }
}

Scalar PhysicalProperty::CalculateDampingTangential( const BinaryProperty &Bnry, const Configure::Particle::ContactForceType &contactForceType )
{
    return 0.0;
}

} // namespace Particle