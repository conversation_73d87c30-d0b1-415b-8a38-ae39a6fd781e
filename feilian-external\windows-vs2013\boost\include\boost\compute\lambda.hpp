//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_LAMBDA_HPP
#define BOOST_COMPUTE_LAMBDA_HPP

#include <boost/compute/lambda/context.hpp>
#include <boost/compute/lambda/functional.hpp>
#include <boost/compute/lambda/get.hpp>
#include <boost/compute/lambda/make_pair.hpp>
#include <boost/compute/lambda/make_tuple.hpp>
#include <boost/compute/lambda/placeholders.hpp>
#include <boost/compute/lambda/result_of.hpp>

#endif // BOOST_COMPUTE_LAMBDA_HPP
