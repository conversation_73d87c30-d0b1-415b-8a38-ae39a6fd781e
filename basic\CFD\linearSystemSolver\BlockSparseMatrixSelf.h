﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BlockSparseMatrixSelf.h
//! <AUTHOR>
//! @brief 块稀疏矩阵类(自定义类型，采用压缩存储)
//! @date 2024-12-10
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-10 气动院
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_CFD_BlockSparseMatrixSelf_
#define _basic_CFD_BlockSparseMatrixSelf_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrix.h"
#include "basic/mesh/SubMesh.h"

/**
* @brief 块稀疏矩阵类
*
*/
class BlockSparseMatrixSelf : public BlockSparseMatrix
{

public:
	/**
	 * @brief 块稀疏矩阵的构造函数
	 * 
	 * 初始化块稀疏矩阵，关联到指定的网格（Mesh）并设置变量数。
	 * 
	 * @param[in] mesh_ 关联的网格对象，类型为Mesh *
	 * @param[in] nVariable_ 变量的数量，类型为const int
	 */
	BlockSparseMatrixSelf(Mesh *mesh_, const int nVariable_);
	
	/**
	 * @brief 块稀疏矩阵的析构函数
	 * 
	 * 释放块稀疏矩阵占用的内存和资源。
	 */
	~BlockSparseMatrixSelf();
	
	/**
	 * @brief 初始化矩阵
	 * 
	 * 执行内部数据结构的初始化操作，准备矩阵以供使用。
	 */
	void Initialize();
	
	/**
	 * @brief 将矩阵所有元素设置为零
	 * 
	 * 初始化或重置块稀疏矩阵，将所有元素设置为零值。
	 */
	void SetValZero();
	
	/**
	 * @brief 向对角线位置添加标量值
	 * 
	 * 在指定行的对角线位置上增加一个标量值
	 * 
	 * @param[in] row 指定的行索引，类型为const int &
	 * @param[in] val 需要添加的标量值，类型为const Scalar &
	 */
	void AddVal2Diag(const int &row, const Scalar &val);
	
	/**
	 * @brief 向对角线位置添加矩阵块
	 * 
	 * 在指定行的对角线位置上增加一个矩阵块
	 * 
	 * @param[in] row 指定的行索引，类型为const int &
	 * @param[in] val 需要添加的矩阵块，类型为const Matrix &
	 */
	void AddBlock2Diag(const int &row, const Matrix &val);
	
	/**
	 * @brief 更新矩阵中的块
	 * 
	 * 根据面ID和相关元素信息更新矩阵中的块
	 * 
	 * @param[in] faceID 面的标识符，类型为const int &
	 * @param[in] elemI 第一个元素的索引，类型为const int &
	 * @param[in] elemJ 第二个元素的索引，类型为const int &
	 * @param[in] valI 需要更新的第一个矩阵块，类型为const Matrix &
	 * @param[in] valJ 需要更新的第二个矩阵块，类型为const Matrix &
	 */
	void UpdateBlocks(const int &faceID, const int &elemI, const int &elemJ, const Matrix &valI, const Matrix &valJ);
	
	/**
	 * @brief 删除指定单元所对应块矩阵的指定行的值
	 * 
	 * 修改指定单元的块矩阵的指定行的对角阵元素为1，其余元素为0
	 * 
	 * @param[in] row 需要删除的矩阵块行索引（单元编号），类型为const int &
	 * @param[in] index0 需要删除的块内行索引（变量编号），类型为const int &
	 */
	void DeleteValsRowi(const int &row, const int &index0);

public:
	/**
	 * @brief 矩阵向量乘积计算
	 *
	 * 该函数计算一个矩阵与一个向量的乘积，并将结果存储在输出向量中。
	 *
	 * @param[in] vec 输入向量，类型为std::vector<Scalar>
	 * @param[out] prod 输出向量，类型为std::vector<Scalar>
	 */
	void MatrixVectorProduct(const std::vector<Scalar> &vec, std::vector<Scalar> &prod) const;

	/**
	 * @brief 将一个值设置到矩阵的对角线位置
	 *
	 * 该函数在指定行数的对角线位置上设置一个值。
	 *
	 * @param[in] row 指定的行索引，类型为const int &
	 * @param[in] val 需要设置的值，类型为const Scalar &
	 */
	void SetVal2Diag(const int &row, const Scalar &val);

	/**
	 * @brief 计算矩阵的逆矩阵
	 *
	 * 该函数计算一个方阵的逆矩阵，并将结果存储在指定的内存位置。
	 *
	 * @param[in] matrix 输入矩阵，类型为Scalar *
	 * @param[out] inverse 输出逆矩阵，类型为Scalar *
	 */
	void MatrixInverse(Scalar *matrix, Scalar *inverse) const;

	/**
	 * @brief 获取矩阵块的指针
	 *
	 * 该函数在指定行和列位置上获取一个矩阵块的指针。
	 *
	 * @param[in] row 指定的行索引，类型为const int &
	 * @param[in] col 指定的列索引，类型为const int &
	 * @return 返回矩阵块的指针，类型为Scalar *
	 */
	Scalar *GetBlock(const int &row, const int &col);

	/**
	 * @brief 设置矩阵块的值
	 *
	 * 该函数在指定行和列位置上设置一个矩阵块的值。
	 *
	 * @param[in] row 指定的行索引，类型为const int &
	 * @param[in] col 指定的列索引，类型为const int &
	 * @param[in] val 需要设置的值数组，类型为Scalar *
	 */
	void SetBlock(const int &row, const int &col, Scalar *val);

	/**
	 * @brief 获取矩阵的块大小
	 *
	 * @return 返回矩阵的块大小，类型为const int
	 */
	const int GetBlockSize() const { return this->blockSize; }

	/**
	 * @brief 获取指定行的起始索引
	 *
	 * 该函数返回指定行的起始索引。
	 *
	 * @param[in] row 行索引，类型为const int &
	 * @return 返回起始索引，类型为int
	 */
	const int GetBlockIndexRowBegin(const int &row) const { return this->rowPtr[row]; }

	/**
	 * @brief 获取对角线位置的元素索引
	 *
	 * 该函数返回指定行或列的对角线位置的元素索引。
	 *
	 * @param[in] row 行索引，类型为const int &
	 * @return 返回对角线位置的元素索引，类型为int
	 */
	const int GetBlockIndexRowDiag(const int &row) const { return this->diagPtr[row]; }

	/**
	 * @brief 获取指定行的列索引
	 *
	 * 该函数返回指定行的列索引。
	 *
	 * @param[in] row 行索引，类型为const int &
	 * @return 返回列索引，类型为int
	 */
	const int GetBlockColIndex(const int &blockID) const { return this->col_ind[blockID]; }

	/**
	 * @brief 获取矩阵中的值
	 *
	 * 该函数返回矩阵中指定位置的值。
	 *
	 * @param[in] index 元素的索引，类型为const int &
	 * @return 返回矩阵中的值，类型为Scalar
	 */
	const Scalar GetValue(const int &index) const { return this->value[index]; }
	
private:
	std::vector<Scalar> value; ///< 矩阵的存储
	
	int rowSize; ///< 矩阵块的行数
	int nVariable2;///< nVariable*nVariable
	int blockSize; ///< 非0块的数量

	std::vector<int> rowPtr; ///< 每行数据块的起始位置
	std::vector<int> diagPtr; ///< 每行对角阵块的位置
	std::vector<std::pair<int, int>> faceIDPtr; ///< 面的左右单元数据块的位置
	std::vector<int> col_ind; ///< 矩阵块所在的列号	
};

#endif