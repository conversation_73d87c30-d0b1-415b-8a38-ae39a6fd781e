//  (C) Copyright <PERSON> 2005.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)


#include <boost/tr1/detail/config_all.hpp>

#if (!defined(BOOST_TR1_TYPE_TRAITS_INCLUDED) || defined(BOOST_TR1_NO_RECURSION)) && defined(BOOST_HAS_CPP_0X)
#ifndef BOOST_TR1_TYPE_TRAITS_INCLUDED
#  define BOOST_TR1_TYPE_TRAITS_INCLUDED
#endif
#  ifdef BOOST_TR1_NO_TYPE_TRAITS_RECURSION2
#     define BOOST_TR1_NO_TYPE_TRAITS_RECURSION3
#  elif defined(BOOST_TR1_NO_TYPE_TRAITS_RECURSION)
#     define BOOST_TR1_NO_TYPE_TRAITS_RECURSION2
#  elif !defined(BOOST_TR1_NO_RECURSION)
#     define BOOST_TR1_NO_RECURSION
#     define BOOST_TR1_NO_TYPE_TRAITS_RECURSION
#  endif
#  if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#     include_next <type_traits>
#  else
#     include BOOST_TR1_STD_HEADER(type_traits)
#  endif
#ifdef BOOST_TR1_NO_TYPE_TRAITS_RECURSION3
#  undef BOOST_TR1_NO_TYPE_TRAITS_RECURSION3
#elif defined(BOOST_TR1_NO_TYPE_TRAITS_RECURSION2)
#  undef BOOST_TR1_NO_TYPE_TRAITS_RECURSION2
#elif defined(BOOST_TR1_NO_TYPE_TRAITS_RECURSION)
#     undef BOOST_TR1_NO_RECURSION
#     undef BOOST_TR1_NO_TYPE_TRAITS_RECURSION
#  endif
#endif

#if !defined(BOOST_TR1_FULL_TYPE_TRAITS_INCLUDED) && !defined(BOOST_TR1_NO_RECURSION)
#  define BOOST_TR1_FULL_TYPE_TRAITS_INCLUDED
#  define BOOST_TR1_NO_RECURSION
#  include <boost/tr1/type_traits.hpp>
#  undef BOOST_TR1_NO_RECURSION
#endif


