//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
//  Copyright (c) 2001-2011 <PERSON>
//
//  Distributed under the Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(SPIRIT_REPOSITORY_QI_PRIMITIVE_APR_28_2009_1258PM)
#define SPIRIT_REPOSITORY_QI_PRIMITIVE_APR_28_2009_1258PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/repository/home/<USER>/primitive/advance.hpp>
#include <boost/spirit/repository/home/<USER>/primitive/flush_multi_pass.hpp>
#include <boost/spirit/repository/home/<USER>/primitive/iter_pos.hpp>

#endif

