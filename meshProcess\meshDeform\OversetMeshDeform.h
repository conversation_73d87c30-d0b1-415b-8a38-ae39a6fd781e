#ifndef _meshProcess_meshDeform_OversetMeshDeform_
#define _meshProcess_meshDeform_OversetMeshDeform_

#include "meshProcess/meshDeform/MeshDeform.h"
#include "meshProcess/meshDeform/RBFGreedyAlg.h"
#include "sourceFlow/package/FlowPackage.h"
#include "meshProcess/zone/ZoneManager.h"
#include "feilian-specialmodule/staticAeroelastic/meshData/MeshData.h"

#if defined(_EnableOverset_)
#include "feilian-specialmodule/oversetMesh/OversetMesh.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#endif

/**
 * @brief 重叠网格变形配置
 * 专门针对非接触子域（如主翼与操纵面分离）的情况
 */
struct OversetDeformConfig
{
    bool enableOversetDeform = true;         // 是否启用重叠网格变形
    bool updateOversetAssembly = true;       // 是否更新重叠装配
    bool enableQualityCheck = true;          // 是否启用基本质量检查
    bool skipFringeElements = true;          // 是否跳过插值单元变形
    bool verboseOutput = false;              // 是否输出详细信息

    // RBF参数
    Scalar rbfRadius = 1.5;                  // RBF半径系数
    int maxRBFNodes = 8000;                  // 最大RBF节点数

    // 智能求解参数
    bool enableSmartSolver = true;           // 启用智能求解策略
    bool enableMKL = true;                   // 启用MKL加速
    bool enableParallel = true;              // 启用并行计算
    int serialThreshold = 500;               // 串行求解阈值
    int parallelThreshold = 2000;            // 并行求解阈值
    int nodeReductionInterval = 3;           // 节点缩减间隔
    bool enableGreedyReduction = true;       // 启用贪心算法缩减
    Scalar greedyTolerance = 1e-6;           // 贪心算法容差

    // 质量控制
    Scalar minElementQuality = 0.1;          // 最小单元质量阈值
    Scalar convergenceTolerance = 1e-6;      // 收敛容差

    // 重叠区域处理
    bool preserveOverlapRegion = true;       // 保持重叠区域
    int maxDeformIterations = 3;             // 最大变形迭代次数
    bool independentZoneDeform = true;       // 独立子域变形
    Scalar oversetRBFRadius = 1.5;           // 重叠网格RBF半径
};

/**
 * @brief 重叠网格动网格变形类
 *
 * 设计原则：
 * 1. 各子域完全独立变形，无需协调
 * 2. 只对计算单元进行变形，跳过插值单元
 * 3. 变形后更新重叠装配关系
 * 4. 最小化复杂度，最大化效率
 */
class OversetMeshDeform
{
public:
    /**
     * @brief 构造函数
     * @param flowPackage_ 流场数据包
     * @param config_ 重叠网格变形配置
     */
    OversetMeshDeform(Package::FlowPackage &flowPackage_,
                     const OversetDeformConfig &config_ = OversetDeformConfig());

    /**
     * @brief 析构函数
     */
    ~OversetMeshDeform();

    /**
     * @brief 静气弹专用的重叠网格变形
     * @param aeroDeformData 气动变形数据
     */
    void ProcessForStaticAeroelasticWithOverset(const std::vector<Solver::AeroMeshData>& aeroDeformData);

    /**
     * @brief 通用的重叠网格变形接口
     * @param zoneDeformations 各子域的变形数据 <zoneID, deformation_vectors>
     */
    void ProcessOversetDeformation(const std::map<int, std::vector<Vector>>& zoneDeformations);

    /**
     * @brief 获取变形统计信息
     */
    void PrintDeformationStatistics() const;

private:
    /**
     * @brief 初始化重叠网格数据
     */
    void Initialize();

    /**
     * @brief 处理单个子域的变形
     * @param zoneID 子域编号
     * @param wallNodes 壁面节点
     * @param deformedNodes 变形后的壁面节点
     */
    void ProcessSingleZoneDeformation(int zoneID,
                                    const std::vector<Node>& wallNodes,
                                    const std::vector<Node>& deformedNodes);

    /**
     * @brief 提取指定子域的壁面节点
     * @param zoneID 子域编号
     * @param wallNodes 输出的壁面节点
     */
    void ExtractZoneWallNodes(int zoneID, std::vector<Node>& wallNodes);

    /**
     * @brief 应用RBF变形到指定子域（只变形计算单元）
     * @param zoneID 子域编号
     * @param wallNodes 壁面节点
     * @param weights RBF权重
     * @param rbfRadius RBF半径
     */
    void ApplyZoneDeformation(int zoneID,
                            const std::vector<Node>& wallNodes,
                            const std::vector<Vector>& weights,
                            Scalar rbfRadius);

    /**
     * @brief 检查节点是否属于插值单元
     * @param nodeID 节点编号
     * @return true 如果节点属于插值单元
     */
    bool IsNodeInFringeElement(int nodeID);

    /**
     * @brief 获取指定子域的所有节点ID
     * @param zoneID 子域编号
     * @param nodeIDs 输出的节点ID列表
     */
    void GetZoneNodeIDs(int zoneID, std::vector<int>& nodeIDs);

    /**
     * @brief 更新重叠装配
     */
    void UpdateOversetAssembly();

    /**
     * @brief 基本的网格质量检查
     * @return true 如果质量合格
     */
    bool CheckMeshQuality();

    /**
     * @brief 计算单元质量
     * @param elemID 单元编号
     * @return 质量指标（0-1，1为最好）
     */
    Scalar CalculateElementQuality(int elemID);

private:
    // 基础数据
    Package::FlowPackage &flowPackage;
    const OversetDeformConfig &config;

    // 网格相关
    Mesh *localMesh;
    ZoneManager *zoneManager;

    // 重叠网格相关
#if defined(_EnableOverset_)
    OversetMesh *oversetMesh;
    ElementField<int> *elemTypeField;
#endif

    // 子域信息
    int nZones;
    std::vector<int> zoneStartElemID;
    std::vector<int> zoneElemNum;

    // 并行相关
    int processorID;
    int nProcessor;

    // 统计信息
    struct DeformStats
    {
        int totalDeformedNodes = 0;
        int skippedFringeNodes = 0;
        int processedZones = 0;
        int qualityIssueElements = 0;
        double deformationTime = 0.0;
        double assemblyUpdateTime = 0.0;
        Scalar maxDeformation = 0.0;

        // 求解策略统计
        int serialSolveZones = 0;        // 串行求解的子域数
        int parallelSolveZones = 0;      // 并行求解的子域数
        int distributedSolveZones = 0;   // 分布式求解的子域数
        int totalRBFNodes = 0;           // RBF节点总数
        int reducedRBFNodes = 0;         // 缩减后的RBF节点数
    } stats;
};

/**
 * @brief 重叠网格变形的便捷接口函数
 *
 * @param flowPackage 流场数据包
 * @param aeroMeshData 气动网格变形数据
 * @param rbfRadius RBF半径系数
 * @param verboseOutput 是否输出详细信息
 */
void ProcessOversetMeshDeformation(Package::FlowPackage& flowPackage,
                                 const std::vector<Solver::AeroMeshData>& aeroMeshData,
                                 Scalar rbfRadius = 1.5,
                                 bool verboseOutput = false);

#endif // _meshProcess_meshDeform_OversetMeshDeform_
