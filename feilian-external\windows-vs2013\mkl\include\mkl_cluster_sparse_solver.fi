!===============================================================================
! Copyright 2004-2018 Intel Corporation.
!
! This software and the related documents are Intel copyrighted  materials,  and
! your use of  them is  governed by the  express license  under which  they were
! provided to you (License).  Unless the License provides otherwise, you may not
! use, modify, copy, publish, distribute,  disclose or transmit this software or
! the related documents without Intel's prior written permission.
!
! This software and the related documents  are provided as  is,  with no express
! or implied  warranties,  other  than those  that are  expressly stated  in the
! License.
!===============================================================================

!   Content:
!           Intel(R) Math Kernel Library (Intel(R) MKL) CLUSTER_SPARSE_SOLVER Fortran header file.
!
!           Contains CLUSTER_SPARSE_SOLVER routine definition
!
!*******************************************************************************

        TYPE MKL_CLUSTER_SPARSE_SOLVER_HANDLE; INTEGER(KIND=8) DUMMY; END TYPE

!
! Subroutine prototype for CLUSTER_SPARSE_SOLVER
!

      INTERFACE CLUSTER_SPARSE_SOLVER
        SUBROUTINE CLUSTER_SPARSE_SOLVER_S( PT, MAXFCT, MNUM, MTYPE, PHASE,N,A,IA,
     &      JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER,          INTENT(IN)    :: MAXFCT
          INTEGER,          INTENT(IN)    :: MNUM
          INTEGER,          INTENT(IN)    :: MTYPE
          INTEGER,          INTENT(IN)    :: PHASE
          INTEGER,          INTENT(IN)    :: N
          INTEGER,          INTENT(IN)    :: IA(*)
          INTEGER,          INTENT(IN)    :: JA(*)
          INTEGER,          INTENT(INOUT) :: PERM(*)
          INTEGER,          INTENT(IN)    :: NRHS
          INTEGER,          INTENT(INOUT) :: IPARM(*)
          INTEGER,          INTENT(IN)    :: MSGLVL
          INTEGER,          INTENT(OUT)   :: ERROR
          INTEGER,        INTENT(IN)    :: COMM

          REAL(KIND=4),     INTENT(IN)    :: A(*)
          REAL(KIND=4),     INTENT(INOUT) :: B(*)
          REAL(KIND=4),     INTENT(OUT)   :: X(*)
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_S

        SUBROUTINE CLUSTER_SPARSE_SOLVER_D( PT, MAXFCT, MNUM, MTYPE, PHASE, N, A, IA,
     &  JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER,          INTENT(IN)    :: MAXFCT
          INTEGER,          INTENT(IN)    :: MNUM
          INTEGER,          INTENT(IN)    :: MTYPE
          INTEGER,          INTENT(IN)    :: PHASE
          INTEGER,          INTENT(IN)    :: N
          INTEGER,          INTENT(IN)    :: IA(*)
          INTEGER,          INTENT(IN)    :: JA(*)
          INTEGER,          INTENT(INOUT) :: PERM(*)
          INTEGER,          INTENT(IN)    :: NRHS
          INTEGER,          INTENT(INOUT) :: IPARM(*)
          INTEGER,          INTENT(IN)    :: MSGLVL
          INTEGER,          INTENT(OUT)   :: ERROR

          REAL(KIND=8),     INTENT(IN)    :: A(*)
          REAL(KIND=8),     INTENT(INOUT) :: B(*)
          REAL(KIND=8),     INTENT(OUT)   :: X(*)
          INTEGER,          INTENT(IN)    :: COMM
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_D

        SUBROUTINE CLUSTER_SPARSE_SOLVER_SC( PT, MAXFCT, MNUM, MTYPE, PHASE, N, A, IA,
     &  JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER,          INTENT(IN)    :: MAXFCT
          INTEGER,          INTENT(IN)    :: MNUM
          INTEGER,          INTENT(IN)    :: MTYPE
          INTEGER,          INTENT(IN)    :: PHASE
          INTEGER,          INTENT(IN)    :: N
          INTEGER,          INTENT(IN)    :: IA(*)
          INTEGER,          INTENT(IN)    :: JA(*)
          INTEGER,          INTENT(INOUT) :: PERM(*)
          INTEGER,          INTENT(IN)    :: NRHS
          INTEGER,          INTENT(INOUT) :: IPARM(*)
          INTEGER,          INTENT(IN)    :: MSGLVL
          INTEGER,          INTENT(OUT)   :: ERROR

          COMPLEX(KIND=4),  INTENT(IN)    :: A(*)
          COMPLEX(KIND=4),  INTENT(INOUT) :: B(*)
          COMPLEX(KIND=4),  INTENT(OUT)   :: X(*)
          INTEGER,          INTENT(IN)    :: COMM
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_SC

        SUBROUTINE CLUSTER_SPARSE_SOLVER_DC( PT, MAXFCT, MNUM, MTYPE, PHASE, N, A, IA,
     & JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER,          INTENT(IN)    :: MAXFCT
          INTEGER,          INTENT(IN)    :: MNUM
          INTEGER,          INTENT(IN)    :: MTYPE
          INTEGER,          INTENT(IN)    :: PHASE
          INTEGER,          INTENT(IN)    :: N
          INTEGER,          INTENT(IN)    :: IA(*)
          INTEGER,          INTENT(IN)    :: JA(*)
          INTEGER,          INTENT(INOUT) :: PERM(*)
          INTEGER,          INTENT(IN)    :: NRHS
          INTEGER,          INTENT(INOUT) :: IPARM(*)
          INTEGER,          INTENT(IN)    :: MSGLVL
          INTEGER,          INTENT(OUT)   :: ERROR
          COMPLEX(KIND=8),  INTENT(IN)    :: A(*)
          COMPLEX(KIND=8),  INTENT(INOUT) :: B(*)
          COMPLEX(KIND=8),  INTENT(OUT)   :: X(*)
          INTEGER,          INTENT(IN)    :: COMM
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_DC
      END INTERFACE

!
! Subroutine prototype for CLUSTER_SPARSE_SOLVER_64
!

      INTERFACE CLUSTER_SPARSE_SOLVER_64
        SUBROUTINE CLUSTER_SPARSE_SOLVER_S_64( PT, MAXFCT, MNUM, MTYPE, PHASE,N,A,IA,
     &      JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MAXFCT
          INTEGER(KIND=8),          INTENT(IN)    :: MNUM
          INTEGER(KIND=8),          INTENT(IN)    :: MTYPE
          INTEGER(KIND=8),          INTENT(IN)    :: PHASE
          INTEGER(KIND=8),          INTENT(IN)    :: N
          INTEGER(KIND=8),          INTENT(IN)    :: IA(*)
          INTEGER(KIND=8),          INTENT(IN)    :: JA(*)
          INTEGER(KIND=8),          INTENT(INOUT) :: PERM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: NRHS
          INTEGER(KIND=8),          INTENT(INOUT) :: IPARM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MSGLVL
          INTEGER(KIND=8),          INTENT(OUT)   :: ERROR
          INTEGER,        INTENT(IN)    :: COMM

          REAL(KIND=4),     INTENT(IN)    :: A(*)
          REAL(KIND=4),     INTENT(INOUT) :: B(*)
          REAL(KIND=4),     INTENT(OUT)   :: X(*)
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_S_64

        SUBROUTINE CLUSTER_SPARSE_SOLVER_D_64( PT, MAXFCT, MNUM, MTYPE, PHASE, N, A, IA,
     &  JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MAXFCT
          INTEGER(KIND=8),          INTENT(IN)    :: MNUM
          INTEGER(KIND=8),          INTENT(IN)    :: MTYPE
          INTEGER(KIND=8),          INTENT(IN)    :: PHASE
          INTEGER(KIND=8),          INTENT(IN)    :: N
          INTEGER(KIND=8),          INTENT(IN)    :: IA(*)
          INTEGER(KIND=8),          INTENT(IN)    :: JA(*)
          INTEGER(KIND=8),          INTENT(INOUT) :: PERM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: NRHS
          INTEGER(KIND=8),          INTENT(INOUT) :: IPARM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MSGLVL
          INTEGER(KIND=8),          INTENT(OUT)   :: ERROR

          REAL(KIND=8),     INTENT(IN)    :: A(*)
          REAL(KIND=8),     INTENT(INOUT) :: B(*)
          REAL(KIND=8),     INTENT(OUT)   :: X(*)
          INTEGER,          INTENT(IN)    :: COMM
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_D_64

        SUBROUTINE CLUSTER_SPARSE_SOLVER_SC_64( PT, MAXFCT, MNUM, MTYPE, PHASE, N, A, IA,
     &  JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MAXFCT
          INTEGER(KIND=8),          INTENT(IN)    :: MNUM
          INTEGER(KIND=8),          INTENT(IN)    :: MTYPE
          INTEGER(KIND=8),          INTENT(IN)    :: PHASE
          INTEGER(KIND=8),          INTENT(IN)    :: N
          INTEGER(KIND=8),          INTENT(IN)    :: IA(*)
          INTEGER(KIND=8),          INTENT(IN)    :: JA(*)
          INTEGER(KIND=8),          INTENT(INOUT) :: PERM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: NRHS
          INTEGER(KIND=8),          INTENT(INOUT) :: IPARM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MSGLVL
          INTEGER(KIND=8),          INTENT(OUT)   :: ERROR

          COMPLEX(KIND=4),  INTENT(IN)    :: A(*)
          COMPLEX(KIND=4),  INTENT(INOUT) :: B(*)
          COMPLEX(KIND=4),  INTENT(OUT)   :: X(*)
          INTEGER,          INTENT(IN)    :: COMM
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_SC_64

        SUBROUTINE CLUSTER_SPARSE_SOLVER_DC_64( PT, MAXFCT, MNUM, MTYPE, PHASE, N, A, IA,
     & JA, PERM, NRHS, IPARM, MSGLVL, B, X, COMM, ERROR )
          IMPORT MKL_CLUSTER_SPARSE_SOLVER_HANDLE
          TYPE(MKL_CLUSTER_SPARSE_SOLVER_HANDLE), INTENT(INOUT) :: PT(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MAXFCT
          INTEGER(KIND=8),          INTENT(IN)    :: MNUM
          INTEGER(KIND=8),          INTENT(IN)    :: MTYPE
          INTEGER(KIND=8),          INTENT(IN)    :: PHASE
          INTEGER(KIND=8),          INTENT(IN)    :: N
          INTEGER(KIND=8),          INTENT(IN)    :: IA(*)
          INTEGER(KIND=8),          INTENT(IN)    :: JA(*)
          INTEGER(KIND=8),          INTENT(INOUT) :: PERM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: NRHS
          INTEGER(KIND=8),          INTENT(INOUT) :: IPARM(*)
          INTEGER(KIND=8),          INTENT(IN)    :: MSGLVL
          INTEGER(KIND=8),          INTENT(OUT)   :: ERROR
          COMPLEX(KIND=8),  INTENT(IN)    :: A(*)
          COMPLEX(KIND=8),  INTENT(INOUT) :: B(*)
          COMPLEX(KIND=8),  INTENT(OUT)   :: X(*)
          INTEGER,          INTENT(IN)    :: COMM
        END SUBROUTINE CLUSTER_SPARSE_SOLVER_DC_64
      END INTERFACE