﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ForceProcess.h
//! <AUTHOR>
//! @brief 气动力计算及监控.
//! @date 2022-06-30
//
//------------------------------修改日志----------------------------------------
// 2022-06-30 乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_flowSolver_ForceProcess_
#define _sourceFlow_flowSolver_ForceProcess_

#include "sourceFlow/package/FlowPackage.h"
#include "sourceFlow/configure/FlowConfigureMacro.h"

/**
 * @brief 气动力计算及监控类
 * 
 */
class ForceProcess
{
public:
    /**
     * @brief 构造函数，创建气动力监控对象
     * 
     * @param[in] flowPackageVector_ 流场场包容器
     */
    ForceProcess(std::vector<Package::FlowPackage *> flowPackageVector_);

    /**
    * @brief 初始化力系数
    *
    */
    void Initialize();

    /**
     * @brief 力系数输出
     * 
     */
    void OutputForceCoefficient();

	/**
	* @brief 保存力数据
	*
	*/
	void OutputForceDimensional(std::fstream &file, int &currentStep);

    /**
     * @brief 本进程部件力计算
     * 
     * @param[in] currentLevel 当前网格层级
     */
    void CalculateLocalForce(const int &currentLevel);

    /**
     * @brief 计算力系数（所有进程的力求和、转风轴系并无量纲化）
     * 
     * @param[in] currentLevel 当前网格层级
     * @return std::vector<std::vector<Vector>>& 
     */
    std::vector<std::vector<Vector>> &CalculateForceCoefficient(const int &currentLevel);

    /**
     * @brief 计算监控气动力
     * 
     * @param[in] currentLevel 当前网格层级
     * @return const std::vector<Scalar> 
     */
    const std::vector<Scalar> CalculateMonitorForce(const int &currentLevel);

    /**
     * @brief 获得监控气动力名称
     * 
     * @return const std::vector<std::string>& 
     */
    const std::vector<std::string> &GetMonitorForceName() {return monitorForceName;}

    /**
     * @brief 获得监控气动力大小
     * 
     * @return const std::vector<std::vector<Vector>>& 
     */
    const std::vector<Scalar> &GetMonitorForce() { return monitorForceValues; }

private:
    std::vector<Package::FlowPackage *> flowPackageVector; ///< 流场包

    Scalar alpha; ///< 来流攻角
    Scalar beta; ///< 来流侧滑角
    Scalar cReference; ///< 参考弦长
    Scalar bReference; ///< 参考展长
    Scalar SReference; ///< 参考面积
    Vector cmReference; ///< 力矩中心
    Scalar densityReference; ///< 密度参考值
    Vector velocityReference; ///< 速度参考值
    Scalar pressureReference; ///< 压力参考值
    Scalar dynamicPressure; ///< 来流动压

    std::vector<std::string> globalBoundaryName; ///< 全局网格边界名称容器

    std::vector<bool> globalWallFlag; ///< 全局所有边界是否为物面，true为物面

    int positionTotalForce; ///< 所有部件的合力在容器force中的位置

    /**
     * @brief 合力类型量枚举
     * 
     */
    enum ForceType
    {
        FORCE_PRESSURE = 0, ///< 压力
        FORCE_VISCOUS = 1, ///< 粘性力
        MOMENT = 2, ///< 力矩
        FORCE_SUM = 3 ///< 合力
    };

    Matrix matrix; ///< 体轴系到风轴系的转换矩阵
    Scalar referenceForce; ///< 力系数无量纲参考量

    /// 所有边界上的不同类型风轴合力容器，外层为全局边界索引，内层为合力的类型索引
    std::vector<std::vector<Vector>> forceWindAll;
    
    /// 所有边界上的不同类型体轴合力容器，外层为全局边界索引，内层为合力的类型索引
    std::vector<std::vector<Vector>> forceBodyAll;    

	/**
	* @brief 输出原始合力类型量枚举
	*
	*/
	enum DimensionalForceType
	{
		Dimensional_FORCE_PRESSURE = 0, ///< 压力
		Dimensional_FORCE_VISCOUS = 1, ///< 粘性力
		Dimensional_MOMENT_PRESSURE = 2, ///< 压力贡献的力矩
		Dimensional_MOMENT_VISCOUS = 3, ///< 粘性立贡献的力矩
		Dimensional_AREA_SUM = 4 ///< 面积分
	};
	/// 所有边界上的不同类型原始合力容器，外层为全局边界索引，内层为合力的类型索引
	std::vector<std::vector<Vector>> forceDimensionalAll;

    std::vector<std::string> monitorForceStrings; ///< 残值打印字符串
    std::vector<std::string> monitorForceName; ///< 监控力名称
    std::vector<Scalar> monitorForceValues; ///< 监控力数值

    bool dimension3D; ///< 三维计算标识，true为三维

    bool fullNS; ///< 不采用薄层假设，采用全NS方程计算
};

#endif
