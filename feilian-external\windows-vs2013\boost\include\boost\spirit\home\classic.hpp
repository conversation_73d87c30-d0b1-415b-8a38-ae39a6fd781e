/*=============================================================================
    Copyright (c) 1998-2008 <PERSON>
    Copyright (c) 2001-2008 Hart<PERSON>t <PERSON>
    Copyright (c) 2001-2003 <PERSON>
    Copyright (c) 2002-2003 <PERSON>
    Copyright (c) 2002 Juan <PERSON>a
    Copyright (c) 2002 Raghavendra Satish
    Copyright (c) 2002 <PERSON>
    Copyright (c) 2001 <PERSON>
    Copyright (c) 2003 Giovanni <PERSON>
    Copyright (c) 2003 <PERSON><PERSON><PERSON>
    Copyright (c) 2003 <PERSON>
    http://spirit.sourceforge.net/
    http://www.boost.org/libs/spirit

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

  See http://www.boost.org/libs/spirit for documentation
=============================================================================*/
#if !defined(BOOST_SPIRIT_CLASSIC_APRIL_11_2008_0849AM)
#define BOOST_SPIRIT_CLASSIC_APRIL_11_2008_0849AM

#include <boost/spirit/home/<USER>/core.hpp>
#include <boost/spirit/home/<USER>/meta.hpp>
#include <boost/spirit/home/<USER>/error_handling.hpp>
#include <boost/spirit/home/<USER>/iterator.hpp>
#include <boost/spirit/home/<USER>/symbols.hpp>
#include <boost/spirit/home/<USER>/utility.hpp>
#include <boost/spirit/home/<USER>/attribute.hpp>

#endif
