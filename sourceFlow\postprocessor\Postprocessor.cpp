﻿#include "sourceFlow/postprocessor/Postprocessor.h"

Postprocessor::Postprocessor(Configure::Flow::FlowConfigure &flowConfigure_, const Post::Type &postType_, Mesh *globalMesh_, const bool &boundaryMeshFlag_)
    :
    flowConfigure(flowConfigure_), globalMesh0(globalMesh_), boundaryMeshFlag(boundaryMeshFlag_)
{
    binaryMesh = flowConfigure.GetPreprocess().binaryFileFlag;
    nPart = flowConfigure.GetPreprocess().partitionNumber;
    dualMesh = flowConfigure.GetPreprocess().dualMeshFlag;

	postType = postType_ == Post::Type::AUTO ? flowConfigure.GetPostprocess().resultType : postType_;
	postPosition = flowConfigure.GetPostprocess().resultPosition;
	exportInteriorFlag = flowConfigure.GetPostprocess().exportInteriorFlag;
	exportValueName = flowConfigure.GetPostprocess().exportValueName;

    resultPath = flowConfigure.GetControl().resultSavePath;
    if (resultPath.find_last_of("/") != resultPath.length() - 1) resultPath = resultPath + "/";

    nZones = flowConfigure.GetMeshParameters().meshNumber;

    material = new Material::Flow::Materials(flowConfigure.GetMaterial().density,
                                             flowConfigure.GetMaterial().viscosity);

    if (postType == Post::Type::VTK)
    {
		if (dualMesh || postPosition == Post::Position::CELL_VERTICE)
		{
			FatalError("Postprocessor::Postprocessor: 暂不支持对偶及格点格式VTK输出！");
		}
    }

	const auto &turbulentType = flowConfigure.GetModel().type;
	viscousFlag = turbulentType > Turbulence::Model::INVISCID;
	turbulenceFlag = turbulentType > Turbulence::Model::LAMINAR;
	ddesFlag = EndWithSuffix(Configure::Flow::turbulenceModelReverseMap.find(turbulentType)->second, "_DDES");
	desFlag = EndWithSuffix(Configure::Flow::turbulenceModelReverseMap.find(turbulentType)->second, "_DES");
    turbulentVariable = Configure::Flow::turbulenceVariableMap.find(turbulentType)->second;

	oversetFlag = flowConfigure.JudgeEnableOversetMesh();
    
	unsteadyFlag = flowConfigure.GetTimeScheme().outerLoopType != Time::UnsteadyType::STEADY;

	motionFlag = flowConfigure.JudgeEnableMotion();

	exportFeaturesFlag = flowConfigure.GetPostprocess().exportFeaturesFlag;
	exportMutFlag = flowConfigure.GetPostprocess().exportMutFlag;

    this->zoneID = 0;

    postPointer = nullptr;
    
    // 初始化后处理结果输出路径
    if (postType == Post::Type::TECPLOT) resultOutputPath = resultPath + "Tecplot/";
    else if (postType == Post::Type::ENSIGHT) resultOutputPath = resultPath + "Ensight/";
    else if (postType == Post::Type::VTK) resultOutputPath = resultPath + "VTK/";
	else
	{
		FatalError("Postprocessor::Postprocessor：后处理类型暂不支持！");
		return;
	}
}

Postprocessor::~Postprocessor()
{
    for (int i = 0; i < localMeshVector.size(); i++)
    {
        if (localMeshVector[i] != nullptr)
        {
            delete localMeshVector[i];
            localMeshVector[i] = nullptr;
        }
    }

    if (material != nullptr) { delete material; material = nullptr; }

    if (postPointer != nullptr) { delete postPointer; postPointer = nullptr; }

    if (globalMesh0 == nullptr)
    {
        if (subMesh != nullptr) { delete subMesh; subMesh = nullptr; }
    }
}

void Postprocessor::Process(const std::string &stepString)
{
    Print("\n" + ObtainInfoTitle("开始处理第" + stepString + "步流场"));

    this->SetProcessStep(stepString);

    // 修改为多域输出
	for (int zoneI = 0; zoneI < nZones; zoneI++)
	{
		this->zoneID = zoneI;

		Print("\n" + ObtainInfoTitle("开始处理第" + ToString(zoneID) + "个zone"));

		this->CreateGlobalMesh();
        CheckStatus(3100);

		// 初始化流场
        this->InitializeFields();
        CheckStatus(3200);

        Print("\n输入全局基本物理场...");
        this->InputBasicFields();
        CheckStatus(3400);

        Print("\n计算其他衍生物理场...");
        this->CalculateOtherFields();
        CheckStatus(3500);

		// 初始化后处理指针
		this->InitializePostPointer();
		CheckStatus(3300);

        Print("\n后处理结果文件输出...");
        this->WritePostResults();
        CheckStatus(3600);

        this->DeletePostPointers();
    }

    Print("\n" + ObtainInfoTitle("第" + currentStep + "步流场后处理完成") + "\n");
}

void Postprocessor::SetProcessStep(const std::string &stepString)
{
    currentStep = stepString;
    fieldPath = resultPath + currentStep + "/";
}

void Postprocessor::SetProcessResultsPath(const std::string &pathString)
{
    resultOutputPath = pathString;
    if (pathString.find_last_of("/") != pathString.length() - 1) resultOutputPath = pathString + "/";
}

void Postprocessor::SetOutputCellVertice(const bool flag)
{
    if (flag) this->postPosition = Post::Position::CELL_VERTICE;
}

void Postprocessor::GetScalarFields(std::vector<ElementField<Scalar> *> &scalarFieldVector)
{
	scalarFieldVector = fieldPackage->GetTempElementFieldAll(Scalar0);
}

void Postprocessor::GetVectorFields(std::vector<ElementField<Vector> *> &vectorFieldVector)
{
	vectorFieldVector = fieldPackage->GetTempElementFieldAll(Vector0);
}

void Postprocessor::CreateGlobalMesh()
{
    // 创建全局网格（为原始网格，而不是对偶网格）
    if (!globalMesh0) this->ReadMesh();
    else              globalMesh = globalMesh0;

    if (boundaryMeshFlag)
    {
        fieldSize = 0;
        for (int i = 0; i < globalMesh->GetBoundarySize(); ++i)
        {
            if (dualMesh)
            {
                std::vector<int> boundaryIDList;
                globalMesh->PopulateBoundaryNodeID(i, boundaryIDList);
                fieldSize += boundaryIDList.size();
            }
            else
            {
                fieldSize += globalMesh->GetBoundaryFaceSize(i);
            }
        }
    }
    else
    {
        if (globalMesh->GetElementNumberAll() == globalMesh->GetElementNumberReal())
            globalMesh->CreateBoundaryGhostElement();
        fieldSize = globalMesh->GetElementNumberAll();
    }
}

void Postprocessor::ReadMesh()
{
    // 生成全局网格并创建虚单元
    Print("读取全局网格...");

    std::string caseName = flowConfigure.GetCaseName();
    std::string outputPath = flowConfigure.GetPreprocess().outputPath;
    if (outputPath.find_last_of("/") != outputPath.length() - 1) outputPath = outputPath + "/";
    std::string zoneMeshname = outputPath + caseName + "_zone" + ToString(zoneID) + ".bMesh";

    std::fstream bMeshFile;
    bMeshFile.open(zoneMeshname, std::fstream::in | std::fstream::binary);
	if (!bMeshFile.is_open())
	{
		FatalError("全局网格文件不存在...");
		return;
	}

    subMesh = new SubMesh();

    // 读取全局网格
    subMesh->ReadMesh(bMeshFile, true, false);

    // 创建虚单元
    subMesh->CreateGhostElement(0);

    // 更新单元面列表
    subMesh->UpdateElementFaceID();

	// 更新真实参与计算的单元信息
	subMesh->UpdateInDomainInfo();

    // 计算几何信息
    subMesh->CalculateCenterAndVolume();

    // 转为mesh
    globalMesh = subMesh->GetMultiGrid(0);

    bMeshFile.close();

    // 更新全局网格信息
    UpdateGlobalMesh();
}

void Postprocessor::InitializeFields()
{
	fieldPackage = new DataPackage(globalMesh);
}

void Postprocessor::InitializePostPointer()
{
    postPointer = nullptr;

    if (boundaryMeshFlag)return;        

    // 初始化后处理指针Tecplot和Ensight
    if (postType == Post::Type::TECPLOT)
    {
		postPointer = new Post::Tecplot(globalMesh, false, postPosition, exportInteriorFlag);
    }
    else if (postType == Post::Type::ENSIGHT)
    {
		postPointer = new Post::Ensight(globalMesh, false, postPosition, exportInteriorFlag);
    }
    else if (postType == Post::Type::VTK)
    {
		postPointer = new Post::VTKLegacy(globalMesh, false, postPosition, exportInteriorFlag);
	}
	else if (postType == Post::Type::CGNS)
	{
		postPointer = new Post::CGNSLegacy(globalMesh, false, postPosition, exportInteriorFlag);
	}
    else
    {
		FatalError("Postprocessor::InitializePostPointer：后处理类型暂不支持！");
		return;
    }
}

void Postprocessor::InputBasicFields()
{
	// 读取计算结果
	rho = this->ReadField("RHO", Scalar0);
	U = this->ReadField("U", Vector0);
	p = this->ReadField("P", Scalar0);
	for (int i = 0; i < turbulentVariable.size(); i++)
	{
		const std::string turbulentVariableName = Configure::Flow::turbulenceVariableNameReverseMap.find(turbulentVariable[i])->second;
		this->ReadField(turbulentVariableName, Scalar0);
	}

	if (turbulenceFlag) MuT = this->ReadField("MU_TURBULENT", Scalar0);
	if (desFlag) this->ReadField("LDOVERLR", Scalar0);
	if (ddesFlag) this->ReadField("SHIELDINGFUNCTION", Scalar0);

	if (unsteadyFlag)
	{
		meanVelocity = this->ReadField("MEANU", Vector0);
		meanPressure = this->ReadField("MEANPRESSURE", Scalar0);
		meanDensity = this->ReadField("MEANDENSITY", Scalar0);
		meanMu = this->ReadField("MEANMU", Scalar0);
		if (turbulenceFlag) meanMuTurbulent = this->ReadField("MEANMUTURBULENT", Scalar0);
	}

	if (oversetFlag) this->ReadField("OversetElemType", Scalar0);
	
	if (motionFlag) this->ReadField("meshVelocity", Vector0);

	
#if defined(_EnableMLTurbModel_)
    this->ReadField("STENSOR", Scalar0);
    this->ReadField("WTENSOR", Scalar0);
    this->ReadField("VORTEX", Scalar0);
    this->ReadField("FD", Scalar0);
    this->ReadField("TKOVERK", Scalar0);
    this->ReadField("CHI", Scalar0);
#endif
}

void Postprocessor::CalculateOtherFields()
{
    // 获取参考量
    const Configure::Flow::FlowReferenceStruct &reference = flowConfigure.GetFlowReference();
    const Scalar &densityReference = reference.density;
    const Vector &velocityReference = reference.velocity;
    const Scalar &pressureReference = reference.staticPressure;

    // 来流动压计算
    const Scalar dynamicPressure = 0.5 * densityReference * velocityReference & velocityReference;

    // 计算T、Cp、Ma
	Print("T、Cp、Ma计算...");
	ElementField<Scalar> *T = &fieldPackage->GetTempElementField("T", Scalar0);
	ElementField<Scalar> *Cp = &fieldPackage->GetTempElementField("CP", Scalar0);
	ElementField<Scalar> *Ma = &fieldPackage->GetTempElementField("MACH", Scalar0);
    ARI_OMP(parallel for schedule(static))
    for (int globalID = 0; globalID < fieldSize; globalID++)
    {
		const Scalar &pTemp = p->GetValue(globalID);
		const Scalar &rhoTemp = rho->GetValue(globalID);
		const Vector &UTemp = U->GetValue(globalID);
		T->SetValue(globalID, material->GetTemperature(pTemp, rhoTemp));
		Cp->SetValue(globalID, (pTemp - pressureReference) / dynamicPressure);
		Ma->SetValue(globalID, UTemp.Mag() / material->GetSoundSpeed(pTemp, rhoTemp));
    }

    // 填充内部单元场
	if (viscousFlag)
	{
		Print("Mu计算...");
		ElementField<Scalar> *Mu = &fieldPackage->GetTempElementField("MU_LAMINAR", Scalar0);
        ARI_OMP(parallel for schedule(static))
        for (int globalID = 0; globalID < fieldSize; globalID++)
			Mu->SetValue(globalID, material->Mu(T->GetValue(globalID)));

		Print("壁面边界Cf、CfMag、YPlus计算...");
		ElementField<Vector> *Cf = &fieldPackage->GetTempElementField("CF", Vector0);
		ElementField<Scalar> *CfMag = &fieldPackage->GetTempElementField("CFMag", Scalar0);
		ElementField<Scalar> *YPlus = &fieldPackage->GetTempElementField("YPLUS", Scalar0);
		for (int patchID = 0; patchID < globalMesh->GetBoundarySize(); patchID++)
		{
			if (!flowConfigure.JudgeWallGlobal(patchID)) continue;

			for (int index = 0; index < globalMesh->GetBoundaryFaceSize(patchID); index++)
			{
				const int &faceID = globalMesh->GetBoundaryFaceID(patchID, index);
				const Face &face = globalMesh->GetFace(faceID);
				const Vector &faceNormal = face.GetNormal();

				// 虚单元存储边界面心物理量
				const int boundaryID = face.GetNeighborID();
				const int adjacentID = face.GetOwnerID();

				const Scalar &rhoFace = rho->GetValue(boundaryID);
				Scalar muFace = Mu->GetValue(boundaryID);
				if (turbulenceFlag) muFace += MuT->GetValue(boundaryID);
				const Vector &UFace = U->GetValue(boundaryID);
				const Vector &UOwner = U->GetValue(adjacentID);

				const Scalar distance = fabs((globalMesh->GetElement(adjacentID).GetCenter() - face.GetCenter()) & faceNormal);
				const Vector dU = UOwner - UFace;
				const Vector dUTang = dU - (dU &faceNormal) * faceNormal;
				const Vector tauWall = muFace / distance * dUTang;

				if (YPlus != nullptr) YPlus->SetValue(boundaryID, sqrt(rhoFace * tauWall.Mag()) * distance / muFace);
				if (Cf != nullptr) Cf->SetValue(boundaryID, tauWall / dynamicPressure);
				if (CfMag != nullptr)
				{
					Scalar tauWallMag;
					if ((dUTang & velocityReference) < 0)
					{
						tauWallMag = -tauWall.Mag() / dynamicPressure;
					}
					else
					{
						tauWallMag = tauWall.Mag() / dynamicPressure;
					}
					CfMag->SetValue(boundaryID, tauWallMag);
				}
			}
		}
    }

	if (unsteadyFlag)
	{
		Print("非定常时均Cp计算...");
		ElementField<Scalar> *meanCp = &fieldPackage->GetTempElementField("MEANCP", Scalar0);
		ARI_OMP(parallel for schedule(static))
		for (int globalID = 0; globalID < fieldSize; globalID++)
		{
			const Scalar &meanPTemp = meanPressure->GetValue(globalID);
			meanCp->SetValue(globalID, (meanPTemp - pressureReference) / dynamicPressure);
		}

		if (viscousFlag)
		{
			Print("非定常壁面边界时均Cf和CfMag计算...");
			ElementField<Vector> *meanCf = &fieldPackage->GetTempElementField("MEANCF", Vector0);
			ElementField<Scalar> *meanCfMag = &fieldPackage->GetTempElementField("MEANCFMAG", Scalar0);

			for (int patchID = 0; patchID < globalMesh->GetBoundarySize(); patchID++)
			{
				if (!flowConfigure.JudgeWallGlobal(patchID)) continue;

				for (int index = 0; index < globalMesh->GetBoundaryFaceSize(patchID); index++)
				{
					const int &faceID = globalMesh->GetBoundaryFaceID(patchID, index);
					const Face &face = globalMesh->GetFace(faceID);
					const Vector &faceNormal = face.GetNormal();

					// 虚单元存储边界面心物理量
					const int boundaryID = face.GetNeighborID();
					const int adjacentID = face.GetOwnerID();
					const Scalar distance = fabs((globalMesh->GetElement(adjacentID).GetCenter() - face.GetCenter()) & faceNormal);

					Scalar meanMuFace = meanMu->GetValue(boundaryID);
					if (meanMuTurbulent != nullptr) meanMuFace += meanMuTurbulent->GetValue(boundaryID);
					const Vector &meanUOwner = meanVelocity->GetValue(adjacentID);

					const Vector meandU = meanUOwner;
					const Vector meandUTang = meandU - (meandU &faceNormal) * faceNormal;
					const Vector meanTauWall = meanMuFace / distance * meandUTang;
					meanCf->SetValue(boundaryID, meanTauWall / dynamicPressure);

					Scalar tauWallMag;
					if ((meandUTang & velocityReference) < 0)
					{
						tauWallMag = -meanTauWall.Mag() / dynamicPressure;
					}
					else
					{
						tauWallMag = meanTauWall.Mag() / dynamicPressure;
					}
					meanCfMag->SetValue(boundaryID, tauWallMag);
				}
			}
		}
	}

	if (!boundaryMeshFlag && surfaceVelocityFlag && viscousFlag)
	{
		Print("壁面边界表面U和Ma投影...");
		for (int patchID = 0; patchID < globalMesh->GetBoundarySize(); patchID++)
		{
			if (!flowConfigure.JudgeWallGlobal(patchID) ||
				flowConfigure.GetGlobalBoundary(patchID).type == Boundary::Type::WALL_SLIPPING) continue;

			for (int index = 0; index < globalMesh->GetBoundaryFaceSize(patchID); index++)
			{
				const int &faceID = globalMesh->GetBoundaryFaceID(patchID, index);
				const Face &face = globalMesh->GetFace(faceID);
				const Vector &faceNormal = face.GetNormal();

				// 虚单元存储边界面心物理量
				const int boundaryID = face.GetNeighborID();
				const int adjacentID = face.GetOwnerID();
				const Vector &UFace = U->GetValue(boundaryID);
				const Vector &UOwner = U->GetValue(adjacentID);
				const Vector UTang = UOwner - (UOwner &faceNormal) * faceNormal;
				U->SetValue(boundaryID, UTang);

				const Scalar &pTemp = p->GetValue(boundaryID);
				const Scalar &rhoTemp = rho->GetValue(boundaryID);
				Ma->SetValue(boundaryID, UTang.Mag() / material->GetSoundSpeed(pTemp, rhoTemp));
			}
		}
	}
}

void Postprocessor::WritePostResults()
{
	std::vector<ElementField<Scalar> *> &scalarFieldVector = fieldPackage->GetTempElementFieldAll(Scalar0);
	for (int i = 0; i < scalarFieldVector.size(); i++)
	{
		if (scalarFieldVector[i]->Assignment()) postPointer->PushScalarField(scalarFieldVector[i]);
	}

	std::vector<ElementField<Vector> *> &vectorFieldVector = fieldPackage->GetTempElementFieldAll(Vector0);
	for (int i = 0; i < vectorFieldVector.size(); i++)
	{
		if (vectorFieldVector[i]->Assignment()) postPointer->PushVectorField(vectorFieldVector[i]);
	}

    MakeDirectory(resultOutputPath);
    MakeDirectory(resultOutputPath + currentStep + "/");
    postPointer->SetCaseName(resultOutputPath + currentStep + "/" + flowConfigure.GetCaseName() + "_zone"+ ToString(zoneID));
    postPointer->WriteFile();

	if(exportFeaturesFlag) this->WriteFeatures();
	if(exportMutFlag) this->WriteMut();
}

void Postprocessor::UpdateGlobalMesh()
{
	if (motionFlag) this->ReadAndUpdateNodes();

    bool MeshDeform = false;
	if (MeshDeform)
	{
		Print("\t动网格计算，修改全局网格几何信息!\n");
		for (int i = 0; i < nPart; i++)
		{
			for (int localID = 0; localID < localMeshVector[i]->GetNodeNumber(); localID++)
			{
				int globalID = localMeshVector[i]->GetNodeGlobalID(localID);
				const Node &localNode = localMeshVector[i]->GetNode(localID);
				globalMesh->SetNode(globalID, localNode);
			}
		}
		globalMesh->CalculateCenterAndVolume();
	}
}

template ElementField<Scalar> *Postprocessor::ReadField(std::string name, const Scalar &value);
template ElementField<Vector> *Postprocessor::ReadField(std::string name, const Vector &value);
template<class Type>
ElementField<Type> *Postprocessor::ReadField(std::string name, const Type &value)
{
	ElementField<Type> *phi = &fieldPackage->GetTempElementField(name, value);

	if (dualMesh)
	{
		NodeField<Type>* fieldTemp = new NodeField<Type>(globalMesh, value, name);
		fieldTemp->ReadFile(fieldPath + phi->GetName() + "_zone" + ToString(zoneID) + ".Field");
		FieldManipulation::NodeFieldToElementField(*fieldTemp, *phi);
	}
	else
	{
		phi->ReadFile(fieldPath + name + "_zone" + ToString(zoneID) + ".Field");
	}

	return phi;
}

void Postprocessor::DeletePostPointers()
{
	if (fieldPackage != nullptr) { delete fieldPackage; fieldPackage = nullptr; }

    if (postPointer != nullptr) { delete postPointer; postPointer = nullptr; }

    if (globalMesh0 == nullptr)
    {
        if (subMesh != nullptr) { delete subMesh; subMesh = nullptr; }
    }
}

void Postprocessor::ReadAndUpdateNodes()
{
	std::fstream file;
	const std::string fileName = fieldPath + "nodes" + "_zone" + ToString(zoneID) + ".XYZ";
	file.open(fileName, std::ios::in | std::ios::binary);
	
	int fileSize;
	IO::Read(file, fileSize, true);
	int nodeNum = globalMesh->GetNodeNumber();
	std::vector<Node> v_nodes(nodeNum);
	IO::Read(file, v_nodes, true, nodeNum);
	file.close();

	for (int i = 0; i < nodeNum; i++) globalMesh->SetNode(i, v_nodes[i]);

	return;
}

void Postprocessor::WriteFeatures()
{
	ElementField<Scalar> *STensor = &fieldPackage->GetTempElementField("STENSOR", Scalar0);
	ElementField<Scalar> *WTensor = &fieldPackage->GetTempElementField("WTENSOR", Scalar0);
	ElementField<Scalar> *vor = &fieldPackage->GetTempElementField("VORTEX", Scalar0);
	ElementField<Scalar> *fd = &fieldPackage->GetTempElementField("FD", Scalar0);
	ElementField<Scalar> *tkOverk = &fieldPackage->GetTempElementField("TKOVERK", Scalar0);
	ElementField<Scalar> *Chi = &fieldPackage->GetTempElementField("CHI", Scalar0);

    std::ofstream outfile(resultOutputPath + "X_feature.dat");
    outfile << "variables = 'STensor' 'WTensor'  'fd'  'tkoverk'  'chi'" << std::endl;
    for (int globalID = 0; globalID < fieldSize; globalID++)
    {
        //const Scalar &Sfeature = this->GetFieldValue(featureVector[1], globalID, Scalar0);
        //const Scalar &Wfeature = this->GetFieldValue(featureVector[2], globalID, Scalar0);
        //const Scalar &vortex = this->GetFieldValue(featureVector[3], globalID, Scalar0);
        //const Scalar &fd = this->GetFieldValue(featureVector[4], globalID, Scalar0);
        //const Scalar &tkoverk = this->GetFieldValue(featureVector[5], globalID, Scalar0);
        //const Scalar &chi = this->GetFieldValue(featureVector[6], globalID, Scalar0);
        //for (int k=0; k<6;k++)
        //{
        //    const Scalar &feature = this->GetFieldValue(featureVector[k], globalID, Scalar0);
        //    outfile << std::setw(16) <<feature;
        //}
        outfile << std::setw(16) << STensor->GetValue(globalID);
        outfile << std::setw(16) << WTensor->GetValue(globalID);
        outfile << std::setw(16) << vor->GetValue(globalID);
        outfile << std::setw(16) << fd->GetValue(globalID);
        outfile << std::setw(16) << tkOverk->GetValue(globalID);
        outfile << std::setw(16) << Chi->GetValue(globalID);
        outfile << std::endl;
    }
    outfile.close();
}

void Postprocessor::WriteMut()
{
    std::ofstream outfile(resultOutputPath + "Y_feature.dat");
    outfile << "variables = 'muT'" << std::endl;
    for (int globalID = 0; globalID < fieldSize; globalID++)
    {
        //const Scalar &muTurbulent = this->GetFieldValue(MuT, globalID, Scalar0);
        Scalar muTurbulent;
		if(unsteadyFlag)
		{
			muTurbulent = meanMuTurbulent->GetValue(globalID);
		}
		else
		{
			muTurbulent = MuT->GetValue(globalID);
		}
        outfile << std::setw(16) << muTurbulent;
        outfile << std::endl;
    }
    outfile.close();
}
