 #pragma once
#include "AltTecUtil.h"
#include "ClassMacros.h"
namespace tecplot { namespace ___3933 { namespace { inline int32_t countNumItems( ___37 const& ___36, int32_t           numDatasetItems, ___3501            ___2099, int32_t           baseItem) { return ___2099 != NULL ? (int32_t)___36.setGetPrevMember(___2099, ___4039) - baseItem : numDatasetItems; } } class ItemSetIterator { public: ItemSetIterator( ___37 const& ___36, int32_t           numDatasetItems, ___3501            ___2099) : ___2337(___36) , m_items(___2099) , m_baseItem(___2099 != NULL ? (int32_t)___2337.___3491(m_items, ___4039)-1 : 0) , m_numItems(countNumItems(___36, numDatasetItems, ___2099, m_baseItem)) , m_item(m_baseItem) { REQUIRE(sizeof(int32_t) == sizeof(___4352) && sizeof(int32_t) == sizeof(___4636)); REQUIRE(VALID_REF_OR_NULL(m_items)); } inline void reset() { m_item = m_baseItem; } inline bool hasNext() const { if (m_items) return m_item != ___4039 - 1; else return m_item - m_baseItem < m_numItems; } inline int32_t next() { REQUIRE(hasNext()); int32_t ___3358 = m_item; if (m_items) m_item = static_cast<int32_t>(___2337.___3491(m_items, m_item + 1)) - 1; else ++m_item; return ___3358; } inline int32_t baseItem() const { return m_baseItem; } inline int32_t ___2812() const { return m_numItems; } private: UNCOPYABLE_CLASS(ItemSetIterator); ___37 const& ___2337; ___3501 const      m_items; int32_t const     m_baseItem; int32_t const     m_numItems; int32_t           m_item; }; }}
