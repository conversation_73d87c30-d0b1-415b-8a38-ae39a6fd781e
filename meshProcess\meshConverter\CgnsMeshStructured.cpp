﻿#include "meshProcess/meshConverter/CgnsMeshStructured.h"

CgnsMeshStructured::CgnsMeshStructured(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_)
    : CgnsMeshBase(MshFileName, meshDimension_, mesh_), nTotalNode(0), nTotalCell(0)
{
}

CgnsMeshStructured::~CgnsMeshStructured()
{
}

int CgnsMeshStructured::ReadMesh(const bool &fullRead)
{
    Print("\n" + ObtainInfoTitle("Start reading mesh") + "\n");

    if (ReadGeneral())
    {
        FatalError("CgnsMeshStructured::ReadMesh: Reading general info is wrong!");
        return 1;
    }

    if (ReadFamilies())
    {
        FatalError("CgnsMeshStructured::ReadMesh: Reading families is wrong!");
        return 1;
    }

    if (ReadZone(fullRead))
    {
        FatalError("CgnsMeshStructured::ReadMesh: Reading zone is wrong!");
        return 1;
    }

    if (cg_close(fileID))
    {
        FatalError("CgnsMeshStructured::ReadMesh: Close file is wrong!");
        return 1;
    }

    Print("\n" + ObtainInfoTitle("Finish reading mesh") + "\n");

    return 0;
}

std::vector<std::string> CgnsMeshStructured::GetBoundaryName()
{
    const int boundarySize = boundaryInfoList.size();

    std::vector<std::string> boundaryNameVector;
    for (int index = 0; index < boundarySize; index++)
    {
        boundaryNameVector.push_back(boundaryInfoList[index].first + "(" + boundaryInfoList[index].second + ")");
    }

    return boundaryNameVector;
}

int CgnsMeshStructured::ReadGeneral()
{
    //打开文件
    if (cg_open(fullFileName.c_str(), CG_MODE_READ, &fileID)) return 1;

    //确认版本号
    float fileVersion;
    if (cg_version(fileID, &fileVersion)) return 1;

    //确认文件存储类型
    int fileType;
    if (cg_get_file_type(fileID, &fileType)) return 1;

    std::string fileTypeName;
    switch (fileType)
    {
    case CG_FILE_NONE:
        fileTypeName = "FILE_NONE";
        break;
    case CG_FILE_ADF:
        fileTypeName = "FILE_ADF";
        break;
    case CG_FILE_HDF5:
        fileTypeName = "FILE_HDF5";
        break;
    case CG_FILE_ADF2:
        fileTypeName = "FILE_ADF2";
        break;
    default:
        FatalError("CgnsMeshStructured::ReadGeneral：Not supported CGNS file type");
        return 1;
        break;
    }

    //确认数据存储精度
    int precision;
    if (cg_precision(fileID, &precision)) return 1;

    //确定文件中有几个base.
    int nBases;
    if (cg_nbases(fileID, &nBases)) return 1;
    if (nBases != 1)
    {
        FatalError("CgnsMeshStructured::ReadGeneral: 仅支持1个base， 当前nbases = " + ToString(nBases));
        return 1;
    }
    baseID = 1;

    //确定文件中的网格zone数
    if (cg_nzones(fileID, baseID, &nZones)) return 1;
    cgnsZones.resize(nZones);

    //确定网格维度
    char_33 basename;
    int physDim;
    if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim)) return 1;
    if ( meshDimension != Mesh::MeshDim::mdNoType && cellDim != (int)meshDimension)
    {
	    FatalError("CgnsMeshStructured::ReadGeneral: 网格维度与输入参数不一致");
        return 1;
    }
    mesh->md_meshDim = (Mesh::MeshDim)cellDim;

    //三维标识
    dim3 = cellDim == 3;

    //打印基本信息
    Print("CGNS文件的基本信息： ");
    Print("\t   version = " + ToString(fileVersion));
    Print("\t      tpye = " + fileTypeName);
    Print("\t presicion = " + ToString(precision));
    Print("\t dimension = " + ToString(cellDim) + "D");

    return 0;
}

int CgnsMeshStructured::ReadFamilies()
{
    // 确定文件中的数据区数量（包含体、边界等）
    int nFamilies;
    if (cg_nfamilies(fileID, baseID, &nFamilies)) return 1;

    if (nFamilies <= 0) return 0; //允许nFamilies为0，边界条件未采用family指定

    //跳过体数据区，获得边界信息
    for (int Fam = 1; Fam <= nFamilies; ++Fam)
    {
        char_33 FamilyName;
        int nFamGeo, nFamBC;
        if (cg_family_read(fileID, baseID, Fam, FamilyName, &nFamBC, &nFamGeo)) return 1;

        if (nFamBC > 0)
        {
            FamilyBCName.push_back(FamilyName);

            char_33 FamBCName;
            BCType_t FamBCType;
            if (cg_fambc_read(fileID, baseID, Fam, 1, FamBCName, &FamBCType)) return 1;
            FamilyBCType.push_back(FamBCType);
        }
    }

    return 0;
}

int CgnsMeshStructured::ReadZone(const bool &fullRead)
{
    // 读取数据域基本信息
    if (ReadZoneGeneral()) return 1;

    // 读取数据域节点数和单元数
    if (ReadZoneSize()) return 1;

    // 更新mesh中的节点和单元数量
    mesh->n_elemNum = (int)nTotalCell;

    // 全部读取时，分配节点和单元容器
    if (fullRead) mesh->v_elem.resize(mesh->n_elemNum);

    // 读取边界条件
    for (int zoneID = 0; zoneID < nZones; ++zoneID) if (ReadBoundaryCondition(zoneID)) return 1;

    // 读取域连接关系
    for (int zoneID = 0; zoneID < nZones; ++zoneID) if (ReadZoneGridConnectivity(zoneID)) return 1;

    // 打印边界信息
    GenerateAndPrintBoundaryInfo();

    // 生成结构网格Block信息
    if (GenerateStructuredBlock()) return 1;

    mesh->n_nodeNum = mesh->GetNodeTotalSize();
    mesh->v_node.resize(mesh->n_nodeNum);

    // 读取坐标
    for (int zoneID = 0; zoneID < nZones; ++zoneID)
    {
        if (fullRead)
        {
            if (ReadCoordinates(zoneID)) return 1;
        }
    }

    return 0;
}

int CgnsMeshStructured::ReadBoundaryCondition(const int &zoneID)
{
    int zoneID_CG = zoneID + 1;
    CgnsZoneInfo &currentZone = cgnsZones[zoneID];

	// 读取边界
	int nBCfromFile = 0;
    if (cg_nbocos(fileID, baseID, zoneID_CG, &nBCfromFile)) return 1;

    currentZone.boundaryType.clear();
    currentZone.boundaryNodeIDRange.clear();
    currentZone.boundaryName.clear();

	currentZone.boundaryType.reserve(nBCfromFile);
	currentZone.boundaryNodeIDRange.reserve(nBCfromFile);
	currentZone.boundaryName.reserve(nBCfromFile);

	int index = -1;
	for (int iBCRegion = 1; iBCRegion <= nBCfromFile; ++iBCRegion)
	{
		// 边界相关信息
		GridLocation_t bcGridLocation; //边界单元构成类型
		char_33 bocoName; //边界名称
		BCType_t bocoType;//边界条件类型
		PointSetType_t ptsetType;
		DataType_t normDataType;
		int normalIndex, nDataSet;
        cgsize_t nBCElem, normListFlag;
        std::string boundaryName;

		//step0:读取边界基本信息
        if (cg_boco_info(fileID, baseID, zoneID_CG, iBCRegion, bocoName, &bocoType, &ptsetType, &nBCElem, &normalIndex,
			&normListFlag, &normDataType, &nDataSet)) return 1;

        // 更新边界名称
        boundaryName = bocoName;

        // ICEM输出二维网格，会将内部网格作为边界，以下判断为实现跳过这类数据
        if (cellDim == 2 && ptsetType == PointSetType_t::ElementList && nBCElem == currentZone.nCell) continue;
        if (cellDim == 2 && ptsetType == PointSetType_t::PointList && nBCElem == currentZone.nNode) continue;
        if (cellDim == 2 && ptsetType == PointSetType_t::PointList && nBCElem == currentZone.nCell) continue;

		index++;

		//step1:确认边界信息构成不是点类型（igr /= Vertex）
		if (cg_goto(fileID, baseID, "Zone_t", zoneID_CG, "ZoneBC_t", 1, "BC_t", iBCRegion, "end")) return 1;
		if (cg_gridlocation_read(&bcGridLocation)) return 1;
		//if (bcGridLocation == Vertex) FatalError("CgnsMeshStructured::ReadBoundaryCondition: 暂不支持边界单元信息为点构成...");

		//step2:确认边界条件指定方式
		//当读取边界类型为FamilySpecified时，需要采用Family中的信息更新边界条件
		if (bocoType == FamilySpecified)
		{
            if (FamilyBCName.size() == 0)
            {
                FatalError("CgnsMeshStructured::ReadBoundaryCondition: Family is empty...");
                return 1;
            }

            char_33 familyname;
            if (cg_famname_read(familyname)) return 1;

            for (int iFam = 1; iFam <= FamilyBCName.size(); ++iFam)
			{
                if (CompareTwoStrings(familyname, FamilyBCName[iFam - 1].c_str()))
                {
                    boundaryName = familyname;
					bocoType = FamilyBCType[iFam - 1];
                    boundaryNameFamilySpecified = true;
					break;
				}
			}
		}
        else
        {
            char_33 familyname;
            if (cg_famname_read(familyname) == 0)
            {
                boundaryName = familyname;
                bocoType = BCTypeNull;
                boundaryNameFamilySpecified = true;
            }
            else
            {
                boundaryNameFamilySpecified = false;
            }
        }

		//step3: 记录基本信息
        currentZone.boundaryName.push_back(boundaryName);
		currentZone.boundaryType.push_back(bocoType);
        currentZone.boundaryNodeIDRange.push_back(std::vector<cgsize_t>(2 * cellDim));

		//step4: 读取边界信息
        if (cg_boco_read(fileID, baseID, zoneID_CG, iBCRegion, &currentZone.boundaryNodeIDRange[index][0], nullptr)) return 1;

        //step5: 检查边界信息
        const std::vector<cgsize_t> &connList = currentZone.boundaryNodeIDRange[index];
        for (int i = 0; i < cellDim; i++)
        {
            if ((connList[i] == connList[i + cellDim]) &&
                (connList[(i + 1) % cellDim] == connList[(i + 1) % cellDim + cellDim]))
            {
                FatalError("CgnsMeshStructured::CreateBoundarySectionMap: boundary info is wrong!");
                return 1;
            }
        }
	}
    
    return 0;
}

int CgnsMeshStructured::ReadZoneGeneral()
{
    // 检查坐标数据区个数
    for (int zoneID = 0; zoneID < nZones; ++zoneID)
    {
        int zoneID_CG = zoneID + 1;
        int ngrids;
        if (cg_ngrids(fileID, baseID, zoneID_CG, &ngrids)) return 1;
        if (ngrids != 1)
        {
            FatalError("CgnsMeshStructured::ReadZoneStructInfo: ngrids > 1...");
            return 1;
        }
    }

    return 0;
}

int CgnsMeshStructured::ReadZoneSize()
{
    // 读取数据域节点数和单元数
    for (int zoneID = 0; zoneID < nZones; ++zoneID)
    {
        // 读取数据域节点数和单元数
        int zoneID_CG = zoneID + 1;

        CgnsZoneInfo &currentZone = cgnsZones[zoneID];

        // 初始化节点数和单元数
        currentZone.nodeSize.clear();
        currentZone.cellSize.clear();
        currentZone.nodeSize.resize(3, 1);
        currentZone.cellSize.resize(3, 1);

        // 读取节点数和单元数
        cgsize_t *isize = new cgsize_t[3 * cellDim];
        if (cg_zone_read(fileID, baseID, zoneID_CG, currentZone.zoneName, isize)) return 1;
        for (int i = 0; i < cellDim; i++)
        {
            currentZone.nodeSize[i] = (int)isize[i];
            currentZone.cellSize[i] = (int)isize[i + cellDim];
        }

        // 更新总网格节点数和单元数
        int nNode = 1, nCell = 1;
        for (int i = 0; i < cellDim; i++)
        {
            nNode *= cgnsZones[zoneID].nodeSize[i];
            nCell *= cgnsZones[zoneID].cellSize[i];
        }
        cgnsZones[zoneID].nNode = nNode;
        cgnsZones[zoneID].nCell = nCell;

        // 更新数据域起始节点和单元编号
        cgnsZones[zoneID].cellStart = nTotalCell;

        // 更新总的节点和单元数量
        nTotalCell += nCell;
    }

    return 0;
}

int CgnsMeshStructured::ReadCoordinates(const int &zoneID)
{
    int zoneID_CG = zoneID + 1;
    CgnsZoneInfo &currentZone = cgnsZones[zoneID];

    //读取点维数
    int nCoords;
    if (cg_ncoords(fileID, baseID, zoneID_CG, &nCoords)) return 1;
    
    //点数下限和上限
    cgsize_t *irmin = new cgsize_t[3];
    cgsize_t *irmax = new cgsize_t[3];
    for (int i = 0; i < 3; i++) { irmin[i] = 1; irmax[i] = currentZone.nodeSize[i]; }
    
    // 读取点坐标    
    char_33 coordname;
	DataType_t dataType;
	float *coor_float = nullptr;
	Scalar *coor_double = nullptr;
    if (cg_coord_info(fileID, baseID, zoneID_CG, 1, &dataType, coordname)) return 1;
	
    if (dataType == DataType_t::RealSingle) coor_float = new float[currentZone.nNode];
    else                                    coor_double = new Scalar[currentZone.nNode];

	for (int coorID = 1; coorID <= nCoords; ++coorID)
	{
        if (cg_coord_info(fileID, baseID, zoneID_CG, coorID, &dataType, coordname)) return 1;

		if (dataType == DataType_t::RealSingle)
		{
            if (cg_coord_read(fileID, baseID, zoneID_CG, coordname, dataType, irmin, irmax, coor_float)) return 1;
            
            for (int k = (int)irmin[2] - 1; k < (int)irmax[2] - 1; k++)
            {
                for (int j = (int)irmin[1] - 1; j < (int)irmax[1] - 1; j++)
                {
                    for (int i = (int)irmin[0] - 1; i < (int)irmax[0] - 1; i++)
                    {
                        const int indexLocal = i + j * (int)irmax[0] + k * (int)irmax[0] * (int)irmax[1];
                        const int indexGlobal = mesh->GetBlock(zoneID).GetNodeIndex(i, j, k);
                        if (coorID == 1)      mesh->v_node[indexGlobal].SetX(coor_float[indexLocal]);
                        else if (coorID == 2) mesh->v_node[indexGlobal].SetY(coor_float[indexLocal]);
                        else                  mesh->v_node[indexGlobal].SetZ(coor_float[indexLocal]);
                    }
                }
            }
		}
		else if (dataType == DataType_t::RealDouble)
		{
            if (cg_coord_read(fileID, baseID, zoneID_CG, coordname, dataType, irmin, irmax, coor_double)) return 1;
            
            for (int k = (int)irmin[2] - 1; k <= (int)irmax[2] - 1; k++)
            {
                for (int j = (int)irmin[1] - 1; j <= (int)irmax[1] - 1; j++)
                {
                    for (int i = (int)irmin[0] - 1; i <= (int)irmax[0] - 1; i++)
                    {
                        const int indexLocal = i + j * (int)irmax[0] + k * (int)irmax[0] * (int)irmax[1];
                        const int indexGlobal = mesh->GetBlock(zoneID).GetNodeIndex(i, j, k);
                        if (coorID == 1)      mesh->v_node[indexGlobal].SetX(coor_double[indexLocal]);
                        else if (coorID == 2) mesh->v_node[indexGlobal].SetY(coor_double[indexLocal]);
                        else                  mesh->v_node[indexGlobal].SetZ(coor_double[indexLocal]);
                    }
                }
            }
		}
        else
        {
            FatalError("CgnsMeshStructured::ReadCoordinates: data type isnot supported!");
            return 1;
        }
	}

	delete[]coor_float;
	delete[]coor_double;

    return 0;
}

void CgnsMeshStructured::GenerateAndPrintBoundaryInfo()
{
    boundaryIndexMap.resize(nZones);

    boundaryInfoList.clear();
    for (int zoneID = 0; zoneID < nZones; zoneID++)
    {
        CgnsZoneInfo &currentZone = cgnsZones[zoneID];
        boundaryIndexMap[zoneID].resize(currentZone.boundaryName.size());
        for (int i = 0; i < currentZone.boundaryName.size(); i++)
		{
			bool existFlag = false;

			if (boundaryNameFamilySpecified)
			{
				for (int j = 0; j < boundaryInfoList.size(); j++)
				{
					existFlag = CompareTwoStrings(boundaryInfoList[j].first, currentZone.boundaryName[i]);
					if (existFlag)
					{
						boundaryIndexMap[zoneID][i] = j;
						break;
					}
				}
			}

            if (!existFlag)
            {
                std::string name = currentZone.boundaryName[i];
                std::string type = BCTypeName[currentZone.boundaryType[i]];
                std::pair<std::string, std::string> info(name, type);
                boundaryInfoList.push_back(info);
                boundaryIndexMap[zoneID][i] = boundaryInfoList.size() - 1;
            }
        }
    }

    const int boundarySize = boundaryInfoList.size();

    // 打印边界信息
    std::ostringstream stringStream;
    stringStream << "\n边界共计 " + ToString(boundarySize) + " 个, 如下...\n";
    stringStream << std::string(70, '-') << "\n";
    stringStream << "|   ID |                            boundary_name |    boundary_type |\n";
    stringStream << std::string(70, '-') << "\n";

    for (int index = 0; index < boundarySize; ++index)
    {
        stringStream
            << "| " << std::setw(4) << ToString(index + 1) << " "
            << "| " << std::setw(40) << std::string(boundaryInfoList[index].first) << " "
            << "| " << std::setw(16) << std::string(boundaryInfoList[index].second) << " |\n";
    }

    stringStream << std::string(70, '-') << "\n";
    Print(stringStream.str());
}

int CgnsMeshStructured::ReadZoneGridConnectivity(const int &zoneID)
{
    int zoneID_CG = zoneID + 1;
    CgnsZoneInfo &currentZone = cgnsZones[zoneID];

    // 读取1to1Connection数量
    int n1to1;
    if (cg_n1to1(fileID, baseID, zoneID_CG, &n1to1)) return 1;
    currentZone.connectivity1to1.resize(n1to1);

    // 读取1to1Connection信息
    for (int i = 1; i <= n1to1; i++)
    {
        Connection1to1 &connectivity = currentZone.connectivity1to1[i - 1];
        connectivity.range.resize(2 * cellDim);
        connectivity.rangeDonor.resize(2 * cellDim);
        connectivity.transform.resize(cellDim);

        if (cg_1to1_read(fileID, baseID, zoneID_CG, i,
                         connectivity.connectName, connectivity.donorName,
                         &connectivity.range[0], &connectivity.rangeDonor[0],
                         &connectivity.transform[0])) return 1;
    }

    return 0;
}

int CgnsMeshStructured::BuildTopology()
{
    Print("\n" + ObtainInfoTitle("Start building mesh topology") + "\n");

    mesh->vv_boundaryFaceID.resize(boundaryInfoList.size());
    for (int i = 0; i < boundaryInfoList.size(); i++) mesh->v_boundaryName.push_back(boundaryInfoList[i].first);

    Print("Calculate face size ...");
    CalculateBoundaryFaceSize();
    CalculateInnerFaceSize();

    const int totalFaceSize = boundaryFaceIndex[nZones] + innerFaceIndex[nZones] + connectionFaceSize;
    mesh->v_face.reserve(totalFaceSize);

    Print("Create boundary faces ...");
    mesh->v_face.resize(boundaryFaceIndex[nZones]);
    for (int zoneID = 0; zoneID < nZones; zoneID++)
        ProcessBoundaryElementZone(zoneID, boundaryFaceIndex[zoneID]);

    Print("Create inner faces...");
    mesh->v_face.resize(boundaryFaceIndex[nZones] + innerFaceIndex[nZones]);
    ARI_OMP(parallel for schedule(static))
    for (int zoneID = 0; zoneID < nZones; zoneID++)
        ProcessVolumeElementZone(zoneID, boundaryFaceIndex[nZones] + innerFaceIndex[zoneID]);

    Print("Create 1to1 connection faces ...");
    mesh->v_face.resize(boundaryFaceIndex[nZones] + innerFaceIndex[nZones] + connectionFaceSize);
    Process1to1Connection(boundaryFaceIndex[nZones] + innerFaceIndex[nZones]);

    Print("Update numbers of elements, faces and nodes in this mesh ...");
    CalculateParameters();

    Print("Calculate geometrical parameters ...");
    mesh->CalculateCenterAndVolume();

    Print("Check face direction ...");
    CheckFaceDirection();

    mesh->PrintMeshInfomation();

    Print("\n" + ObtainInfoTitle("End building mesh topology") + "\n");

    return 0;
}

int CgnsMeshStructured::BuildBCTopology()
{
    Print("\n" + ObtainInfoTitle("Start building mesh boundary topology") + "\n");

    mesh->vv_boundaryFaceID.resize(boundaryInfoList.size());
    for (int i = 0; i < boundaryInfoList.size(); i++) mesh->v_boundaryName.push_back(boundaryInfoList[i].first);

    Print("Calculate boundary face size ...");
    CalculateBoundaryFaceSize();

    Print("Create boundary faces ...");
    mesh->v_face.resize(boundaryFaceIndex[nZones]);
    for (int zoneID = 0; zoneID < nZones; zoneID++)
        ProcessBoundaryElementZone(zoneID, boundaryFaceIndex[zoneID]);

    Print("Update numbers of elements, faces and nodes in this mesh ...");
    CalculateParameters();

    Print("Calculate geometrical parameters ...");
    mesh->CalculateCenterAndVolume();

    Print("Check face direction ...");
    CheckFaceDirection();

    mesh->PrintMeshInfomation();
    
    Print("\n" + ObtainInfoTitle("End building mesh boundary topology") + "\n");
    
    return 0;
}

int CgnsMeshStructured::BuildVolumeTopology()
{
    Print("\n" + ObtainInfoTitle("Start building mesh volume topology") + "\n");

    Print("Calculate inner face size ...");
    CalculateInnerFaceSize();

    Print("Create inner faces...");
    mesh->v_face.resize(boundaryFaceIndex[nZones] + innerFaceIndex[nZones]);
    ARI_OMP(parallel for schedule(static))
    for (int zoneID = 0; zoneID < nZones; zoneID++)
        ProcessVolumeElementZone(zoneID, boundaryFaceIndex[nZones] + innerFaceIndex[zoneID]);

    Print("Create 1to1 connection faces ...");
    mesh->v_face.resize(boundaryFaceIndex[nZones] + innerFaceIndex[nZones] + connectionFaceSize);
    Process1to1Connection(boundaryFaceIndex[nZones] + innerFaceIndex[nZones]);

    Print("Update numbers of elements, faces and nodes in this mesh ...");
    CalculateParameters();

    Print("Calculate geometrical parameters ...");
    mesh->CalculateCenterAndVolume();

    Print("Check face direction ...");
    CheckFaceDirection();

    Print("\n" + ObtainInfoTitle("End building mesh volume topology") + "\n"); 
    
    return 0;
}

void CgnsMeshStructured::CalculateBoundaryFaceSize()
{
    // 统计边界面数量
    boundaryFaceIndex.clear();
    boundaryFaceIndex.resize(nZones + 1, 0);
    for (int zoneID = 0; zoneID < nZones; zoneID++)
    {
        CgnsZoneInfo &currentZone = cgnsZones[zoneID];

        const auto &bcConnList = currentZone.boundaryNodeIDRange;
        const int bcSize = bcConnList.size();

        int boundaryFaceSize = 0;
        for (int bcID = 0; bcID < bcSize; bcID++)
        {
            const int bcIDI0 = (int)bcConnList[bcID][0] - 1;
            const int bcIDI1 = (int)bcConnList[bcID][0 + cellDim] - 1;

            const int bcIDJ0 = (int)bcConnList[bcID][1] - 1;
            const int bcIDJ1 = (int)bcConnList[bcID][1 + cellDim] - 1;

            const int bcIDK0 = dim3 ? (int)bcConnList[bcID][2] - 1 : 0;
            const int bcIDK1 = dim3 ? (int)bcConnList[bcID][2 + cellDim] - 1 : 0;

            int faceSize = Max(bcIDI1 - bcIDI0, 1) * Max(bcIDJ1 - bcIDJ0, 1) * Max(bcIDK1 - bcIDK0, 1);
            boundaryFaceSize += faceSize;

            const int boundaryID = boundaryIndexMap[zoneID][bcID];
            mesh->vv_boundaryFaceID[boundaryID].reserve(mesh->vv_boundaryFaceID[boundaryID].capacity() + faceSize);
        }
        boundaryFaceIndex[zoneID + 1] = boundaryFaceIndex[zoneID] + boundaryFaceSize;
    }
}

void CgnsMeshStructured::CalculateInnerFaceSize()
{
    // 统计内部面数量
    innerFaceIndex.clear();
    innerFaceIndex.resize(nZones + 1, 0);
    for (int zoneID = 0; zoneID < nZones; zoneID++)
    {
        CgnsZoneInfo &currentZone = cgnsZones[zoneID];

        int innerFaceSize = 0;
        if (dim3)
        {
            innerFaceSize += (currentZone.nodeSize[0] - 2) * (currentZone.nodeSize[1] - 1) * (currentZone.nodeSize[2] - 1);
            innerFaceSize += (currentZone.nodeSize[1] - 2) * (currentZone.nodeSize[2] - 1) * (currentZone.nodeSize[0] - 1);
            innerFaceSize += (currentZone.nodeSize[2] - 2) * (currentZone.nodeSize[0] - 1) * (currentZone.nodeSize[1] - 1);
        }
        else
        {
            innerFaceSize += (currentZone.nodeSize[0] - 2) * (currentZone.nodeSize[1] - 1);
            innerFaceSize += (currentZone.nodeSize[0] - 1) * (currentZone.nodeSize[1] - 2);
        }
        innerFaceIndex[zoneID + 1] = innerFaceIndex[zoneID] + innerFaceSize;
    }

    // 统计交接面数量
    connectionFaceSize = 0;
    const int &connectionSize = mesh->multiStructuredBlock.GetConnectionSize();
    for (int connectID = 0; connectID < connectionSize; connectID++)
    {
        const std::vector<int> &leftRange = mesh->GetConnection(connectID).leftRange;

        if (dim3)
        {
            if (leftRange[0] == leftRange[3]) connectionFaceSize += abs(leftRange[4] - leftRange[1]) * abs(leftRange[5] - leftRange[2]);
            if (leftRange[1] == leftRange[4]) connectionFaceSize += abs(leftRange[3] - leftRange[0]) * abs(leftRange[5] - leftRange[2]);
            if (leftRange[2] == leftRange[5]) connectionFaceSize += abs(leftRange[3] - leftRange[0]) * abs(leftRange[4] - leftRange[1]);
        }
        else
        {
            if (leftRange[0] == leftRange[3]) connectionFaceSize += abs(leftRange[4] - leftRange[1]);
            if (leftRange[1] == leftRange[4]) connectionFaceSize += abs(leftRange[3] - leftRange[0]);
        }
    }
}

int CgnsMeshStructured::GenerateStructuredBlock()
{
    for (int zoneID = 0; zoneID < nZones; ++zoneID)
    {
        CgnsZoneInfo &zone = cgnsZones[zoneID];

        // 边界点范围
        const auto &bcConnList = zone.boundaryNodeIDRange;
        std::vector<std::vector<int>> boundaryRange(bcConnList.size());
        for (int bcID = 0; bcID < bcConnList.size(); bcID++)
        {
            boundaryRange[bcID].clear();
            boundaryRange[bcID].resize(6, 0);
            for (int index = 0; index < cellDim; index++)
            {
                boundaryRange[bcID][index] = (int)bcConnList[bcID][index] - 1;
                boundaryRange[bcID][index + 3] = (int)bcConnList[bcID][index + cellDim] - 1;
            }
        }

        mesh->AddBlock(Block(zone.nodeSize[0], zone.nodeSize[1], zone.nodeSize[2], (int)zone.cellStart, boundaryRange));

        for (int connectionID = 0; connectionID < zone.connectivity1to1.size(); connectionID++)
        {
            const Connection1to1 &connection = zone.connectivity1to1[connectionID];

            int DonorZoneID = -1;
            for (int j = 0; j < nZones; j++)
            {
                if (strcmp(connection.donorName, cgnsZones[j].zoneName) == 0)
                {
                    DonorZoneID = j;
                    break;
                }
            }

            std::vector<int> range(6, 0), range_donor(6, 0), transform({ 1, 2, 3 });
            for (int j = 0; j < cellDim; j++)
            {
                range[j] = (int)connection.range[j] - 1;
                range[j + 3] = (int)connection.range[j + cellDim] - 1;
                range_donor[j] = (int)connection.rangeDonor[j] - 1;
                range_donor[j + 3] = (int)connection.rangeDonor[j + cellDim] - 1;
                transform[j] = connection.transform[j];
            }

            mesh->AddConnection(Connection(zoneID, DonorZoneID, range, range_donor, transform));
        }
    }

    mesh->SetNodeIndex();

    return 0;
}

int CgnsMeshStructured::ProcessVolumeElementZone(const int &zoneID, const int &faceID0)
{
    // 起始面编号
    int faceID = faceID0;

    // 遍历所有内部面
    CgnsZoneInfo &currentZone = cgnsZones[zoneID];
    const Block &block = mesh->GetBlock(zoneID);
    for (int k = 0; k < block.nodeK; ++k)
    {
        for (int j = 0; j < block.nodeJ; ++j)
        {
            for (int i = 0; i < block.nodeI; ++i)
            {
                if (i == block.cellI || j == block.cellJ) continue; //跳过最大编号的边界面
                if (dim3 && k == block.cellK) continue;

                //单元的点构成
                const int ownerID = block.GetElementIndex(i, j, k);
                const int i0j0k0 = block.GetNodeIndex(i, j, k);
                const int i0j1k0 = block.GetNodeIndex(i, j + 1, k);
                const int i1j0k0 = block.GetNodeIndex(i + 1, j, k);
                const int i1j1k0 = block.GetNodeIndex(i + 1, j + 1, k);

                if (dim3)
                {
                    const int i0j0k1 = block.GetNodeIndex(i, j, k + 1);
                    const int i0j1k1 = block.GetNodeIndex(i, j + 1, k + 1);
                    const int i1j0k1 = block.GetNodeIndex(i + 1, j, k + 1);
                    const int i1j1k1 = block.GetNodeIndex(i + 1, j + 1, k + 1);
                    mesh->v_elem[ownerID].v_nodeID = { i0j0k0, i0j0k1, i1j0k1, i1j0k0, i0j1k0, i0j1k1, i1j1k1, i1j1k0 };

                    //仅处理本单元的大编号面3个（本单元为Owner）

                    if (i + 1 < block.cellI)
                    {
                        //I最大的面i+1：四点构成:(j,k),(j+1,k),(j+1,k+1),(j,k+1)
                        mesh->v_face[faceID] = Face(i1j0k0, i1j1k0, i1j1k1, i1j0k1);
                        mesh->v_face[faceID].n_owner = ownerID;
                        const int neighIDI = block.GetElementIndex(i + 1, j, k);
                        mesh->v_face[faceID].n_neighbor = neighIDI;
                        mesh->v_elem[ownerID].v_faceID.push_back(faceID);
                        mesh->v_elem[neighIDI].v_faceID.push_back(faceID);
                        faceID++;
                    }

                    if (j + 1 < block.cellJ)
                    {
                        //J最大的面j+1：四点构成:(i,k),(i,k+1),(i+1,k+1),(i+1,k)
                        mesh->v_face[faceID] = Face(i0j1k0, i0j1k1, i1j1k1, i1j1k0);
                        mesh->v_face[faceID].n_owner = ownerID;
                        const int neighIDJ = block.GetElementIndex(i, j + 1, k);
                        mesh->v_face[faceID].n_neighbor = neighIDJ;
                        mesh->v_elem[ownerID].v_faceID.push_back(faceID);
                        mesh->v_elem[neighIDJ].v_faceID.push_back(faceID);
                        faceID++;
                    }

                    if (k + 1 < block.cellK)
                    {
                        //K最大的面k+1：四点构成:(i,j),(i+1,j),(i+1,j+1),(i,j+1)
                        mesh->v_face[faceID] = Face(i0j0k1, i1j0k1, i1j1k1, i0j1k1);
                        mesh->v_face[faceID].n_owner = ownerID;
                        const int neighIDK = block.GetElementIndex(i, j, k + 1);
                        mesh->v_face[faceID].n_neighbor = neighIDK;
                        mesh->v_elem[ownerID].v_faceID.push_back(faceID);
                        mesh->v_elem[neighIDK].v_faceID.push_back(faceID);
                        faceID++;
                    }
                }
                else
                {
                    mesh->v_elem[ownerID].v_nodeID = { i0j0k0, i1j0k0, i1j1k0, i0j1k0 };

                    //仅处理本单元的大编号面2个（本单元为Owner）

                    if (i + 1 < block.cellI)
                    {
                        //I最大的面i+1：两点构成:(j,k),(j+1,k)
                        mesh->v_face[faceID] = Face(i1j0k0, i1j1k0);
                        mesh->v_face[faceID].n_owner = ownerID;
                        const int neighIDI = block.GetElementIndex(i + 1, j, k);
                        mesh->v_face[faceID].n_neighbor = neighIDI;
                        mesh->v_elem[ownerID].v_faceID.push_back(faceID);
                        mesh->v_elem[neighIDI].v_faceID.push_back(faceID);
                        faceID++;
                    }

                    if (j + 1 < block.cellJ)
                    {
                        //J最大的面j+1：两点构成:(i+1,k),(i,k)
                        mesh->v_face[faceID] = Face(i1j1k0, i0j1k0);
                        mesh->v_face[faceID].n_owner = ownerID;
                        const int neighIDJ = block.GetElementIndex(i, j + 1, k);
                        mesh->v_face[faceID].n_neighbor = neighIDJ;
                        mesh->v_elem[ownerID].v_faceID.push_back(faceID);
                        mesh->v_elem[neighIDJ].v_faceID.push_back(faceID);
                        faceID++;
                    }
                }
            }
        }
    }

    return 0;
}

int CgnsMeshStructured::ProcessBoundaryElementZone(const int &zoneID, const int &faceID0)
{
    // 起始面编号
    int faceID = faceID0;

    // 遍历所有边界面
    CgnsZoneInfo &currentZone = cgnsZones[zoneID];
    const auto &bcConnList = currentZone.boundaryNodeIDRange;
    const int bcSize = bcConnList.size();
    const Block &block = mesh->GetBlock(zoneID);
    for (int bcID = 0; bcID < bcSize; bcID++)
    {
        const int boundaryID = boundaryIndexMap[zoneID][bcID];

        const int bcIDI0 = (int)bcConnList[bcID][0] - 1;
        const int bcIDI1 = (int)bcConnList[bcID][0 + cellDim] - 1;

        const int bcIDJ0 = (int)bcConnList[bcID][1] - 1;
        const int bcIDJ1 = (int)bcConnList[bcID][1 + cellDim] - 1;

        const int bcIDK0 = dim3 ? (int)bcConnList[bcID][2] - 1 : 0;
        const int bcIDK1 = dim3 ? (int)bcConnList[bcID][2 + cellDim] - 1 : 0;

        for (int k = bcIDK0; k <= bcIDK1; ++k)
        {
            for (int j = bcIDJ0; j <= bcIDJ1; ++j)
            {
                for (int i = bcIDI0; i <= bcIDI1; ++i)
                {
                    int ownerID = -1;

                    if (bcIDI0 == bcIDI1) // I边界
                    {
                        if (j + 1 > bcIDJ1) continue;

                        const int j0k0 = block.GetNodeIndex(i, j, k);
                        const int j1k0 = block.GetNodeIndex(i, j + 1, k);

                        if (dim3)
                        {
                            if (k + 1 > bcIDK1) continue;

                            const int j0k1 = block.GetNodeIndex(i, j, k + 1);
                            const int j1k1 = block.GetNodeIndex(i, j + 1, k + 1);

                            if (bcIDI0 == 0) // I0边界
                            {
                                ownerID = block.GetElementIndex(i, j, k);
                                mesh->v_face[faceID] = Face(j0k0, j0k1, j1k1, j1k0);
                            }
                            else // IDim边界
                            {
                                ownerID = block.GetElementIndex(i - 1, j, k);
                                mesh->v_face[faceID] = Face(j0k0, j1k0, j1k1, j0k1);
                            }
                        }
                        else
                        {
                            if (bcIDI0 == 0) // I0边界
                            {
                                ownerID = block.GetElementIndex(i, j, k);
                                mesh->v_face[faceID] = Face(j1k0, j0k0);
                            }
                            else // IDim边界
                            {
                                ownerID = block.GetElementIndex(i - 1, j, k);
                                mesh->v_face[faceID] = Face(j0k0, j1k0);
                            }
                        }
                    }

                    if (bcIDJ0 == bcIDJ1) // J边界
                    {
                        if (i + 1 > bcIDI1) continue;

                        int i0k0 = block.GetNodeIndex(i, j, k);
                        int i1k0 = block.GetNodeIndex(i + 1, j, k);

                        if (dim3)
                        {
                            if (k + 1 > bcIDK1) continue;

                            int i0k1 = block.GetNodeIndex(i, j, k + 1);
                            int i1k1 = block.GetNodeIndex(i + 1, j, k + 1);

                            if (bcIDJ0 == 0) // J0边界
                            {
                                ownerID = block.GetElementIndex(i, j, k);
                                mesh->v_face[faceID] = Face(i0k0, i1k0, i1k1, i0k1);
                            }
                            else // JDim边界
                            {
                                ownerID = block.GetElementIndex(i, j - 1, k);
                                mesh->v_face[faceID] = Face(i0k0, i0k1, i1k1, i1k0);
                            }
                        }
                        else
                        {
                            if (bcIDJ0 == 0) // J0边界
                            {
                                ownerID = block.GetElementIndex(i, j, k);
                                mesh->v_face[faceID] = Face(i0k0, i1k0);
                            }
                            else // JDim边界
                            {
                                ownerID = block.GetElementIndex(i, j - 1, k);
                                mesh->v_face[faceID] = Face(i1k0, i0k0);
                            }
                        }
                    }

                    if (dim3 && bcIDK0 == bcIDK1) // K边界
                    {
                        if (i + 1 > bcIDI1 || j + 1 > bcIDJ1) continue;

                        int i0j0 = block.GetNodeIndex(i, j, k);
                        int i0j1 = block.GetNodeIndex(i, j + 1, k);
                        int i1j0 = block.GetNodeIndex(i + 1, j, k);
                        int i1j1 = block.GetNodeIndex(i + 1, j + 1, k);

                        if (bcIDK0 == 0) // K0边界
                        {
                            ownerID = block.GetElementIndex(i, j, k);
                            mesh->v_face[faceID] = Face(i0j0, i0j1, i1j1, i1j0);
                        }
                        else // KDim边界
                        {
                            ownerID = block.GetElementIndex(i, j, k - 1);
                            mesh->v_face[faceID] = Face(i0j0, i1j0, i1j1, i0j1);
                        }
                    }

                    if (ownerID == -1)
                    {
                        FatalError("CgnsMeshStructured::ProcessBoundaryElementZone: wrong bc info!");
                        return 1;
                    }

                    mesh->v_face[faceID].n_owner = ownerID;
                    mesh->v_face[faceID].n_neighbor = -1;
                    if (!mesh->v_elem.empty()) mesh->v_elem[ownerID].v_faceID.push_back(faceID);

                    mesh->vv_boundaryFaceID[boundaryID].push_back(faceID);

                    faceID++;
                }
            }
        }
    }

    return 0;
}

int CgnsMeshStructured::Process1to1Connection(const int &faceID0)
{
    // 面起始编号
    int faceID = faceID0;

    // 遍历所有交界面（无重复）生成面信息
    const int &connectionSize = mesh->multiStructuredBlock.GetConnectionSize();
    for (int connectID = 0; connectID < connectionSize; connectID++)
    {
		const Connection &connect = mesh->GetConnection(connectID);
		const Block &left = mesh->GetBlock(connect.leftID);
		const Block &right = mesh->GetBlock(connect.rightID);
        const std::vector<int> &leftRange = connect.leftRange;
        const std::vector<int> &transform = connect.transform;
        
        const int deltaI = leftRange[3] - leftRange[0];
        const int deltaJ = leftRange[4] - leftRange[1];
        const int deltaK = leftRange[5] - leftRange[2];
		for (int ii = 0; ii <= abs(deltaI); ii++)
		{
			for (int jj = 0; jj <= abs(deltaJ); jj++)
			{
				for (int kk = 0; kk <= abs(deltaK); kk++)
				{
					const int i = deltaI > 0 ? ii : deltaI + ii;
					const int j = deltaJ > 0 ? jj : deltaJ + jj;
					const int k = deltaK > 0 ? kk : deltaK + kk;

                    const std::vector<int> indexLeft0 = connect.ObtainLeftIndex(i, j, k);
                    const std::vector<int> indexRight0 = connect.ObtainRightIndex(i, j, k);
                    const int &iL = indexLeft0[0], &jL = indexLeft0[1], &kL = indexLeft0[2];
                    const int &iR = indexRight0[0], &jR = indexRight0[1], &kR = indexRight0[2];

                    int ownerID = -1, neighID = -1;

                    if (leftRange[0] == leftRange[3]) // 左block是I边界
                    {
                        if (jL + 1 > Max(leftRange[1], leftRange[4])) continue;
                        if (dim3 && kL + 1 > Max(leftRange[2], leftRange[5])) continue;

                        if (leftRange[0] == 0) ownerID = left.GetElementIndex(iL, jL, kL);
                        else                   ownerID = left.GetElementIndex(iL - 1, jL, kL);

                        int iR1 = iR, jR1 = jR, kR1 = kR;

                        if (leftRange[0] == 0)
                        {
                            // 左I为小编号，左右同向时右端为大编号
                            if (transform[0] == 1) iR1 = iR - 1;
                            else if (transform[0] == 2) jR1 = jR - 1;
                            else if (transform[0] == 3) kR1 = kR - 1;
                        }
                        else
                        {
                            // 左I为大编号，左右反向时右端为大编号
                            if (transform[0] == -1) iR1 = iR - 1;
                            else if (transform[0] == -2) jR1 = jR - 1;
                            else if (transform[0] == -3) kR1 = kR - 1;
                        }

                        if (transform[1] == -1 || transform[2] == -1) iR1 = iR - 1;
                        if (transform[1] == -2 || transform[2] == -2) jR1 = jR - 1;
                        if (transform[1] == -3 || transform[2] == -3) kR1 = kR - 1;

                        neighID = right.GetElementIndex(iR1, jR1, kR1);

                        const int j0k0 = left.GetNodeIndex(iL, jL, kL);
                        const int j1k0 = left.GetNodeIndex(iL, jL + 1, kL);

                        if (dim3)
                        {
                            const int j0k1 = left.GetNodeIndex(iL, jL, kL + 1);
                            const int j1k1 = left.GetNodeIndex(iL, jL + 1, kL + 1);
                            if (leftRange[0] == 0) mesh->v_face[faceID] = Face(j0k0, j0k1, j1k1, j1k0);
                            else                   mesh->v_face[faceID] = Face(j0k0, j1k0, j1k1, j0k1);

                        }
                        else
                        {
                            if (leftRange[0] == 0) mesh->v_face[faceID] = Face(j1k0, j0k0);
                            else                   mesh->v_face[faceID] = Face(j0k0, j1k0);
                        }
                    }

                    if (leftRange[1] == leftRange[4]) // J边界
                    {
                        if (iL + 1 > Max(leftRange[0], leftRange[3])) continue;
                        if (dim3 && kL + 1 > Max(leftRange[2], leftRange[5])) continue;

                        if (leftRange[1] == 0) ownerID = left.GetElementIndex(iL, jL, kL);
                        else                   ownerID = left.GetElementIndex(iL, jL - 1, kL);

                        int iR1 = iR, jR1 = jR, kR1 = kR;

                        if (leftRange[1] == 0)
                        {
                            // 左J为小编号，左右同向时右端为大编号
                            if (transform[1] == 1) iR1 = iR - 1;
                            else if (transform[1] == 2) jR1 = jR - 1;
                            else if (transform[1] == 3) kR1 = kR - 1;
                        }
                        else
                        {
                            // 左J为大编号，左右反向时右端为大编号
                            if (transform[1] == -1) iR1 = iR - 1;
                            else if (transform[1] == -2) jR1 = jR - 1;
                            else if (transform[1] == -3) kR1 = kR - 1;
                        }

                        if (transform[0] == -1 || transform[2] == -1) iR1 = iR - 1;
                        if (transform[0] == -2 || transform[2] == -2) jR1 = jR - 1;
                        if (transform[0] == -3 || transform[2] == -3) kR1 = kR - 1;

                        neighID = right.GetElementIndex(iR1, jR1, kR1);

                        const int i0k0 = left.GetNodeIndex(iL, jL, kL);
                        const int i1k0 = left.GetNodeIndex(iL + 1, jL, kL);

                        if (dim3)
                        {
                            const int i0k1 = left.GetNodeIndex(iL, jL, kL + 1);
                            const int i1k1 = left.GetNodeIndex(iL + 1, jL, kL + 1);
                            if (leftRange[1] == 0) mesh->v_face[faceID] = Face(i0k0, i1k0, i1k1, i0k1);
                            else                   mesh->v_face[faceID] = Face(i0k0, i0k1, i1k1, i1k0);
                        }
                        else
                        {
                            if (leftRange[1] == 0) mesh->v_face[faceID] = Face(i0k0, i1k0);
                            else                   mesh->v_face[faceID] = Face(i1k0, i0k0);
                        }
                    }

                    if (dim3 && leftRange[2] == leftRange[5]) // K边界
                    {
                        if (iL + 1 > Max(leftRange[0], leftRange[3])) continue;
                        if (jL + 1 > Max(leftRange[1], leftRange[4])) continue;

                        const int i0j0 = left.GetNodeIndex(iL,     jL,     kL);
                        const int i0j1 = left.GetNodeIndex(iL,     jL + 1, kL);
                        const int i1j0 = left.GetNodeIndex(iL + 1, jL,     kL);
                        const int i1j1 = left.GetNodeIndex(iL + 1, jL + 1, kL);

                        if (leftRange[2] == 0) ownerID = left.GetElementIndex(iL, jL, kL);
                        else                   ownerID = left.GetElementIndex(iL, jL, kL - 1);

                        int iR1 = iR, jR1 = jR, kR1 = kR;

                        if (leftRange[2] == 0)
                        {
                            // 左K为小编号，左右同向时右端为大编号
                            if (transform[2] == 1) iR1 = iR - 1;
                            else if (transform[2] == 2) jR1 = jR - 1;
                            else if (transform[2] == 3) kR1 = kR - 1;
                        }
                        else
                        {
                            // 左K为大编号，左右反向时右端为大编号
                            if (transform[2] == -1) iR1 = iR - 1;
                            else if (transform[2] == -2) jR1 = jR - 1;
                            else if (transform[2] == -3) kR1 = kR - 1;
                        }

                        if (transform[0] == -1 || transform[1] == -1) iR1 = iR - 1;
                        if (transform[0] == -2 || transform[1] == -2) jR1 = jR - 1;
                        if (transform[0] == -3 || transform[1] == -3) kR1 = kR - 1;

                        neighID = right.GetElementIndex(iR1, jR1, kR1);

                        if (leftRange[2] == 0) mesh->v_face[faceID] = Face(i0j0, i0j1, i1j1, i1j0);
                        else                   mesh->v_face[faceID] = Face(i0j0, i1j0, i1j1, i0j1);
                    }

                    if (ownerID == -1 || neighID == -1)
                    {
                        FatalError("CgnsMeshStructured::ProcessBoundaryElementZone: wrong bc info!");
                        return 1;
                    }

                    mesh->v_face[faceID].n_owner = ownerID;
                    mesh->v_face[faceID].n_neighbor = neighID;
                    mesh->v_elem[ownerID].v_faceID.push_back(faceID);
                    mesh->v_elem[neighID].v_faceID.push_back(faceID);

                    faceID++;
				}
            }
        }
    }

    return 0;
}