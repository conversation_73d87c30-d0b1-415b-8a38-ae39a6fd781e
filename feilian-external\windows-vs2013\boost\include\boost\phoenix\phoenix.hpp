/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#ifndef BOOST_PHOENIX_PHOENIX_HPP
#define BOOST_PHOENIX_PHOENIX_HPP

#include <boost/phoenix/core.hpp>
#include <boost/phoenix/function.hpp>
#include <boost/phoenix/operator.hpp>
#include <boost/phoenix/statement.hpp>
#include <boost/phoenix/object.hpp>
#include <boost/phoenix/scope.hpp>
#include <boost/phoenix/bind.hpp>
#include <boost/phoenix/stl.hpp>
#include <boost/phoenix/fusion.hpp>

#endif
