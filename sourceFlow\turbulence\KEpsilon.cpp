﻿#include "sourceFlow/turbulence/KEpsilon.h"

namespace Turbulence
{

	KEpsilon::KEpsilon(Package::FlowPackage &flowPackage)
		:
		BaseTurbulence(flowPackage),
		k(*GetTurbulencePointer(FlowMacro::Scalar::K)),
		epsilon(*GetTurbulencePointer(FlowMacro::Scalar::EPSILON)),

		residualK(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_K)),
		residualEpsilon(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_EPSILON)),

		gradientK(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_K)),
		gradientEpsilon(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_EPSILON)),

		two3(2.0 / 3.0),
		sigmaK(1.0), sigma<PERSON>(1.3),
		Cmu(0.09),Ce1(1.44), Ce2(1.92)
		
	{
		// 获取远场值
		const std::vector<Scalar> &farValue = turbulenceBoundary.GetFarValue();

		for (int m = 0; m < turbulenceSize; ++m)
		{
			if (turbulenceMacro[m] == FlowMacro::Scalar::K) kFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::EPSILON) epsilonFree = farValue[m];
		}
	}

	KEpsilon::~KEpsilon()
	{
	}

	void KEpsilon::CalculateMuTurbulent()
	{
		//计算mut
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &rhoI = rho.GetValue(elementID);
			const Scalar &kITemp = k.GetValue(elementID);
			const Scalar &eITemp = epsilon.GetValue(elementID);

			const Scalar kI = Max(1.e-5 * kFree, kITemp);
			const Scalar eI = Max(eITemp, 1.e-5 * epsilonFree);

			const Scalar &d = mesh->GetNearWallDistance(elementID);
			const Scalar &muL = muLaminar.GetValue(elementID);
			const Scalar kI2 = kI * kI;

			const Scalar ReT = rhoI * kI2 / (eI * muL);
			const Scalar ReY = rhoI * sqrt(kI) * d / muL;

			const Scalar fMuTemp = 1.0 - exp(-0.0165 * ReY);
			const Scalar fMu = fMuTemp * fMuTemp *(1.0 + 20.5 / ReT);

			const Scalar muTurbulentTemp = Cmu * fMu * ReT * muL;
			muTurbulent->SetValue(elementID, muTurbulentTemp);
		}

		return;
	}

	void KEpsilon::AddSourceResidual()
	{
		if (currentLevel > 0) return;

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int elementID = 0; elementID < elementNumber; ++elementID)
		{
			const Scalar &rhoI = rho.GetValue(elementID);
			const Scalar muTurblentI = muTurbulent->GetValue(elementID);
			const Scalar &kITemp = k.GetValue(elementID);
			const Scalar &eITemp = epsilon.GetValue(elementID);

			const Scalar kI = Max(1.e-5 * kFree, kITemp);
			const Scalar eI = Max(eITemp, 1.e-5 * epsilonFree);

			const Scalar omegaI = eI / kI;

			const Scalar rhoK = rhoI * kI;
			const Scalar rhoE = rhoI * eI;

			const Scalar &muL = muLaminar.GetValue(elementID);
			const Scalar kI2 = kI * kI;

			const Tensor &gradUI = gradientU.GetValue(elementID);
			Tensor tau = flowPackage.CalculateTau(muTurblentI, gradUI);
			tau.AddDiag(-two3 * rhoK);
			const Scalar tauS = Max(tau && gradUI, SMALL);
			const Scalar p_k_prod = tauS;
			
			const Scalar p_k_dest = rhoE;

			const Scalar ReT = rhoI * kI2 / (eI * muL);

			const Scalar &d = mesh->GetNearWallDistance(elementID);

			Scalar fe1 = Scalar0;
			const Scalar ReY = rhoI * sqrt(kI) * d / muL;
			const Scalar fMuTemp = 1.0 - exp(-0.0165 * ReY);
			const Scalar fMu = fMuTemp * fMuTemp *(1.0 + 20.5 / ReT);

			if (d > SMALL)
			{
				fe1 = 1.0 + pow(0.05 / fMu, 3.0);
			}
			
			const Scalar fe2 = 1.0 - 0.3 * exp(-ReT * ReT);
			
			const Scalar p_e_prod = Ce1 * fe1 * p_k_prod * omegaI;
			const Scalar p_e_dest = Ce2 * fe2 * p_k_dest * omegaI;

			const Scalar &volume = mesh->GetElement(elementID).GetVolume();
			const Scalar p_k_source = volume * (p_k_prod - p_k_dest);
			const Scalar p_e_source = volume * (p_e_prod - p_e_dest);

			residualK.AddValue(elementID, -p_k_source);
			residualEpsilon.AddValue(elementID, -p_e_source);

			// 点隐式Jacobian
			//const Scalar jacobianK = -betaK * wI * volume * produceK_dest;
			//const Scalar jacobianE = (-2.0 * betaW * wI - Max(0.0, p_w_Cross) / rhoI * wInverse) * volume;
			//const Scalar jacobianK = Scalar0;
			//const Scalar jacobianE = Scalar0;
			const Scalar jacobianK = -volume * 2.0 * rhoK * fMu *0.09;
			const Scalar jacobianE = -volume * 2.0 * Ce2 * fe2 * eI / kI;

			jacobianTurbulence[0]->SetValue(elementID, jacobianK);
			jacobianTurbulence[1]->SetValue(elementID, jacobianE);
		}
	}

	int KEpsilon::CheckAndLimit()
	{
		const Scalar limitCoff = 1.e-5;
		const Scalar kMin = limitCoff * kFree;
		const Scalar eMin = limitCoff * epsilonFree;

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &kI = k.GetValue(elementID);
			Scalar kLimited = Max(kI, kMin);
			k.SetValue(elementID, kLimited);

			const Scalar &eI = epsilon.GetValue(elementID);
			Scalar eLimited = Max(eI, eMin);
			epsilon.SetValue(elementID, eLimited);
		}

		int elementID = -1;
		elementID = Max(elementID, k.CheckAndLimit(0.0, INF));
		elementID = Max(elementID, epsilon.CheckAndLimit(0.0, INF));
		return elementID;
	}

	std::vector<std::pair<Scalar, Scalar>> KEpsilon::CalculateGammaFace(const int &faceID)
	{
		//得到面相关信息
		const int &leftID = mesh->GetFace(faceID).GetOwnerID();
		const int &rightID = mesh->GetFace(faceID).GetNeighborID();

		//计算面心值
		const Scalar muLaminarFace = 0.5 * (muLaminar.GetValue(leftID) + muLaminar.GetValue(rightID));
		const Scalar muTurbulentFace = 0.5 * (muTurbulent->GetValue(leftID) + muTurbulent->GetValue(rightID));

		//计算面的扩散项通量
		std::vector<std::pair<Scalar, Scalar>> gamma(2);
		gamma[0].first = muLaminarFace + muTurbulentFace / sigmaK; //K方程的扩散系数
		gamma[1].first = muLaminarFace + muTurbulentFace / sigmaE; //Epsilon方程的扩散系数

		gamma[0].second = gamma[0].first;
		gamma[1].second = gamma[1].first;


		return gamma;
	}

	void KEpsilon::InitializeTurbulentField()
	{
		ElementField<Vector> Ucorrected = U;

		Scalar freeStreamRe = flowPackage.GetFlowConfigure().GetFlowReference().Reynolds;
		Vector freeStreamVelocity = flowPackage.GetFlowConfigure().GetFlowReference().velocity;
		Scalar freeStreamDensity = flowPackage.GetFlowConfigure().GetFlowReference().density;
		Scalar freeStreamMuLaminar = flowPackage.GetFlowConfigure().GetFlowReference().muLaminar;
		Scalar cfTheory = 0.058 * pow(freeStreamRe, -0.2);
		Scalar UMag = freeStreamVelocity.Mag();
		Scalar tauWall = 0.5 * cfTheory * freeStreamDensity * UMag * UMag;
		Scalar Utau = sqrt(tauWall / freeStreamDensity);
		Scalar blThickness = Scalar0;

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);

			const Scalar &d = mesh->GetNearWallDistance(elementID);

			Scalar yPlusTemp = Utau * d / freeStreamMuLaminar * freeStreamDensity;
			Scalar yPlus = Max(yPlusTemp, exp(-5.0));

			Scalar UMagInner = Utau / 0.41 * (log(yPlus) + 5.0);
			Scalar UMagTemp = Min(UMag, UMagInner);
			if (UMagTemp < UMag) blThickness = d;

			Scalar Ux = UMagTemp * freeStreamVelocity.X() / UMag;
			Scalar Uy = UMagTemp * freeStreamVelocity.Y() / UMag;
			Scalar Uz = UMagTemp * freeStreamVelocity.Z() / UMag;

			Vector UcorrectedLocal;
			UcorrectedLocal.SetX(Ux);
			UcorrectedLocal.SetY(Uy);
			UcorrectedLocal.SetZ(Uz);

			Ucorrected.SetValue(elementID, UcorrectedLocal);
		}

		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);

			Scalar d = mesh->GetNearWallDistance(elementID);
			d = Max(d, SMALL);
			Scalar lmix = Min(0.41 * d, 0.09 * blThickness);
			Scalar UMag = Ucorrected.GetValue(elementID).Mag();
			Scalar kTemp = kFree;
			Scalar epsilonTemp = epsilonFree;
			if (d < blThickness)
			{
				kTemp = pow(UMag / d, 2.0) * lmix * lmix / sqrt(Cmu) + kFree * d / blThickness;
				epsilonTemp = pow(Cmu, 0.75) * pow(kTemp, 1.5) / lmix + epsilonFree * d / blThickness;
			}

			k.SetValue(elementID, kTemp);
			epsilon.SetValue(elementID, epsilonTemp);
		}

		return;
	}

}//namespace Turbulence
