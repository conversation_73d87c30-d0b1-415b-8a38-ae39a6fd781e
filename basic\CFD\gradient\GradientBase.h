﻿#ifndef _basic_CFD_gradient_GradientBase_
#define _basic_CFD_gradient_GradientBase_

#include "basic/field/ElementField.h"
#include "basic/configure/Configure.h"
#include "basic/configure/ConfigureMacro.h"

/**
 * @brief 梯度计算命名空间
 * 
 */
namespace Gradient
{

/**
 * @brief 梯度计算基类
 * 
 */
class GradientBase
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] nodeCenter_ 格点标识
     */
    GradientBase(Mesh *mesh_, const bool nodeCenter_ = false);

    /**
     * @brief 析构函数
     * 
     */
    ~GradientBase();
    
    /**
     * @brief 标量场梯度计算函数
     * 
     * @param[in] phi 标量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    virtual void CalculateScalar(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi) = 0;

    /**
     * @brief 矢量场梯度计算函数
     * 
     * @param[in] phi 矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    virtual void CalculateVector(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi) = 0;

protected:
    /// 当前物理场所对应的网格
	Mesh *mesh;
    
    ///< 格点标识
    const bool nodeCenter;
};

}
#endif