﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BlockSparseMatrix.h
//! <AUTHOR>
//! @brief 块稀疏矩阵类
//! @date 2024-12-10
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-10 气动院
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_CFD_BlockSparseMatrix_
#define _basic_CFD_BlockSparseMatrix_

#include "basic/common/Configuration.h"
#include "basic/common/SystemControl.h"
#include "basic/mesh/Mesh.h"

/**
 * @brief 块稀疏矩阵抽象基类
 *
 */
class BlockSparseMatrix
{
public:
	/**
	 * @brief 构造函数
	 *
	 * 初始化块稀疏矩阵，关联到指定的网格（Mesh）并设置变量数。
	 * 
	 * @param[in] mesh 关联的网格对象，类型为Mesh*
	 * @param[in] nvariable 变量的数量，类型为const int
	 */
	BlockSparseMatrix(Mesh *mesh_, const int nVariable_) : mesh(mesh_), nVariable(nVariable_) {}

	/**
	 * @brief 构造函数
	 *
	 */
	virtual ~BlockSparseMatrix() {}

	/**
	 * @brief 初始化矩阵结构
	 *
	 * 子类实现此函数以完成矩阵的具体初始化操作。
	 */
	virtual void Initialize() = 0;

	/**
	 * @brief 将矩阵所有元素设置为零
	 *
	 * 此方法将块稀疏矩阵中的所有存储的元素值重置为零，准备矩阵进行新的填充或计算。
	 */
	virtual void SetValZero() = 0;

	/**
	 * @brief 向对角线位置添加标量值
	 *
	 * 在指定行的对角线位置上增加一个标量值。
	 * 
	 * @param[in] row 指定的行索引，类型为const int&
	 * @param[in] val 需要添加的标量值，类型为const Scalar&
	 */
	virtual void AddVal2Diag(const int &row, const Scalar &val) = 0;

	/**
	 * @brief 向对角线位置添加矩阵块
	 *
	 * 在指定行的对角线位置上增加一个矩阵块。
	 * 
	 * @param[in] row 指定的行索引，类型为const int&
	 * @param[in] val 需要添加的矩阵块，类型为const Matrix&
	 */
	virtual void AddBlock2Diag(const int &row, const Matrix &val) = 0;

	/**
	 * @brief 更新矩阵中的块
	 *
	 * 根据面ID和相关元素信息更新矩阵中的块。
	 * 
	 * @param[in] faceID 面的标识符，类型为const int&
	 * @param[in] elemI 第一个元素的索引，类型为const int&
	 * @param[in] elemJ 第二个元素的索引，类型为const int&
	 * @param[in] valI 需要更新的第一个矩阵块，类型为const Matrix&
	 * @param[in] valJ 需要更新的第二个矩阵块，类型为const Matrix&
	 */
	virtual void UpdateBlocks(const int &faceID, const int &elemI,
							 const int &elemJ, const Matrix &valI,
							 const Matrix &valJ) = 0;

	/**
	 * @brief 删除指定单元所对应块矩阵的指定行的值
	 * 
	 * 修改指定单元的块矩阵的指定行的对角阵元素为1，其余元素为0
	 * 
	 * @param[in] row 需要删除的矩阵块行索引（单元编号），类型为const int &
	 * @param[in] index0 需要删除的块内行索引（变量编号），类型为const int &
	 */
	virtual void DeleteValsRowi(const int &row, const int &index0) = 0;

protected:
	const int nVariable; ///< 每个块的每行元素数量
	Mesh *mesh; ///< 网格指针
};

#endif