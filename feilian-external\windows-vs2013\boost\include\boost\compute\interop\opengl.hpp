//---------------------------------------------------------------------------//
// Copyright (c) 2013-2014 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_INTEROP_OPENGL_HPP
#define BOOST_COMPUTE_INTEROP_OPENGL_HPP

/// \file
///
/// Meta-header to include all Boost.Compute OpenGL interop headers.

#include <boost/compute/interop/opengl/acquire.hpp>
#include <boost/compute/interop/opengl/context.hpp>
#include <boost/compute/interop/opengl/opengl_buffer.hpp>
#include <boost/compute/interop/opengl/opengl_renderbuffer.hpp>
#include <boost/compute/interop/opengl/opengl_texture.hpp>

#endif // BOOST_COMPUTE_INTEROP_OPENGL_HPP
