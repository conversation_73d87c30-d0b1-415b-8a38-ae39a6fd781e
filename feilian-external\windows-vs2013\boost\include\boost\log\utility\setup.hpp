/*
 *          Copyright <PERSON><PERSON> 2007 - 2015.
 * Distributed under the Boost Software License, Version 1.0.
 *    (See accompanying file LICENSE_1_0.txt or copy at
 *          http://www.boost.org/LICENSE_1_0.txt)
 */
/*!
 * \file   setup.hpp
 * \author <PERSON><PERSON>
 * \date   16.02.2013
 *
 * This header includes all library setup helpers.
 */

#ifndef BOOST_LOG_UTILITY_SETUP_HPP_INCLUDED_
#define BOOST_LOG_UTILITY_SETUP_HPP_INCLUDED_

#include <boost/log/detail/setup_config.hpp>

#include <boost/log/utility/setup/common_attributes.hpp>

#include <boost/log/utility/setup/console.hpp>
#include <boost/log/utility/setup/file.hpp>

#include <boost/log/utility/setup/from_settings.hpp>
#include <boost/log/utility/setup/from_stream.hpp>

#include <boost/log/utility/setup/settings.hpp>
#include <boost/log/utility/setup/settings_parser.hpp>
#include <boost/log/utility/setup/filter_parser.hpp>
#include <boost/log/utility/setup/formatter_parser.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_LOG_UTILITY_SETUP_HPP_INCLUDED_
