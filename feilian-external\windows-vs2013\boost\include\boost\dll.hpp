// Copyright 2014 <PERSON><PERSON>, <PERSON>.
// Copyright 2015-2016 <PERSON>.
//
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt
// or copy at http://www.boost.org/LICENSE_1_0.txt)


#ifndef BOOST_DLL_DLL_HPP
#define BOOST_DLL_DLL_HPP

/// \file boost/dll.hpp
/// \brief Includes all the non-experimental headers of the Boost.DLL library.

#include <boost/config.hpp>
#include <boost/dll/shared_library.hpp>
#include <boost/dll/alias.hpp>
#include <boost/dll/import.hpp>
#include <boost/dll/library_info.hpp>
#include <boost/dll/runtime_symbol_info.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
# pragma once
#endif

#endif // BOOST_DLL_DLL_HPP

