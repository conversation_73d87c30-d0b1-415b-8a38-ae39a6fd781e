/*
    Copyright 2005-2007 Adobe Systems Incorporated
   
    Use, modification and distribution are subject to the Boost Software License,
    Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt).

    See http://opensource.adobe.com/gil for most recent version including documentation.
*/

/*************************************************************************************************/

#ifndef GIL_DYNAMICIMAGE_ALL_HPP
#define GIL_DYNAMICIMAGE_ALL_HPP

////////////////////////////////////////////////////////////////////////////////////////
/// \file               
/// \brief Includes all of the GIL dynamic image extension files, for convenience
/// \author <PERSON><PERSON><PERSON> and <PERSON><PERSON> \n
///         Adobe Systems Incorporated
/// \date   2005-2007 \n Last updated on May 8, 2006
///
////////////////////////////////////////////////////////////////////////////////////////

#include "../../gil_all.hpp"
#include "algorithm.hpp"
#include "any_image.hpp"
#include "apply_operation.hpp"
#include "variant.hpp"
#include "image_view_factory.hpp"

#endif
