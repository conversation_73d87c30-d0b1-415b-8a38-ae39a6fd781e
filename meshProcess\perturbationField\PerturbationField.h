////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file PerturbationField.h
//! <AUTHOR>
//! @brief 摄动场数据结构定义.
//! @date 2024-12-20
//
//------------------------------修改日志----------------------------------------
// 2024-12-20 李艳亮、乔龙
//     说明：建立并规范化
//
//------------------------------------------------------------------------------

#ifndef _meshProcess_perturbationField_PerturbationField_
#define _meshProcess_perturbationField_PerturbationField_

#include "basic/common/CommonMacro.h"
#include "basic/common/Vector.h"
#include "basic/mesh/Mesh.h"
#include <vector>
#include <string>

/**
 * @brief 摄动场命名空间
 * 
 */
namespace PerturbationField
{

/**
 * @brief 单个模态的摄动场数据结构
 * 
 */
struct ModalPerturbationData
{
    int modeIndex; ///< 模态索引
    int nodeNumber; ///< 节点数量
    std::vector<Vector> nodeDisplacements; ///< 节点位移摄动场
    std::string fileName; ///< 对应的文件名
    bool isLoaded; ///< 是否已加载数据
    
    ModalPerturbationData()
    {
        modeIndex = -1;
        nodeNumber = 0;
        fileName = "";
        isLoaded = false;
        nodeDisplacements.clear();
    }
    
    /**
     * @brief 初始化模态摄动场数据
     * 
     * @param modeIndex_ 模态索引
     * @param nodeNumber_ 节点数量
     * @param fileName_ 文件名
     */
    void Initialize(int modeIndex_, int nodeNumber_, const std::string &fileName_)
    {
        modeIndex = modeIndex_;
        nodeNumber = nodeNumber_;
        fileName = fileName_;
        nodeDisplacements.resize(nodeNumber, Vector0);
        isLoaded = false;
    }
    
    /**
     * @brief 清空数据
     * 
     */
    void Clear()
    {
        nodeDisplacements.clear();
        isLoaded = false;
    }
    
    /**
     * @brief 获取指定节点的摄动位移
     * 
     * @param nodeID 节点ID
     * @return const Vector& 摄动位移向量
     */
    const Vector &GetNodeDisplacement(int nodeID) const
    {
        if (nodeID >= 0 && nodeID < nodeNumber)
            return nodeDisplacements[nodeID];
        else
            return Vector0;
    }
    
    /**
     * @brief 设置指定节点的摄动位移
     * 
     * @param nodeID 节点ID
     * @param displacement 摄动位移向量
     */
    void SetNodeDisplacement(int nodeID, const Vector &displacement)
    {
        if (nodeID >= 0 && nodeID < nodeNumber)
            nodeDisplacements[nodeID] = displacement;
    }
};

/**
 * @brief 摄动场管理器类
 * 
 */
class PerturbationFieldManager
{
public:
    /**
     * @brief 构造函数
     * 
     * @param mesh_ 网格指针
     */
    PerturbationFieldManager(Mesh *mesh_);
    
    /**
     * @brief 析构函数
     * 
     */
    ~PerturbationFieldManager();
    
    /**
     * @brief 初始化摄动场管理器
     * 
     * @param numModes_ 模态数量
     * @param perturbationPath_ 摄动场文件路径
     * @param fileNames_ 摄动场文件名列表
     * @param binaryFormat_ 文件格式标识
     */
    void Initialize(int numModes_, const std::string &perturbationPath_, 
                   const std::vector<std::string> &fileNames_, bool binaryFormat_);
    
    /**
     * @brief 加载所有摄动场数据
     * 
     * @return int 成功返回0，失败返回错误码
     */
    int LoadAllPerturbationFields();
    
    /**
     * @brief 加载指定模态的摄动场数据
     * 
     * @param modeIndex 模态索引
     * @return int 成功返回0，失败返回错误码
     */
    int LoadPerturbationField(int modeIndex);
    
    /**
     * @brief 验证摄动场数据
     * 
     * @return bool 验证通过返回true，否则返回false
     */
    bool ValidatePerturbationFields();
    
    /**
     * @brief 获取指定模态的摄动场数据
     * 
     * @param modeIndex 模态索引
     * @return const ModalPerturbationData& 模态摄动场数据
     */
    const ModalPerturbationData &GetModalPerturbationData(int modeIndex) const;
    
    /**
     * @brief 获取指定模态指定节点的摄动位移
     * 
     * @param modeIndex 模态索引
     * @param nodeID 节点ID
     * @return const Vector& 摄动位移向量
     */
    const Vector &GetNodePerturbationDisplacement(int modeIndex, int nodeID) const;
    
    /**
     * @brief 计算模态叠加后的节点位移
     * 
     * @param modalAmplitudes 模态幅值数组
     * @param nodeDisplacements 输出的节点位移数组
     */
    void ComputeModalSuperposition(const std::vector<Scalar> &modalAmplitudes, 
                                  std::vector<Vector> &nodeDisplacements);
    
    /**
     * @brief 输出摄动场数据到文件（用于分区后的子网格）
     * 
     * @param outputPath 输出路径
     * @param caseName 算例名称
     * @param partitionID 分区ID
     * @param nodeMapping 节点映射关系（全局ID到局部ID）
     */
    void OutputPartitionedPerturbationFields(const std::string &outputPath, 
                                           const std::string &caseName,
                                           int partitionID,
                                           const std::vector<int> &nodeMapping);
    
    /**
     * @brief 获取模态数量
     * 
     * @return int 模态数量
     */
    int GetNumModes() const { return numModes; }
    
    /**
     * @brief 获取节点数量
     * 
     * @return int 节点数量
     */
    int GetNodeNumber() const { return nodeNumber; }
    
    /**
     * @brief 检查是否已初始化
     * 
     * @return bool 已初始化返回true，否则返回false
     */
    bool IsInitialized() const { return initialized; }

private:
    /**
     * @brief 从ASCII文件读取摄动场数据
     * 
     * @param fileName 文件名
     * @param modalData 模态数据
     * @return int 成功返回0，失败返回错误码
     */
    int ReadPerturbationFieldASCII(const std::string &fileName, ModalPerturbationData &modalData);
    
    /**
     * @brief 从二进制文件读取摄动场数据
     * 
     * @param fileName 文件名
     * @param modalData 模态数据
     * @return int 成功返回错误码
     */
    int ReadPerturbationFieldBinary(const std::string &fileName, ModalPerturbationData &modalData);
    
    /**
     * @brief 验证单个模态的摄动场数据
     * 
     * @param modeIndex 模态索引
     * @return bool 验证通过返回true，否则返回false
     */
    bool ValidateModalData(int modeIndex);

private:
    Mesh *mesh; ///< 网格指针
    int numModes; ///< 模态数量
    int nodeNumber; ///< 节点数量
    std::string perturbationPath; ///< 摄动场文件路径
    std::vector<std::string> fileNames; ///< 摄动场文件名列表
    bool binaryFormat; ///< 文件格式标识
    bool initialized; ///< 初始化标识
    
    std::vector<ModalPerturbationData> modalPerturbationData; ///< 模态摄动场数据容器
};

} // namespace PerturbationField

#endif // _meshProcess_perturbationField_PerturbationField_
