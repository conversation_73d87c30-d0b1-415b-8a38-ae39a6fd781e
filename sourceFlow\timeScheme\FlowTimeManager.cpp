﻿#include "sourceFlow/timeScheme/FlowTimeManager.h"

namespace Time
{
namespace Flow
{

FlowTimeManager::FlowTimeManager(Package::FlowPackage &data)
    :
    flowPackage(data)
{
    this->SetTimePointer();
}

FlowTimeManager::~FlowTimeManager()
{
    if (this->time != nullptr) { delete this->time; this->time = nullptr; }
}

void FlowTimeManager::Initialize(const Initialization::Type &initialType)
{
    if (time != nullptr) time->Initialize(initialType);
    else FatalError("FolwTimeManager::Initialize: TimePointer is nullptr!");
    
    return;
}

void FlowTimeManager::CalculateResidual()
{
    if (time != nullptr) time->CalculateResidual();
    else FatalError("FolwTimeManager::Initialize: TimePointer is nullptr!");

    return;
}

void FlowTimeManager::Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction)
{
    if (time != nullptr) time->Iterate(recalculateResiduals, calculateForcingFunction);
    else FatalError("FolwTimeManager::Iterate: TimePointer is nullptr!");

    return;
}

void FlowTimeManager::UpdateBoundaryCondition()
{
    if (time != nullptr) time->UpdateBoundaryCondition();
    else FatalError("FlowTimeManager::UpdateBoundaryCondition: TimePointer is nullptr!");

    return;
}

void FlowTimeManager::SetFineGridLevel(const int &fineMeshLevel)
{
    if (time != nullptr) time->SetFineGridLevel(fineMeshLevel);
}

void FlowTimeManager::SaveOld()
{
    if (time != nullptr) time->SaveOld();
}

void FlowTimeManager::UpdateGradientAndMuT()
{
    if (time != nullptr) time->UpdateGradientAndMuT();
}

void FlowTimeManager::SetTimePointer()
{
    const Time::Scheme &timeScheme = flowPackage.GetFlowConfigure().GetTimeScheme().innnerLoopType;

    switch (timeScheme)
    {
    case Time::Scheme::RUNGE_KUTTA:
    {
        time = new Time::Flow::RungeKutta(flowPackage);
        break;
    }
    case Time::Scheme::LUSGS:
    case Time::Scheme::DPLUR:
    {
        time = new Time::Flow::LUSGS(flowPackage);
        break;
    }
    case Time::Scheme::GMRES:
#if defined(_EnablePETSC_)
    case Time::Scheme::CG:
    case Time::Scheme::BICG:
#endif
    {
        time = new Time::Flow::ExactJacobian(flowPackage);
        break; 
    }
    default:
    {
		FatalError("FlowTimeManager::SetTimePointer: TimeSchemeType is unkown!");
		return;
    }
    }
    
    return;
}

}//namespace Flow
}//namespace Time