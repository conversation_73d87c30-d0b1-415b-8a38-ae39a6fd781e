﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/AUSMScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

AUSMScheme::AUSMScheme(Package::FlowPackage &data,                       
                       Limiter::Limiter *limiter,
                       Flux::Flow::Precondition::Precondition *precondition)
    :
    UpwindScheme(data, limiter, precondition)
{
}

NSFaceFlux AUSMScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    //获取左右面值
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;
    
    //得到面法向和面积大小
    const Face &face = mesh->GetFace(faceID);
    const Vector &faceNormal = face.GetNormal();
    const Scalar &faceArea = face.GetArea();    

    //计算左右面的温度、焓和声速
    const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
    const Scalar enthalpyLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);
    const Scalar criticalSoundLeft = sqrt(enthalpyLeft * 2.0 * (gamma - 1) / (gamma + 1));
    const Scalar TRight = material.GetTemperature(pRight, rhoRight);
    const Scalar enthalpyRight = Cp * TRight + 0.5 * (URight & URight);
    const Scalar criticalSoundRight = sqrt(enthalpyRight * 2.0 * (gamma - 1) / (gamma + 1));

    //计算声速面心值
    const Scalar velocityNormLeft = (ULeft & faceNormal);
    const Scalar velocityNormRight = (URight & faceNormal);
    const Scalar soundTildaLeft = criticalSoundLeft * criticalSoundLeft / Max(criticalSoundLeft, velocityNormLeft);
    const Scalar soundTildaRight = criticalSoundRight * criticalSoundRight / Max(criticalSoundRight, -velocityNormRight);
    const Scalar soundShared = Min(soundTildaLeft, soundTildaRight);

    //计算左右面的马赫数
    const Scalar machLeft = velocityNormLeft / soundShared;
    const Scalar machRight = velocityNormRight / soundShared;

    //计算Ma+和Ma-
    Scalar machLeftPositive;
    Scalar machLeftNegative;
    Scalar positive;
    Scalar negative;
    const Scalar BETA = 0.125;
    const Scalar ALPHA = 3.0 / 16.0;

    if (fabs(machLeft) >= 1.0)
    {
        machLeftPositive = 0.5 * (machLeft + fabs(machLeft));
        positive = 0.5 * (1.0 + (machLeft > 0.0 ? 1.0 : -1.0));
    }
    else
    {
        machLeftPositive = 0.25 * pow(machLeft + 1.0, 2.0) + BETA * pow((pow(machLeft, 2.0) - 1.0), 2.0);
        positive = 0.25 * pow(machLeft + 1.0, 2.0) * (2.0 - machLeft) + ALPHA * machLeft * pow(pow(machLeft, 2.0) - 1.0, 2.0);
    }

    if (fabs(machRight) >= 1.0)
    {
        machLeftNegative = 0.5 * (machRight - fabs(machRight));
        negative = 0.5 * (1.0 - (machRight > 0.0 ? 1.0 : -1.0));
    }
    else
    {
        machLeftNegative = -0.25 * pow(machRight - 1.0, 2.0) - BETA * pow((pow(machRight, 2.0) - 1.0), 2.0);
        negative = 0.25 * pow(machRight - 1.0, 2.0) * (2.0 + machRight) - ALPHA * machRight * pow(pow(machRight, 2.0) - 1.0, 2.0);
    }

    //计算马赫数、压强和速度的面心值
    const Scalar machShared = machLeftPositive + machLeftNegative;
    const Scalar pShared = positive * pLeft + negative * pRight;
    const Scalar velocityShared = machShared * soundShared;

    //计算面通量（缺面积）
    if (machShared >= 0.0)
    {
        const Scalar left = rhoLeft * velocityShared * faceArea;
        faceFlux.massFlux = left;
        faceFlux.momentumFlux = left * ULeft + pShared * faceNormal * faceArea;
        faceFlux.energyFlux = left * enthalpyLeft;
    }
    else
    {
        const Scalar right = rhoRight * velocityShared * faceArea;
        faceFlux.massFlux = right;
        faceFlux.momentumFlux = right * URight + pShared * faceNormal * faceArea;
        faceFlux.energyFlux = right * enthalpyRight;
    }
    
    return faceFlux;
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux