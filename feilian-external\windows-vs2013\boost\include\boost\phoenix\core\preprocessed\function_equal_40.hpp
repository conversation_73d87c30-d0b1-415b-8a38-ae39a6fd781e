/*==============================================================================
    Copyright (c) 2001-2010 <PERSON>
    Copyright (c) 2004 Daniel <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
                template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 1 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) ; } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 2 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 3 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 4 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 5 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 6 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 7 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 8 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 9 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 10 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 11 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 12 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 13 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 14 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 15 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 16 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 17 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 18 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 19 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 20 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 21 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 22 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 23 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 24 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 25 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 26 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 27 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 28 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 29 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 30 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 31 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 32 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 33 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 34 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 35 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ) && function_equal_()( proto::child_c< 34 >(e1) , proto::child_c< 34 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 36 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ) && function_equal_()( proto::child_c< 34 >(e1) , proto::child_c< 34 >(e2) ) && function_equal_()( proto::child_c< 35 >(e1) , proto::child_c< 35 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 37 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ) && function_equal_()( proto::child_c< 34 >(e1) , proto::child_c< 34 >(e2) ) && function_equal_()( proto::child_c< 35 >(e1) , proto::child_c< 35 >(e2) ) && function_equal_()( proto::child_c< 36 >(e1) , proto::child_c< 36 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 38 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ) && function_equal_()( proto::child_c< 34 >(e1) , proto::child_c< 34 >(e2) ) && function_equal_()( proto::child_c< 35 >(e1) , proto::child_c< 35 >(e2) ) && function_equal_()( proto::child_c< 36 >(e1) , proto::child_c< 36 >(e2) ) && function_equal_()( proto::child_c< 37 >(e1) , proto::child_c< 37 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 39 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ) && function_equal_()( proto::child_c< 34 >(e1) , proto::child_c< 34 >(e2) ) && function_equal_()( proto::child_c< 35 >(e1) , proto::child_c< 35 >(e2) ) && function_equal_()( proto::child_c< 36 >(e1) , proto::child_c< 36 >(e2) ) && function_equal_()( proto::child_c< 37 >(e1) , proto::child_c< 37 >(e2) ) && function_equal_()( proto::child_c< 38 >(e1) , proto::child_c< 38 >(e2) ); } template <typename Expr1> result_type evaluate( Expr1 const& e1 , Expr1 const& e2 , mpl::long_< 40 > ) const { return function_equal_()( proto::child_c<0>(e1) , proto::child_c<0>(e2) ) && function_equal_()( proto::child_c< 1 >(e1) , proto::child_c< 1 >(e2) ) && function_equal_()( proto::child_c< 2 >(e1) , proto::child_c< 2 >(e2) ) && function_equal_()( proto::child_c< 3 >(e1) , proto::child_c< 3 >(e2) ) && function_equal_()( proto::child_c< 4 >(e1) , proto::child_c< 4 >(e2) ) && function_equal_()( proto::child_c< 5 >(e1) , proto::child_c< 5 >(e2) ) && function_equal_()( proto::child_c< 6 >(e1) , proto::child_c< 6 >(e2) ) && function_equal_()( proto::child_c< 7 >(e1) , proto::child_c< 7 >(e2) ) && function_equal_()( proto::child_c< 8 >(e1) , proto::child_c< 8 >(e2) ) && function_equal_()( proto::child_c< 9 >(e1) , proto::child_c< 9 >(e2) ) && function_equal_()( proto::child_c< 10 >(e1) , proto::child_c< 10 >(e2) ) && function_equal_()( proto::child_c< 11 >(e1) , proto::child_c< 11 >(e2) ) && function_equal_()( proto::child_c< 12 >(e1) , proto::child_c< 12 >(e2) ) && function_equal_()( proto::child_c< 13 >(e1) , proto::child_c< 13 >(e2) ) && function_equal_()( proto::child_c< 14 >(e1) , proto::child_c< 14 >(e2) ) && function_equal_()( proto::child_c< 15 >(e1) , proto::child_c< 15 >(e2) ) && function_equal_()( proto::child_c< 16 >(e1) , proto::child_c< 16 >(e2) ) && function_equal_()( proto::child_c< 17 >(e1) , proto::child_c< 17 >(e2) ) && function_equal_()( proto::child_c< 18 >(e1) , proto::child_c< 18 >(e2) ) && function_equal_()( proto::child_c< 19 >(e1) , proto::child_c< 19 >(e2) ) && function_equal_()( proto::child_c< 20 >(e1) , proto::child_c< 20 >(e2) ) && function_equal_()( proto::child_c< 21 >(e1) , proto::child_c< 21 >(e2) ) && function_equal_()( proto::child_c< 22 >(e1) , proto::child_c< 22 >(e2) ) && function_equal_()( proto::child_c< 23 >(e1) , proto::child_c< 23 >(e2) ) && function_equal_()( proto::child_c< 24 >(e1) , proto::child_c< 24 >(e2) ) && function_equal_()( proto::child_c< 25 >(e1) , proto::child_c< 25 >(e2) ) && function_equal_()( proto::child_c< 26 >(e1) , proto::child_c< 26 >(e2) ) && function_equal_()( proto::child_c< 27 >(e1) , proto::child_c< 27 >(e2) ) && function_equal_()( proto::child_c< 28 >(e1) , proto::child_c< 28 >(e2) ) && function_equal_()( proto::child_c< 29 >(e1) , proto::child_c< 29 >(e2) ) && function_equal_()( proto::child_c< 30 >(e1) , proto::child_c< 30 >(e2) ) && function_equal_()( proto::child_c< 31 >(e1) , proto::child_c< 31 >(e2) ) && function_equal_()( proto::child_c< 32 >(e1) , proto::child_c< 32 >(e2) ) && function_equal_()( proto::child_c< 33 >(e1) , proto::child_c< 33 >(e2) ) && function_equal_()( proto::child_c< 34 >(e1) , proto::child_c< 34 >(e2) ) && function_equal_()( proto::child_c< 35 >(e1) , proto::child_c< 35 >(e2) ) && function_equal_()( proto::child_c< 36 >(e1) , proto::child_c< 36 >(e2) ) && function_equal_()( proto::child_c< 37 >(e1) , proto::child_c< 37 >(e2) ) && function_equal_()( proto::child_c< 38 >(e1) , proto::child_c< 38 >(e2) ) && function_equal_()( proto::child_c< 39 >(e1) , proto::child_c< 39 >(e2) ); }
