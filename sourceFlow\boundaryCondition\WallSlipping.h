﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallSlipping.h
//! <AUTHOR> 乔龙
//! @brief 滑移壁面边界条件类(绝热)
//! @date  2022-11-22
//
//------------------------------修改日志----------------------------------------
//
// 2022-11-22 李艳亮 乔龙 
// 说明：建立并规范化
// 
//------------------------------------------------------------------------------
#ifndef _sourceFlow_boundaryCondition_WallSlipping_
#define _sourceFlow_boundaryCondition_WallSlipping_

#include "sourceFlow/boundaryCondition/Wall.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 滑移壁面边界条件类
 * 
 */
class WallSlipping :public Wall
{
public:
    /**
     * @brief 构造函数，初始化绝热壁面边界条件
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     */
    WallSlipping(const int &boundaryPatchID, Package::FlowPackage &data);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 绝热壁面边界条件具体实现函数
     * 
     */
    void UpdateBoundaryCondition();
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();
    
};

} // namespace Flow
} // namespace Boundary


#endif
