///////////////////////////////////////////////////////////////
//  Copyright <PERSON> 2006-1011
//  Copyright <PERSON> 2011
//  Copyright 2012 <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_

#ifndef BOOST_MP_RANDOM_HPP
#define BOOST_MP_RANDOM_HPP

#if defined(__GNUC__) || defined(_MSC_VER)
# pragma message("NOTE: Use of this header (boost/multiprecision/random.hpp) is deprecated: please use the random number library headers directly.")
#endif


#include <boost/random.hpp>

#endif
