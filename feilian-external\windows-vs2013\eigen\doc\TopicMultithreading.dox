namespace Eigen {

/** \page TopicMultiThreading Eigen and multi-threading

\section TopicMultiThreading_MakingEigenMT Make Eigen run in parallel

Some Eigen's algorithms can exploit the multiple cores present in your hardware. To this end, it is enough to enable OpenMP on your compiler, for instance:
 * GCC: \c -fopenmp
 * ICC: \c -openmp
 * MSVC: check the respective option in the build properties.
You can control the number of thread that will be used using either the OpenMP API or Eigen's API using the following priority:
\code
 OMP_NUM_THREADS=n ./my_program
 omp_set_num_threads(n);
 Eigen::setNbThreads(n);
\endcode
Unless setNbThreads has been called, Eigen uses the number of threads specified by OpenMP. You can restore this behavior by calling \code setNbThreads(0); \endcode
You can query the number of threads that will be used with:
\code
n = Eigen::nbThreads( );
\endcode
You can disable Eigen's multi threading at compile time by defining the EIGEN_DONT_PARALLELIZE preprocessor token.

Currently, the following algorithms can make use of multi-threading:
 - general dense matrix - matrix products
 - PartialPivLU
 - row-major-sparse * dense vector/matrix products
 - ConjugateGradient with \c Lower|Upper as the \c UpLo template parameter.
 - BiCGSTAB with a row-major sparse matrix format.
 - LeastSquaresConjugateGradient

\section TopicMultiThreading_UsingEigenWithMT Using Eigen in a multi-threaded application

In the case your own application is multithreaded, and multiple threads make calls to Eigen, then you have to initialize Eigen by calling the following routine \b before creating the threads:
\code
#include <Eigen/Core>

int main(int argc, char** argv)
{
  Eigen::initParallel();
  
  ...
}
\endcode

\note With Eigen 3.3, and a fully C++11 compliant compiler (i.e., <a href="http://en.cppreference.com/w/cpp/language/storage_duration#Static_local_variables">thread-safe static local variable initialization</a>), then calling \c initParallel() is optional.

\warning note that all functions generating random matrices are \b not re-entrant nor thread-safe. Those include DenseBase::Random(), and DenseBase::setRandom() despite a call to Eigen::initParallel(). This is because these functions are based on std::rand which is not re-entrant. For thread-safe random generator, we recommend the use of boost::random or c++11 random feature.

In the case your application is parallelized with OpenMP, you might want to disable Eigen's own parallization as detailed in the previous section.

*/

}
