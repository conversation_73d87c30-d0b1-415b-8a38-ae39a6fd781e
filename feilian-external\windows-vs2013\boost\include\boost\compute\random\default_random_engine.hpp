//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_RANDOM_DEFAULT_RANDOM_ENGINE_HPP
#define BOOST_COMPUTE_RANDOM_DEFAULT_RANDOM_ENGINE_HPP

#include <boost/compute/random/mersenne_twister_engine.hpp>

namespace boost {
namespace compute {

typedef mt19937 default_random_engine;

} // end compute namespace
} // end boost namespace

#endif // BOOST_COMPUTE_RANDOM_DEFAULT_RANDOM_ENGINE_HPP
