/*=============================================================================
    Copyright (c) 2001-2014 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_X3_CHAR_CLASS_TAGS_APRIL_16_2006_1051AM)
#define BOOST_SPIRIT_X3_CHAR_CLASS_TAGS_APRIL_16_2006_1051AM


namespace boost { namespace spirit { namespace x3
{
    ///////////////////////////////////////////////////////////////////////////
    struct char_tag {};
    struct alnum_tag {};
    struct alpha_tag {};
    struct blank_tag {};
    struct cntrl_tag {};
    struct digit_tag {};
    struct graph_tag {};
    struct print_tag {};
    struct punct_tag {};
    struct space_tag {};
    struct xdigit_tag {};
    struct lower_tag {};
    struct upper_tag {};
}}}

#endif
