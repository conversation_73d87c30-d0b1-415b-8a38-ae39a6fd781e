﻿#include "feilian-specialmodule/particle/force/ExternalForce.h"
#include "sourceFlow/material/Materials.h"
#include "basic/field/FieldManipulation.h"

namespace Particle
{

ExternalForce::ExternalForce(const Configure::Particle::ParticleConfigure &configure_,
                             std::vector<Particle *> &particles_, 
                             PhysicalProperty *physicalProperty_,
                             const std::vector<int> &backgroundID_,
                             const Package::FlowPackage *flowPackage_)
    : configure(configure_), particles(particles_),
      physicalProperty(physicalProperty_), 
	  backgroundID(backgroundID_),
	  rho(nullptr), U(nullptr), p(nullptr), muL(nullptr),
	  gradRhoFlag(false), gradUFlag(false), gradPFlag(false), muLFlag(false),
	  gradRho(nullptr), gradU(nullptr), gradP(nullptr), gradMu(nullptr),
	  rhoU(nullptr), gradRhoU(nullptr), gradGradP(nullptr)
{
	if (flowPackage_ != nullptr)
	{
		rho = flowPackage_->GetField().density;
		U = flowPackage_->GetField().velocity;
		p = flowPackage_->GetField().pressure;
		muL = flowPackage_->GetField().muLaminar;
		
		Mesh *mesh = rho->GetMesh();
		dim2 = mesh->GetMeshDimension() == Mesh::MeshDim::md2D;

		if (muL == nullptr)
		{
			Material::Flow::Materials material(Material::Flow::DensityType::IDEAL_GAS, Material::Flow::ViscosityType::SUTHERLAND);
			muLValue = material.Mu();

			//muL = new ElementField<Scalar>(mesh, mu, "MU");
			//muL->SetGhostlValueParallel();
			//muL->SetGhostlValueBoundary();
			//muLFlag = true;
		}

		dragForce = configure.externalForce.dragForce;
		liftForce = configure.externalForce.liftForce;
		pressureGradientForce = configure.externalForce.pressureGradientForce;
		addedMassForce = configure.externalForce.addedMassForce;
		rotationlDrag = configure.externalForce.rotationlDrag;
		interpolationMethod = configure.externalForce.interpolation.method;
		conservationFlag = configure.externalForce.interpolation.conservationFlag;

		if (conservationFlag) rhoU = new ElementField<Vector>(mesh, Vector0, "RHOU");

		if (interpolationMethod == Configure::Particle::InterpolationMethod::TAYLOR_1ST)
		{
			gradRhoFlag = flowPackage_->GetGradientField().gradientRho == nullptr;
			if (gradRhoFlag) gradRho = new ElementField<Vector>(mesh, Vector0, "GRADIENT_RHO");
			else             gradRho = flowPackage_->GetGradientField().gradientRho;

			if (conservationFlag)
			{
				gradRhoU = new ElementField<Tensor>(mesh, Tensor0, "GRADIENT_RHOU");
			}
			else
			{
				gradUFlag = flowPackage_->GetGradientField().gradientU == nullptr;
				if (gradUFlag) gradU = new ElementField<Tensor>(mesh, Tensor0, "GRADIENT_U");
				else           gradU = flowPackage_->GetGradientField().gradientU;
			}

			gradGradP = new ElementField<Tensor>(mesh, Tensor0, "GRADIENT_GRADIENT_P");

			gradMu = new ElementField<Vector>(mesh, Vector0, "GRADIENT_MU");
		}

		// 升力
		if (liftForce && gradU == nullptr)
		{
			gradUFlag = flowPackage_->GetGradientField().gradientU == nullptr;
			if (gradUFlag) gradU = new ElementField<Tensor>(mesh, Tensor0, "GRADIENT_U");
			else           gradU = flowPackage_->GetGradientField().gradientU;
		}

		// 压力梯度力
		if (pressureGradientForce && gradP == nullptr)
		{
			gradPFlag = flowPackage_->GetGradientField().gradientP == nullptr;
			if (gradPFlag) gradP = new ElementField<Vector>(mesh, Vector0, "GRADIENT_P");
			else           gradP = flowPackage_->GetGradientField().gradientP;
		}
	}
}

ExternalForce::~ExternalForce()
{
	if (gradRhoFlag) { delete gradRho; gradRho = nullptr; }
	if (gradUFlag) { delete gradU; gradU = nullptr; }
	if (gradPFlag) { delete gradP; gradP = nullptr; }
	if (gradMu != nullptr) { delete gradMu; gradMu = nullptr; }

	if (rhoU != nullptr) { delete rhoU; rhoU = nullptr; }
	if (gradRhoU != nullptr) { delete gradRhoU; gradRhoU = nullptr; }

	if (gradGradP != nullptr) { delete gradGradP; gradGradP = nullptr; }
	if (muLFlag) { delete muL; muL = nullptr; }
}

void ExternalForce::UpdateFlowVariables()
{
	if (rho != nullptr && gradRho != nullptr) FieldManipulation::Gradient(*rho, *gradRho);

	if (U != nullptr && gradU != nullptr) FieldManipulation::Gradient(*U, *gradU);

	if (p != nullptr && gradP != nullptr) FieldManipulation::Gradient(*p, *gradP);

	if (gradP != nullptr && gradGradP != nullptr) FieldManipulation::Gradient(*gradP, *gradGradP);

	if (muL != nullptr && gradMu != nullptr) FieldManipulation::Gradient(*muL, *gradMu);

	if (rhoU != nullptr)
	{
		const int &elemNum = rhoU->GetMesh()->GetElementNumberAll();
		for (int elemID = 0; elemID < elemNum; elemID++)
		{
			rhoU->SetValue(elemID, rho->GetValue(elemID) * U->GetValue(elemID));
		}
		if (gradRhoU != nullptr) FieldManipulation::Gradient(*rhoU, *gradRhoU);
	}
}

void ExternalForce::CalculateExternalForce()
{
    for(int i = 0; i < this->numParticleCurrent; ++i)
    {
		if (this->particles[i] != nullptr)
		{
            // 重力
            this->CalculateGravity(i);

			if (rho != nullptr)
			{
				const Scalar &diameter = this->particles[i]->diameter;
				const Vector &position = this->particles[i]->position;
				const Vector &velocity = this->particles[i]->linearVelocity;

				// 环境流体密度
				Scalar rhoF = GetValue(position, backgroundID[i], rho, gradRho);
				if (rhoF < SMALL) rhoF = GetValue(position, backgroundID[i], rho);

				// 颗粒相对环境流体的滑移速度
				Vector UF;
				if (conservationFlag)
				{
					const Vector rhoUF = GetValue(position, backgroundID[i], rhoU, gradRhoU);
					UF = rhoUF / rhoF;
				}
				else
				{
					UF = GetValue(position, backgroundID[i], U, gradU);
				}

				// 环境流体粘性系数
				Scalar muF;
				if (muL != nullptr)
				{
					muF = GetValue(position, backgroundID[i], muL, gradMu);
					if (muF < SMALL) muF = GetValue(position, backgroundID[i], muL);
				}
				else
				{
					muF = muLValue;
				}

				// 阻力
				if (dragForce) this->CalculateDragForce(i, diameter, velocity, rhoF, UF, muF);

				// 升力
				if (liftForce)
				{
					// 环境流体旋度
					const Tensor gradU0 = GetValue(position, backgroundID[i], gradU);
					const Vector omega(gradU0.YZ() - gradU0.ZY(), gradU0.ZX() - gradU0.XZ(), gradU0.XY() - gradU0.YX());

					this->CalculateLiftForce(i, diameter, velocity, rhoF, UF, muF, omega);
				}

				// 压力梯度力
				if (pressureGradientForce)
				{
					// 环境流体压力梯度
					const Vector gradPI = GetValue(position, backgroundID[i], gradP, gradGradP);

					this->CalculatePressureGradientForce(i, diameter, gradPI);
				}

				// 附加质量力
				if (addedMassForce) this->CalculateAddedMassForce(i, backgroundID[i]);

				// 旋转阻力
				if (rotationlDrag) this->CalculateRotationlDrag(i, backgroundID[i]);
			}
        }
    }
}

void ExternalForce::CalculateGravity(const int &particleID)
{
	const Scalar &mass = this->physicalProperty->GetMass(this->particles[particleID]->type);
	this->particles[particleID]->force += this->configure.reference.gravity * mass;
}

void ExternalForce::CalculateDragForce(const int &particleID, const Scalar &dP, const Vector &uP, const Scalar &rhoF, const Vector &uF, const Scalar &muF)
{
	// 相对速度及速度大小
	const Vector vs = uF - uP;
	const Scalar vsMag = vs.Mag();

    // 颗粒雷诺数
	const Scalar Rep = rhoF * vsMag * dP / muF;

    // 颗粒阻力系数
    const Scalar Cd = ClaculateDragCoefficient(particleID, Rep);

    // 颗粒投影面积
	const Scalar Ap = dim2 ? dP : (0.25 * PI * dP * dP);

    // 计算颗粒阻力
	const Vector dragForce = 0.5 * Cd * rhoF * Ap * vsMag * vs;
	// const Vector dragForce = 3.0 * PI * dP * muF * (1.0 + 0.15 * pow(Rep, 0.687)) * vs;

    // 累加
	this->particles[particleID]->force += dragForce;
}

void ExternalForce::CalculateLiftForce(const int &particleID, const Scalar &dP, const Vector &uP, const Scalar &rhoF, const Vector &uF, const Scalar &muF, const Vector &omegaF)
{
	// Saffman升力系数
	const Scalar coef = 1.0;

	// 相对速度及速度大小
	const Vector vs = uF - uP;
	// const Scalar vsMag = vs.Mag();

	// // 颗粒雷诺数
	// const Scalar ReP = rhoF * vsMag * dP / muF;
	// 
	// // 剪切流的雷诺数
	// const Scalar ReS = rhoF *dP *dP * omegaF.Mag() / muF;
	// 
	// // 剪切升力系数
	// const Scalar beta = 0.5 * ReS / ReP;
	// Scalar f_Re = Scalar0;
	// if (ReP > 40) f_Re = 0.0524 * sqrt(beta * ReP);
	// else          f_Re = (1.0 - 0.3314 * sqrt(beta)) * exp(-0.1 * ReP) + 0.3314 * sqrt(beta);
	// const Scalar C_LS = 4.1126 / sqrt(ReS) * f_Re;

	// // 计算颗粒阻力
	// const Vector liftForce = C_LS * rhoF * PI / 8.0 * dP *dP *dP * (vs ^ omegaF);

	const Vector liftForce = 1.615 * rhoF * dP *dP * sqrt(muF / rhoF / omegaF.Mag()) * (vs ^ omegaF);

	// 累加
	this->particles[particleID]->force += liftForce;
}

void ExternalForce::CalculatePressureGradientForce(const int &particleID, const Scalar &dP, const Vector &gradPI)
{
    // 颗粒投影面积
	const Scalar Vp = dim2 ? (PI / 4.0 * dP * dP) : (PI / 6.0 * dP * dP * dP);

    // 计算颗粒阻力
    const Vector dragForce = - Vp * gradPI;

    // 累加
	this->particles[particleID]->force += dragForce;
}

void ExternalForce::CalculateAddedMassForce(const int &particleID, const int &elemIDList)
{

}

void ExternalForce::CalculateRotationlDrag(const int &particleID, const int &elemIDList)
{

}

Scalar ExternalForce::ClaculateDragCoefficient(const int &particleID, const Scalar &Rep)
{
    // Schiller-Naumann 相关性
    if (Rep <= 1000)
    {
        return 24.0 / Rep * (1.0 + 0.15 * pow(Rep, 0.687));
    }
    else
    {
        return 0.44;
    }
}

template Scalar ExternalForce::GetValue(const Vector &pos, const int &elemID, ElementField<Scalar> *field);
template Vector ExternalForce::GetValue(const Vector &pos, const int &elemID, ElementField<Vector> *field);
template<class Type>
Type ExternalForce::GetValue(const Vector &pos, const int &elemID, ElementField<Type> *field)
{
	if (interpolationMethod == Configure::Particle::InterpolationMethod::INTERPOLATION_CONSTANT)
	{
		return field->GetValue(elemID);
	}
	else if (interpolationMethod == Configure::Particle::InterpolationMethod::IDW)
	{
		Mesh *mesh = field->GetMesh();
		const Element &elem = mesh->GetElement(elemID);
		const int faceSize = elem.GetFaceSize();

		Scalar distance = (elem.GetCenter() - pos).Mag();
		if (distance < SMALL) return field->GetValue(elemID);
		else
		{
			Scalar weight = 1.0 / distance;
			Scalar denominator = weight;
			Type numerator = weight * field->GetValue(elemID);
			for (int i = 0; i < faceSize; i++)
			{
				const int &faceID = elem.GetFaceID(i);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();
				const int adjElemID = (ownerID == elemID) ? neighID : ownerID;

				weight = 1.0 / (mesh->GetElement(adjElemID).GetCenter() - pos).Mag();
				numerator = numerator + weight * field->GetValue(adjElemID);
				denominator += weight;
			}

			return numerator / denominator; // 返回加权平均值
		}
	}
	else
	{
		FatalError("插值格式暂不支持！");
	}

	return field->GetValue(elemID);

}

template Scalar ExternalForce::GetValue(const Vector &pos, const int &elemID, ElementField<Scalar> *field, ElementField<Vector> *gradientField);
template Vector ExternalForce::GetValue(const Vector &pos, const int &elemID, ElementField<Vector> *field, ElementField<Tensor> *gradientField);
template<class Type, class TypeGradient>
Type ExternalForce::GetValue(const Vector &pos, const int &elemID, ElementField<Type> *field, ElementField<TypeGradient> *gradientField)
{
	if (interpolationMethod == Configure::Particle::InterpolationMethod::TAYLOR_1ST)
	{
		if (gradientField != nullptr)
		{
			const Type &fieldValue = field->GetValue(elemID);
			const TypeGradient &gradValue = gradientField->GetValue(elemID);
			const Vector distance = pos - field->GetMesh()->GetElement(elemID).GetCenter();
			return fieldValue + Dot(distance, gradValue);
		}
	}

	return GetValue(pos, elemID, field);
}

} // namespace Particle
