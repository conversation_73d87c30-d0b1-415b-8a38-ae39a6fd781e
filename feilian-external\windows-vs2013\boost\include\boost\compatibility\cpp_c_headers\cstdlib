// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CSTDLIB_HEADER
#define __CSTDLIB_HEADER

#include <stdlib.h>

namespace std {
  using ::abort;
  using ::atexit;
  using ::exit;
  using ::getenv;
  using ::system;
  using ::calloc;
  using ::malloc;
  using ::free;
  using ::realloc;
  using ::atol;
  using ::mblen;
  using ::strtod;
  using ::wctomb;
  using ::atof;
  using ::mbstowcs;
  using ::strtol;
  using ::wcstombs;
  using ::atoi;
  using ::mbtowc;
  using ::strtoul;
  using ::bsearch;
  using ::qsort;
  using ::div_t;
  using ::ldiv_t;
  using ::abs;
  using ::labs;
  using ::srand;
  using ::div;
  using ::ldiv;
  using ::rand;
}

#endif // CSTDLIB_HEADER
