namespace Eigen {

/** \eigenManualPage TutorialMatrixClass The Matrix class

\eigenAutoToc

In Eigen, all matrices and vectors are objects of the Matrix template class.
Vectors are just a special case of matrices, with either 1 row or 1 column.

\section TutorialMatrixFirst3Params The first three template parameters of Matrix

The Matrix class takes six template parameters, but for now it's enough to
learn about the first three first parameters. The three remaining parameters have default
values, which for now we will leave untouched, and which we
\ref TutorialMatrixOptTemplParams "discuss below".

The three mandatory template parameters of Matrix are:
\code
Matrix<typename Scalar, int RowsAtCompileTime, int ColsAtCompileTime>
\endcode
\li \c Scalar is the scalar type, i.e. the type of the coefficients.
    That is, if you want a matrix of floats, choose \c float here.
    See \ref TopicScalarTypes "Scalar types" for a list of all supported
    scalar types and for how to extend support to new types.
\li \c RowsAtCompileTime and \c ColsAtCompileTime are the number of rows
    and columns of the matrix as known at compile time (see 
    \ref TutorialMatrixDynamic "below" for what to do if the number is not
    known at compile time).

We offer a lot of convenience typedefs to cover the usual cases. For example, \c Matrix4f is
a 4x4 matrix of floats. Here is how it is defined by Eigen:
\code
typedef Matrix<float, 4, 4> Matrix4f;
\endcode
We discuss \ref TutorialMatrixTypedefs "below" these convenience typedefs.

\section TutorialMatrixVectors Vectors

As mentioned above, in Eigen, vectors are just a special case of
matrices, with either 1 row or 1 column. The case where they have 1 column is the most common;
such vectors are called column-vectors, often abbreviated as just vectors. In the other case
where they have 1 row, they are called row-vectors.

For example, the convenience typedef \c Vector3f is a (column) vector of 3 floats. It is defined as follows by Eigen:
\code
typedef Matrix<float, 3, 1> Vector3f;
\endcode
We also offer convenience typedefs for row-vectors, for example:
\code
typedef Matrix<int, 1, 2> RowVector2i;
\endcode

\section TutorialMatrixDynamic The special value Dynamic

Of course, Eigen is not limited to matrices whose dimensions are known at compile time.
The \c RowsAtCompileTime and \c ColsAtCompileTime template parameters can take the special
value \c Dynamic which indicates that the size is unknown at compile time, so must
be handled as a run-time variable. In Eigen terminology, such a size is referred to as a
\em dynamic \em size; while a size that is known at compile time is called a
\em fixed \em size. For example, the convenience typedef \c MatrixXd, meaning
a matrix of doubles with dynamic size, is defined as follows:
\code
typedef Matrix<double, Dynamic, Dynamic> MatrixXd;
\endcode
And similarly, we define a self-explanatory typedef \c VectorXi as follows:
\code
typedef Matrix<int, Dynamic, 1> VectorXi;
\endcode
You can perfectly have e.g. a fixed number of rows with a dynamic number of columns, as in:
\code
Matrix<float, 3, Dynamic>
\endcode

\section TutorialMatrixConstructors Constructors

A default constructor is always available, never performs any dynamic memory allocation, and never initializes the matrix coefficients. You can do:
\code
Matrix3f a;
MatrixXf b;
\endcode
Here,
\li \c a is a 3-by-3 matrix, with a plain float[9] array of uninitialized coefficients,
\li \c b is a dynamic-size matrix whose size is currently 0-by-0, and whose array of
coefficients hasn't yet been allocated at all.

Constructors taking sizes are also available. For matrices, the number of rows is always passed first.
For vectors, just pass the vector size. They allocate the array of coefficients
with the given size, but don't initialize the coefficients themselves:
\code
MatrixXf a(10,15);
VectorXf b(30);
\endcode
Here,
\li \c a is a 10x15 dynamic-size matrix, with allocated but currently uninitialized coefficients.
\li \c b is a dynamic-size vector of size 30, with allocated but currently uninitialized coefficients.

In order to offer a uniform API across fixed-size and dynamic-size matrices, it is legal to use these
constructors on fixed-size matrices, even if passing the sizes is useless in this case. So this is legal:
\code
Matrix3f a(3,3);
\endcode
and is a no-operation.

Finally, we also offer some constructors to initialize the coefficients of small fixed-size vectors up to size 4:
\code
Vector2d a(5.0, 6.0);
Vector3d b(5.0, 6.0, 7.0);
Vector4d c(5.0, 6.0, 7.0, 8.0);
\endcode

\section TutorialMatrixCoeffAccessors Coefficient accessors

The primary coefficient accessors and mutators in Eigen are the overloaded parenthesis operators.
For matrices, the row index is always passed first. For vectors, just pass one index.
The numbering starts at 0. This example is self-explanatory:

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include tut_matrix_coefficient_accessors.cpp
</td>
<td>
\verbinclude tut_matrix_coefficient_accessors.out
</td></tr></table>

Note that the syntax <tt> m(index) </tt>
is not restricted to vectors, it is also available for general matrices, meaning index-based access
in the array of coefficients. This however depends on the matrix's storage order. All Eigen matrices default to
column-major storage order, but this can be changed to row-major, see \ref TopicStorageOrders "Storage orders".

The operator[] is also overloaded for index-based access in vectors, but keep in mind that C++ doesn't allow operator[] to
take more than one argument. We restrict operator[] to vectors, because an awkwardness in the C++ language
would make matrix[i,j] compile to the same thing as matrix[j] !

\section TutorialMatrixCommaInitializer Comma-initialization

%Matrix and vector coefficients can be conveniently set using the so-called \em comma-initializer syntax.
For now, it is enough to know this example:

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr>
<td>\include Tutorial_commainit_01.cpp </td>
<td>\verbinclude Tutorial_commainit_01.out </td>
</tr></table>


The right-hand side can also contain matrix expressions as discussed in \ref TutorialAdvancedInitialization "this page".

\section TutorialMatrixSizesResizing Resizing

The current size of a matrix can be retrieved by \link EigenBase::rows() rows()\endlink, \link EigenBase::cols() cols() \endlink and \link EigenBase::size() size()\endlink. These methods return the number of rows, the number of columns and the number of coefficients, respectively. Resizing a dynamic-size matrix is done by the \link PlainObjectBase::resize(Index,Index) resize() \endlink method.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr>
<td>\include tut_matrix_resize.cpp </td>
<td>\verbinclude tut_matrix_resize.out </td>
</tr></table>

The resize() method is a no-operation if the actual matrix size doesn't change; otherwise it is destructive: the values of the coefficients may change.
If you want a conservative variant of resize() which does not change the coefficients, use \link PlainObjectBase::conservativeResize() conservativeResize()\endlink, see \ref TopicResizing "this page" for more details.

All these methods are still available on fixed-size matrices, for the sake of API uniformity. Of course, you can't actually
resize a fixed-size matrix. Trying to change a fixed size to an actually different value will trigger an assertion failure;
but the following code is legal:

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr>
<td>\include tut_matrix_resize_fixed_size.cpp </td>
<td>\verbinclude tut_matrix_resize_fixed_size.out </td>
</tr></table>


\section TutorialMatrixAssignment Assignment and resizing

Assignment is the action of copying a matrix into another, using \c operator=. Eigen resizes the matrix on the left-hand side automatically so that it matches the size of the matrix on the right-hand size. For example:

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr>
<td>\include tut_matrix_assignment_resizing.cpp </td>
<td>\verbinclude tut_matrix_assignment_resizing.out </td>
</tr></table>

Of course, if the left-hand side is of fixed size, resizing it is not allowed.

If you do not want this automatic resizing to happen (for example for debugging purposes), you can disable it, see
\ref TopicResizing "this page".


\section TutorialMatrixFixedVsDynamic Fixed vs. Dynamic size

When should one use fixed sizes (e.g. \c Matrix4f), and when should one prefer dynamic sizes (e.g. \c MatrixXf)?
The simple answer is: use fixed
sizes for very small sizes where you can, and use dynamic sizes for larger sizes or where you have to. For small sizes,
especially for sizes smaller than (roughly) 16, using fixed sizes is hugely beneficial
to performance, as it allows Eigen to avoid dynamic memory allocation and to unroll
loops. Internally, a fixed-size Eigen matrix is just a plain array, i.e. doing
\code Matrix4f mymatrix; \endcode
really amounts to just doing
\code float mymatrix[16]; \endcode
so this really has zero runtime cost. By contrast, the array of a dynamic-size matrix
is always allocated on the heap, so doing
\code MatrixXf mymatrix(rows,columns); \endcode
amounts to doing
\code float *mymatrix = new float[rows*columns]; \endcode
and in addition to that, the MatrixXf object stores its number of rows and columns as
member variables.

The limitation of using fixed sizes, of course, is that this is only possible
when you know the sizes at compile time. Also, for large enough sizes, say for sizes
greater than (roughly) 32, the performance benefit of using fixed sizes becomes negligible.
Worse, trying to create a very large matrix using fixed sizes inside a function could result in a
stack overflow, since Eigen will try to allocate the array automatically as a local variable, and
this is normally done on the stack.
Finally, depending on circumstances, Eigen can also be more aggressive trying to vectorize
(use SIMD instructions) when dynamic sizes are used, see \ref TopicVectorization "Vectorization".

\section TutorialMatrixOptTemplParams Optional template parameters

We mentioned at the beginning of this page that the Matrix class takes six template parameters,
but so far we only discussed the first three. The remaining three parameters are optional. Here is
the complete list of template parameters:
\code
Matrix<typename Scalar,
       int RowsAtCompileTime,
       int ColsAtCompileTime,
       int Options = 0,
       int MaxRowsAtCompileTime = RowsAtCompileTime,
       int MaxColsAtCompileTime = ColsAtCompileTime>
\endcode
\li \c Options is a bit field. Here, we discuss only one bit: \c RowMajor. It specifies that the matrices
      of this type use row-major storage order; by default, the storage order is column-major. See the page on
      \ref TopicStorageOrders "storage orders". For example, this type means row-major 3x3 matrices:
      \code
      Matrix<float, 3, 3, RowMajor>
      \endcode
\li \c MaxRowsAtCompileTime and \c MaxColsAtCompileTime are useful when you want to specify that, even though
      the exact sizes of your matrices are not known at compile time, a fixed upper bound is known at
      compile time. The biggest reason why you might want to do that is to avoid dynamic memory allocation.
      For example the following matrix type uses a plain array of 12 floats, without dynamic memory allocation:
      \code
      Matrix<float, Dynamic, Dynamic, 0, 3, 4>
      \endcode

\section TutorialMatrixTypedefs Convenience typedefs

Eigen defines the following Matrix typedefs:
\li MatrixNt for Matrix<type, N, N>. For example, MatrixXi for Matrix<int, Dynamic, Dynamic>.
\li VectorNt for Matrix<type, N, 1>. For example, Vector2f for Matrix<float, 2, 1>.
\li RowVectorNt for Matrix<type, 1, N>. For example, RowVector3d for Matrix<double, 1, 3>.

Where:
\li N can be any one of \c 2, \c 3, \c 4, or \c X (meaning \c Dynamic).
\li t can be any one of \c i (meaning int), \c f (meaning float), \c d (meaning double),
      \c cf (meaning complex<float>), or \c cd (meaning complex<double>). The fact that typedefs are only
    defined for these five types doesn't mean that they are the only supported scalar types. For example,
    all standard integer types are supported, see \ref TopicScalarTypes "Scalar types".


*/

}
