﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file AgglomerateManager.h
//! <AUTHOR>
//! @brief 多重网格聚合类的管理器
//! @date 2022-01-10
//
//------------------------------修改日志----------------------------------------
// 2022-01-10 李艳亮、乔龙
//    说明：建立
//
//
//------------------------------修改日志----------------------------------------

#ifndef _meshProcess_agglomeration_AgglomerateManager_
#define _meshProcess_agglomeration_AgglomerateManager_

#include "basic/configure/ConfigureMacro.h"
#include "meshProcess/agglomeration/AgglomerateMGridGen.h"
#include "meshProcess/agglomeration/AgglomerateSeed.h"
#include "meshProcess/agglomeration/AgglomerateStructuredMesh.h"

/**
 * @brief 网格聚合管理器类
 * 
 */
class AgglomerateManager
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] type 聚合方法
     * @param[in] nLayer 边界层法向聚合层数
     * @param[in] coarseRationNormal 边界层法向聚合率
     * @param[in] coarseRationTangential 边界层切向聚合率
     * @param[in] minsize 最小聚合网格数量
     * @param[in] maxsize 最大聚合网格数量
     * @param[in] singletonsRemovementFlag_ 孤立单元去除标志，true为去除
     * @param[in] nodeCenter_ 格点标识，true为格点
     * @param[in] structured_ 结构形式网格标识
     */
    AgglomerateManager(const Preprocessor::AgglomerateType &type, const int &nLayer,
					   const int &coarseRationNormal, const int &coarseRationTangential,
					   const int &minsize, const int &maxsize,
					   const bool &singletonsRemovementFlag_ = true,
					   const bool &nodeCenter_ = false,
                       const bool &structured_ = true);
	
    /**
     * @brief 析构函数
     * 
     */
    ~AgglomerateManager();

    /**
     * @brief 创建粗网格
     * 
     * @param[in] pmesh 细网格
     * @param[out] multiGrid 粗网格
     * @param[in] wallList 壁面边界列表
     * @param[in] level 粗网格层级
     */
    void CreateMultiGrid(Mesh* pmesh, MultiGrid* multiGrid, const std::vector<int> &wallList, const int &level);

private:
    /**
     * @brief 创建粗网格聚合指针
     * 
     * @param[in] type 聚合方法
     * @param[in] nLayer 边界层法向聚合层数
     * @param[in] coarseRationNormal 边界层法向聚合率
     * @param[in] coarseRationTangential 边界层切向聚合率
     * @param[in] minsize 最小聚合网格数量
     * @param[in] maxsize 最大聚合网格数量
     */
    void SetPointer(const Preprocessor::AgglomerateType &type, const int &nLayer, const int &coarseRationNormal, const int &coarseRationTangential);

    /**
     * @brief 生成粗网格
     * 
     * @param[in] fineMesh 细网格
     * @param[out] multiGrid 粗网格
     * @param[in] fineToCoarseMap 细网格与粗网格映射关系
     * @param[in,out] coarseToFineMap 粗网格与细网格映射关系
     */
	void CreateMultiGridTopology(Mesh *fineMesh, MultiGrid *multiGrid, const std::vector<int> &fineToCoarseMap, std::vector<std::vector<int>> &coarseToFineMap);

    /**
     * @brief 建立粗网格面信息（含合并粗网格碎面）
     * 根据面的点构成判断碎面是否相邻，仅合并相邻碎面
     * 若面的点列表为空，则默认碎面相邻
     * 
	 * @param[in] fineMesh 细网格
     * @param[in, out] coarseMesh 粗网格
	 * @param[in] fineToCoarseMap 细网格与粗网格映射关系
	 * @param[in,out] coarseToFineMap 粗网格与细网格映射关系
     */
	void CreateMultiGridFace(Mesh *fineMesh, MultiGrid *coarseMesh, const std::vector<int> &fineToCoarseMap, std::vector<std::vector<int>> &coarseToFineMap);

    /**
     * @brief 合并指定编号的碎面,并计算面积比值
     * 
     * @param[in] fineMesh 细网格
     * @param[in] smallFaceIDList 粗网格碎面编号列表
     * @param[out] bigFace 合并后的大面
	 * @return Scalar 大面的面积除以碎面的面积和
     */
    Scalar MergeSmallFace(Mesh *fineMesh, std::vector<int> &smallFaceIDList, Face &bigFace, const std::vector<int> &fineToCoarseMap);
    
    /**
     * @brief 根据面的点构成判断两个面是否相邻
     * 
     * @param[in] face1 待判断面
     * @param[in] face2 待判断面
     * @return true 两个面相邻
     * @return false 两个面不相邻
     */
    bool JudgeAdjacent(const Face &face1, const Face &face2);

    /**
     * @brief 去除孤立单元
     * 
     * @param[in] fineMesh 细网格
     * @param[in,out] fineToCoarseMap 细网格与粗网格映射关系
     * @param[in,out] coarseToFineMap 粗网格与细网格映射关系
     * @param[in,out] nCoarseCells 粗网格单元数量
     */
	void RemoveSingletons(Mesh* fineMesh,
                          std::vector<int> &fineToCoarseMap,
                          std::vector<std::vector<int>> &coarseToFineMap,
                          int &nCoarseCells);

	/**
	* @brief 判断结构网格点数是否满足聚合要求
	* @param[in] mesh 网格指针
	*
	*/
	bool JudgeDimension(Mesh *mesh);
	
private:
    const Preprocessor::AgglomerateType &type; ///< 聚合方法
    const bool &nodeCenter; ///< 边界数据存储在原始网格节点标识
	const bool &singletonsRemovementFlag; ///< 孤立单元去除标志，true为去除
    const int &nLayer; ///< 边界层法向聚合层数
    const int &coarseRationNormal; ///< 边界层法向聚合率
    const int &coarseRationTangential; ///< 边界层内切向聚合率
    const int &minSize; ///< 最小聚合网格数量
    const int &maxSize; ///< 最大聚合网格数量
    const bool &structured; ///< 结构形式网格标识
};
#endif // _meshProcess_agglomeration_AgglomerateManager_
