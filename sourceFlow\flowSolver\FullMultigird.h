﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FullMultigird.h
//! <AUTHOR>
//! @brief 基于网格序列的完全多重网格求解类.
//! @date 2022-03-16
//
//------------------------------修改日志----------------------------------------
//
// 2022-03-16 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_flowSolver_FullMultigird_
#define _sourceFlow_flowSolver_FullMultigird_

#include "sourceFlow/flowSolver/MultigridSolver.h"
#include "basic/CFD/smoother/Smoother.h"

/**
 * @brief 流场计算完全多重网格求解类
 * 
 */
class FullMultigird
{
public:
    /**
     * @brief 流场解算器构造函数
     * 根据设置参数文件，建立基于当地网格的特定流体流动解算器
     * 
     * @param[in] subMesh_ 当地网格，含细网格和所有粗网格
     * @param[in, out] flowPackageVector_ 流场包对象容器
     * @param[in, out] timeSchemeVector_ 时间推进对象容器
     * @param[in, out] resultProcess_ 计算结果监控及IO对象
     */
    FullMultigird(SubMesh *subMesh_,
                  std::vector<Package::FlowPackage *> &flowPackageVector_,
                  std::vector<Time::Flow::FlowTimeManager *> &timeSchemeVector_,
                  FlowResultsProcess *resultProcess_);

    /**
     * @brief 析构函数
     * 释放流场解算器构造函数中通过new建立的数据
     * 
     */
    ~FullMultigird();

    /**
     * @brief 流场解算器求解函数
     * 
     */
    void Solve();
    
private:
    /// 当地网格，含细网格和所有粗网格
    SubMesh *subMesh;
    
    /// 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
    const Configure::Flow::FlowConfigure &flowConfigure;

    /// 不同网格层级流场监控及输出对象
    std::vector<FlowResultsProcess *> resultProcessList;

    // 多重网格求解器
    MultigridSolver multigridSolver;

    /// 多重网格总层数
    int multigridLevel;

    /// 初始化类型
    Initialization::Type initialType;

    /// 由细网格和所有粗网格上的物理场包所构成的容器, 容器大小为总网格层数
    std::vector<Package::FlowPackage *> &flowPackageVector;

    /// 对于不同层级的网格，流场求解时采用的具体时间推进对象所构成的容器, 容器大小为总网格层数
    std::vector<Time::Flow::FlowTimeManager *> timeSchemeVector;

    /// 内循环起始网格层级容器
    /// 不采用全多重时容器大小为1，值为0
    /// 采用全多重时容器大小为多重网格求解器总网格层数，值为粗网格层级到细网格层级
    std::vector<int> startLevelVector;
};
#endif
