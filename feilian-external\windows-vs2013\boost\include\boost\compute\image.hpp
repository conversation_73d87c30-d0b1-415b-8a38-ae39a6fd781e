//---------------------------------------------------------------------------//
// Copyright (c) 2013-2015 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_IMAGE_HPP
#define BOOST_COMPUTE_IMAGE_HPP

/// \file
///
/// Meta-header to include all Boost.Compute image headers.

#include <boost/compute/image/image1d.hpp>
#include <boost/compute/image/image2d.hpp>
#include <boost/compute/image/image3d.hpp>
#include <boost/compute/image/image_format.hpp>
#include <boost/compute/image/image_object.hpp>
#include <boost/compute/image/image_sampler.hpp>

#endif // BOOST_COMPUTE_IMAGE_HPP
