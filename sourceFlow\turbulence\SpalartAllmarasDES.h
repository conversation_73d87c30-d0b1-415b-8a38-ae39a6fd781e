﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//

//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_SpalartAllmarasDES_
#define _sourceFlow_turbulence_SpalartAllmarasDES_

#include "sourceFlow/turbulence/SpalartAllmaras.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流SA-DES类(DES97)
 * 
 */
class  SpalartAllmarasDES : public SpalartAllmaras
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
	SpalartAllmarasDES(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
	~SpalartAllmarasDES();

	/**
	* @brief 累加源项通量残差
	*
	*/
	void AddSourceResidual();


private:
	ElementField<Scalar> *ldOverlr;     ///< 网格单元中最大边长的体场

    // DES常数
    Scalar Cdes; ///< SA-DES模型中网格尺度的缩放系数

};

} // namespace Turbulence
#endif 