 #pragma once
 #if defined _WIN32
 #if !defined MSWIN
 #define MSWIN 
 #endif
 #endif 
int32_t ___3982(int32_t  ___1397); int32_t ___3981(int32_t  ___1397); int32_t ___3977( int32_t         ___1397, char const*     ___4178, char const*     ___4351, char const*     ___1439, char const*     ___3448, int32_t  const* ___1408, int32_t  const* ___942, int32_t  const* ___4439); int32_t ___3992( int32_t         ___1397, char const*     ___4598, int32_t  const* ___4599, int32_t  const* ___1910, int32_t  const* ___2117, int32_t  const* ___2162, int32_t  const* ___1836, int32_t  const* ___2109, int32_t  const* ___2138, double const*   ___3641, int32_t  const* ___3786, int32_t  const* ___2975, int32_t  const* ___2005, int32_t  const* ___2801, int32_t  const* ___1440, int32_t  const* ___2805, int32_t  const* ___2799, int32_t  const* ___2798, int32_t  const* ___2983, int32_t  const* ___4327, int32_t  const* ___3552, int32_t  const* ___3550); int32_t ___3972( int32_t         ___1397, int32_t  const* N, void const*     ___816, int32_t  const* ___2014); int32_t ___3979( int32_t         ___1397, int32_t  const* ___2689); int32_t ___3980( int32_t         ___1397, int32_t  const* N, int32_t  const* ___2689); int32_t ___3973(int32_t  ___1397); int32_t ___3978( int32_t     ___1397, char const* S); int32_t ___3989( int32_t     ___1397, char const* S); int32_t ___3976( int32_t         ___1397, double const*   ___4575, double const*   ___4592, double const*   ___4716, int32_t  const* ___3160, int32_t  const* ___227, int32_t  const* ___4600, int32_t  const* Color, int32_t  const* ___1412, int32_t  const* ___2023, int32_t  const* ___1652, int32_t  const* ___2264, double const*   ___2987, double const*   ___2290, int32_t  const* ___2794, int32_t  const* ___188, int32_t  const* ___176, double const*   ___187, double const*   ___171, int32_t  const* ___3443, int32_t  const* ___496, int32_t  const* ___2836, int32_t  const* ___2838, float const*    ___4573, float const*    ___4590, float const*    ___4597, char const*     mfc); int32_t ___3988( int32_t         ___1397, double const*   ___4575, double const*   ___4592, double const*   ___4714, int32_t  const* ___3160, int32_t  const* ___227, int32_t  const* ___4600, int32_t  const* ___353, int32_t  const* ___1453, double const*   ___1451, int32_t  const* ___411, double const*   ___409, double const*   ___407, int32_t  const* ___403, int32_t  const* ___405, double const*   ___57, int32_t  const* ___39, double const*   ___2288, int32_t  const* ___4081, int32_t  const* ___3443, int32_t  const* ___496, char const*     ___3813, char const*     mfc); void ___3975( int32_t         ___1397, int32_t  const* ___2891); int32_t ___3971( int32_t     ___1397, char const* ___2686, char const* ___4315); int32_t ___3991( int32_t     ___1397, char const* ___2686, char const* ___4315); int32_t ___3990( int32_t         ___1397, int32_t  const* ___4337, char const*     ___2686, char const*     ___4315); int32_t ___3974( int32_t         ___1397, int32_t  const* ___1258); int32_t ___3986(int32_t  ___1397); int32_t ___3987(int32_t  ___1397); int32_t ___3983( int32_t         ___1397, int32_t  const* ___1294, int32_t  const* ___1297, int32_t  const* ___1259, int32_t  const* ___1303, int32_t  const* ___1253, int32_t  const* ___1254, int32_t  const* ___1256); int32_t ___3985( int32_t         ___1397, int32_t  const* ___2806, int32_t  const* ___1294, int32_t  const* ___1297, int32_t  const* ___1259, int32_t  const* ___1303); int32_t ___3984( int32_t         ___1397, int32_t  const* ___2778, int32_t  const* ___1253, int32_t  const* ___1254, int32_t  const* ___1256);
