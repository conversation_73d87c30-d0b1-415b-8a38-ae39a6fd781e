﻿#include "meshProcess/meshConverter/DlgMesh.h"

DlgMesh::DlgMesh(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_, const bool &binary_)
:MeshConverter(MshFileName, meshDimension_, mesh_), binary(binary_)
{
}

DlgMesh::~DlgMesh()
{
}

int DlgMesh::ReadMesh(const bool &fullRead)
{
    std::fstream file;
	if (binary) file.open(fullFileName.c_str(), std::ios::in | std::ios::binary);
	else        file.open(fullFileName.c_str(), std::ios::in);

    InfoStruct info;

    //读取dual_grid_data根节点
    ReadInfoLine(file, info);

    //读取title子节点
    ReadInfoLine(file, info);
    ReadString(file, dlgMesh.title);
    
    //读取n_dim子节点
    ReadInfoLine(file, info);
    IO::Read(file, dlgMesh.n_dim, binary);
	if (meshDimension != Mesh::MeshDim::mdNoType && dlgMesh.n_dim != (int)meshDimension)
        FatalError("DlgMesh::ReadMesh: 网格维度与输入参数不一致");
    
    //读取region子节点
    ReadInfoLine(file, info);
	
	const int reginSize = info.subNodesNumber;
	dlgMesh.regions.resize(reginSize);
	for (int regionID = 0; regionID < reginSize; ++regionID)
    {
        // 读取细网格
        ReadInfoLine(file, info);
        ReadGrid(file, dlgMesh.regions[regionID], info.subNodesNumber);
    }

    return 0;
}

std::vector<std::string> DlgMesh::GetBoundaryName()
{
    std::vector<std::string> boundaryNameVector;
    for (int patchID = 0; patchID < dlgMesh.regions[0].n_bound; patchID++)
		boundaryNameVector.push_back(dlgMesh.regions[0].boundarys[patchID].b_name);
    
    return boundaryNameVector;
}

void DlgMesh::ConvertSubMesh(SubMesh *subMesh)
{
	const int totalLevel = dlgMesh.regions.size();
	subMesh->n_level = totalLevel;
	subMesh->v_multiGrid.resize(totalLevel - 1);
	for (int level = 0; level < totalLevel; ++level)
    {
        if (level == 0)
        {
            this->ConvertMesh((Mesh*)subMesh, level);
        }
        else
        {
            this->ConvertMesh((Mesh*)(&subMesh->v_multiGrid[level-1]), level);
            this->ConvertElementMap(subMesh->v_multiGrid[level-1].v_elemMap, level);
        }
    }
}

void DlgMesh::ConvertMesh(Mesh *mesh_, const int &level)
{
    const auto &grid = dlgMesh.regions[level];

    mesh_->zoneID = 0;
    mesh_->est_shapeType = Element::ElemShapeType::estNoType;
    mesh_->md_meshDim = (Mesh::MeshDim)dlgMesh.n_dim;

    mesh_->n_elemNum = grid.n_nodes;
    mesh_->v_elem.resize(mesh_->n_elemNum);
    for (int elementID = 0; elementID < mesh_->n_elemNum; ++elementID)
    {
        mesh_->v_elem[elementID].center = grid.coordinates[elementID];
        mesh_->v_elem[elementID].volume = grid.volumes[elementID];
        mesh_->v_elem[elementID].et_type = Element::ElemType::real;
    }

    mesh_->n_faceNum = grid.n_edges;
    for (int patchID = 0; patchID < grid.n_bound; ++patchID)
		mesh_->n_faceNum += grid.boundarys[patchID].b_nodes.size();
    mesh_->v_face.reserve(mesh_->n_faceNum);

    mesh_->v_boundaryIDGlobal.resize(grid.n_bound);
    mesh_->v_boundaryName.resize(grid.n_bound);
    mesh_->vv_boundaryFaceID.resize(grid.n_bound);
    mesh_->vv_innerElementID.resize(grid.n_bound);
    for (int patchID = 0; patchID < grid.n_bound; ++patchID)
    {
        const auto &boundary = grid.boundarys[patchID];
        mesh_->v_boundaryIDGlobal[patchID] = patchID;
        mesh_->v_boundaryName[patchID] = boundary.b_name;

        const int faceSize = boundary.b_nodes.size();
        mesh_->vv_boundaryFaceID[patchID].resize(faceSize);
        mesh_->vv_innerElementID[patchID].resize(faceSize);
        for (int index = 0; index < faceSize; ++index)
        {
            mesh_->v_face.push_back(Face());
            const int faceID = mesh_->v_face.size() - 1;

            Face &face = mesh_->v_face[faceID];
            face.n_owner = boundary.b_nodes[index];
            face.center = grid.coordinates[face.n_owner];
            face.areaMag = boundary.b_surfaces[index].Mag();
            face.normal = boundary.b_surfaces[index] / face.areaMag;
            
            mesh_->vv_boundaryFaceID[patchID][index] = faceID;
            mesh_->vv_innerElementID[patchID][index] = boundary.b_inner_nodes[index];

			//mesh_->v_elem[face.n_owner].v_faceID.push_back(faceID);
        }
    }

    for (int index = 0; index < grid.n_edges; ++index)
    {
        mesh_->v_face.push_back(Face());
        const int faceID = mesh_->v_face.size() - 1;

        Face &face = mesh_->v_face[faceID];
        face.n_owner = grid.edge_nodes[index].first;
        face.n_neighbor = grid.edge_nodes[index].second;
        face.center = 0.5 * (grid.coordinates[face.n_owner] + grid.coordinates[face.n_neighbor]);
        face.areaMag = grid.edge_surfaces[index].Mag();
        face.normal = grid.edge_surfaces[index] / face.areaMag;

		//mesh_->v_elem[face.n_owner].v_faceID.push_back(faceID);
		//mesh_->v_elem[face.n_neighbor].v_faceID.push_back(faceID);
    }
    
	std::vector<int> elementFaceSize(mesh_->n_elemNum, 0);
	for (int faceID = 0; faceID < mesh_->n_faceNum; ++faceID)
	{
		const int &ownerID = mesh_->v_face[faceID].n_owner;
		const int &neighID = mesh_->v_face[faceID].n_neighbor;
		elementFaceSize[ownerID]++;
		if (neighID != -1) elementFaceSize[neighID]++;
	}
	for (int elementID = 0; elementID < mesh_->n_elemNum; ++elementID)
		mesh_->v_elem[elementID].v_faceID.reserve(elementFaceSize[elementID]);

	for (int faceID = 0; faceID < mesh_->n_faceNum; ++faceID)
	{
		const int &ownerID = mesh_->v_face[faceID].n_owner;
		const int &neighID = mesh_->v_face[faceID].n_neighbor;
		mesh_->v_elem[ownerID].v_faceID.push_back(faceID);
		if(neighID != -1) mesh_->v_elem[neighID].v_faceID.push_back(faceID);
	}

	mesh_->v_nearWallDistance = grid.wall_distances;

    mesh_->n_nodeNum = 0;
    mesh_->v_node.clear();

    mesh_->n_elemNum_all = mesh_->n_elemNum;
    mesh_->n_elemNum_ghostBoundary = 0;
    mesh_->n_elemNum_ghostParallel = 0;
    mesh_->n_elemNum_ghostOverlap = 0;
}

void DlgMesh::ConvertElementMap(std::vector<std::vector<int>> &v_elemMap, const int &level)
{
    const auto &grid = dlgMesh.regions[level];
    const auto &IDList = grid.node_c2f;
    const int coarseSize = grid.node_c2f_idx.size() - 1;
    v_elemMap.resize(coarseSize);

    for (int coarseID = 0; coarseID < coarseSize; ++coarseID)
    {
        const int &beginID = grid.node_c2f_idx[coarseID];
        const int &endID = grid.node_c2f_idx[coarseID+1];
        const int fineSize = endID - beginID;
        v_elemMap[coarseID].resize(fineSize);
        for (int index = 0; index < fineSize; ++index)
            v_elemMap[coarseID][index] = IDList[beginID + index];
    }
}

void DlgMesh::ReadGrid(std::fstream &file, DlgGrid &grid, const int &subNodesNumber)
{
    InfoStruct info;

    for (int subNodeID = 0; subNodeID < subNodesNumber; subNodeID++)
    {
        ReadInfoLine(file, info);
        
        if(info.name=="n_nodes")
        {
            //读取原始网格节点数n_nodes，为对偶网格单元数
            IO::Read(file, grid.n_nodes, binary);
        }
        else if (info.name == "n_unsmbs" || info.name == "n_edges")
        {
            //读取原始网格边数n_edges，为对偶网格内部面数
            IO::Read(file, grid.n_edges, binary);
        }
        else if (info.name == "unsmb_nodes" || info.name == "edge_nodes")
        {
            //读取原始网格边构成信息edge_nodes，为对偶网格面左右单元编号
            ReadPairVector(file, info, grid.edge_nodes);
        }
        else if(info.name=="element_group")
        {
            //读取原始网格单元信息element_group
            grid.element_groups.push_back(DlgElementGroup());
            int groupID = grid.element_groups.size()-1;
            ReadElementGroup(file, grid.element_groups[groupID], info.subNodesNumber);
        }
        else if (info.name == "node_unsmblist_i" || info.name == "node_edgelist_i")
        {
            //读取原始网格信息节点和边的拓扑关系node_edgelist_i，每个edge仅属于最小编号node
            ReadIntVector(file, info, grid.node_edgelist_index);
        }
        else if (info.name == "node_unsmblist" || info.name == "node_edgelist")
        {
            //读取原始网格信息节点和边的拓扑关系node_edgelist_i，每个edge仅属于最小编号node
            ReadIntVector(file, info, grid.node_edgelist_value);
        }
        else if(info.name=="volumes")
        {
            //读取对偶网格控制体体积volumes
            IO::Read(file, grid.volumes, binary, info.valueNumber);
        }
        else if(info.name=="coordinates")
        {
            //读取原始网格节点坐标coordinates
            ReadVectorVector(file, info, grid.coordinates);
        }
        else if (info.name == "unsmb_surfaces" || info.name == "edge_surfaces")
        {
            //读取对偶网格面积矢量
            ReadVectorVector(file, info, grid.edge_surfaces);
        }
        else if (info.name == "n_unsmb_colors" || info.name == "n_edge_colors")
        {
            //读取原始网格边的颜色总数n_edge_colors，为对偶网格面的颜色总数
            IO::Read(file, grid.n_edge_colors, binary);
        }
        else if(info.name=="color_indices")
        {
            //读取原始网格边的按颜色分类的编号范围color_indices，为对偶网格面的颜色总数
            ReadPairVector(file, info, grid.color_indices);
        }
        else if (info.name == "n_bunsmbs" || info.name == "n_bedges")
        {
            //读取原始网格边界边总数n_bedges
            IO::Read(file, grid.n_bedges, binary);
        }
        else if(info.name=="n_b_colors")
        {
            //读取原始网格边界边颜色n_b_colors
            IO::Read(file, grid.n_b_colors, binary);
        }
        else if(info.name=="n_bound")
        {
            //读取原始网格边界总数n_bound
            IO::Read(file, grid.n_bound, binary);
        }
        else if(info.name=="boundary")
        {
            //读取边界信息
            grid.boundarys.push_back(DlgBoundary());
            int patchID = grid.boundarys.size()-1;
            ReadBoundary(file, patchID, grid.boundarys[patchID], info.subNodesNumber);
        }
        else if(info.name=="node_f2c")
        {
            //读取细网格单元所对应的粗网格单元编号
            ReadIntVector(file, info, grid.node_f2c);
        }
        else if(info.name=="node_c2f_idx")
        {
            //读取粗网格单元所对应的细网格单元编号的位置索引
            ReadIntVector(file, info, grid.node_c2f_idx);
        }
        else if(info.name=="node_c2f")
        {
            //读取粗网格单元所对应的细网格单元编号
            ReadIntVector(file, info, grid.node_c2f);
        }
        else if (info.name == "wall_distances")
        {
            //读取对偶网格控制体体积volumes
            IO::Read(file, grid.wall_distances, binary, info.valueNumber);
        }
        else
        {
            FatalError("DlgMesh::ReadGrid: info node is wrong!");
        }
    }
}

void DlgMesh::ReadElementGroup(std::fstream &file, DlgElementGroup &element_group, const int &subNodesNumber)
{
    InfoStruct info;

    for (int subNodeID = 0; subNodeID < subNodesNumber; subNodeID++)
    {
        ReadInfoLine(file, info);
        
        if(info.name=="element_type" || info.name=="bound_elem_type")
        {
            //读取原始单元类型element_type
            ReadString(file, element_group.element_type);
        }
        else if(info.name=="element_nodes" || info.name=="bound_elem_nodes")
        {
            //读取原始单元节点构成element_nodes
            std::vector<int> intVectorTemp;
            ReadIntVector(file, info, intVectorTemp);
            element_group.element_nodes.resize(info.valueNumber);
            for (int i=0; i<info.valueNumber; i++)
            {
                element_group.element_nodes[i].resize(info.valueDimension);
                for (int j=0; j<info.valueDimension; j++)
                    element_group.element_nodes[i][j] = intVectorTemp[i+j*info.valueNumber];
            }
        }
    }
}

void DlgMesh::ReadBoundary(std::fstream &file, const int &patchID, DlgBoundary &dlgBoundary, const int &subNodesNumber)
{
    InfoStruct info;
    
    for (int subNodeID = 0; subNodeID < subNodesNumber; subNodeID++)
    {
        ReadInfoLine(file, info);

        if(info.name=="b_name")
        {
            //读取边界名称b_name
            ReadString(file, dlgBoundary.b_name);
        }
        else if(info.name=="belem_group")
        {
            //读取原始网格边界单元信息belem_group
            dlgBoundary.belem_groups.push_back(DlgElementGroup());
            int groupID = dlgBoundary.belem_groups.size()-1;
            ReadElementGroup(file, dlgBoundary.belem_groups[groupID], info.subNodesNumber);
        }
        else if(info.name=="n_b_nodes")
        {
            //读取原始网格边界节点数量n_b_nodes，与对偶网格边界面数量一致
            IO::Read(file, dlgBoundary.n_b_nodes, binary);
        }
        else if(info.name=="b_nodes")
        {
            //读取原始网格边界节点编号b_nodes，为对偶网格n_owner
            ReadIntVector(file, info, dlgBoundary.b_nodes);
        }
        else if(info.name=="b_surfaces")
        {
            //读取对偶网格边界面面积矢量b_surfaces，为合并后的面积
            ReadVectorVector(file, info, dlgBoundary.b_surfaces);
        }
        else if(info.name=="b_class")
        {
            //读取边界类型b_class
            ReadString(file, dlgBoundary.b_class);
        }
        else if(info.name=="b_inner_nodes")
        {
            //读取边界单元相邻内部节点编号b_inner_nodes
            ReadIntVector(file, info, dlgBoundary.b_inner_nodes);
        }
    }
}

bool DlgMesh::ReadInfoLine(std::fstream &file, InfoStruct &info)
{
	if (binary)
	{
		FatalError("DlgMesh::ReadInfoLine: binary file isnot supported!");
		// std::vector<std::string> stringTemp(20);
		// getline(file, stringTemp[0],  '\n');
		// getline(file, stringTemp[1],  '\n');
		// getline(file, stringTemp[2],  '\n');
		// getline(file, stringTemp[3],  '\n');
		// getline(file, stringTemp[4],  '\n');
		// getline(file, stringTemp[5],  '\n');
		// getline(file, stringTemp[6],  '\n');
		// getline(file, stringTemp[7],  '\n');
		// getline(file, stringTemp[8],  '\n');
		// getline(file, stringTemp[9],  '\n');
		// getline(file, stringTemp[10], '\n');
		// getline(file, stringTemp[11], '\n');
		// getline(file, stringTemp[12], '\n');
		// getline(file, stringTemp[13], '\n');
		// getline(file, stringTemp[14], '\n');
		// getline(file, stringTemp[15], '\n');
		// getline(file, stringTemp[16], '\n');
		// getline(file, stringTemp[17], '\n');
		// getline(file, stringTemp[18], '\n');
		// getline(file, stringTemp[19], '\n');
		// getline(file, stringTemp[20], '\n');
        // char name[20];
        // char type[4];
        // long ndim, nsize, nsub;
        // 
        // std::cout << "size = " << sizeof(ndim) << std::endl;
        // file.read(reinterpret_cast<char*>(&name), sizeof(name));
        // file.read(reinterpret_cast<char*>(&type), sizeof(type));
        // file.read(reinterpret_cast<char*>(&ndim), sizeof(ndim));
        // file.read(reinterpret_cast<char*>(&nsize), sizeof(nsize));
        // file.read(reinterpret_cast<char*>(&nsub), sizeof(nsub));

		//char str1[17], str2[5];
		//std::cin.getline(str1, sizeof(str1));
		//std::string temp;
		//file >> temp;
		//file >> info.name >> info.type >> temp >> info.valueDimension >> info.valueNumber >> info.subNodesNumber;
	}
	else
	{
		std::string stringTemp;
		while (JudgeBlank(stringTemp)) getline(file, stringTemp, '\n');
		//if(!binary) while(JudgeBlank(stringTemp)) getline(file, stringTemp);
		//else        FatalError("DlgMesh::ReadInfoLine: binary isnot supported");
		std::istringstream stringStream(stringTemp);
		stringStream >> info.name >> info.type >> info.valueDimension >> info.valueNumber >> info.subNodesNumber;
	}

    if(info.name == "") return false;
    else                return true;
}

void DlgMesh::ReadString(std::fstream &file, std::string &stringTemp)
{
    if(!binary) getline(file, stringTemp);
    else        FatalError("DlgMesh::ReadString: binary isnot supported");
    stringTemp.erase(0, stringTemp.find_first_of("'") + 1);
    stringTemp.erase(stringTemp.find_last_of("'"));
    stringTemp.erase(stringTemp.find_last_not_of(" ") + 1);
}

void DlgMesh::ReadPairVector(std::fstream &file, InfoStruct &info, std::vector<std::pair<int, int>> &pairVector)
{
    std::vector<int> intVectorTemp;
    ReadIntVector(file, info, intVectorTemp);

    pairVector.resize(info.valueNumber);
    for (int i=0; i<info.valueNumber; i++)
    {
        pairVector[i].first = intVectorTemp[i];
        pairVector[i].second = intVectorTemp[i+info.valueNumber];
    }
}

void DlgMesh::ReadIntVector(std::fstream &file, InfoStruct &info, std::vector<int> &intVector)
{
    IO::Read(file, intVector, binary, info.valueDimension * info.valueNumber);
    for (int i=0; i<intVector.size(); i++) intVector[i] -= 1;
}

void DlgMesh::ReadVectorVector(std::fstream &file, InfoStruct &info, std::vector<Vector> &vectorVector)
{
    std::vector<Scalar> scalarVectorTemp;
    IO::Read(file, scalarVectorTemp, binary, info.valueDimension * info.valueNumber);
    vectorVector.resize(info.valueNumber);
    for (int i=0; i<info.valueNumber; i++)
    {
        vectorVector[i].SetX(scalarVectorTemp[i]);
        vectorVector[i].SetY(scalarVectorTemp[i+info.valueNumber]);
        if(dlgMesh.n_dim==3) vectorVector[i].SetZ(scalarVectorTemp[i+info.valueNumber+info.valueNumber]);
    }
}
