﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BlockSparseMatrixPetsc.h
//! <AUTHOR>
//! @brief 块稀疏矩阵类（基于Petsc）
//! @date 2024-12-10
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-10 气动院
//    说明：建立。
//------------------------------------------------------------------------------

#if defined(_EnablePETSC_)

#ifndef _basic_CFD_BlockSparseMatrixPetsc_
#define _basic_CFD_BlockSparseMatrixPetsc_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrix.h"
#include "basic/mesh/SubMesh.h"

#include <petsc.h>
#include <petscksp.h>

/**
 * @brief 块稀疏矩阵类，使用PETSc进行实现
 *
 * 这个类继承自BlockSparseMatrix，提供了基于PETSc的块稀疏矩阵的具体实现。
 * 它用于在并行计算环境中高效地处理和存储大规模稀疏矩阵。
 */
class BlockSparseMatrixPetsc : public BlockSparseMatrix
{
public:
	/**
	 * @brief 构造函数
	 *
	 * 初始化块稀疏矩阵，关联到指定的网格（Mesh）并设置变量数。
	 * 
	 * @param[in] mesh 关联的网格对象，类型为Mesh*
	 * @param[in] nvariable 变量的数量，类型为const int
	 */
	BlockSparseMatrixPetsc(Mesh *mesh, const int nvariable);

	/**
	 * @brief 析构函数
	 *
	 * 释放块稀疏矩阵占用的内存和资源。
	 */
	~BlockSparseMatrixPetsc();

	/**
	 * @brief 初始化矩阵
	 *
	 * 执行内部数据结构的初始化操作，准备矩阵以供使用。
	 * 这包括PETSc矩阵对象的创建和初始化。
	 */
	void Initialize();

	/**
	 * @brief 将矩阵所有元素设置为零
	 *
	 * 初始化或重置块稀疏矩阵，将所有元素设置为零值。
	 */
	void SetValZero();

	/**
	 * @brief 向对角线位置添加标量值
	 *
	 * 在指定行的对角线位置上增加一个标量值。
	 * 
	 * @param[in] row 指定的行索引，类型为const int&
	 * @param[in] val 需要添加的标量值，类型为const Scalar&
	 */
	void AddVal2Diag(const int &row, const Scalar &val);

	/**
	 * @brief 向对角线位置添加矩阵块
	 *
	 * 在指定行的对角线位置上增加一个矩阵块。
	 * 
	 * @param[in] row 指定的行索引，类型为const int&
	 * @param[in] val 需要添加的矩阵块，类型为const Matrix&
	 */
	void AddBlock2Diag(const int &row, const Matrix &val);

	/**
	 * @brief 更新矩阵中的块
	 *
	 * 根据面ID和相关元素信息更新矩阵中的块。
	 * 
	 * @param[in] faceID 面的标识符，类型为const int&
	 * @param[in] elemI 第一个元素的索引，类型为const int&
	 * @param[in] elemJ 第二个元素的索引，类型为const int&
	 * @param[in] valI 需要更新的第一个矩阵块，类型为const Matrix&
	 * @param[in] valJ 需要更新的第二个矩阵块，类型为const Matrix&
	 */
	void UpdateBlocks(const int &faceID, const int &elemI, const int &elemJ, const Matrix &valI, const Matrix &valJ);
	
	/**
	 * @brief 删除指定单元所对应块矩阵的指定行的值
	 * 
	 * 修改指定单元的块矩阵的指定行的对角阵元素为1，其余元素为0
	 * 
	 * @param[in] row 需要删除的矩阵块行索引（单元编号），类型为const int &
	 * @param[in] index0 需要删除的块内行索引（变量编号），类型为const int &
	 */
	void DeleteValsRowi(const int &row, const int &index0);

	/**
	 * @brief 获取PETSc矩阵对象
	 *
	 * 返回内部使用的PETSc矩阵对象，用于进一步的高级操作。
	 * 
	 * @return PETSc矩阵对象，类型为Mat*
	 */
	Mat &GetMat() { return matrix; }

private:

	/**
	 * @brief 创建全局编号
	 *
	 */
	void CreateGlobalID();

private:

	/// PETSc矩阵对象, 用于实际的矩阵操作和存储。
	Mat matrix;

	/// 零向量，大小为块元素数量
	std::vector<PetscScalar> zeroRow;

	/// 实数1
	PetscScalar one;

	/// 当地网格单元全局编号
	std::vector<int> globalIDs;

	/// 边界单元索引
	std::vector<int> boundaryElementIndex;
	
	/// 边界单元系数矩阵值
	std::vector<std::vector<std::pair<int, Matrix>>> boundaryBlocks;
};

#endif
#endif