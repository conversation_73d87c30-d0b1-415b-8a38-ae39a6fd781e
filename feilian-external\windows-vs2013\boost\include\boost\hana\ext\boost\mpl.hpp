/*!
@file
Includes all the adaptors for the Boost.MPL library.

@copyright <PERSON> 2013-2016
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_HANA_EXT_BOOST_MPL_HPP
#define BOOST_HANA_EXT_BOOST_MPL_HPP

//! @ingroup group-ext
//! @defgroup group-ext-mpl Boost.MPL adapters
//! Adapters for Boost.MPL containers.

#include <boost/hana/ext/boost/mpl/integral_c.hpp>
#include <boost/hana/ext/boost/mpl/list.hpp>
#include <boost/hana/ext/boost/mpl/vector.hpp>

#endif // !BOOST_HANA_EXT_BOOST_MPL_HPP
