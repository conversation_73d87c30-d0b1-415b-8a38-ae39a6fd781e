/*=============================================================================
    Copyright (c) 2001-2003 <PERSON>
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_ERROR_HANDLING_MAIN_HPP)
#define BOOST_SPIRIT_ERROR_HANDLING_MAIN_HPP

#include <boost/spirit/home/<USER>/version.hpp>

///////////////////////////////////////////////////////////////////////////////
//
//  Master header for Spirit.ErrorHandling
//
///////////////////////////////////////////////////////////////////////////////

#include <boost/spirit/home/<USER>/error_handling/exceptions.hpp>

#endif // !defined(BOOST_SPIRIT_ERROR_HANDLING_MAIN_HPP)
