// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CSTDIO_HEADER
#define __CSTDIO_HEADER

#include <stdio.h>

namespace std {
  using ::FILE;
  using ::fpos_t;
  using ::size_t;
  using ::clearerr;
  using ::fgets;
  using ::fscanf;
  using ::gets;
  using ::rename;
  using ::tmpfile;
  using ::fclose;
  using ::fopen;
  using ::fseek;
  using ::perror;
  using ::rewind;
  using ::tmpnam;
  using ::feof;
  using ::fprintf;
  using ::fsetpos;
  using ::printf;
  using ::scanf;
  using ::ungetc;
  using ::ferror;
  using ::fputc;
  using ::ftell;
  using ::putc;
  using ::setbuf;
  using ::vfprintf;
  using ::fflush;
  using ::fputs;
  using ::fwrite;
  using ::putchar;
  using ::setvbuf;
  using ::vprintf;
  using ::fgetc;
  using ::fread;
  using ::getc;
  using ::puts;
  using ::sprintf;
  using ::vsprintf;
  using ::fgetpos;
  using ::freopen;
  using ::getchar;
  using ::remove;
  using ::sscanf;
}

#endif // CSTDIO_HEADER
