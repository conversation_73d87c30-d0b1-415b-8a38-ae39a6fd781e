// Copyright <PERSON>, <PERSON> 2005. Use, modification and 
// distribution is subject to the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)

//  See www.boost.org/libs/parameter for documentation.

#ifndef BOOST_PARAMETER_050401_HPP
#define BOOST_PARAMETER_050401_HPP

#include <boost/parameter/parameters.hpp>
#include <boost/parameter/keyword.hpp>
#include <boost/parameter/binding.hpp>
#include <boost/parameter/value_type.hpp>
#include <boost/parameter/macros.hpp>
#include <boost/parameter/match.hpp>
#include <boost/parameter/name.hpp>
#include <boost/parameter/preprocessor.hpp>

#endif // BOOST_PARAMETER_050401_HPP

