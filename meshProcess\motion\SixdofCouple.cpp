﻿#include "meshProcess/motion/SixdofCouple.h"

SixdofCouple::SixdofCouple(Sixdof& DOF, int extrapolType, 
	double relaxFactor , double subitConvThreshold)
{
	if (GetMPIRank() == 0) Print("\nInitializing SixdofCouple ...");

	this->extrapolator = abs(extrapolType);
	this->relaxFacFixed = std::min(std::max(relaxFactor,0.0), 1.0);
	this->subitConvThreshold = subitConvThreshold;

	this->motion_data = DOF.GetMotionData("actual");
	this->motion_data_old = DOF.GetMotionData("old");
	this->motion_data_oold = DOF.GetMotionData("old");
	this->motion_data_ooold = DOF.GetMotionData("old");
}

void SixdofCouple::InitializeTimestep()
{
	DOF->AdvanceTime();
	DOF->VarSwap();
}

SixdofCouple::~SixdofCouple()
{
}
