// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CWCHAR_HEADER
#define __CWCHAR_HEADER

#include <wchar.h>

namespace std {
  using ::mbstate_t;
  using ::wint_t;
  using ::size_t;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::btowc;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::getwchar;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::ungetwc;
#endif
  using ::wcscpy;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wcsrtombs;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wmemchr;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::fgetwc;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::mbrlen;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::vfwprintf;
#endif
#endif
  using ::wcscspn;
  using ::wcsspn;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wmemcmp;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::fgetws;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::mbrtowc;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::vswprintf;
#endif
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::wcsftime;
#endif
  using ::wcsstr;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wmemcpy;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::fputwc;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::mbsinit;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::vwprintf;
#endif
#endif
  using ::wcslen;
  using ::wcstod;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wmemmove;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::fputws;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::mbsrtowcs;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wcrtomb;
#endif
  using ::wcsncat;
  using ::wcstok;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wmemset;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
#if !(defined(__DECCXX_VER) && __DECCXX_VER <= 60290024)
  using ::fwide;
#endif
#endif
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::putwc;
#endif
  using ::wcscat;
  using ::wcsncmp;
  using ::wcstol;
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wprintf;
#endif
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::fwprintf;
#endif
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::putwchar;
#endif
  using ::wcschr;
  using ::wcsncpy;
  using ::wcstoul;
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wscanf;
#endif
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::fwscanf;
#endif
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::swprintf;
#endif
#endif
  using ::wcscmp;
  using ::wcspbrk;
  using ::wcsxfrm;
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
  using ::getwc;
#endif
#if !(defined(__linux) && defined(__DECCXX_VER) && __DECCXX_VER <= 60390005)
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::swscanf;
#endif
#endif
  using ::wcscoll;
  using ::wcsrchr;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wctob;
#endif
}

#endif // CWCHAR_HEADER
