﻿#ifndef _particle_basic_IndexVector_
#define _particle_basic_IndexVector_

#include "basic/common/ConfigUtility.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{
class IndexVector
{
public:
    int x;
    int y;
    int z;
    
public:
	IndexVector()
	{
		x = 0;
		y = 0;
		z = 0;
	}

    IndexVector(const int &x_, const int &y_, const int &z_)
    {
        x = x_;
        y = y_;
        z = z_;
    }

	IndexVector(const Vector &vec)
	{
		x = (int)(vec.X());
		y = (int)(vec.Y());
		z = (int)(vec.Z());
	}

    const Vector ToDoubleVector()const 
    {
        return Vector((double)(this->x), (double)(this->y), (double)(this->z));
    }
};

} // namespace Particle

#endif