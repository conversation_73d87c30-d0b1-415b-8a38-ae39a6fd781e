 #pragma once
#include "ThirdPartyHeadersBegin.h"
#  include <boost/shared_ptr.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "AltTecUtil.h"
namespace tecplot { namespace ___3933 { typedef boost::shared_ptr<class ___1352> FieldDataPtr; class ___1352 { public: ___1352(___37* tecUtil, ___1172 zone, ___1172 ___4336, bool writable = false, bool ___964 = false); ___1352() : m_tecUtil(0) , m_fieldData(0) , m_rawDataPtr(0) , m_getFieldValue(0) , m_setFieldValue(0) , ___2459(___1369) , ___2669(___4329) , ___2668(0) , m_allocated(false) {} ~___1352() { if (m_allocated) free(m_rawDataPtr); } void allocate( FieldDataType_e ___1363, ValueLocation_e ___4326, ___2227 ___4325); bool ___2067() const { return (VALID_REF(m_tecUtil) && ___2668 > 0 && (m_fieldData != NULL || m_rawDataPtr != NULL) && VALID_ENUM(___2459, FieldDataType_e)); } double ___1780(___2227 index) const; ValueLocation_e ___1786() const { return ___2669; } FieldDataType_e getValueType() const { return ___2459; } ___2227 ___1783() const { return ___2668; } void ___3504(___2227 index, double ___4314); void ___1759(double* minVal, double* maxVal) const; void* getRawPointer() const { ENSURE(VALID_REF_OR_NULL(m_rawDataPtr)); return m_rawDataPtr; } private: ___1352 RawValuePtrGetReadableOrWritablePtr( ___1172 zone, ___1172 ___4336, ___372  getWritable, ___372  getDerived); ___37* m_tecUtil; ___1361 m_fieldData; void* m_rawDataPtr; ___1383 m_getFieldValue; ___1384 m_setFieldValue; FieldDataType_e ___2459; ValueLocation_e ___2669; ___2227 ___2668; bool m_allocated; }; inline double ___1352::___1780(___2227 index) const { REQUIRE(0 < index && index <= ___2668); double ___4314; ___478(VALID_REF_OR_NULL(m_rawDataPtr)); if (m_rawDataPtr != NULL) { switch(___2459) { case FieldDataType_Float: ___4314 = (double)(((float *)m_rawDataPtr)[index - 1]); break; case FieldDataType_Double: ___4314 = ((double *)m_rawDataPtr)[index - 1]; break; case FieldDataType_Int32: ___4314 = (double)(((int32_t *)m_rawDataPtr)[index - 1]); break; case FieldDataType_Int16: ___4314 = (double)(((int16_t *)m_rawDataPtr)[index - 1]); break; case FieldDataType_Byte: ___4314 = (double)(((uint8_t *)m_rawDataPtr)[index - 1]); break; case ___1365: ___4314 = (double)((((uint8_t *)m_rawDataPtr)[(index - 1) / 8] >> ((index - 1) % 8)) & (uint8_t)0x1); break; default: ___478(___1305); ___4314 = 0.0; break; } } else { ___478(VALID_FN_REF(m_getFieldValue)); ___4314 = m_getFieldValue(m_fieldData, index - 1); } return ___4314; } inline void ___1352::___3504(___2227 index, double ___4314) { REQUIRE(0 < index && index <= ___2668); ___478(VALID_REF_OR_NULL(m_rawDataPtr)); if (m_rawDataPtr != NULL) { switch (___2459) { case FieldDataType_Float: ((float *)(m_rawDataPtr))[index - 1] = ___650(___4314); break; case FieldDataType_Double: ((double *)(m_rawDataPtr))[index - 1] = ___489(___4314); break; case FieldDataType_Int32: ((___2227 *)(m_rawDataPtr))[index - 1] = ___652(___4314); break; case FieldDataType_Int16: ((short *)(m_rawDataPtr))[index - 1] = ___651(___4314); break; case FieldDataType_Byte: ((uint8_t *)(m_rawDataPtr))[index - 1] = CONVERT_DOUBLE_TO_UINT8(___4314); break; case ___1365: { ___2227 byteOffset = (index - 1) / 8; uint8_t    bitMask = (uint8_t)(01 << ((index - 1) % 8)); if (___4314 < 1.0) ((uint8_t *)(m_rawDataPtr))[byteOffset] &= ~bitMask; else ((uint8_t *)(m_rawDataPtr))[byteOffset] |= bitMask; } break; default: ___478(___1305); } } else { ___478(VALID_FN_REF(m_setFieldValue)); m_setFieldValue(m_fieldData, index - 1, (double)(___4314)); } } }}
