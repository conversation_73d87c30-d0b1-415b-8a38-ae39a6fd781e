﻿#include "feilian-specialmodule/particle/contact/ContactList.h"

namespace Particle
{
namespace Contact
{

ContactList::ContactList(const int &numContactsMax_, const int &numParticles_)
{
    this->numContactsMax = numContactsMax_;
    this->numParticles = numParticles_;
    this->numContacts = 0;

	this->contactList.clear();
	this->contactIndex.clear();
	this->blankIndex.clear();
	this->contactStatus.clear();

    this->contactList.resize(numContactsMax, std::make_pair(-1, -1));
    this->contactIndex.reserve(numContactsMax);
    this->blankIndex.reserve(numContactsMax);
    
    this->contactStatus.resize(numContactsMax_, ContactStatus::NO_CONTACT);
    
	this->contactPair.clear();
    this->contactPair.resize(numContactsMax_);

}

int ContactList::AddContact(const int &Mem_i , const int &Mem_j, const int &id_i, const int &id_j )
{
    // 形成接触信息（小编号在前）
    int l_mem_i, l_mem_j, l_id_i, l_id_j;
    if( id_i < id_j )
    {
        l_mem_i = Mem_i;
        l_mem_j = Mem_j;
        l_id_i = id_i;
        l_id_j = id_j;
    }
    else
    {
        l_mem_i = Mem_j;
        l_mem_j = Mem_i;
        l_id_i = id_j;
        l_id_j = id_i;
    }

    const int& listSize = this->contactIndex.size();

    // 遍历当前接触索引，判断接触是否已经存在
    int pos = -1;
    for (int i = 0; i < listSize; ++i)
    {
        const int& index = this->contactIndex[i];
        if (l_id_i == this->contactList[index].first && l_id_j == this->contactList[index].second)
        {
            pos = index;
            break;
        }
    }

    // 如果接触存在，则当前接触为已有接触，标记为OLD_CONTACT
    if (pos > -1)
    {
        this->contactStatus[pos] = ContactStatus::OLD_CONTACT;
        this->numContacts++;
    }
    else
    {
        // 获得空索引的最后一个位置，将其设置为当前新出现的接触
        pos = this->blankIndex[this->blankIndex.size() - 1];
        this->contactStatus[pos] = ContactStatus::NEW_CONTACT;

        // 新接触相应的接触列表及接触信息更新
        this->contactPair[pos] = ContactInfo(l_mem_i, l_mem_j, l_id_i, l_id_j);
        this->contactList[pos] = std::make_pair(l_id_i, l_id_j);

        // 空接触列表缩短1
        this->blankIndex.pop_back();

        // 接触列表增加1
        this->contactIndex.push_back(pos);
    }

    // 接触数量增加1
    this->numContacts++;

	return pos;
}

void ContactList::RemvReleased()
{
    this->blankIndex.clear();
    this->contactIndex.clear();
    for (int i = this->numContactsMax - 1; i > -1; --i)
    {
        if (this->contactStatus[i] == ContactStatus::NO_CONTACT)
        {
            this->blankIndex.push_back(i);
        }
        else if (this->contactStatus[i] == ContactStatus::PROCESSED_CONTACT)
        {
            this->contactStatus[i] = ContactStatus::NO_CONTACT;
            this->blankIndex.push_back(i);
        }
        else
        {
            this->contactIndex.push_back(i);
        }
    }
}

} // namespace Contact

} // namespace Particle