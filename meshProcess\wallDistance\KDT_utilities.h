﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ${ARI_SOURCE_DIR}/basic/mesh/Kdt_utilities.h
//! <AUTHOR>
//! @brief KDT节点和树类  
//! 
//! @date  2021-5-12
//
//------------------------------修改日志----------------------------------------
//
// 2022-04-30 曾凯 
// 说明：基于原Kdt模块建立
// 
// 2022-05-30 曾凯
// 说明：大幅修改和优化：
//      1.将需要在模块外部进行的大量准备工作移至模块内部，现在外部只需输入点云即可创建树，只需输入查找目标即可返回结果；
//      2.加入快速搜索算法，改进树均衡性，已实现完全均衡建树
//      3.添加序列化和反序列化，以进行并行通信
//      4.实现最近距离搜索和范围搜索两种搜索方法，以满足不同应用场景
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_KDT_utilities_
#define _meshProcess_wallDistance_KDT_utilities_

#include <vector>
#include <queue>
#include <string.h>
#include "basic/common/Configuration.h"
#include "basic/common/SystemControl.h"
#include "basic/mesh/MeshSupport.h"

template < typename PointType, typename DataType >
class DataStruct_KdtNode
{
public:
    typedef DataStruct_KdtNode< PointType, DataType >        KdtNode;
    typedef std::vector<KdtNode *>          KdtNodeList;
    typedef typename KdtNodeList::iterator  KdtNodeListIter;

public:
    // 点坐标
    PointType    point;

    // 分割轴
    int splitAxis;

    // 点在原始点云中的位置编号
    int          pointID;

    //! 点在树中的深度
    int          level;

    //! 左子树
    KdtNode      *left;

    //! 右子树
    KdtNode      *right;

    // 空间维度
    int    ndim;
    
	// 节点需要存储的数据信息，通常为点的原始编号等
	DataType data;

    // 节点代表的空间
    PointType spaceMin;
    PointType spaceMax;

    // 左右子树序列化后的位置编号
    int  leftID, rightID; 

private:
    int begin, end;

public:
    // 构造函数，创建新节点
    DataStruct_KdtNode();
    DataStruct_KdtNode(const int &ndim, int begin_, int end_, int level_);

    // 析构函数，释放动态申请内存
    ~DataStruct_KdtNode();

    void AddNode(std::vector<int> &pointIDlist, 
                 const std::vector<PointType> &pointCloud,
                 const std::vector<DataType> &dataCloud);

    void QuickSort(const std::vector<PointType> &pointCloud, std::vector<int> &src, int begin, int end);

    // 搜索算法一：最临近搜索算法
    void FindNearestNeighbour(const PointType &tgtPoint, Scalar &nearestDist, DataType &nearestPointData);
    // 给定一个六面体的下界和上界、一个球体的体心和半径，判断两者是否相交
    bool JudgeSphereCubeIntersection(const PointType &cubeMin, const PointType &cubeMax, const PointType &sphereCenter, const Scalar &sphereRadius);

    // 搜索算法二：范围搜索
    void FindNodesInRegion(const PointType &rangeMin, const PointType &rangeMax, std::vector<KdtNode *> &nodeList);
    bool IsInRegion(const PointType &rangeMin, const PointType &rangeMax);

    // 序列化及反序列化
    void SerializeNode(std::vector<KdtNode> &serializedTree);
    void DeSerializeNode(std::vector<KdtNode> &serializedTree);


    // int           GetNodeNum();

  //  DataType GetData() const { return item; };

public:
#if defined(_BaseParallelMPI_)
    template <class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        ar &point;
        ar &pointID;
        ar &level;
        ar &ndim;
        ar &leftID;
        ar &rightID;
        ar &splitAxis;
    }
#endif
};

// KDTree类
template < typename PointType, typename DataType >
class DataStruct_KdtTree
{
public:
    typedef typename DataStruct_KdtNode<PointType, DataType>::KdtNode          KdtNode;
    typedef typename DataStruct_KdtNode<PointType, DataType>::KdtNodeList      KdtNodeList;
    typedef typename DataStruct_KdtNode<PointType, DataType>::KdtNodeListIter  KdtNodeListIter;
    typedef DataStruct_KdtTree< PointType, DataType>         KdtTree;

    // 序列化后的树
    std::vector<KdtNode> serializedTree;

private:
    int        ndim;

    // 要构建Kdt树的点云集合
    const std::vector<PointType> &pointCloud;
    // 要存储的数据集合，与pointCloud点云一一对应，不需要存储时可传入一个空vector
	const std::vector<DataType> &dataCloud;

    // 点的编号集合，用于点排序
    std::vector<int> pointIDlist;

    // Kdt树的整体上、下界
    PointType pointCloudMax;
    PointType pointCloudMin;

    // 根结点
    KdtNode    *root;

public:

    DataStruct_KdtTree(const int &ndim_);

    DataStruct_KdtTree(const int &ndim_,
                       const std::vector<PointType> &pointCloud_,
                       const std::vector<DataType> &dataCloud_);

    //! 析构函数，释放动态申请内存
    ~DataStruct_KdtTree();

    // 计算点集合的上下界
    void ComputePointCloudLimits();

    // 搜索方法一：最邻近搜索Nearest Neighbour Search，返回最短距离及相应点编号
    std::pair<Scalar, DataType> FindNearestNeighbour(const PointType &tgtPoint);

    // 搜索方法二： 范围搜索 Range Search
    void FindNodesInRegion(const PointType &rangeMin, const PointType &rangeMax, std::vector<KdtNode *> &nodeList);

    const PointType & GetMin();

    const PointType & GetMax();

    // Kdt树的序列化和反序列化
    void SerializeTree();
    void DeSerializeTree();

    // // 向树中增加一个节点
    // void AddNode(KdtNode *node, std::vector<int> &pointIDlist);

    // int  GetNodeNum();



    // KdtNode * GetRoot() const;

    // void LevelTraverse() const;

public:
#if defined(_BaseParallelMPI_)
    template <class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        ar & ndim;
        ar & pointCloudMin;
        ar & pointCloudMax;
        ar & serializedTree;
    }
#endif
};

#endif // !_MESH_Kdt_UTILITIES_