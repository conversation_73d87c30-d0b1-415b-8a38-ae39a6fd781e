﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SpalartAllmaras.h
//! <AUTHOR> 唐海龙
//! @brief 湍流类：SpalartAllmaras
//! @date 2021-04-05
//
//------------------------------修改日志----------------------------------------
// 2021-04-05 李艳亮 唐海龙
//    说明：建立并规范化。
// 2021-08-20 增加SA湍流模型
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_SpalartAllmaras_
#define _sourceFlow_turbulence_SpalartAllmaras_

#include "sourceFlow/turbulence/BaseTurbulence.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流SpalartAllmaras类
 * 
 */
class  SpalartAllmaras : public BaseTurbulence
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
    SpalartAllmaras(Package::FlowPackage &flowPackage);    

    /**
    * @brief 析构函数
    *
    */
    ~SpalartAllmaras();

    /**
     * @brief 计算湍流粘性系数
     * 
     */
    void CalculateMuTurbulent();

    /**
     * @brief 各项残值计算前的准备工作
     * 
     */
    void PreCalculate(){};

    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();

    /**
     * @brief 湍流量限制
     * 
     * @return int 
     */
    int CheckAndLimit();

    /**
     * @brief 湍流场初始化
     * 
     */
    void InitializeTurbulentField();

protected:
    /**
     * @brief 计算扩散项面心处扩散系数
     * 
     * @param[in] faceID 面编号
     * @return std::vector<std::pair<Scalar, Scalar>> 
     */
    std::vector<std::pair<Scalar, Scalar>> CalculateGammaFace(const int &faceID);

    /**
     * @brief 累加源项通量残差
     * 
     * @param[in] elementID 单元编号
     * @param[in] ldOverlr
     * @param[in] gammaBC
     */
	void AddSourceResidual(const int &elementID, const Scalar &ldOverlr = 1.0, const Scalar &gammaBC = 1.0);

private:
    ElementField<Scalar> &nut; ///< 湍流量nut
    ElementField<Vector> *gradientNut; ///< 湍流量nut梯度场
    ElementField<Scalar> &residualNut; ///< 湍流量nut残值场

    const Scalar Cb1; ///< SA模型的专有常数Cb1
    const Scalar Cb2; ///< SA模型的专有常数Cb2
    const Scalar Cnu1; ///< SA模型的专有常数Cnu1
    const Scalar Cnu2; ///< SA模型的专有常数Cnu2
    const Scalar Cnu3; ///< SA模型的专有常数Cnu3
    const Scalar sigma; ///< SA模型的专有常数sigma
    const Scalar karmann; ///< SA模型的专有常数karmann
    const Scalar Cw1; ///< SA模型的专有常数Cw1
    const Scalar Cw2; ///< SA模型的专有常数Cw2
    const Scalar Cw3; ///< SA模型的专有常数Cw3
    const Scalar Ct1; ///< SA模型的专有常数Ct1
    const Scalar Ct2; ///< SA模型的专有常数Ct2
    const Scalar Ct3; ///< SA模型的专有常数Ct3
    const Scalar Ct4; ///< SA模型的专有常数Ct4
    
    const Scalar Cv1p3; ///< SA模型局部量Cv1p3
    const Scalar Cw3p6; ///< SA模型局部量Cw3p6
    const Scalar kap2; ///< SA模型局部量kap2
    const Scalar rkap2; ///< SA模型局部量rkap2
    const Scalar rsigma; ///< SA模型局部量rsigma
    const Scalar Cw1k; ///< SA模型局部量Cw1k
    const Scalar Cb2s; ///< SA模型局部量Cb2s
    const Scalar fwLimit; ///< SA模型局部量fwLimit
    const Scalar fwstar; ///< SA模型局部量fwstar
    const Scalar rlim; ///< SA模型局部量rlim
    
    Matrix Jacobian_i;
};

} // namespace Turbulence
#endif
