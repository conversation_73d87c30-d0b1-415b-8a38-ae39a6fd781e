/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    
    
    
    
    
    
    template <typename A0>
    struct dynamic<A0> : noncopyable
    {
        typedef
            vector1<A0>
            tuple_type;
        typedef
            dynamic<A0>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1>
    struct dynamic<A0 , A1> : noncopyable
    {
        typedef
            vector2<A0 , A1>
            tuple_type;
        typedef
            dynamic<A0 , A1>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2>
    struct dynamic<A0 , A1 , A2> : noncopyable
    {
        typedef
            vector3<A0 , A1 , A2>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3>
    struct dynamic<A0 , A1 , A2 , A3> : noncopyable
    {
        typedef
            vector4<A0 , A1 , A2 , A3>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4>
    struct dynamic<A0 , A1 , A2 , A3 , A4> : noncopyable
    {
        typedef
            vector5<A0 , A1 , A2 , A3 , A4>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5> : noncopyable
    {
        typedef
            vector6<A0 , A1 , A2 , A3 , A4 , A5>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6> : noncopyable
    {
        typedef
            vector7<A0 , A1 , A2 , A3 , A4 , A5 , A6>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7> : noncopyable
    {
        typedef
            vector8<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8> : noncopyable
    {
        typedef
            vector9<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9> : noncopyable
    {
        typedef
            vector10<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10> : noncopyable
    {
        typedef
            vector11<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11> : noncopyable
    {
        typedef
            vector12<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12> : noncopyable
    {
        typedef
            vector13<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13> : noncopyable
    {
        typedef
            vector14<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14> : noncopyable
    {
        typedef
            vector15<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14; typedef typename expression::dynamic_member< mpl::int_< 14> , self_type * >::type const member15;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15> : noncopyable
    {
        typedef
            vector16<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14; typedef typename expression::dynamic_member< mpl::int_< 14> , self_type * >::type const member15; typedef typename expression::dynamic_member< mpl::int_< 15> , self_type * >::type const member16;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16> : noncopyable
    {
        typedef
            vector17<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14; typedef typename expression::dynamic_member< mpl::int_< 14> , self_type * >::type const member15; typedef typename expression::dynamic_member< mpl::int_< 15> , self_type * >::type const member16; typedef typename expression::dynamic_member< mpl::int_< 16> , self_type * >::type const member17;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17> : noncopyable
    {
        typedef
            vector18<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14; typedef typename expression::dynamic_member< mpl::int_< 14> , self_type * >::type const member15; typedef typename expression::dynamic_member< mpl::int_< 15> , self_type * >::type const member16; typedef typename expression::dynamic_member< mpl::int_< 16> , self_type * >::type const member17; typedef typename expression::dynamic_member< mpl::int_< 17> , self_type * >::type const member18;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18> : noncopyable
    {
        typedef
            vector19<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14; typedef typename expression::dynamic_member< mpl::int_< 14> , self_type * >::type const member15; typedef typename expression::dynamic_member< mpl::int_< 15> , self_type * >::type const member16; typedef typename expression::dynamic_member< mpl::int_< 16> , self_type * >::type const member17; typedef typename expression::dynamic_member< mpl::int_< 17> , self_type * >::type const member18; typedef typename expression::dynamic_member< mpl::int_< 18> , self_type * >::type const member19;
        mutable dynamic_frame_type* frame;
    };
    
    
    
    
    
    
    
    template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19>
    struct dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19> : noncopyable
    {
        typedef
            vector20<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19>
            tuple_type;
        typedef
            dynamic<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19>
            self_type;
        typedef
            dynamic_frame<self_type>
            dynamic_frame_type;
        dynamic()
            : frame(0) {}
        template <int N>
        static
        typename expression::dynamic_member<mpl::int_<N>, self_type *>::type const
        init(self_type * scope)
        {
            return
                expression::
                    dynamic_member<mpl::int_<N>, self_type *>::
                        make(mpl::int_<N>(), scope);
        }
        typedef typename expression::dynamic_member< mpl::int_< 0> , self_type * >::type const member1; typedef typename expression::dynamic_member< mpl::int_< 1> , self_type * >::type const member2; typedef typename expression::dynamic_member< mpl::int_< 2> , self_type * >::type const member3; typedef typename expression::dynamic_member< mpl::int_< 3> , self_type * >::type const member4; typedef typename expression::dynamic_member< mpl::int_< 4> , self_type * >::type const member5; typedef typename expression::dynamic_member< mpl::int_< 5> , self_type * >::type const member6; typedef typename expression::dynamic_member< mpl::int_< 6> , self_type * >::type const member7; typedef typename expression::dynamic_member< mpl::int_< 7> , self_type * >::type const member8; typedef typename expression::dynamic_member< mpl::int_< 8> , self_type * >::type const member9; typedef typename expression::dynamic_member< mpl::int_< 9> , self_type * >::type const member10; typedef typename expression::dynamic_member< mpl::int_< 10> , self_type * >::type const member11; typedef typename expression::dynamic_member< mpl::int_< 11> , self_type * >::type const member12; typedef typename expression::dynamic_member< mpl::int_< 12> , self_type * >::type const member13; typedef typename expression::dynamic_member< mpl::int_< 13> , self_type * >::type const member14; typedef typename expression::dynamic_member< mpl::int_< 14> , self_type * >::type const member15; typedef typename expression::dynamic_member< mpl::int_< 15> , self_type * >::type const member16; typedef typename expression::dynamic_member< mpl::int_< 16> , self_type * >::type const member17; typedef typename expression::dynamic_member< mpl::int_< 17> , self_type * >::type const member18; typedef typename expression::dynamic_member< mpl::int_< 18> , self_type * >::type const member19; typedef typename expression::dynamic_member< mpl::int_< 19> , self_type * >::type const member20;
        mutable dynamic_frame_type* frame;
    };
