
#ifndef BOOST_MPL_MULTISET_MULTISET0_HPP_INCLUDED
#define BOOST_MPL_MULTISET_MULTISET0_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2003-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

//#include <boost/mpl/multiset/aux_/at.hpp>
//#include <boost/mpl/multiset/aux_/front.hpp>
//#include <boost/mpl/multiset/aux_/push_front.hpp>
//#include <boost/mpl/multiset/aux_/pop_front.hpp>
//#include <boost/mpl/multiset/aux_/back.hpp>
//#include <boost/mpl/multiset/aux_/clear.hpp>
//#include <boost/mpl/multiset/aux_/O1_size.hpp>
//#include <boost/mpl/multiset/aux_/size.hpp>
//#include <boost/mpl/multiset/aux_/empty.hpp>
//#include <boost/mpl/multiset/aux_/empty.hpp>
#include <boost/mpl/multiset/aux_/insert_impl.hpp>
#include <boost/mpl/multiset/aux_/count_impl.hpp>
//#include <boost/mpl/multiset/aux_/has_key_impl.hpp>
//#include <boost/mpl/multiset/aux_/begin_end_impl.hpp>
//#include <boost/mpl/multiset/aux_/iterator.hpp>
#include <boost/mpl/multiset/aux_/item.hpp>
#include <boost/mpl/multiset/aux_/multiset0.hpp>
#include <boost/mpl/multiset/aux_/tag.hpp>

#endif // BOOST_MPL_MULTISET_MULTISET0_HPP_INCLUDED
