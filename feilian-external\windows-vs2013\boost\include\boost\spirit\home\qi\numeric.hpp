/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_NUMERIC_FEBRUARY_05_2007_1231PM)
#define BOOST_SPIRIT_NUMERIC_FEBRUARY_05_2007_1231PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/numeric/bool.hpp>
#include <boost/spirit/home/<USER>/numeric/int.hpp>
#include <boost/spirit/home/<USER>/numeric/uint.hpp>
#include <boost/spirit/home/<USER>/numeric/real.hpp>

#endif
