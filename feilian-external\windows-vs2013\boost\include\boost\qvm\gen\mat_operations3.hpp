//Copyright (c) 2008-2016 <PERSON> and Reverge Studios, Inc.

//Distributed under the Boost Software License, Version 1.0. (See accompanying
//file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_QVM_B3A6DB3C87C3E99245ED1C6B747DE
#define BOOST_QVM_B3A6DB3C87C3E99245ED1C6B747DE

//This file was generated by a program. Do not edit manually.

#include <boost/qvm/assert.hpp>
#include <boost/qvm/deduce_mat.hpp>
#include <boost/qvm/deduce_vec.hpp>
#include <boost/qvm/enable_if.hpp>
#include <boost/qvm/error.hpp>
#include <boost/qvm/inline.hpp>
#include <boost/qvm/mat_traits.hpp>
#include <boost/qvm/quat_traits.hpp>
#include <boost/qvm/scalar_traits.hpp>
#include <boost/qvm/throw_exception.hpp>

namespace
boost
    {
    namespace
    qvm
        {
        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            deduce_mat2<A,B,3,3> >::type
        operator+( A const & a, B const & b )
            {
            typedef typename deduce_mat2<A,B,3,3>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==3);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==3);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b);
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)+mat_traits<B>::template read_element<0,1>(b);
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)+mat_traits<B>::template read_element<0,2>(b);
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)+mat_traits<B>::template read_element<1,0>(b);
            mat_traits<R>::template write_element<1,1>(r)=mat_traits<A>::template read_element<1,1>(a)+mat_traits<B>::template read_element<1,1>(b);
            mat_traits<R>::template write_element<1,2>(r)=mat_traits<A>::template read_element<1,2>(a)+mat_traits<B>::template read_element<1,2>(b);
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)+mat_traits<B>::template read_element<2,0>(b);
            mat_traits<R>::template write_element<2,1>(r)=mat_traits<A>::template read_element<2,1>(a)+mat_traits<B>::template read_element<2,1>(b);
            mat_traits<R>::template write_element<2,2>(r)=mat_traits<A>::template read_element<2,2>(a)+mat_traits<B>::template read_element<2,2>(b);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator+;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct plus_mm_defined;

            template <>
            struct
            plus_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            deduce_mat2<A,B,3,1> >::type
        operator+( A const & a, B const & b )
            {
            typedef typename deduce_mat2<A,B,3,1>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==3);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==1);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b);
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)+mat_traits<B>::template read_element<1,0>(b);
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)+mat_traits<B>::template read_element<2,0>(b);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator+;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct plus_mm_defined;

            template <>
            struct
            plus_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            deduce_mat2<A,B,1,3> >::type
        operator+( A const & a, B const & b )
            {
            typedef typename deduce_mat2<A,B,1,3>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==1);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==3);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b);
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)+mat_traits<B>::template read_element<0,1>(b);
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)+mat_traits<B>::template read_element<0,2>(b);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator+;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct plus_mm_defined;

            template <>
            struct
            plus_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            deduce_mat2<A,B,3,3> >::type
        operator-( A const & a, B const & b )
            {
            typedef typename deduce_mat2<A,B,3,3>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==3);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==3);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b);
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)-mat_traits<B>::template read_element<0,1>(b);
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)-mat_traits<B>::template read_element<0,2>(b);
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)-mat_traits<B>::template read_element<1,0>(b);
            mat_traits<R>::template write_element<1,1>(r)=mat_traits<A>::template read_element<1,1>(a)-mat_traits<B>::template read_element<1,1>(b);
            mat_traits<R>::template write_element<1,2>(r)=mat_traits<A>::template read_element<1,2>(a)-mat_traits<B>::template read_element<1,2>(b);
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)-mat_traits<B>::template read_element<2,0>(b);
            mat_traits<R>::template write_element<2,1>(r)=mat_traits<A>::template read_element<2,1>(a)-mat_traits<B>::template read_element<2,1>(b);
            mat_traits<R>::template write_element<2,2>(r)=mat_traits<A>::template read_element<2,2>(a)-mat_traits<B>::template read_element<2,2>(b);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_mm_defined;

            template <>
            struct
            minus_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            deduce_mat2<A,B,3,1> >::type
        operator-( A const & a, B const & b )
            {
            typedef typename deduce_mat2<A,B,3,1>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==3);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==1);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b);
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)-mat_traits<B>::template read_element<1,0>(b);
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)-mat_traits<B>::template read_element<2,0>(b);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_mm_defined;

            template <>
            struct
            minus_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            deduce_mat2<A,B,1,3> >::type
        operator-( A const & a, B const & b )
            {
            typedef typename deduce_mat2<A,B,1,3>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==1);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==3);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b);
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)-mat_traits<B>::template read_element<0,1>(b);
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)-mat_traits<B>::template read_element<0,2>(b);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_mm_defined;

            template <>
            struct
            minus_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        operator+=( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)+=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<0,1>(a)+=mat_traits<B>::template read_element<0,1>(b);
            mat_traits<A>::template write_element<0,2>(a)+=mat_traits<B>::template read_element<0,2>(b);
            mat_traits<A>::template write_element<1,0>(a)+=mat_traits<B>::template read_element<1,0>(b);
            mat_traits<A>::template write_element<1,1>(a)+=mat_traits<B>::template read_element<1,1>(b);
            mat_traits<A>::template write_element<1,2>(a)+=mat_traits<B>::template read_element<1,2>(b);
            mat_traits<A>::template write_element<2,0>(a)+=mat_traits<B>::template read_element<2,0>(b);
            mat_traits<A>::template write_element<2,1>(a)+=mat_traits<B>::template read_element<2,1>(b);
            mat_traits<A>::template write_element<2,2>(a)+=mat_traits<B>::template read_element<2,2>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator+=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct plus_eq_mm_defined;

            template <>
            struct
            plus_eq_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            A &>::type
        operator+=( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)+=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<1,0>(a)+=mat_traits<B>::template read_element<1,0>(b);
            mat_traits<A>::template write_element<2,0>(a)+=mat_traits<B>::template read_element<2,0>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator+=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct plus_eq_mm_defined;

            template <>
            struct
            plus_eq_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        operator+=( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)+=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<0,1>(a)+=mat_traits<B>::template read_element<0,1>(b);
            mat_traits<A>::template write_element<0,2>(a)+=mat_traits<B>::template read_element<0,2>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator+=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct plus_eq_mm_defined;

            template <>
            struct
            plus_eq_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        operator-=( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)-=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<0,1>(a)-=mat_traits<B>::template read_element<0,1>(b);
            mat_traits<A>::template write_element<0,2>(a)-=mat_traits<B>::template read_element<0,2>(b);
            mat_traits<A>::template write_element<1,0>(a)-=mat_traits<B>::template read_element<1,0>(b);
            mat_traits<A>::template write_element<1,1>(a)-=mat_traits<B>::template read_element<1,1>(b);
            mat_traits<A>::template write_element<1,2>(a)-=mat_traits<B>::template read_element<1,2>(b);
            mat_traits<A>::template write_element<2,0>(a)-=mat_traits<B>::template read_element<2,0>(b);
            mat_traits<A>::template write_element<2,1>(a)-=mat_traits<B>::template read_element<2,1>(b);
            mat_traits<A>::template write_element<2,2>(a)-=mat_traits<B>::template read_element<2,2>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_eq_mm_defined;

            template <>
            struct
            minus_eq_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            A &>::type
        operator-=( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)-=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<1,0>(a)-=mat_traits<B>::template read_element<1,0>(b);
            mat_traits<A>::template write_element<2,0>(a)-=mat_traits<B>::template read_element<2,0>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_eq_mm_defined;

            template <>
            struct
            minus_eq_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        operator-=( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)-=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<0,1>(a)-=mat_traits<B>::template read_element<0,1>(b);
            mat_traits<A>::template write_element<0,2>(a)-=mat_traits<B>::template read_element<0,2>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_eq_mm_defined;

            template <>
            struct
            minus_eq_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            deduce_mat<A> >::type
        operator*( A const & a, B b )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)*b;
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)*b;
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)*b;
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)*b;
            mat_traits<R>::template write_element<1,1>(r)=mat_traits<A>::template read_element<1,1>(a)*b;
            mat_traits<R>::template write_element<1,2>(r)=mat_traits<A>::template read_element<1,2>(a)*b;
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)*b;
            mat_traits<R>::template write_element<2,1>(r)=mat_traits<A>::template read_element<2,1>(a)*b;
            mat_traits<R>::template write_element<2,2>(r)=mat_traits<A>::template read_element<2,2>(a)*b;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct mul_ms_defined;

            template <>
            struct
            mul_ms_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==1 && is_scalar<B>::value,
            deduce_mat<A> >::type
        operator*( A const & a, B b )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)*b;
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)*b;
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)*b;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct mul_ms_defined;

            template <>
            struct
            mul_ms_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            deduce_mat<A> >::type
        operator*( A const & a, B b )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)*b;
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)*b;
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)*b;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct mul_ms_defined;

            template <>
            struct
            mul_ms_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            A &>::type
        operator*=( A & a, B b )
            {
            mat_traits<A>::template write_element<0,0>(a)*=b;
            mat_traits<A>::template write_element<0,1>(a)*=b;
            mat_traits<A>::template write_element<0,2>(a)*=b;
            mat_traits<A>::template write_element<1,0>(a)*=b;
            mat_traits<A>::template write_element<1,1>(a)*=b;
            mat_traits<A>::template write_element<1,2>(a)*=b;
            mat_traits<A>::template write_element<2,0>(a)*=b;
            mat_traits<A>::template write_element<2,1>(a)*=b;
            mat_traits<A>::template write_element<2,2>(a)*=b;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct mul_eq_ms_defined;

            template <>
            struct
            mul_eq_ms_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==1 && is_scalar<B>::value,
            A &>::type
        operator*=( A & a, B b )
            {
            mat_traits<A>::template write_element<0,0>(a)*=b;
            mat_traits<A>::template write_element<1,0>(a)*=b;
            mat_traits<A>::template write_element<2,0>(a)*=b;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct mul_eq_ms_defined;

            template <>
            struct
            mul_eq_ms_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            A &>::type
        operator*=( A & a, B b )
            {
            mat_traits<A>::template write_element<0,0>(a)*=b;
            mat_traits<A>::template write_element<0,1>(a)*=b;
            mat_traits<A>::template write_element<0,2>(a)*=b;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct mul_eq_ms_defined;

            template <>
            struct
            mul_eq_ms_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            deduce_mat<A> >::type
        operator/( A const & a, B b )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)/b;
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)/b;
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)/b;
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)/b;
            mat_traits<R>::template write_element<1,1>(r)=mat_traits<A>::template read_element<1,1>(a)/b;
            mat_traits<R>::template write_element<1,2>(r)=mat_traits<A>::template read_element<1,2>(a)/b;
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)/b;
            mat_traits<R>::template write_element<2,1>(r)=mat_traits<A>::template read_element<2,1>(a)/b;
            mat_traits<R>::template write_element<2,2>(r)=mat_traits<A>::template read_element<2,2>(a)/b;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator/;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct div_ms_defined;

            template <>
            struct
            div_ms_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==1 && is_scalar<B>::value,
            deduce_mat<A> >::type
        operator/( A const & a, B b )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)/b;
            mat_traits<R>::template write_element<1,0>(r)=mat_traits<A>::template read_element<1,0>(a)/b;
            mat_traits<R>::template write_element<2,0>(r)=mat_traits<A>::template read_element<2,0>(a)/b;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator/;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct div_ms_defined;

            template <>
            struct
            div_ms_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            deduce_mat<A> >::type
        operator/( A const & a, B b )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=mat_traits<A>::template read_element<0,0>(a)/b;
            mat_traits<R>::template write_element<0,1>(r)=mat_traits<A>::template read_element<0,1>(a)/b;
            mat_traits<R>::template write_element<0,2>(r)=mat_traits<A>::template read_element<0,2>(a)/b;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator/;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct div_ms_defined;

            template <>
            struct
            div_ms_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            A &>::type
        operator/=( A & a, B b )
            {
            mat_traits<A>::template write_element<0,0>(a)/=b;
            mat_traits<A>::template write_element<0,1>(a)/=b;
            mat_traits<A>::template write_element<0,2>(a)/=b;
            mat_traits<A>::template write_element<1,0>(a)/=b;
            mat_traits<A>::template write_element<1,1>(a)/=b;
            mat_traits<A>::template write_element<1,2>(a)/=b;
            mat_traits<A>::template write_element<2,0>(a)/=b;
            mat_traits<A>::template write_element<2,1>(a)/=b;
            mat_traits<A>::template write_element<2,2>(a)/=b;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator/=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct div_eq_ms_defined;

            template <>
            struct
            div_eq_ms_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==1 && is_scalar<B>::value,
            A &>::type
        operator/=( A & a, B b )
            {
            mat_traits<A>::template write_element<0,0>(a)/=b;
            mat_traits<A>::template write_element<1,0>(a)/=b;
            mat_traits<A>::template write_element<2,0>(a)/=b;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator/=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct div_eq_ms_defined;

            template <>
            struct
            div_eq_ms_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            A &>::type
        operator/=( A & a, B b )
            {
            mat_traits<A>::template write_element<0,0>(a)/=b;
            mat_traits<A>::template write_element<0,1>(a)/=b;
            mat_traits<A>::template write_element<0,2>(a)/=b;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator/=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct div_eq_ms_defined;

            template <>
            struct
            div_eq_ms_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        assign( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<0,1>(a)=mat_traits<B>::template read_element<0,1>(b);
            mat_traits<A>::template write_element<0,2>(a)=mat_traits<B>::template read_element<0,2>(b);
            mat_traits<A>::template write_element<1,0>(a)=mat_traits<B>::template read_element<1,0>(b);
            mat_traits<A>::template write_element<1,1>(a)=mat_traits<B>::template read_element<1,1>(b);
            mat_traits<A>::template write_element<1,2>(a)=mat_traits<B>::template read_element<1,2>(b);
            mat_traits<A>::template write_element<2,0>(a)=mat_traits<B>::template read_element<2,0>(b);
            mat_traits<A>::template write_element<2,1>(a)=mat_traits<B>::template read_element<2,1>(b);
            mat_traits<A>::template write_element<2,2>(a)=mat_traits<B>::template read_element<2,2>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::assign;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct assign_mm_defined;

            template <>
            struct
            assign_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            A &>::type
        assign( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<1,0>(a)=mat_traits<B>::template read_element<1,0>(b);
            mat_traits<A>::template write_element<2,0>(a)=mat_traits<B>::template read_element<2,0>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::assign;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct assign_mm_defined;

            template <>
            struct
            assign_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        assign( A & a, B const & b )
            {
            mat_traits<A>::template write_element<0,0>(a)=mat_traits<B>::template read_element<0,0>(b);
            mat_traits<A>::template write_element<0,1>(a)=mat_traits<B>::template read_element<0,1>(b);
            mat_traits<A>::template write_element<0,2>(a)=mat_traits<B>::template read_element<0,2>(b);
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::assign;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct assign_mm_defined;

            template <>
            struct
            assign_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class R,class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<R>::rows==3 && mat_traits<A>::rows==3 &&
            mat_traits<R>::cols==3 && mat_traits<A>::cols==3,
            R>::type
        convert_to( A const & a )
            {
            R r;
            mat_traits<R>::template write_element<0,0>(r) = mat_traits<A>::template read_element<0,0>(a);
            mat_traits<R>::template write_element<0,1>(r) = mat_traits<A>::template read_element<0,1>(a);
            mat_traits<R>::template write_element<0,2>(r) = mat_traits<A>::template read_element<0,2>(a);
            mat_traits<R>::template write_element<1,0>(r) = mat_traits<A>::template read_element<1,0>(a);
            mat_traits<R>::template write_element<1,1>(r) = mat_traits<A>::template read_element<1,1>(a);
            mat_traits<R>::template write_element<1,2>(r) = mat_traits<A>::template read_element<1,2>(a);
            mat_traits<R>::template write_element<2,0>(r) = mat_traits<A>::template read_element<2,0>(a);
            mat_traits<R>::template write_element<2,1>(r) = mat_traits<A>::template read_element<2,1>(a);
            mat_traits<R>::template write_element<2,2>(r) = mat_traits<A>::template read_element<2,2>(a);
            return r;
            }

        template <class R,class A>
        BOOST_QVM_INLINE
        typename enable_if_c<
            is_mat<R>::value && is_quat<A>::value &&
            mat_traits<R>::rows==3 && mat_traits<R>::cols==3,
            R>::type
        convert_to( A const & q )
            {
            typedef typename mat_traits<R>::scalar_type T;
            T const a=quat_traits<A>::template read_element<0>(q);
            T const b=quat_traits<A>::template read_element<1>(q);
            T const c=quat_traits<A>::template read_element<2>(q);
            T const d=quat_traits<A>::template read_element<3>(q);
            T const bb = b*b;
            T const cc = c*c;
            T const dd = d*d;
            T const bc = b*c;
            T const bd = b*d;
            T const cd = c*d;
            T const ab = a*b;
            T const ac = a*c;
            T const ad = a*d;
            T const one = scalar_traits<T>::value(1);
            T const two = one+one;
            R r;
            mat_traits<R>::template write_element<0,0>(r) = one - two*(cc+dd);
            mat_traits<R>::template write_element<0,1>(r) = two*(bc-ad);
            mat_traits<R>::template write_element<0,2>(r) = two*(bd+ac);
            mat_traits<R>::template write_element<1,0>(r) = two*(bc+ad);
            mat_traits<R>::template write_element<1,1>(r) = one - two*(bb+dd);
            mat_traits<R>::template write_element<1,2>(r) = two*(cd-ab);
            mat_traits<R>::template write_element<2,0>(r) = two*(bd-ac);
            mat_traits<R>::template write_element<2,1>(r) = two*(cd+ab);
            mat_traits<R>::template write_element<2,2>(r) = one - two*(bb+cc);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::convert_to;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct convert_to_m_defined;

            template <>
            struct
            convert_to_m_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class R,class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<R>::rows==3 && mat_traits<A>::rows==3 &&
            mat_traits<R>::cols==1 && mat_traits<A>::cols==1,
            R>::type
        convert_to( A const & a )
            {
            R r;
            mat_traits<R>::template write_element<0,0>(r) = mat_traits<A>::template read_element<0,0>(a);
            mat_traits<R>::template write_element<1,0>(r) = mat_traits<A>::template read_element<1,0>(a);
            mat_traits<R>::template write_element<2,0>(r) = mat_traits<A>::template read_element<2,0>(a);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::convert_to;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct convert_to_m_defined;

            template <>
            struct
            convert_to_m_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class R,class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<R>::rows==1 && mat_traits<A>::rows==1 &&
            mat_traits<R>::cols==3 && mat_traits<A>::cols==3,
            R>::type
        convert_to( A const & a )
            {
            R r;
            mat_traits<R>::template write_element<0,0>(r) = mat_traits<A>::template read_element<0,0>(a);
            mat_traits<R>::template write_element<0,1>(r) = mat_traits<A>::template read_element<0,1>(a);
            mat_traits<R>::template write_element<0,2>(r) = mat_traits<A>::template read_element<0,2>(a);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::convert_to;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct convert_to_m_defined;

            template <>
            struct
            convert_to_m_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            bool>::type
        operator==( A const & a, B const & b )
            {
            return
                mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b) &&
                mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b) &&
                mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b) &&
                mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b) &&
                mat_traits<A>::template read_element<1,1>(a)==mat_traits<B>::template read_element<1,1>(b) &&
                mat_traits<A>::template read_element<1,2>(a)==mat_traits<B>::template read_element<1,2>(b) &&
                mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b) &&
                mat_traits<A>::template read_element<2,1>(a)==mat_traits<B>::template read_element<2,1>(b) &&
                mat_traits<A>::template read_element<2,2>(a)==mat_traits<B>::template read_element<2,2>(b);
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator==;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct eq_mm_defined;

            template <>
            struct
            eq_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            bool>::type
        operator==( A const & a, B const & b )
            {
            return
                mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b) &&
                mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b) &&
                mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b);
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator==;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct eq_mm_defined;

            template <>
            struct
            eq_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            bool>::type
        operator==( A const & a, B const & b )
            {
            return
                mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b) &&
                mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b) &&
                mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b);
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator==;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct eq_mm_defined;

            template <>
            struct
            eq_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            bool>::type
        operator!=( A const & a, B const & b )
            {
            return
                !(mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b)) ||
                !(mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b)) ||
                !(mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b)) ||
                !(mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b)) ||
                !(mat_traits<A>::template read_element<1,1>(a)==mat_traits<B>::template read_element<1,1>(b)) ||
                !(mat_traits<A>::template read_element<1,2>(a)==mat_traits<B>::template read_element<1,2>(b)) ||
                !(mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b)) ||
                !(mat_traits<A>::template read_element<2,1>(a)==mat_traits<B>::template read_element<2,1>(b)) ||
                !(mat_traits<A>::template read_element<2,2>(a)==mat_traits<B>::template read_element<2,2>(b));
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator!=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct neq_mm_defined;

            template <>
            struct
            neq_mm_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
            bool>::type
        operator!=( A const & a, B const & b )
            {
            return
                !(mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b)) ||
                !(mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b)) ||
                !(mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b));
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator!=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct neq_mm_defined;

            template <>
            struct
            neq_mm_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            bool>::type
        operator!=( A const & a, B const & b )
            {
            return
                !(mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b)) ||
                !(mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b)) ||
                !(mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b));
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator!=;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct neq_mm_defined;

            template <>
            struct
            neq_mm_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3,
            deduce_mat<A> >::type
        operator-( A const & a )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=-mat_traits<A>::template read_element<0,0>(a);
            mat_traits<R>::template write_element<0,1>(r)=-mat_traits<A>::template read_element<0,1>(a);
            mat_traits<R>::template write_element<0,2>(r)=-mat_traits<A>::template read_element<0,2>(a);
            mat_traits<R>::template write_element<1,0>(r)=-mat_traits<A>::template read_element<1,0>(a);
            mat_traits<R>::template write_element<1,1>(r)=-mat_traits<A>::template read_element<1,1>(a);
            mat_traits<R>::template write_element<1,2>(r)=-mat_traits<A>::template read_element<1,2>(a);
            mat_traits<R>::template write_element<2,0>(r)=-mat_traits<A>::template read_element<2,0>(a);
            mat_traits<R>::template write_element<2,1>(r)=-mat_traits<A>::template read_element<2,1>(a);
            mat_traits<R>::template write_element<2,2>(r)=-mat_traits<A>::template read_element<2,2>(a);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_m_defined;

            template <>
            struct
            minus_m_defined<3,3>
                {
                static bool const value=true;
                };
            }

        template <class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==1,
            deduce_mat<A> >::type
        operator-( A const & a )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=-mat_traits<A>::template read_element<0,0>(a);
            mat_traits<R>::template write_element<1,0>(r)=-mat_traits<A>::template read_element<1,0>(a);
            mat_traits<R>::template write_element<2,0>(r)=-mat_traits<A>::template read_element<2,0>(a);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_m_defined;

            template <>
            struct
            minus_m_defined<3,1>
                {
                static bool const value=true;
                };
            }

        template <class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<A>::cols==3,
            deduce_mat<A> >::type
        operator-( A const & a )
            {
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)=-mat_traits<A>::template read_element<0,0>(a);
            mat_traits<R>::template write_element<0,1>(r)=-mat_traits<A>::template read_element<0,1>(a);
            mat_traits<R>::template write_element<0,2>(r)=-mat_traits<A>::template read_element<0,2>(a);
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator-;
            }

        namespace
        qvm_detail
            {
            template <int R,int C>
            struct minus_m_defined;

            template <>
            struct
            minus_m_defined<1,3>
                {
                static bool const value=true;
                };
            }

        template <class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3,
            typename mat_traits<A>::scalar_type>::type
        determinant( A const & a )
            {
            typedef typename mat_traits<A>::scalar_type T;
            T const a00=mat_traits<A>::template read_element<0,0>(a);
            T const a01=mat_traits<A>::template read_element<0,1>(a);
            T const a02=mat_traits<A>::template read_element<0,2>(a);
            T const a10=mat_traits<A>::template read_element<1,0>(a);
            T const a11=mat_traits<A>::template read_element<1,1>(a);
            T const a12=mat_traits<A>::template read_element<1,2>(a);
            T const a20=mat_traits<A>::template read_element<2,0>(a);
            T const a21=mat_traits<A>::template read_element<2,1>(a);
            T const a22=mat_traits<A>::template read_element<2,2>(a);
            T det=(a00*(a11*a22-a12*a21)-a01*(a10*a22-a12*a20)+a02*(a10*a21-a11*a20));
            return det;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::determinant;
            }

        namespace
        qvm_detail
            {
            template <int D>
            struct determinant_defined;

            template <>
            struct
            determinant_defined<3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3 && is_scalar<B>::value,
            deduce_mat<A> >::type
        inverse( A const & a, B det )
            {
            typedef typename mat_traits<A>::scalar_type T;
            BOOST_QVM_ASSERT(det!=scalar_traits<B>::value(0));
            T const a00=mat_traits<A>::template read_element<0,0>(a);
            T const a01=mat_traits<A>::template read_element<0,1>(a);
            T const a02=mat_traits<A>::template read_element<0,2>(a);
            T const a10=mat_traits<A>::template read_element<1,0>(a);
            T const a11=mat_traits<A>::template read_element<1,1>(a);
            T const a12=mat_traits<A>::template read_element<1,2>(a);
            T const a20=mat_traits<A>::template read_element<2,0>(a);
            T const a21=mat_traits<A>::template read_element<2,1>(a);
            T const a22=mat_traits<A>::template read_element<2,2>(a);
            T const f=scalar_traits<T>::value(1)/det;
            typedef typename deduce_mat<A>::type R;
            R r;
            mat_traits<R>::template write_element<0,0>(r)= f*(a11*a22-a12*a21);
            mat_traits<R>::template write_element<0,1>(r)=-f*(a01*a22-a02*a21);
            mat_traits<R>::template write_element<0,2>(r)= f*(a01*a12-a02*a11);
            mat_traits<R>::template write_element<1,0>(r)=-f*(a10*a22-a12*a20);
            mat_traits<R>::template write_element<1,1>(r)= f*(a00*a22-a02*a20);
            mat_traits<R>::template write_element<1,2>(r)=-f*(a00*a12-a02*a10);
            mat_traits<R>::template write_element<2,0>(r)= f*(a10*a21-a11*a20);
            mat_traits<R>::template write_element<2,1>(r)=-f*(a00*a21-a01*a20);
            mat_traits<R>::template write_element<2,2>(r)= f*(a00*a11-a01*a10);
            return r;
            }

        template <class A>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<A>::cols==3,
            deduce_mat<A> >::type
        inverse( A const & a )
            {
            typedef typename mat_traits<A>::scalar_type T;
            T det=determinant(a);
            if( det==scalar_traits<T>::value(0) )
                BOOST_QVM_THROW_EXCEPTION(zero_determinant_error());
            return inverse(a,det);
            }

        namespace
        sfinae
            {
            using ::boost::qvm::inverse;
            }

        namespace
        qvm_detail
            {
            template <int D>
            struct inverse_m_defined;

            template <>
            struct
            inverse_m_defined<3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            deduce_mat2<A,B,3,3> >::type
        operator*( A const & a, B const & b )
            {
            typedef typename mat_traits<A>::scalar_type Ta;
            typedef typename mat_traits<B>::scalar_type Tb;
            Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
            Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
            Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
            Ta const a10 = mat_traits<A>::template read_element<1,0>(a);
            Ta const a11 = mat_traits<A>::template read_element<1,1>(a);
            Ta const a12 = mat_traits<A>::template read_element<1,2>(a);
            Ta const a20 = mat_traits<A>::template read_element<2,0>(a);
            Ta const a21 = mat_traits<A>::template read_element<2,1>(a);
            Ta const a22 = mat_traits<A>::template read_element<2,2>(a);
            Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
            Tb const b01 = mat_traits<B>::template read_element<0,1>(b);
            Tb const b02 = mat_traits<B>::template read_element<0,2>(b);
            Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
            Tb const b11 = mat_traits<B>::template read_element<1,1>(b);
            Tb const b12 = mat_traits<B>::template read_element<1,2>(b);
            Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
            Tb const b21 = mat_traits<B>::template read_element<2,1>(b);
            Tb const b22 = mat_traits<B>::template read_element<2,2>(b);
            typedef typename deduce_mat2<A,B,3,3>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==3);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==3);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=a00*b00+a01*b10+a02*b20;
            mat_traits<R>::template write_element<0,1>(r)=a00*b01+a01*b11+a02*b21;
            mat_traits<R>::template write_element<0,2>(r)=a00*b02+a01*b12+a02*b22;
            mat_traits<R>::template write_element<1,0>(r)=a10*b00+a11*b10+a12*b20;
            mat_traits<R>::template write_element<1,1>(r)=a10*b01+a11*b11+a12*b21;
            mat_traits<R>::template write_element<1,2>(r)=a10*b02+a11*b12+a12*b22;
            mat_traits<R>::template write_element<2,0>(r)=a20*b00+a21*b10+a22*b20;
            mat_traits<R>::template write_element<2,1>(r)=a20*b01+a21*b11+a22*b21;
            mat_traits<R>::template write_element<2,2>(r)=a20*b02+a21*b12+a22*b22;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*;
            }

        namespace
        qvm_detail
            {
            template <int R,int CR,int C>
            struct mul_mm_defined;

            template <>
            struct
            mul_mm_defined<3,3,3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            A &>::type
        operator*=( A & a, B const & b )
            {
            typedef typename mat_traits<A>::scalar_type Ta;
            typedef typename mat_traits<B>::scalar_type Tb;
            Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
            Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
            Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
            Ta const a10 = mat_traits<A>::template read_element<1,0>(a);
            Ta const a11 = mat_traits<A>::template read_element<1,1>(a);
            Ta const a12 = mat_traits<A>::template read_element<1,2>(a);
            Ta const a20 = mat_traits<A>::template read_element<2,0>(a);
            Ta const a21 = mat_traits<A>::template read_element<2,1>(a);
            Ta const a22 = mat_traits<A>::template read_element<2,2>(a);
            Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
            Tb const b01 = mat_traits<B>::template read_element<0,1>(b);
            Tb const b02 = mat_traits<B>::template read_element<0,2>(b);
            Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
            Tb const b11 = mat_traits<B>::template read_element<1,1>(b);
            Tb const b12 = mat_traits<B>::template read_element<1,2>(b);
            Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
            Tb const b21 = mat_traits<B>::template read_element<2,1>(b);
            Tb const b22 = mat_traits<B>::template read_element<2,2>(b);
            mat_traits<A>::template write_element<0,0>(a)=a00*b00+a01*b10+a02*b20;
            mat_traits<A>::template write_element<0,1>(a)=a00*b01+a01*b11+a02*b21;
            mat_traits<A>::template write_element<0,2>(a)=a00*b02+a01*b12+a02*b22;
            mat_traits<A>::template write_element<1,0>(a)=a10*b00+a11*b10+a12*b20;
            mat_traits<A>::template write_element<1,1>(a)=a10*b01+a11*b11+a12*b21;
            mat_traits<A>::template write_element<1,2>(a)=a10*b02+a11*b12+a12*b22;
            mat_traits<A>::template write_element<2,0>(a)=a20*b00+a21*b10+a22*b20;
            mat_traits<A>::template write_element<2,1>(a)=a20*b01+a21*b11+a22*b21;
            mat_traits<A>::template write_element<2,2>(a)=a20*b02+a21*b12+a22*b22;
            return a;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*=;
            }

        namespace
        qvm_detail
            {
            template <int D>
            struct mul_eq_mm_defined;

            template <>
            struct
            mul_eq_mm_defined<3>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==3 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==1,
            deduce_mat2<A,B,3,1> >::type
        operator*( A const & a, B const & b )
            {
            typedef typename mat_traits<A>::scalar_type Ta;
            typedef typename mat_traits<B>::scalar_type Tb;
            Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
            Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
            Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
            Ta const a10 = mat_traits<A>::template read_element<1,0>(a);
            Ta const a11 = mat_traits<A>::template read_element<1,1>(a);
            Ta const a12 = mat_traits<A>::template read_element<1,2>(a);
            Ta const a20 = mat_traits<A>::template read_element<2,0>(a);
            Ta const a21 = mat_traits<A>::template read_element<2,1>(a);
            Ta const a22 = mat_traits<A>::template read_element<2,2>(a);
            Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
            Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
            Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
            typedef typename deduce_mat2<A,B,3,1>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==3);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==1);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=a00*b00+a01*b10+a02*b20;
            mat_traits<R>::template write_element<1,0>(r)=a10*b00+a11*b10+a12*b20;
            mat_traits<R>::template write_element<2,0>(r)=a20*b00+a21*b10+a22*b20;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*;
            }

        namespace
        qvm_detail
            {
            template <int R,int CR,int C>
            struct mul_mm_defined;

            template <>
            struct
            mul_mm_defined<3,3,1>
                {
                static bool const value=true;
                };
            }

        template <class A,class B>
        BOOST_QVM_INLINE_OPERATIONS
        typename lazy_enable_if_c<
            mat_traits<A>::rows==1 && mat_traits<B>::rows==3 &&
            mat_traits<A>::cols==3 && mat_traits<B>::cols==3,
            deduce_mat2<A,B,1,3> >::type
        operator*( A const & a, B const & b )
            {
            typedef typename mat_traits<A>::scalar_type Ta;
            typedef typename mat_traits<B>::scalar_type Tb;
            Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
            Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
            Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
            Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
            Tb const b01 = mat_traits<B>::template read_element<0,1>(b);
            Tb const b02 = mat_traits<B>::template read_element<0,2>(b);
            Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
            Tb const b11 = mat_traits<B>::template read_element<1,1>(b);
            Tb const b12 = mat_traits<B>::template read_element<1,2>(b);
            Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
            Tb const b21 = mat_traits<B>::template read_element<2,1>(b);
            Tb const b22 = mat_traits<B>::template read_element<2,2>(b);
            typedef typename deduce_mat2<A,B,1,3>::type R;
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==1);
            BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==3);
            R r;
            mat_traits<R>::template write_element<0,0>(r)=a00*b00+a01*b10+a02*b20;
            mat_traits<R>::template write_element<0,1>(r)=a00*b01+a01*b11+a02*b21;
            mat_traits<R>::template write_element<0,2>(r)=a00*b02+a01*b12+a02*b22;
            return r;
            }

        namespace
        sfinae
            {
            using ::boost::qvm::operator*;
            }

        namespace
        qvm_detail
            {
            template <int R,int CR,int C>
            struct mul_mm_defined;

            template <>
            struct
            mul_mm_defined<1,3,3>
                {
                static bool const value=true;
                };
            }

        }
    }

#endif
