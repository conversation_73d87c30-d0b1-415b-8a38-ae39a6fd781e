﻿#include "meshProcess/zone/ZoneManager.h"

ZoneManager::ZoneManager(const Mesh * srcMesh)
{
	std::vector<int> meshZoneIDs;

	// 搜集所有进程网格的域编号
#if defined(_BaseParallelMPI_)
	boost::mpi::all_gather(mpi_world, srcMesh->GetMeshZoneID(), meshZoneIDs);
#endif

	// 获取各子域信息
	int nproc = meshZoneIDs.size();
	nZones = meshZoneIDs[nproc - 1] + 1;

	zoneMpiStartRank.resize(nZones);
	zoneMpiEndRank.resize(nZones);
	for (int zoneI = 0; zoneI < nZones; zoneI++)
	{
		std::vector<int>::iterator it = std::find(meshZoneIDs.begin(), meshZoneIDs.end(), zoneI);
        zoneMpiStartRank[zoneI] = (int)(it - meshZoneIDs.begin());

		if (zoneI >0)
		{
			zoneMpiEndRank[zoneI - 1] = zoneMpiStartRank[zoneI] - 1;
		}
	}
	zoneMpiEndRank[nZones - 1] = nproc - 1;

	// TODO:目前一个进程还是只有一块子域，暂时先按该分区方式填充子域信息
	zoneStartElemID.resize(nZones);
	zoneElemNum.resize(nZones);
	zoneElemNumAll.resize(nZones);
	for (int zoneID = 0; zoneID < nZones; zoneID++)
	{
		if (zoneID == srcMesh->GetMeshZoneID())
		{
			zoneStartElemID[zoneID] = 0;
			zoneElemNum[zoneID] = srcMesh->GetElementNumberReal();
			zoneElemNumAll[zoneID] = srcMesh->GetElementNumberAll();
		}
		else
		{
			zoneStartElemID[zoneID] = -1;
			zoneElemNum[zoneID] = -1;
		}
	}

	// TODO:目前一个进程还是只有一块子域，暂时先按该分区方式填充边界信息
	zoneBoundaryID.resize(nZones);
	for (int zoneID = 0; zoneID < nZones; zoneID++)
	{
		for (int i = 0; i < srcMesh->GetBoundarySize(); i++)
		{
			if (zoneID == srcMesh->GetMeshZoneID())
			{
				zoneBoundaryID[zoneID].push_back(i);
			}
		}
	}

	//this->nZones = srcMesh.size();

	//   for (int zoneID = 0; zoneID < nZones; zoneID++)
	//   {
	//	this->zoneElemNum.push_back(srcMesh[zoneID]->GetElementNumberReal());
	//	this->zoneNames.push_back(srcMesh[zoneID]->GetMeshName());
	//}
}

ZoneManager::~ZoneManager()
{
}

/*
Mesh *ZoneManager::MergeMeshes(std::vector<Mesh *> &srcMesh)
{
	Mesh *mergedMesh = new Mesh;
	mergedMesh->md_meshDim = srcMesh[0]->md_meshDim;
	mergedMesh->st_fileName = srcMesh[0]->st_fileName;
	mergedMesh->st_meshName = srcMesh[0]->st_meshName;

	for (int zoneI = 0; zoneI < srcMesh.size(); zoneI++)
	{
		mergedMesh->est_shapeType = srcMesh[zoneI]->est_shapeType;

		mergedMesh->n_elemNum += srcMesh[zoneI]->GetElementNumberReal();
		mergedMesh->n_faceNum += srcMesh[zoneI]->n_faceNum;
		mergedMesh->n_nodeNum += srcMesh[zoneI]->n_nodeNum;

		mergedMesh->v_elem.insert(mergedMesh->v_elem.end(), srcMesh[zoneI]->v_elem.begin(), srcMesh[zoneI]->v_elem.end());
		mergedMesh->v_face.insert(mergedMesh->v_face.end(), srcMesh[zoneI]->v_face.begin(), srcMesh[zoneI]->v_face.end());
		mergedMesh->v_node.insert(mergedMesh->v_node.end(), srcMesh[zoneI]->v_node.begin(), srcMesh[zoneI]->v_node.end());
		mergedMesh->v_vertice.insert(mergedMesh->v_vertice.end(), srcMesh[zoneI]->v_vertice.begin(), srcMesh[zoneI]->v_vertice.end());

		delete srcMesh[zoneI];
	}

	return mergedMesh;
}
*/

void ZoneManager::DistributeZones(const int &gPart, const std::vector<Mesh *> &srcMesh)
{
	int pFree = gPart - nZones;
	int pID = 0;
	int nAll =0;

	for (int zoneID = 0; zoneID < nZones; zoneID++)
	{
		nAll+=this->zoneElemNum[zoneID];
	}
	
	this->zoneMpiStartRank.resize(nZones);
	this->zoneMpiEndRank.resize(nZones);
	for (int zoneI = 0; zoneI < nZones; zoneI++)
	{
		int n = srcMesh[zoneI]->GetElementNumberReal();

		Scalar weight = (Scalar)n / (Scalar)nAll;
        int np = (int)(floor(pFree * weight + 0.5));
		this->zoneMpiStartRank[zoneI] = pID;
		this->zoneMpiEndRank[zoneI] = pID + np;

		pID += np + 1;
	}
}

void ZoneManager::WriteZoneInfoToXML()
{
	std::fstream zoneInfoFile;
	zoneInfoFile.open("zoneInfo.xml", std::ios::out);

	zoneInfoFile << "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" << std::endl;
	zoneInfoFile << "<zoneInformation>" << std::endl;
	zoneInfoFile << "\t" << "<number>" << this->nZones << "</number>" << std::endl;

	zoneInfoFile << "\t" << "<names>";
	for (int zoneID = 0; zoneID < this->nZones; zoneID++)
	{
		zoneInfoFile << this->zoneNames[zoneID] << " ";
	}
	zoneInfoFile << "</names>" << std::endl;

	zoneInfoFile << "\t" << "<elemNum>";
	for (int zoneID = 0; zoneID < this->nZones; zoneID++)
	{
		zoneInfoFile << this->zoneElemNum[zoneID] << " ";
	}
	zoneInfoFile << "</elemNum>" << std::endl;

	zoneInfoFile << "\t" << "<mpiStartRank>";
	for (int zoneID = 0; zoneID < this->nZones; zoneID++)
	{
		zoneInfoFile << this->zoneMpiStartRank[zoneID] << " ";
	}
	zoneInfoFile << "</mpiStartRank>" << std::endl;

	zoneInfoFile << "\t" << "<mpiEndRank>";
	for (int zoneID = 0; zoneID < this->nZones; zoneID++)
	{
		zoneInfoFile << this->zoneMpiEndRank[zoneID] << " ";
	}
	zoneInfoFile << "</mpiEndRank>" << std::endl;

	zoneInfoFile << "</zoneInformation>" << std::endl;

	zoneInfoFile.close();
}

void ZoneManager::ReadZoneInfoFromXML()
{
	try
	{
		read_xml("zoneInfo.xml", zoneInfoXml);
	}
	catch(std::exception)
	{
		FatalError("ZoneManager: zoneInfo.xml can not be found!");
	}

	this->ReadZoneNumber();

	this->ReadZoneNames();

	this->ReadZoneElemNum();

	this->ReadZoneMpiRank();
}

void ZoneManager::ReadZoneNumber()
{
	try
	{
		BOOST_AUTO(rootZoneInfo, zoneInfoXml.get_child("zoneInformation"));
		nZones = rootZoneInfo.get<int>("number");
	}
	catch (std::exception)
	{
		FatalError("ZoneManager::ReadZoneNumber: Zone number is not exist!");
	}	
}
void ZoneManager::ReadZoneNames()
{
	try
	{
		BOOST_AUTO(rootZoneInfo, zoneInfoXml.get_child("zoneInformation"));
		std::istringstream nameStream(rootZoneInfo.get<std::string>("names"));

		zoneNames.resize(nZones);
		for (int zoneID = 0; zoneID < nZones; zoneID++)
		{
			nameStream >> zoneNames[zoneID];
		}
	}
	catch (std::exception)
	{
		FatalError("ZoneManager::ReadZoneNames: Zone names are not exist!");
	}	
}
void ZoneManager::ReadZoneElemNum()
{
	try
	{
		BOOST_AUTO(rootZoneInfo, zoneInfoXml.get_child("zoneInformation"));
		std::istringstream elemNumStream(rootZoneInfo.get<std::string>("elemNum"));

		zoneElemNum.resize(nZones);
		for (int zoneID = 0; zoneID < nZones; zoneID++)
		{
			std::string tmp;
			elemNumStream >> tmp;
			zoneElemNum[zoneID] = std::stoi(tmp) ;
		}
	}
	catch (std::exception)
	{
		FatalError("ZoneManager::ReadZoneElem: Zone element numbers are not exist!");
	}	
}
void ZoneManager::ReadZoneMpiRank()
{
	try
	{
		BOOST_AUTO(rootZoneInfo, zoneInfoXml.get_child("zoneInformation"));
		std::istringstream mpiStartRankStream(rootZoneInfo.get<std::string>("mpiStartRank"));

		zoneMpiStartRank.resize(nZones);
		for (int zoneID = 0; zoneID < nZones; zoneID++)
		{		
			std::string tmp;
			mpiStartRankStream >> tmp;
			zoneMpiStartRank[zoneID] = std::stoi(tmp);
		}

		std::istringstream mpiEndRankStream(rootZoneInfo.get<std::string>("mpiEndRank"));
		zoneMpiEndRank.resize(nZones);
		for (int zoneID = 0; zoneID < nZones; zoneID++)
		{
			std::string tmp;
			mpiEndRankStream >> tmp;
			zoneMpiEndRank[zoneID] = std::stoi(tmp);
		}
}
	catch (std::exception)
	{
		FatalError("ZoneManager::ReadZoneElem: Zone MPI ranks are not exist!");
	}	
}

void ZoneManager::PrintZoneInfo(int processorID)
{
	return;
	//if (GetMPIRank() == 0) Print("\n 网格包含"+ToString(nZones)+"个子域: ");
	//for (int zoneID=0; zoneID< nZones; zoneID++) 
	//{
	//	if (GetMPIRank() == 0) Print("\tzoneID " + ToString(zoneID) + ": " +zoneNames[zoneID]+ ", " + ToString(zoneElemNum[zoneID])+"个网格单元");
	//}
}


const int ZoneManager::GetBoundaryZoneID(const int &boundaryID)
{
	for (int zoneID = 0; zoneID < nZones; zoneID++)
	{
		for (int bcI = 0; bcI < zoneBoundaryID[zoneID].size(); bcI++)
		{
			if (zoneBoundaryID[zoneID][bcI] == boundaryID)
			{
				return zoneID;
			}
		}
	}
	FatalError("ZoneManager: 无法找到给定边界所属的域信息！");
	return 0;
}

const int ZoneManager::GetElemZoneID(const int &elemID)
{
	for (int zoneID = 0; zoneID < nZones; zoneID++)
	{
		if (elemID >= zoneStartElemID[zoneID] && elemID < zoneStartElemID[zoneID] + zoneElemNumAll[zoneID])
		{
			return zoneID;
		}
	}

	FatalError("ZoneManager: elemID=" + ToString(elemID) + " is not belong to any zone!");

	return 0;
}
