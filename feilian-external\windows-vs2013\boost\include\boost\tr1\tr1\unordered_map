//  (C) Copyright <PERSON> 2008.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)


#ifndef BOOST_TR1_UNORDERED_MAP_INCLUDED
#  define BOOST_TR1_UNORDERED_MAP_INCLUDED
#  include <boost/tr1/detail/config_all.hpp>

#  ifdef BOOST_HAS_CPP_0X
#     if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#        include_next <unordered_map>
#     else
#        include BOOST_TR1_STD_HEADER(unordered_map)
#     endif
#  endif

#  if !defined(BOOST_TR1_NO_RECURSION)
#  define BOOST_TR1_NO_RECURSION
#  ifdef BOOST_HAS_TR1_UNORDERED_MAP
#     if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#        include_next BOOST_TR1_HEADER(unordered_map)
#     else
#        include BOOST_TR1_STD_HEADER(BOOST_TR1_PATH(unordered_map))
#     endif
#  else
#     include <boost/tr1/unordered_map.hpp>
#  endif
#  undef BOOST_TR1_NO_RECURSION
#  endif
#endif


