﻿#ifndef _basic_CFD_gradient_GradientManager_
#define _basic_CFD_gradient_GradientManager_

#include "basic/field/ElementField.h"
#include "basic/configure/Configure.h"
#include "basic/configure/ConfigureMacro.h"
#include "basic/CFD/gradient/GreenGauss.h"
#include "basic/CFD/gradient/LeastSquare.h"

/**
 * @brief 梯度计算命名空间
 * 
 */
namespace Gradient
{

/**
 * @brief 梯度计算管理器
 * 
 */
class GradientManager
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] gradientType_ 梯度计算方法
     * @param[in] nodeCenter_ 格点标识
     */
    GradientManager(Mesh *mesh_, const FieldManipulation::GradientScheme method_ = FieldManipulation::GradientScheme::GREEN_GAUSS, const bool nodeCenter_ = false);

    /**
     * @brief 析构函数
     * 
     */
    ~GradientManager();
    
    /**
     * @brief 梯度计算函数
     * 
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template<class Type, class TypeGradient>
    void Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

private:
    GradientBase *gradient;
    
};

}
#endif