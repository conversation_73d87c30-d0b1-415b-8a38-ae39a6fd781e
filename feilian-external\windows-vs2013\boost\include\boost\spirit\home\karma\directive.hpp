//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_KARMA_DIRECTIVE_FEB_21_2007_0833PM)
#define BOOST_SPIRIT_KARMA_DIRECTIVE_FEB_21_2007_0833PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/directive/encoding.hpp>

///////////////////////////////////////////////////////////////////////////////
//  directives related to alignment 
//  left_align[...], right_align[...], center[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/left_alignment.hpp>
#include <boost/spirit/home/<USER>/directive/right_alignment.hpp>
#include <boost/spirit/home/<USER>/directive/center_alignment.hpp>

///////////////////////////////////////////////////////////////////////////////
//  directives related to truncating length
//  maxwidth[...], columns[]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/maxwidth.hpp>
#include <boost/spirit/home/<USER>/directive/columns.hpp>

///////////////////////////////////////////////////////////////////////////////
//  directives related to character case
//  lower[...] and upper[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/upper_lower_case.hpp>

///////////////////////////////////////////////////////////////////////////////
//  directives related to delimiting generators 
//  delimit[...] and verbatim[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/verbatim.hpp>
#include <boost/spirit/home/<USER>/directive/delimit.hpp>
#include <boost/spirit/home/<USER>/directive/no_delimit.hpp>

///////////////////////////////////////////////////////////////////////////////
//  repeat directives
//  repeat[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/repeat.hpp>

///////////////////////////////////////////////////////////////////////////////
//  omit, skip, and duplicate directives
//  omit[...], skip[...], duplicate[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/omit.hpp>
#include <boost/spirit/home/<USER>/directive/duplicate.hpp>

///////////////////////////////////////////////////////////////////////////////
//  buffer directive
//  buffer[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/buffer.hpp>

///////////////////////////////////////////////////////////////////////////////
//  strict and relaxed directives
//  strict[...], relaxed[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/strict_relaxed.hpp>

///////////////////////////////////////////////////////////////////////////////
//  as_string and as_wstring directives
//  as_string[...], as_wstring[...]
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/directive/as.hpp>

#endif
