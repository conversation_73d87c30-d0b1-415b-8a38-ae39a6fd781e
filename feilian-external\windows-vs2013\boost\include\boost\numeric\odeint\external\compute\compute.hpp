/*
  [auto_generated]
  boost/numeric/odeint/external/compute/compute.hpp

  [begin_description]
  includes all headers required for using odeint with Boost.Compute
  [end_description]

  Copyright 2009-2013 <PERSON><PERSON>
  Copyright 2009-2013 <PERSON>

  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or
  copy at http://www.boost.org/LICENSE_1_0.txt)
*/


#ifndef BOOST_NUMERIC_ODEINT_EXTERNAL_COMPUTE_COMPUTE_HPP_DEFINED
#define BOOST_NUMERIC_ODEINT_EXTERNAL_COMPUTE_COMPUTE_HPP_DEFINED

#include <boost/numeric/odeint/external/compute/compute_algebra.hpp>
#include <boost/numeric/odeint/external/compute/compute_operations.hpp>
#include <boost/numeric/odeint/external/compute/compute_algebra_dispatcher.hpp>
#include <boost/numeric/odeint/external/compute/compute_operations_dispatcher.hpp>
#include <boost/numeric/odeint/external/compute/compute_resize.hpp>

#endif // BOOST_NUMERIC_ODEINT_EXTERNAL_COMPUTE_COMPUTE_HPP_DEFINED
