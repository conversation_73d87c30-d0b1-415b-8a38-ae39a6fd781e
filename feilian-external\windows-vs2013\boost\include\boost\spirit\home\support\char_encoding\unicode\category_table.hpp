/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    AUTOGENERATED. DO NOT EDIT!!!
==============================================================================*/
#include <boost/cstdint.hpp>

namespace boost { namespace spirit { namespace ucd { namespace detail
{
    static const ::boost::uint8_t category_stage1[] = {

      0,   1,   2,   3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,  14,  15, 
     16,  17,  18,  19,  20,  21,  22,  23,  24,  25,  26,  27,  28,  29,  30,  31, 
     32,  33,  34,  35,  36,  37,  38,  39,  40,  41,  34,  42,  43,  44,  45,  46, 
     47,  48,  49,  40,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  50,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  51, 
     52,  21,  21,  21,  53,  21,  54,  55,  56,  57,  58,  59,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  60,  61,  61,  61,  61,  61,  61,  61,  61, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  21,  63,  64,  21,  65,  66,  67, 
     68,  69,  70,  71,  72,  73,  73,  73,  74,  75,  76,  77,  78,  73,  73,  73, 
     79,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     21,  21,  21,  80,  81,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     82,  82,  82,  82,  83,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     84,  85,  86,  87,  88,  89,  90,  91,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     92,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
     21,  21,  21,  21,  21,  21,  94,  82,  82,  82,  82,  82,  82,  82,  82,  82, 
     82,  82,  82,  82,  82,  82,  82,  95,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  21,  21,  96,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     97,  98,  99,  99,  99,  99,  99,  99,  99,  99,  99,  99,  99,  99,  99,  99, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73, 
     73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  73,  93, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 100, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 
     62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62,  62, 100
    };

    static const ::boost::uint16_t category_stage2[] = {

    // block 0
      32,   32,   32,   32,   32,   32,   32,   32,   32,  544,  544,  544,  544,  544,   32,   32, 
      32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32, 
     536,   44,   44,   44,   49,   44,   44,   44,   41,   42,   44,   48,   44,   40,   44,   44, 
    1040, 1040, 1040, 1040, 1040, 1040, 1040, 1040, 1040, 1040,   44,   44,   48,   48,   48,   44, 
      44, 1216, 1216, 1216, 1216, 1216, 1216,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,   41,   44,   42,   50,   43, 
      50, 1345, 1345, 1345, 1345, 1345, 1345,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,   41,   48,   42,   48,   32, 
      32,   32,   32,   32,   32,  544,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32, 
      32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32,   32, 
     536,   44,   49,   49,   49,   49,   51,   51,   50,   51,  321,   45,   48, 4129,   51,   50, 
      51,   48,   18,   18,   50,  321,   51,   44,   50,   18,  321,   46,   18,   18,   18,   44, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,   48,  192,  192,  192,  192,  192,  192,  192,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,   48,  321,  321,  321,  321,  321,  321,  321,  321, 


    // block 1
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  321,  192,  321,  192,  321,  192,  321,  192, 
     321,  192,  321,  192,  321,  192,  321,  192,  321,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  192,  321,  192,  321,  192,  321,  321, 
     321,  192,  192,  321,  192,  321,  192,  192,  321,  192,  192,  192,  321,  321,  192,  192, 
     192,  192,  321,  192,  192,  321,  192,  192,  192,  321,  321,  321,  192,  192,  321,  192, 
     192,  321,  192,  321,  192,  321,  192,  192,  321,  192,  321,  321,  192,  321,  192,  192, 
     321,  192,  192,  192,  321,  192,  321,  192,  192,  321,  321,   68,  192,  321,  321,  321, 
      68,   68,   68,   68,  192,   66,  321,  192,   66,  321,  192,   66,  321,  192,  321,  192, 
     321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     321,  192,   66,  321,  192,  321,  192,  192,  192,  321,  192,  321,  192,  321,  192,  321, 


    // block 2
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  321,  321,  321,  321,  321,  321,  192,  192,  321,  192,  192,  321, 
     321,  192,  321,  192,  192,  192,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,   68,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     323,  323,  323,  323,  323,  323,  323,  323,  323,   67,   67,   67,   67,   67,   67,   67, 
     323,  323,   50,   50,   50,   50,   67,   67,   67,   67,   67,   67,   67,   67,   67,   67, 
      67,   67,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50, 
     323,  323,  323,  323,  323,   50,   50,   50,   50,   50,   50,   50,   67,   50,   67,   50, 
      50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50, 


    // block 3
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,  328,    8,    8,    8,    8,    8,    8,    8,    8,    8, 4104, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
     192,  321,  192,  321,   67,   50,  192,  321,    0,    0,  323,  321,  321,  321,   44,    0, 
       0,    0,    0,    0,   50,   50,  192,   44,  192,  192,  192,    0,  192,    0,  192,  192, 
     321,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,    0,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  192, 
     321,  321,  192,  192,  192,  321,  321,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     321,  321,  321,  321,  192,  321,   48,  192,  321,  192,  192,  321,  321,  192,  192,  192, 


    // block 4
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,   51,    8,    8,    8,    8,    8,    9,    9,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 


    // block 5
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  320,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,    0,    0,   67,   44,   44,   44,   44,   44,   44, 
       0,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,    0,   44,   40,    0,    0,    0,    0,    0, 
       0,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   40,   72, 
      44,   72,   72,   44,   72,   72,   44,   72,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0, 
      68,   68,   68,   44,   44,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 6
      33,   33,   33,   33,    0,    0,   48,   48,   48,   44,   44,   49,   44,   44,   51,   51, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   44,    0,    0,   44,   44, 
       0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      67,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   72,   72,   72,   72,   72, 
      72,   72,   72,   72,   72,   72,   72,   72,    8,   72,   72,   72,   72,   72,   72,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   44,   44,   44,   44,   68,   68, 
      72,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   44,   68,   72,   72,   72,   72,   72,   72,   72,   33,    9,    8, 
       8,   72,   72,   72,   72,   67,   67,   72,   72,   51,    8,    8,    8,   72,   68,   68, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   68,   68,   68,   51,   51,   68, 


    // block 7
      44,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,    0,   33, 
      68,   72,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    0,    0,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72, 
      72,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,   67,   67,   51,   44,   44,   44,   67,    0,    0,    0,    0,    0, 


    // block 8
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,    0,    0,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 9
      64,   72,   72,   74,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    8,   68,   74,   74, 
      74,   72,   72,   72,   72,   72,   72,   72,   72,   74,   74,   74,   74,    8,   64,    0, 
      68,    8,    8,    8,    8,   64,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   72,   72,   44,   44,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      44,   67,   68,    0,    0,    0,    0,    0,    0,   64,   64,   68,   68,   68,   68,   68, 
       0,   72,   74,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,   68, 
      68,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,    0,   68,    0,    0,    0,   68,   68,   68,   68,    0,    0,    8,   68,   74,   74, 
      74,   72,   72,   72,   72,    0,    0,   74,   74,    0,    0,   74,   74,    8,   68,    0, 
       0,    0,    0,    0,    0,    0,    0,   74,    0,    0,    0,    0,   68,   68,    0,   68, 
      68,   68,   72,   72,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      68,   68,   49,   49,   18,   18,   18,   18,   18,   18,   51,    0,    0,    0,    0,    0, 


    // block 10
       0,   72,   72,   74,    0,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,   68, 
      68,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,    0,   68,   68,    0,   68,   68,    0,   68,   68,    0,    0,    8,    0,   74,   74, 
      74,   72,   72,    0,    0,    0,    0,   72,   72,    0,    0,   72,   72,    8,    0,    0, 
       0,   72,    0,    0,    0,    0,    0,    0,    0,   68,   68,   68,   68,    0,   68,    0, 
       0,    0,    0,    0,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      72,   72,   68,   68,   68,   72,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,   72,   72,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68, 
      68,   68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,    0,   68,   68,    0,   68,   68,   68,   68,   68,    0,    0,    8,   68,   74,   74, 
      74,   72,   72,   72,   72,   72,    0,   72,   72,   74,    0,   74,   74,    8,    0,    0, 
      68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   72,   72,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
       0,   49,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 11
       0,   72,   74,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,   68, 
      68,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,    0,   68,   68,    0,   68,   68,   68,   68,   68,    0,    0,    8,   68,   74,   72, 
      74,   72,   72,   72,   72,    0,    0,   74,   74,    0,    0,   74,   74,    8,    0,    0, 
       0,    0,    0,    0,    0,    0,   72,   74,    0,    0,    0,    0,   68,   68,    0,   68, 
      68,   68,   72,   72,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      51,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,   72,   68,    0,   68,   68,   68,   68,   68,   68,    0,    0,    0,   68,   68, 
      68,    0,   68,   68,   68,   68,    0,    0,    0,   68,   68,    0,   68,    0,   68,   68, 
       0,    0,    0,   68,   68,    0,    0,    0,   68,   68,   68,    0,    0,    0,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,   74,   74, 
      72,   74,   74,    0,    0,    0,   74,   74,   74,    0,   74,   74,   74,    8,    0,    0, 
      68,    0,    0,    0,    0,    0,    0,   74,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      18,   18,   18,   51,   51,   51,   51,   51,   51,   49,   51,    0,    0,    0,    0,    0, 


    // block 12
       0,   74,   74,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68, 
      68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,    0,   68,   68,   68,   68,   68,    0,    0,    0,   68,   72,   72, 
      72,   74,   74,   74,   74,    0,   72,   72,   72,    0,   72,   72,   72,    8,    0,    0, 
       0,    0,    0,    0,    0,   72,   72,    0,   68,   68,    0,    0,    0,    0,    0,    0, 
      68,   68,   72,   72,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
       0,    0,    0,    0,    0,    0,    0,    0,   18,   18,   18,   18,   18,   18,   18,   51, 
       0,    0,   74,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68, 
      68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,    0,   68,   68,   68,   68,   68,    0,    0,    8,   68,   74,   72, 
      74,   74,   74,   74,   74,    0,   72,   74,   74,    0,   74,   74,   72,    8,    0,    0, 
       0,    0,    0,    0,    0,   74,   74,    0,    0,    0,    0,    0,    0,    0,   68,    0, 
      68,   68,   72,   72,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
       0,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 13
       0,    0,   74,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68, 
      68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,   68,   74,   74, 
      74,   72,   72,   72,   72,    0,   74,   74,   74,    0,   74,   74,   74,    8,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,   74,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   72,   72,    0,    0,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      18,   18,   18,   18,   18,   18,    0,    0,    0,   51,   68,   68,   68,   68,   68,   68, 
       0,    0,   74,   74,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,    0,    0,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    8,    0,    0,    0,    0,   74, 
      74,   74,   72,   72,   72,    0,   72,    0,   74,   74,   74,   74,   74,   74,   74,   74, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,   74,   74,   44,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 14
       0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   72,   68,   68,   72,   72,   72,   72,   72,   72,   72,    0,    0,    0,    0,   49, 
      68,   68,   68,   68,   68,   68,   67,    8,    8,    8,    8,    8,    8,   72,    8,   44, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   44,   44,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,   68,   68,    0,   68,    0,    0,   68,   68,    0,   68,    0,    0,   68,    0,    0, 
       0,    0,    0,    0,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68, 
       0,   68,   68,   68,    0,   68,    0,   68,    0,    0,   68,   68,    0,   68,   68,   68, 
      68,   72,   68,   68,   72,   72,   72,   72,   72,   72,    0,   72,   72,   68,    0,    0, 
      68,   68,   68,   68,   68,    0,   67,    0,    8,    8,    8,    8,    8,   72,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,   68,   68,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 15
      68,   51,   51,   51,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44, 
      44,   44,   44,   51,   51,   51,   51,   51,    8,    8,   51,   51,   51,   51,   51,   51, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   51,    8,   51,    8,   51,    8,   41,   42,   41,   42,   10,   10, 
      68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0, 
       0,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   74, 
      72,   72,    8,    8,    8,   44,    8,    8,   68,   68,   68,   68,    0,    0,    0,    0, 
      72,   72,   72,   72,   72,   72,   72,   72,    0,   72,   72,   72,   72,   72,   72,   72, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,    0,   51,   51, 
      51,   51,   51,   51,   51,   51,    8,   51,   51,   51,   51,   51,   51,    0,   51,   51, 
      44,   44,   44,   44,   44,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 16
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   74,   74,   72,   72,   72, 
      72,   74,   72,   72,   72,   72,   72,    8,   74,    8,    8,   74,   74,   72,   72,   68, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   44,   44,   44,   44,   44,   44, 
      68,   68,   68,   68,   68,   68,   74,   74,   72,   72,   68,   68,   68,   68,   72,   72, 
      72,   68,   74,   10,   10,   68,   68,   74,   74,   10,   10,   10,   10,   10,   68,   68, 
      68,   72,   72,   72,   72,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   72,   74,   74,   72,   72,   10,   10,   10,   10,   10,   10,    8,   68,   10, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,   64,   64,   51,   51, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   44,   67,    0,    0,    0, 


    // block 17
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   64,   64,   64,   64,   64, 4164, 
    4164,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   64,   64,   64,   64,   64,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   64,   64,   64,   64,   64,   64, 


    // block 18
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,    0,   68,   68,   68,   68,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,    0,   68,   68,   68,   68,    0,    0,   68,   68,   68,   68,   68,   68,   68,    0, 
      68,    0,   68,   68,   68,   68,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 


    // block 19
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,    0,   68,   68,   68,   68,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,   72, 
      51,   44,   44,   44,   44,   44,   44,   44,   44,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 20
       0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 


    // block 21
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 


    // block 22
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   44,   44,   68, 
      68,   68,   68,   68,   68,   68,   68,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
     536,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   41,   42,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   44,   44,   44,   81,   81, 
      81,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 23
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68, 
      68,   68,   72,   72,    8,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   72,   72,    8,   44,   44,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   72,   72,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68, 
      68,    0,   72,   72,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68, 4129, 4129,   74,   72,   72,   72,   72,   72,   72,   72,   74,   74, 
      74,   74,   74,   74,   74,   74,   72,   74,   74,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,   44,   44,   44,   67,   44,   44,   44,   49,   68,    8,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,    0,    0,    0, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,    0,    0,    0,    0,    0,    0, 


    // block 24
      44,   44,   44,   44,   44,   44,   40,   44,   44,   44,   44, 4104, 4104, 4104,  536,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   67,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   72,   68,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 25
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0, 
      72,   72,   72,   74,   74,   74,   74,   72,   72,   74,   74,   74,    0,    0,    0,    0, 
      74,   74,   72,   74,   74,   74,   74,   74,   74,    8,    8,    8,    0,    0,    0,    0, 
      51,    0,    0,    0,   44,   44,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
      68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   64,   64,    0,    0,    0,    0, 
      74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74, 
      74,   68,   68,   68,   68,   68,   68,   68,   74,   74,    0,    0,    0,    0,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,    0,   44,   44, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 


    // block 26
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   72,   72,   74,   74,   74,    0,    0,   44,   44, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0, 
       0,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,   64,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 27
      72,   72,   72,   72,   74,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,    8,   74,   72,   72,   72,   72,   72,   74,   72,   74,   74,   74, 
      74,   74,   72,   74,   10,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   44,   44,   44,   44,   44,   44, 
      44,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0, 
      72,   72,   74,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   74,   72,   72,   72,   72,   74,   74,   72,   72,   10,    0,    0,    0,   68,   68, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 28
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   74,   74,   74,   74,   74,   74,   74,   74,   72,   72,   72,   72, 
      72,   72,   72,   72,   74,   74,    8,    8,    0,    0,    0,   44,   44,   44,   44,   44, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,   68,   68,   68, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   67,   67,   67,   67,   67,   67,   44,   44, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,   64,   64,   64,   64,    0,   64,   64, 
      64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 29
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  323,  323,  323,  323, 
     323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323, 
     323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323, 
     323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323, 
     323,  323,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  323,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  323,  323,  323,  323,  323, 
     323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323, 
     323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323,  323, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    8,    8,    8,    8,    8,    8,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    8,    8, 


    // block 30
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  321,  321,  321,  321,  321,  321,  321,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 


    // block 31
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192, 
     321,  321,  321,  321,  321,  321,    0,    0,  192,  192,  192,  192,  192,  192,    0,    0, 
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192, 
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192, 
     321,  321,  321,  321,  321,  321,    0,    0,  192,  192,  192,  192,  192,  192,    0,    0, 
     321,  321,  321,  321,  321,  321,  321,  321,    0,  192,    0,  192,    0,  192,    0,  192, 
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,    0,    0, 
     321,  321,  321,  321,  321,  321,  321,  321,   66,   66,   66,   66,   66,   66,   66,   66, 
     321,  321,  321,  321,  321,  321,  321,  321,   66,   66,   66,   66,   66,   66,   66,   66, 
     321,  321,  321,  321,  321,  321,  321,  321,   66,   66,   66,   66,   66,   66,   66,   66, 
     321,  321,  321,  321,  321,    0,  321,  321,  192,  192,  192,  192,   66,   50,  321,   50, 
      50,   50,  321,  321,  321,    0,  321,  321,  192,  192,  192,  192,   66,   50,   50,   50, 
     321,  321,  321,  321,    0,    0,  321,  321,  192,  192,  192,  192,    0,   50,   50,   50, 
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,   50,   50,   50, 
       0,    0,  321,  321,  321,    0,  321,  321,  192,  192,  192,  192,   66,   50,   50,    0, 


    // block 32
     536,  536,  536,  536,  536,  536,  536,  536,  536,  536,  536, 4129, 4129, 4129, 4129, 4129, 
      40,   40,   40,   40,   40,   40,   44,   44,   45,   46,   41,   45,   45,   46,   41,   45, 
      44,   44,   44,   44,   44,   44,   44,   44,  537,  538, 4129, 4129, 4129, 4129, 4129,  536, 
      44,   44,   44,   44,   44,   44,   44,   44,   44,   45,   46,   44,   44,   44,   44,   43, 
      43,   44,   44,   44,   48,   41,   42,   44,   44,   44,   44,   44,   44,   44,   44,   44, 
      44,   44,   48,   44,   43,   44,   44,   44,   44,   44,   44,   44,   44,   44,   44,  536, 
    4129, 4129, 4129, 4129, 4129, 4096, 4096, 4096, 4096, 4096, 4129, 4129, 4129, 4129, 4129, 4129, 
      18,   65,    0,    0,   18,   18,   18,   18,   18,   18,   48,   48,   48,   41,   42,   65, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   48,   48,   48,   41,   42,    0, 
     323,  323,  323,  323,  323,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      49,   49,   49,   49,   49,   49,   49,   49,   49,   49,   49,   49,   49,   49,   49,   49, 
      49,   49,   49,   49,   49,   49,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    9,    9,    9, 
       9,    8,    9,    9,    9,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8,    8, 
       8,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 33
      51,   51,  192,   51,   51,   51,   51,  192,   51,   51,  321,  192,  192,  192,  321,  321, 
     192,  192,  192,  321,   51,  192,   51,   51,   51,  192,  192,  192,  192,  192,   51,   51, 
      51,   51,   51,   51,  192,   51,  192,   51,  192,   51,  192,  192,  192,  192,   51,  321, 
     192,  192,  192,  192,  321,   68,   68,   68,   68,  321,   51,   51,  321,  321,  192,  192, 
      48,   48,   48,   48,   48,  192,  321,  321,  321,  321,   51,   48,   51,   51,  321,   51, 
       0,    0,    0,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
     209,  209,  209,  209,  209,  209,  209,  209,  209,  209,  209,  209,  209,  209,  209,  209, 
     337,  337,  337,  337,  337,  337,  337,  337,  337,  337,  337,  337,  337,  337,  337,  337, 
      81,   81,   81,  192,  321,   81,   81,   81,   81,    0,    0,    0,    0,    0,    0,    0, 
      48,   48,   48,   48,   48,   51,   51,   51,   51,   51,   48,   48,   51,   51,   51,   51, 
      48,   51,   51,   48,   51,   51,   48,   51,   51,   51,   51,   51,   51,   51,   48,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   48,   48, 
      51,   51,   48,   51,   48,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 


    // block 34
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 


    // block 35
      51,   51,   51,   51,   51,   51,   51,   51,   48,   48,   48,   48,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      48,   48,   51,   51,   51,   51,   51,   51,   51,   41,   42,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   48,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   48,   48,   48,   48, 
      48,   48,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 36
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243, 
     243,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243,  243, 
     371,  371,  371,  371,  371,  371,  371,  371,  371,  371,  371,  371,  371,  371,  371,  371, 
     371,  371,  371,  371,  371,  371,  371,  371,  371,  371,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 


    // block 37
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   48,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   48,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   48,   48,   48,   48,   48,   48,   48,   48, 


    // block 38
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   48, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0, 
      51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 39
       0,   51,   51,   51,   51,    0,   51,   51,   51,   51,    0,    0,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,    0,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,   51,    0,   51, 
      51,   51,   51,    0,    0,    0,   51,    0,   51,   51,   51,   51,   51,   51,   51,    0, 
       0,   51,   51,   51,   51,   51,   51,   51,   41,   42,   41,   42,   41,   42,   41,   42, 
      41,   42,   41,   42,   41,   42,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   51,    0,    0,    0,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
       0,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0, 
      48,   48,   48,   48,   48,   41,   42,   48,   48,   48,   48,    0,   48,    0,    0,    0, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   41,   42,   41,   42,   41,   42,   41,   42,   41,   42, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 


    // block 40
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 


    // block 41
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   41,   42,   41,   42,   41,   42,   41,   42,   41,   42,   41,   42,   41, 
      42,   41,   42,   41,   42,   41,   42,   41,   42,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   41,   42,   41,   42,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   41,   42,   48,   48, 


    // block 42
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48,   48, 
      48,   48,   48,   48,   48,   51,   51,   48,   48,   48,   48,   48,   48,    0,    0,    0, 
      51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 43
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,    0, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,    0, 
     192,  321,  192,  192,  192,  321,  321,  192,  321,  192,  321,  192,  321,  192,  192,  192, 
     192,  321,  192,  321,  321,  192,  321,  321,  321,  321,  321,  321,  321,  323,  192,  192, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  321,   51,   51,   51,   51,   51,   51,  192,  320,  192,  320,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,   44,   44,   44,   44,   18,   44,   44, 


    // block 44
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,   67, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,    0, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,    0, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,    0, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,    0, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72, 
      72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72,   72, 


    // block 45
      44,   44,   45,   46,   45,   46,   44,   44,   44,   45,   46,   44,   45,   46,   44,   44, 
      44,   44,   44,   44,   44,   44,   44,   40,   44,   44,   40,   44,   45,   46,   44,   44, 
      45,   46,   41,   42,   41,   42,   41,   42,   41,   42,   44,   44,   44,   44,   44,   67, 
      44,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 46
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0, 


    // block 47
     536,   44,   44,   44,   51,   67,   68,   81,   41,   42,   41,   42,   41,   42,   41,   42, 
      41,   42,   51,   51,   41,   42,   41,   42,   41,   42,   41,   42,   40,   41,   42,   42, 
      51,   81,   81,   81,   81,   81,   81,   81,   81,   81,    8,    8,    8,    8,    8,    8, 
      40,   67,   67,   67,   67,   67,   51,   51,   81,   81,   81,   67,   68,   44,   51,   51, 
       0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,    0,    8,    8,   50,   50,   67,   67,   68, 
      40,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   44,   67,   67,   67,   68, 


    // block 48
       0,    0,    0,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
       0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68, 4164,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0, 
      51,   51,   18,   18,   18,   18,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 


    // block 49
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0, 


    // block 50
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 


    // block 51
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 52
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   67,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 


    // block 53
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0, 


    // block 54
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   67,   44,   44,   44, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   68,   68,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
       0,    0,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,   68,    8, 
       9,    9,    9,   44,    0,    0,    0,    0,    0,    0,    0,    0,    8,    8,   44,   67, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,    0,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 55
      50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50,   50, 
      50,   50,   50,   50,   50,   50,   50,   67,   67,   67,   67,   67,   67,   67,   67,   67, 
      50,   50,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     321,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321,  192,  321, 
     323,  321,  321,  321,  321,  321,  321,  321,  321,  192,  321,  192,  321,  192,  192,  321, 
     192,  321,  192,  321,  192,  321,  192,  321,   67,   50,   50,  192,  321,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,   68,   68,   68,   68,   68, 


    // block 56
      68,   68,    8,   68,   68,   68,    8,   68,   68,   68,   68,    8,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   74,   74,   72,   72,   74,   51,   51,   51,   51,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   44,   44,   44,   44,    0,    0,    0,    0,    0,    0,    0,    0, 
      74,   74,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74,   74, 
      74,   74,   74,   74,    8,    0,    0,    0,    0,    0,    0,    0,    0,    0,   44,   44, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,   64,   64,   64,   64,   64,   64,    0,    0,    0,   64,    0,    0,    0,    0, 


    // block 57
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   72,   72,   72,   72,   72,    8,    8,    8,   44,   44, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   72,   72,   72,   72,   72,   72,   72,   72,   72, 
      72,   72,   74,   10,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,   44, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,   64, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 58
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   72,   72,   72,   72,   72,   72,   74, 
      74,   72,   72,   74,   74,   72,   72,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   72,   68,   68,   68,   68,   68,   68,   68,   68,   72,   74,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,   44,   44,   44,   44, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,    0,    0,    0,   64,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0, 
      64,    0,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,   64,   64,   64,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 59
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 60
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,    0,    0,    0,    0,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0,    0, 


    // block 61
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 
      35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35,   35, 


    // block 62
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 


    // block 63
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   64,   64,   64,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 64
     321,  321,  321,  321,  321,  321,  321,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,  321,  321,  321,  321,  321,    0,    0,    0,    0,    0,   68,   72,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   48,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,    0,   68,    0, 
      68,   68,    0,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 


    // block 65
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   41,   42, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
       0,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0, 
    2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 
    2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   49,   51,    0,    0, 


    // block 66
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
      44,   44,   44,   44,   44,   44,   44,   41,   42,   44,    0,    0,    0,    0,    0,    0, 
       8,    8,    8,    8,    8,    8,    8,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      44,   40,   40,   43,   43,   41,   42,   41,   42,   41,   42,   41,   42,   41,   42,   41, 
      42,   41,   42,   41,   42,   44,   44,   41,   42,   44,   44,   44,   44,   43,   43,   43, 
      44,   44,   44,    0,   44,   44,   44,   44,   40,   41,   42,   41,   42,   41,   42,   44, 
      44,   44,   48,   40,   48,   48,   48,    0,   44,   49,   44,   44,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 4129, 


    // block 67
       0,   44,   44,   44,   49,   44,   44,   44,   41,   42,   44,   48,   44,   40,   44,   44, 
    1040, 1040, 1040, 1040, 1040, 1040, 1040, 1040, 1040, 1040,   44,   44,   48,   48,   48,   44, 
      44, 1216, 1216, 1216, 1216, 1216, 1216,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,   41,   44,   42,   50,   43, 
      50, 1345, 1345, 1345, 1345, 1345, 1345,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,   41,   48,   42,   48,   41, 
      42,   44,   41,   42,   44,   44,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      67,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   67,   67, 
    4164,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0, 
       0,    0,   68,   68,   68,   68,   68,   68,    0,    0,   68,   68,   68,   68,   68,   68, 
       0,    0,   68,   68,   68,   68,   68,   68,    0,    0,   68,   68,   68,    0,    0,    0, 
      49,   49,   48,   50,   51,   49,   49,    0,   51,   48,   48,   48,   48,   51,   51,    0, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096,   33,   33,   33,   51,   51, 2048, 2048, 


    // block 68
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   68,   68,    0,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0, 


    // block 69
      44,   44,   51,    0,    0,    0,    0,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,   18,   18,    0,    0,    0,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   18,   18,   18,   18,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   18,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    8,    0,    0, 


    // block 70
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 71
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0, 
      18,   18,   18,   18,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   81,   68,   68,   68,   68,   68,   68,   68,   68,   81,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,   44, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,    0,    0,    0,    0,   68,   68,   68,   68,   68,   68,   68,   68, 
      44,   81,   81,   81,   81,   81,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 72
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 73
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 74
      68,   68,   68,   68,   68,   68,    0,    0,   68,    0,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,    0,   68,   68,    0,    0,    0,   68,    0,    0,   68, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 75
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   18,   18,   18,   18,    0,    0,    0,    0,    0,   44, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,   44, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 76
      68,   72,   72,   72,    0,   72,   72,    0,    0,    0,    0,    0,   72,   72,   72,   72, 
      68,   68,   68,   68,    0,   68,   68,   68,    0,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,    0,    0,    0,    0,    8,    8,    8,    0,    0,    0,    0,    8, 
      18,   18,   18,   18,   18,   18,   18,   18,    0,    0,    0,    0,    0,    0,    0,    0, 
      44,   44,   44,   44,   44,   44,   44,   44,   44,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 77
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 78
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 79
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 80
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 81
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81,   81, 
      81,   81,   81,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      44,   44,   44,   44,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 82
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 


    // block 83
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 84
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 85
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,    0,    0,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   10,   10,    8,    8,    8,   51,   51,   51,   10,   10,   10, 
      10,   10,   10, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129,    8,    8,    8,    8,    8, 
       8,    8,    8,   51,   51,    8,    8,    8,    8,    8,    8,    8,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    8,    8,    8,    8,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 86
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,    8,    8,    8,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 87
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
      18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18,   18, 
      18,   18,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 88
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321, 
     321,  321,  321,  321,  321,    0,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  192,    0,  192,  192, 
       0,    0,  192,    0,    0,  192,  192,    0,    0,  192,  192,  192,  192,    0,  192,  192, 
     192,  192,  192,  192,  192,  192,  321,  321,  321,  321,    0,  321,    0,  321,  321,  321, 
     321,  321,  321,  321,    0,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 


    // block 89
     321,  321,  321,  321,  192,  192,    0,  192,  192,  192,  192,    0,    0,  192,  192,  192, 
     192,  192,  192,  192,  192,    0,  192,  192,  192,  192,  192,  192,  192,    0,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,    0,  192,  192,  192,  192,    0, 
     192,  192,  192,  192,  192,    0,  192,    0,    0,    0,  192,  192,  192,  192,  192,  192, 
     192,    0,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 


    // block 90
     321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,    0,    0,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,   48,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,   48,  321,  321,  321,  321, 
     321,  321,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,   48,  321,  321,  321,  321, 


    // block 91
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,   48,  321,  321,  321,  321,  321,  321,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,   48,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,   48, 
     321,  321,  321,  321,  321,  321,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,   48, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,   48,  321,  321,  321,  321,  321,  321, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192,  192, 
     192,  192,  192,  192,  192,  192,  192,  192,  192,   48,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321,  321, 
     321,  321,  321,   48,  321,  321,  321,  321,  321,  321,  192,  321,    0,    0,   16,   16, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 
      16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16,   16, 


    // block 92
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,    0,    0,    0,    0, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51,   51, 
      51,   51,   51,   51,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 93
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 2048, 2048, 


    // block 94
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 95
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64,   64, 
      64,   64,   64,   64,   64,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 96
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68, 
      68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,   68,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
       0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 


    // block 97
    4096, 4129, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 
    4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 
    4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 
    4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 
    4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 
    4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 4129, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 


    // block 98
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 4104, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 


    // block 99
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 
    4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 4096, 


    // block 100
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 
      34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34,   34, 2048, 2048
    };

    inline ::boost::uint16_t category_lookup(::boost::uint32_t ch)
    {
        ::boost::uint32_t block_offset = category_stage1[ch / 256] * 256;
        return category_stage2[block_offset + ch % 256];
    }

}}}} // namespace boost::spirit::unicode::detail
