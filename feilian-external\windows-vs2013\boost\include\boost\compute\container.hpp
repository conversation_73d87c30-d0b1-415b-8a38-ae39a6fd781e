//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_CONTAINER_HPP
#define BOOST_COMPUTE_CONTAINER_HPP

/// \file
///
/// Meta-header to include all Boost.Compute container headers.

#include <boost/compute/container/array.hpp>
#include <boost/compute/container/basic_string.hpp>
#include <boost/compute/container/dynamic_bitset.hpp>
#include <boost/compute/container/flat_map.hpp>
#include <boost/compute/container/flat_set.hpp>
#include <boost/compute/container/mapped_view.hpp>
#include <boost/compute/container/string.hpp>
#include <boost/compute/container/vector.hpp>

#endif // BOOST_COMPUTE_CONTAINER_HPP
