/**
 * -*- c++ -*-
 *
 * \file operations.hpp
 *
 * \brief This header includes several headers from the operation directory.
 *
 * Copyright (c) 2009, <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0. (See
 * accompanying file LICENSE_1_0.txt or copy at
 * http://www.boost.org/LICENSE_1_0.txt)
 *
 * \author <PERSON><PERSON> (guwi17 at gmx dot de)
 */

#ifndef BOOST_NUMERIC_UBLAS_OPERATIONS_HPP
#define BOOST_NUMERIC_UBLAS_OPERATIONS_HPP

#include <boost/numeric/ublas/operation/begin.hpp>
#include <boost/numeric/ublas/operation/end.hpp>
#include <boost/numeric/ublas/operation/num_columns.hpp>
#include <boost/numeric/ublas/operation/num_rows.hpp>
#include <boost/numeric/ublas/operation/size.hpp>

#endif
