﻿#include "feilian-specialmodule/particle/integration/AB3AM4.h"

namespace Particle
{
AB3AM4::AB3AM4()
{
	AB3_coeff = { 23.0 / 12.0, 16.0 / 12.0, 5.0 / 12.0 };
	AM4_coeff = { 9.0 / 24.0, 19.0 / 24.0, 5.0 / 24.0, 1.0 / 24.0 };
}

AB3AM4::AB3AM4(const Vector &pos, const Vector &vel, Vector &x0, std::vector<Vector> &v0, std::vector<Vector> &a0 )
{
	v0.resize(3, Vector0);
	a0.resize(3, Vector0);

	x0 = pos;
	v0[0] = vel;

    AB3_coeff = { 23.0 / 12.0, 16.0 / 12.0, 5.0 / 12.0 };
    AM4_coeff = { 9.0 / 24.0, 19.0 / 24.0, 5.0 / 24.0, 1.0 / 24.0 };
}

void AB3AM4::Predict(const <PERSON><PERSON><PERSON> &dt, const Vector &x0, const std::vector<Vector> &v0, const std::vector<Vector> &a0, Vector &pos, Vector &vel)
{
    // 预测位置和速度
	pos = x0 + (AB3_coeff[0] * v0[0] - AB3_coeff[1] * v0[1] + AB3_coeff[2] * v0[2]) * dt;
	vel = v0[0] + (AB3_coeff[0] * a0[0] - AB3_coeff[1] * a0[1] + AB3_coeff[2] * a0[2]) * dt;
}

void AB3AM4::Correct(Vector &acc, const Scalar &dt, Vector &pos, Vector &vel, Vector &x0, std::vector<Vector> &v0, std::vector<Vector> &a0, const bool &flag)
{
	// 计算修正后的位置和速度
	Vector c_pos = x0 + (AM4_coeff[0] * vel + AM4_coeff[1] * v0[0] - AM4_coeff[2] * v0[1] + AM4_coeff[3] * v0[2]) * dt;
	Vector c_vel = flag ? vel : v0[0] + (AM4_coeff[0] * acc + AM4_coeff[1] * a0[0] - AM4_coeff[2] * a0[1] + AM4_coeff[3] * a0[2]) * dt;

    // 更新下一步计算所需变量
	x0 = c_pos;
    
	v0[2] = v0[1];
	v0[1] = v0[0];
	v0[0] = c_vel;
    
	a0[2] = a0[1];
	a0[1] = a0[0];
	a0[0] = acc;
    
    // 更新位置和速度     
    pos = c_pos;
    vel = c_vel;
}

} // namespace Particle