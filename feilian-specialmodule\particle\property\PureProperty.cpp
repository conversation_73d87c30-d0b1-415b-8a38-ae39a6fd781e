﻿#include "feilian-specialmodule/particle/property/PureProperty.h"

namespace Particle
{

PureProperty::PureProperty()
{
    this->density   = Scalar0;
    this->YoungModulus = Scalar0;
    this->poissonsRatio = Scalar0;
    this->yeildStress = Scalar0;
    this->shearModulus = Scalar0;
    this->kappa = Scalar0;

    this->radius = Scalar0;
    this->Calculate();
}

PureProperty::PureProperty(const Scalar &density_, const Scalar &YoungModulus_,
                           const Scalar &poissonsRatio_, const Scalar &yeildStress_)
{
    this->density   = density_;
    this->YoungModulus = YoungModulus_;
    this->poissonsRatio = poissonsRatio_;
    this->yeildStress = yeildStress_;
    this->shearModulus = YoungModulus_ / (2.0 * (1.0 + poissonsRatio_));
    this->kappa = (1.0 - poissonsRatio_) / (1.0 - 0.5 * poissonsRatio_);
    
    this->radius = Scalar0;
    this->Calculate();
}

PureProperty::PureProperty(const Configure::Particle::ParticleConfigure &options, const int &dim)
{
    this->density   = options.material.density;
    this->YoungModulus = options.material.YoungModulus;
    this->poissonsRatio = options.material.PoissonRatio;
    this->yeildStress = options.material.yeildStress;
    this->shearModulus = this->YoungModulus / (2.0 * (1.0 + this->poissonsRatio));
    this->kappa = (1.0 - this->poissonsRatio) / (1.0 - 0.5 * this->poissonsRatio);
    
	this->dimension = dim;

    this->radius = Scalar0;
    this->Calculate();
}

void PureProperty::Calculate()
{
	if (this->dimension == 3)
	{
		this->volume = 4.0 / 3.0 * PI * this->radius * this->radius * this->radius;
		this->mass = this->density * this->volume;
		this->inertia = 0.4 * this->mass * this->radius * this->radius;
	}
	else
	{
		this->volume = PI * this->radius * this->radius;
		this->mass = this->density * this->volume;
		this->inertia = 0.5 * this->mass * this->radius * this->radius;
	}
}

} // namespace Particle