import numpy as np
#import pandas as pd
#import os.path
#import sys

#from Data_driven_relevant_func import is_number, get_coor_feat_targ_resu_columns, \
#                                      NN_make_model, RF_make_model

# 计时
import time

from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
import joblib    # 用来存储随机森林模型

def is_number(str):
    # 文件读入, 判定是否为数字行
    try:
    # 使用float有一个例外是'NaN',为保证严密单独判断
        if str == 'NaN':
            return False
        float(str)
        return True
    except ValueError:
        return False

def RF_make_model(RF_type, 
                  N_estimators, 
                  Criterion, 
                  Max_features):
    # 产生随机森林的模型
    # RF_type=1: 回归模型
    #        =2: 分类模型(用于symbol)
    
    from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
    
    if RF_type==1:
        model = RandomForestRegressor(n_estimators = N_estimators, 
                                      criterion = Criterion, 
                                      max_features = Max_features,
                                      verbose = 1,
                                      n_jobs = 56)
    elif RF_type==2:
        model = RandomForestClassifier(n_estimators = N_estimators, 
                                       criterion = Criterion, 
                                       max_features = Max_features)
    return model

#print('程序历时：0s')
tick = time.perf_counter()

Location_Case = './'

#print('程序历时：'+str(time.perf_counter()-tick)+'s')
print("读入训练集特征")
print("1. 读入训练集输入特征")
# 定义X_train_sep的list
GroupNum_train = 2
N_input = 6
Location_train_input = ['char' for i in range(GroupNum_train)]
Location_train_input[0] = Location_Case + 'data/4deg/'
Location_train_input[1] = Location_Case + 'data/6deg/'


X_train_sep = [0 for i in range(GroupNum_train)]
    
for i in range(GroupNum_train):
    print('训练集', i)
    # 初始化data并读入输入特征
    data = np.empty((0,N_input))
    file_name = Location_train_input[i] + 'X_feature.dat'
    with open(file_name,'r') as f:
        line = f.readline().strip()
        while line:
            linestr = line.split( )
            line = f.readline().strip()
            while is_number(linestr[0]):
               a = linestr
               a = np.array([float(i) for i in a]).reshape((1,N_input))
               data = np.append(data, a, axis=0)
               break
    # 把data的值存入X_train_sep中
    X_train_sep[i] = data

# 获取X_train_sep[i]的维度定义Group_Len
Group_Len = [0 for i in range(GroupNum_train)]
for i in range(GroupNum_train):
    Group_Len[i] = len(X_train_sep[i])
# 将X_train_sep纵向合并得到X_train
X_train = X_train_sep[0]
for i in range(GroupNum_train-1):
    X_train = np.concatenate((X_train, X_train_sep[i+1]), axis=0) 

print('程序历时：'+str(time.perf_counter()-tick)+'s')

print("2. 读入训练集预测特征")
N_output = 1

mutTrue_sep = [0 for i in range(GroupNum_train)]
for i in range(GroupNum_train):
    print('mutTrue', i)
    data = np.empty((0,N_output))
    file_name = Location_train_input[i] + 'mutTrue.dat'
    with open(file_name,'r') as f:
        line = f.readline().strip()
        while line:
            linestr = line.split( )
            line = f.readline().strip()
            while is_number(linestr[0]):
                a = linestr
                a = np.array([float(i) for i in a]).reshape((1,N_output))
                data = np.append(data, a, axis=0)
                break
    mutTrue_sep[i] = data

mutTrue = mutTrue_sep[0]
for i in range(GroupNum_train-1):
    mutTrue = np.concatenate((mutTrue, mutTrue_sep[i+1]), axis=0) 

mutBase_sep = [0 for i in range(GroupNum_train)]
for i in range(GroupNum_train):
    print('mutBase', i)
    data = np.empty((0,N_output))
    file_name = Location_train_input[i] + 'mutBase.dat'
    with open(file_name,'r') as f:
        line = f.readline().strip()
        while line:
            linestr = line.split( )
            line = f.readline().strip()
            while is_number(linestr[0]):
                a = linestr
                a = np.array([float(i) for i in a]).reshape((1,N_output))
                data = np.append(data, a, axis=0)
                break
    mutBase_sep[i] = data

# 将Y_train_sep纵向合并得到Y_train
mutBase = mutBase_sep[0]
for i in range(GroupNum_train-1):
    mutBase = np.concatenate((mutBase, mutBase_sep[i+1]), axis=0) 

print('计算Y_train(delta_log_mut)')
Y_train = mutTrue
samplesNumb = mutTrue.shape[0]
for j in range(samplesNumb):
    #print(mutBase[j][0])
    mutTrueTemp = np.maximum(mutTrue[j][0],1.e-15)
    mutBaseTemp = np.maximum(mutBase[j][0],1.e-15)
    Y_train[j][0] = np.log(mutTrueTemp / mutBaseTemp)

print('程序历时：'+str(time.perf_counter()-tick)+'s')

print('从文件中读入随机森林结构，并且进行训练')
# 创建存储best_RF实例的list
Best_RF = [0 for i in range(N_output)]
for i in range(N_output):
    print('森林：delta_log_mut')
    # 从文件中读入参数
    #with open(Location_Case+'RF_parameters.txt') as f:
    #    data_c = f.read().splitlines()
    #    best_RF_type = int(data_c[1])
    #    best_N_estimators = int(data_c[3])
    #    best_Criterion = data_c[5]
    #    best_Max_features = data_c[7]
    best_RF_type = 1
    best_N_estimators = 300
    best_Criterion = 'absolute_error'
    best_Max_features = 'log2'
    # 定义模型
    Best_RF[i] = RF_make_model(best_RF_type, best_N_estimators, best_Criterion, best_Max_features)
    # 重新训练
    Best_RF[i].fit(X_train, Y_train[:,i])

# 训练后重要性排序输出
#OutputFile = Location_Case+'RF_Feature_Importance'+'.txt'
#with open(OutputFile, 'w') as f:
#    char = 'variables = No. '
#    for i in range(len(var_output)):
#        char = char + var_output[i] + ' '
#    f.write(char+'\n')
#    for i in range(X_train.shape[1]):  # 这里统一用了X_train的特征数，如果每个网络使用的特征不一致会出问题
#        list_temp = []
#        list_temp.append(i+1)
#        for j in range(N_output):
#            list_temp.append(Best_RF[j].feature_importances_[i])
#        char = ' '.join(str(ii) for ii in list_temp)
#        f.write(char+'\n')

# 训练后随机森林权重输出
for i in range(N_output):
    joblib.dump(Best_RF[i],Location_Case+'RF_weights.pkl')

print('训练完成')
print('程序历时：'+str(time.perf_counter()-tick)+'s')
