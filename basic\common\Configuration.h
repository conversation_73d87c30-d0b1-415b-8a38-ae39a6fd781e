﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Configuration.h
//! <AUTHOR>
//! @brief 基本常量数据.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_PlatformOS_
#define _basic_common_PlatformOS_

#define SMALL   1e-15 ///< 实型无穷小量
#define INF     1e30  ///< 实型无穷大量

#define ZERO    0.0 ///< 实型值0.0
#define ONE     1.0 ///< 实型值1.0

#define PI      3.141592653589793238462643383279 ///< pi值
#define SIGMASB 5.67e-8 ///< Stefan-Boltzmann Constant

typedef double  Scalar; ///< 标量
#define Scalar0 ((double)0.0) ///< 标量零

typedef int  Int; ///< 有符号整型
typedef long unsigned int  Index; ///< 无符号整型

#define NOMINMAX ///< Disable VS default min and max

#endif
