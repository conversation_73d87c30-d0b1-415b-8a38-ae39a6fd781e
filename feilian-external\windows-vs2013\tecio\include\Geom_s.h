 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <boost/foreach.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "SzlFileLoader.h"
#include "fileio.h"
namespace tecplot { namespace tecioszl { struct ___1556 : public tecplot::___3933::___1556 { public: static ___1556 invalidGeom() { return ___1556( 0.0, 0.0, 0.0, ___663, ___1305, 1, ___364, ___4455, ___1305, GeomType_Invalid, ___2271, 2.0, 0.1, 72, ___192, ___181, 5.0, 12.0, ___3446, ___498, std::vector<std::vector<tecplot::___3933::___4580> >(), "" ); } ___1556( double ___4574, double ___4591, double ___4715, CoordSys_e ___3159, ___372 ___2004, ___1172 zone, ___516 color, ___516 ___1411, ___372 ___2022, GeomType_e ___1651, LinePattern_e ___2263, double ___2986, double ___2289, uint16_t ___2793, ArrowheadStyle_e arrowheadStyle, ArrowheadAttachment_e arrowheadAttachment, double arrowheadSize, double arrowheadAngle, Scope_e ___3442, Clipping_e ___495, std::vector<std::vector<tecplot::___3933::___4580> > const& ___1572, std::string const& ___2330) : tecplot::___3933::___1556(___4574, ___4591, ___4715, ___3159, ___2004, zone, color, ___1411, ___2022, ___1651, ___2263, ___2986, ___2289, ___2793, arrowheadStyle, arrowheadAttachment, arrowheadSize, arrowheadAngle, ___3442, ___495, ___1572, ___2330) {} bool ___2067() { return VALID_ENUM(___2616, CoordSys_e) && VALID_ENUM(___2466, GeomType_e); } void writeToFile(___3933::FileWriterInterface& outputFile, bool ___4480) const { writeScalar(outputFile, ___2617[0], ___4480); writeScalar(outputFile, ___2617[1], ___4480); writeScalar(outputFile, ___2617[2], ___4480); writeScalar(outputFile, (uint32_t)___2616, ___4480); writeScalar(outputFile, ___2484, ___4480); writeScalar(outputFile, ___2677, ___4480); writeScalar(outputFile, ___2395, ___4480); writeScalar(outputFile, ___2462, ___4480); writeScalar(outputFile, ___2486, ___4480); writeScalar(outputFile, (uint32_t)___2466, ___4480); writeScalar(outputFile, (uint32_t)___2489, ___4480); writeScalar(outputFile, ___2615, ___4480); writeScalar(outputFile, ___2490, ___4480); writeScalar(outputFile, ___2502, ___4480); writeScalar(outputFile, (uint32_t)___2343, ___4480); writeScalar(outputFile, (uint32_t)___2341, ___4480); writeScalar(outputFile, ___2342, ___4480); writeScalar(outputFile, ___2340, ___4480); writeScalar(outputFile, (uint32_t)___2619, ___4480); writeScalar(outputFile, (uint32_t)___2394, ___4480); writeScalar(outputFile, (uint64_t)___2464.size(), ___4480); BOOST_FOREACH(std::vector<tecplot::___3933::___4580> const& vec, ___2464) { writeScalar(outputFile, (uint64_t)vec.size(), ___4480); BOOST_FOREACH(tecplot::___3933::___4580 const& xyz, vec) { writeScalar(outputFile, xyz.x(), ___4480); writeScalar(outputFile, xyz.___4583(), ___4480); writeScalar(outputFile, xyz.z(), ___4480); } } ___4544(outputFile, ___2491, ___4480); } uint64_t sizeInFile(bool ___4480) const { uint64_t sizeInFile = 0; sizeInFile += scalarSizeInFile(___2617[0], ___4480); sizeInFile += scalarSizeInFile(___2617[1], ___4480); sizeInFile += scalarSizeInFile(___2617[2], ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2616, ___4480); sizeInFile += scalarSizeInFile(___2484, ___4480); sizeInFile += scalarSizeInFile(___2677, ___4480); sizeInFile += scalarSizeInFile(___2395, ___4480); sizeInFile += scalarSizeInFile(___2462, ___4480); sizeInFile += scalarSizeInFile(___2486, ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2466, ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2489, ___4480); sizeInFile += scalarSizeInFile(___2615, ___4480); sizeInFile += scalarSizeInFile(___2490, ___4480); sizeInFile += scalarSizeInFile(___2502, ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2343, ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2341, ___4480); sizeInFile += scalarSizeInFile(___2342, ___4480); sizeInFile += scalarSizeInFile(___2340, ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2619, ___4480); sizeInFile += scalarSizeInFile((uint32_t)___2394, ___4480); sizeInFile += scalarSizeInFile((uint64_t)___2464.size(), ___4480); BOOST_FOREACH(std::vector<tecplot::___3933::___4580> const& vec, ___2464) { sizeInFile += scalarSizeInFile((uint64_t)vec.size(), ___4480); BOOST_FOREACH(tecplot::___3933::___4580 const& xyz, vec) { sizeInFile += scalarSizeInFile(xyz.x(), ___4480); sizeInFile += scalarSizeInFile(xyz.___4583(), ___4480); sizeInFile += scalarSizeInFile(xyz.z(), ___4480); } } sizeInFile += stringSizeInFile(___2491, ___4480); return sizeInFile; } ___1556(___3933::___1399& inputFile, bool readASCII) { readScalar(inputFile, ___2617[0], readASCII); readScalar(inputFile, ___2617[1], readASCII);
readScalar(inputFile, ___2617[2], readASCII); READ_ENUM(___2616, CoordSys_e, inputFile, readASCII); readScalar(inputFile, ___2484, readASCII); readScalar(inputFile, ___2677, readASCII); readScalar(inputFile, ___2395, readASCII); readScalar(inputFile, ___2462, readASCII); readScalar(inputFile, ___2486, readASCII); READ_ENUM(___2466, GeomType_e, inputFile, readASCII); READ_ENUM(___2489, LinePattern_e, inputFile, readASCII); readScalar(inputFile, ___2615, readASCII); readScalar(inputFile, ___2490, readASCII); readScalar(inputFile, ___2502, readASCII); READ_ENUM(___2343, ArrowheadStyle_e, inputFile, readASCII); READ_ENUM(___2341, ArrowheadAttachment_e, inputFile, readASCII); readScalar(inputFile, ___2342, readASCII); readScalar(inputFile, ___2340, readASCII); READ_ENUM(___2619, Scope_e, inputFile, readASCII); READ_ENUM(___2394, Clipping_e, inputFile, readASCII); uint64_t length; readScalar(inputFile, length, readASCII); ___2464.resize((size_t)length); for(size_t i = 0; i < ___2464.size(); ++i) { readScalar(inputFile, length, readASCII); ___2464[i].reserve((size_t)length); double x, ___4583, z; for(uint64_t ___2105 = 0; ___2105 < length; ++___2105) { readScalar(inputFile, x, readASCII); readScalar(inputFile, ___4583, readASCII); readScalar(inputFile, z, readASCII); ___2464[i].push_back(___3933::___4580(x, ___4583, z)); } } readString(inputFile, ___2491, readASCII); } }; }}
