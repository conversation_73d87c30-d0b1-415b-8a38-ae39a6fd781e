﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceBase.h
//! <AUTHOR>
//! @brief 壁面距离基类
//! @date  2022-11-07
//
//------------------------------修改日志----------------------------------------
//
// 2022-11-07 李艳亮、乔龙
// 说明：建立并规范化
//
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_WallDistanceBase_
#define _meshProcess_wallDistance_WallDistanceBase_

#include "basic/mesh/Mesh.h"

/**
 * @brief 近壁面距离基类
 * 
 */
class WallDistanceBase
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in, out] mesh 网格指针
	* @param[in] wallBoundaryFace_ 物面面元容器（包含面元信息和构成面的点坐标）
	*/
    WallDistanceBase(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_);
    
	/**
	* @brief 虚析构函数
	*
	*/
    virtual ~WallDistanceBase() = 0;

	/**
	* @brief 计算壁面距离（虚函数）
	*
	*/
    virtual void Calculate() = 0;

protected:
	/**
	* @brief 计算单元体心到面的距离
	*
	* @param[in] elementID 单元编号
	* @param[in] faceID 面在面元容器中的序列号
	* @param[in] project 是否按投影方式计算 
	             false: 直接计算到面心的距离
	             true: 按投影点计算距离，如投影点在多边形外部，按到各顶点计算距离取最小值
	* @return Scalar
	*/
    Scalar CalculateFromElementTOFace(const int &elementID, const int &faceID, const bool &project);
    
protected:
	Mesh* pmesh; ///< 网格指针
	const std::vector<std::pair<Face, std::vector<Node>>> &WallBoundaryFace; ///< 物面面元容器（包含面元信息和构成面的点坐标）
	std::vector<Scalar> &wallDistance; ///< 存放壁面距离的容器
	const int elementSize; ///< 网格的单元数量（真实）
};

#endif
