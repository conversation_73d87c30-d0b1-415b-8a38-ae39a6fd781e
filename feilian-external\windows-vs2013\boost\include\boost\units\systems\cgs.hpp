// Boost.Units - A C++ library for zero-overhead dimensional analysis and 
// unit/quantity manipulation and conversion
//
// Copyright (C) 2003-2008 <PERSON>
// Copyright (C) 2008 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_UNITS_CGS_HPP
#define BOOST_UNITS_CGS_HPP

/// \file
/// Includes all the cgs unit headers

#include <string>

#include <boost/units/quantity.hpp>

#include <boost/units/systems/cgs/base.hpp>

#include <boost/units/systems/cgs/dimensionless.hpp>
#include <boost/units/systems/cgs/length.hpp>
#include <boost/units/systems/cgs/mass.hpp>
#include <boost/units/systems/cgs/time.hpp>

#include <boost/units/systems/cgs/acceleration.hpp>
#include <boost/units/systems/cgs/area.hpp>
#include <boost/units/systems/cgs/current.hpp>
#include <boost/units/systems/cgs/dynamic_viscosity.hpp>
#include <boost/units/systems/cgs/energy.hpp>
#include <boost/units/systems/cgs/force.hpp>
#include <boost/units/systems/cgs/frequency.hpp>
#include <boost/units/systems/cgs/kinematic_viscosity.hpp>
#include <boost/units/systems/cgs/mass_density.hpp>
#include <boost/units/systems/cgs/momentum.hpp>
#include <boost/units/systems/cgs/power.hpp>
#include <boost/units/systems/cgs/pressure.hpp>
#include <boost/units/systems/cgs/velocity.hpp>
#include <boost/units/systems/cgs/volume.hpp>
#include <boost/units/systems/cgs/wavenumber.hpp>

#endif // BOOST_UNITS_CGS_HPP
