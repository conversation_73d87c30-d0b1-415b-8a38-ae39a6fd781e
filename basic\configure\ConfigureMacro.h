﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ConfigureMacro.h
//! <AUTHOR>
//! @brief 流场解算器相关枚举
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-31 乔龙
//     说明：添加注释
//
// 2021-03-29 乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _basic_configure_ConfigureMacro_
#define _basic_configure_ConfigureMacro_

/**
 * @brief 多重网格命名空间
 * 
 */
namespace MultigridType
{
/**
 * @brief 多重网格循环类型
 * 
 */
enum Type
{
    NONE_MULTIGRID = 0, ///< 不采用多重网格
    V, ///< V循环，粗网格计算一次
    W ///< W循环，粗网格计算一次
};

/**
 * @brief 多重网格限制方法
 * 
 */
enum TransferOperator
{
    NONE_TRANSFER = 0, ///< 不插值
    INJECTION, ///< 注入式插值
    LINEAR, ///< 线性插值
    LINEAR_LIMITER ///< 带限制器的线性插值
};

}

/**
 * @brief 残值光顺命名空间
 * 
 */
namespace Smoother
{
/**
 * @brief 光顺类型枚举
 * 
 */
enum Scheme
{
    NONE_SMOOTH = 0, ///< 不光顺
    CONSTANT, ///< 常系数光顺
    DISTANCE_WEIGHT ///< 距离加权光顺
};

} // namespace Smoother

/**
 * @brief 场运算命名空间
 * 
 */
namespace FieldManipulation
{
/**
 * @brief 梯度计算格式枚举
 * 
 */
enum GradientScheme
{
    GREEN_GAUSS = 0, ///< 采用Green-Gauss方法计算梯度
    GREEN_GAUSS_M1, ///< 采用Green-Gauss方法计算梯度，投影修正1
    GREEN_GAUSS_M2, ///< 采用Green-Gauss方法计算梯度，投影修正2
    GREEN_GAUSS_M3, ///< 采用Green-Gauss方法计算梯度，距离加权修正
    LEAST_SQUARE, ///< 采用最小二乘法方法计算梯度，面相邻单元
    LEAST_SQUARE_VERTEX ///< 采用最小二乘法方法计算梯度，点相邻单元
};
};

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 壁面距离计算方法枚举
 * 
 */
enum WallDistance
{
    NONE_WALL_DISTANCE = 0, ///< 不计算壁面距离
    TRAVERSE, ///< 穷举法
    ADT, ///< 修改的ADT方法
	KDT
};
}

/**
 * @brief 前处理命名空间
 * 
 */
namespace Preprocessor
{
/**
 * @brief 网格类型枚举
 * 
 */
enum MeshType
{
    FLUENT = 0, ///< Fluent msh格式网格
    CGNS, ///< cgns格式网格
    ADLG, ///< 十进制对偶网格
    BDLG ///< 二进制对偶网格
};

/**
 * @brief 多重网格聚合方法枚举
 * 
 */
enum AgglomerateType
{
    NONE_AGGLOMERATE = 0, ///< 不聚合
    MGRIDGEN, ///< 采用MGridGen库进行聚合
    SEED ///< 采用Seed方法聚合
};

/**
 * @brief 分区方法枚举
 * 
 */
enum DecomposeType
{
    NONE_DECOMPOSE = 0, ///< 不分区
    METIS ///< 采用metis软件进行分区
};

/**
 * @brief 网格单元排序枚举
 * 
 */
enum RenumberType
{
    NONE_RENUMBER = 0, ///< 不进行网格单元排序
    RCM ///<采用RCM方法进行网格单元排序
};
} // namespace Preprocessor

/**
 * @brief 后处理命名空间
 * 
 */
namespace Post
{
/**
 * @brief 后处理输出结果类型枚举
 * 
 */
enum Type
{
    TECPLOT = 0, ///< 后处理输出结果为Tecplot格式
    ENSIGHT,  ///< 后处理输出结果为Ensight格式
	VTK,  ///< 后处理输出结果为VTK格式
	CGNS,  ///< 后处理输出结果为CGNS格式
	AUTO ///< 自动判断输出类型
};

/**
 * @brief 物理量存储在原始网格中的位置枚举
 * 
 */
enum Position
{
    CELL_CENTER = 0, ///< 物理量存储在格心
    CELL_VERTICE ///< 物理量存储在格点
};
} // namespace Post

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 边界条件类型
 * 
 */
enum Type
{
    INTERIOR = 0, ///< 内部面
    EXTRAPOLATION, ///< 外推边界
    SYMMETRY, ///< 对称边界
    FARFIELD, ///< 远场边界

	PERIODIC = 10, ///< 周期性边界
	OVERSET,  ///< 重叠网格边界

    INFLOW_SPECIFY = 20, ///< 来流指定边界
    INFLOW_TOTAL_CONDITION, ///< 来流总条件边界
    INFLOW_TOTAL_CONDITION_MACH, ///< 来流总条件边界（含来流马赫数）
    MASSFLOW_INLET, ///< 质量流量入口
    MASSFLOW_OUTLET, ///< 质量流量出口
    OUTFLOW_PRESSURE, ///< 出流压力边界
	NACELLE_INLET, ///< 短舱入口边界（计算域出口）
	NACELLE_EXHAUST, ///< 短舱出口边界（计算域入口）

    WALL = 100, ///< 壁面边界从100开始
    WALL_ADIABATIC, ///< 绝热壁面
    WALL_ISOTHERMAL, ///< 等温壁面
    WALL_HEAT_FLUX, ///< 指定热通量壁面
    WALL_HEATFLUX_FILE, ///< 从文件读取热通量
    Wall_TEMPERATURE_FILE, ///< 从文件读取温度

	WALL_MOVING = 200, ///< 运动壁面（绝热）
	WALL_SLIPPING, ///< 滑移壁面（绝热）
    WALL_ROTATE, ///< 旋转壁面

    WALL_RIBLETS, ///< 微型凹槽壁面
    
	SYNTHETIC_JET = 300, ///< 合成射流边界
    
	WIND_TUNNEL_WALL_PRESSURE = 400 ///< 风洞壁压边界条件

};
} // namespace Boundary

namespace OversetType
{
	/**
	* @brief 重叠插值方法枚举
	*
	*/
	enum InterpolationType
	{
		InverseDistance = 0, //反距离加权
		Linear = 1,  // 线性插值
		LeastSquare = 2 //最小二乘
	};
}

namespace MotionType
{
	/**
	* @brief 运动类型枚举
	*
	*/
	enum Type
	{
		STATIONARY = 0, ///< 静止，无运动
		TRANSLATION, ///< 刚体平移
		ROTATION, ///< 刚体旋转
		TRANSLATION_ROTATION, ///< 刚体平移+旋转
		XDOF, ///< 刚体x自由度运动
		MORPHING ///< 变形运动
	};
}
#endif