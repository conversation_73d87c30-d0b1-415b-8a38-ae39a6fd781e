namespace Eigen {

/** \page Eigen2ToEigen3 Porting from Eigen2 to Eigen3

This page lists the most important API changes between Eigen2 and Eigen3,
and gives tips to help porting your application from Eigen2 to Eigen3.

\eigenAutoToc

\section CompatibilitySupport Eigen2 compatibility support

Up to version 3.2 %Eigen provides <a href="http://eigen.tuxfamily.org/dox-3.2/Eigen2SupportModes.html">Eigen2 support modes</a>. These are removed now, because they were barely used anymore and became hard to maintain after internal re-designs.
You can still use them by first <a href="http://eigen.tuxfamily.org/dox-3.2/Eigen2ToEigen3.html">porting your code to Eigen 3.2</a>.

\section Using The USING_PART_OF_NAMESPACE_EIGEN macro

The USING_PART_OF_NAMESPACE_EIGEN macro has been removed. In Eigen 3, just do:
\code
using namespace Eigen;
\endcode

\section ComplexDot Dot products over complex numbers

This is the single trickiest change between Eigen 2 and Eigen 3. It only affects code using \c std::complex numbers as scalar type.

Eigen 2's dot product was linear in the first variable. Eigen 3's dot product is linear in the second variable. In other words, the Eigen 2 code \code x.dot(y) \endcode is equivalent to the Eigen 3 code \code y.dot(x) \endcode In yet other words, dot products are complex-conjugated in Eigen 3 compared to Eigen 2. The switch to the new convention was commanded by common usage, especially with the notation \f$ x^Ty \f$ for dot products of column-vectors.

\section VectorBlocks Vector blocks

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th></th>
<tr><td>\code
vector.start(length)
vector.start<length>()
vector.end(length)
vector.end<length>()
\endcode</td><td>\code
vector.head(length)
vector.head<length>()
vector.tail(length)
vector.tail<length>()
\endcode</td></tr>
</table>


\section Corners Matrix Corners

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th></th>
<tr><td>\code
matrix.corner(TopLeft,r,c)
matrix.corner(TopRight,r,c)
matrix.corner(BottomLeft,r,c)
matrix.corner(BottomRight,r,c)
matrix.corner<r,c>(TopLeft)
matrix.corner<r,c>(TopRight)
matrix.corner<r,c>(BottomLeft)
matrix.corner<r,c>(BottomRight)
\endcode</td><td>\code
matrix.topLeftCorner(r,c)
matrix.topRightCorner(r,c)
matrix.bottomLeftCorner(r,c)
matrix.bottomRightCorner(r,c)
matrix.topLeftCorner<r,c>()
matrix.topRightCorner<r,c>()
matrix.bottomLeftCorner<r,c>()
matrix.bottomRightCorner<r,c>()
\endcode</td>
</tr>
</table>

Notice that Eigen3 also provides these new convenience methods: topRows(), bottomRows(), leftCols(), rightCols(). See in class DenseBase.

\section CoefficientWiseOperations Coefficient wise operations

In Eigen2, coefficient wise operations which have no proper mathematical definition (as a coefficient wise product)
were achieved using the .cwise() prefix, e.g.:
\code a.cwise() * b \endcode
In Eigen3 this .cwise() prefix has been superseded by a new kind of matrix type called
Array for which all operations are performed coefficient wise. You can easily view a matrix as an array and vice versa using
the MatrixBase::array() and ArrayBase::matrix() functions respectively. Here is an example:
\code
Vector4f a, b, c;
c = a.array() * b.array();
\endcode
Note that the .array() function is not at all a synonym of the deprecated .cwise() prefix.
While the .cwise() prefix changed the behavior of the following operator, the array() function performs
a permanent conversion to the array world. Therefore, for binary operations such as the coefficient wise product,
both sides must be converted to an \em array as in the above example. On the other hand, when you
concatenate multiple coefficient wise operations you only have to do the conversion once, e.g.:
\code
Vector4f a, b, c;
c = a.array().abs().pow(3) * b.array().abs().sin();
\endcode
With Eigen2 you would have written:
\code
c = (a.cwise().abs().cwise().pow(3)).cwise() * (b.cwise().abs().cwise().sin());
\endcode

\section PartAndExtract Triangular and self-adjoint matrices

In Eigen 2 you had to play with the part, extract, and marked functions to deal with triangular and selfadjoint matrices. In Eigen 3, all these functions have been removed in favor of the concept of \em views:

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th></tr>
<tr><td>\code
A.part<UpperTriangular>();
A.part<StrictlyLowerTriangular>(); \endcode</td>
<td>\code
A.triangularView<Upper>()
A.triangularView<StrictlyLower>()\endcode</td></tr>
<tr><td>\code
A.extract<UpperTriangular>();
A.extract<StrictlyLowerTriangular>();\endcode</td>
<td>\code
A.triangularView<Upper>()
A.triangularView<StrictlyLower>()\endcode</td></tr>
<tr><td>\code
A.marked<UpperTriangular>();
A.marked<StrictlyLowerTriangular>();\endcode</td>
<td>\code
A.triangularView<Upper>()
A.triangularView<StrictlyLower>()\endcode</td></tr>
<tr><td colspan="2"></td></tr>
<tr><td>\code
A.part<SelfAdfjoint|UpperTriangular>();
A.extract<SelfAdfjoint|LowerTriangular>();\endcode</td>
<td>\code
A.selfadjointView<Upper>()
A.selfadjointView<Lower>()\endcode</td></tr>
<tr><td colspan="2"></td></tr>
<tr><td>\code
UpperTriangular
LowerTriangular
UnitUpperTriangular
UnitLowerTriangular
StrictlyUpperTriangular
StrictlyLowerTriangular
\endcode</td><td>\code
Upper
Lower
UnitUpper
UnitLower
StrictlyUpper
StrictlyLower
\endcode</td>
</tr>
</table>

\sa class TriangularView, class SelfAdjointView

\section TriangularSolveInPlace Triangular in-place solving

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th></tr>
<tr><td>\code A.triangularSolveInPlace<XxxTriangular>(Y);\endcode</td><td>\code A.triangularView<Xxx>().solveInPlace(Y);\endcode</td></tr>
</table>


\section Decompositions Matrix decompositions

Some of Eigen 2's matrix decompositions have been renamed in Eigen 3, while some others have been removed and are replaced by other decompositions in Eigen 3.

<table class="manual">
  <tr>
    <th>Eigen 2</th>
    <th>Eigen 3</th>
    <th>Notes</th>
  </tr>
  <tr>
    <td>LU</td>
    <td>FullPivLU</td>
    <td class="alt">See also the new PartialPivLU, it's much faster</td>
  </tr>
  <tr>
    <td>QR</td>
    <td>HouseholderQR</td>
    <td class="alt">See also the new ColPivHouseholderQR, it's more reliable</td>
  </tr>
  <tr>
    <td>SVD</td>
    <td>JacobiSVD</td>
    <td class="alt">We currently don't have a bidiagonalizing SVD; of course this is planned.</td>
  </tr>
  <tr>
    <td>EigenSolver and friends</td>
    <td>\code #include<Eigen/Eigenvalues> \endcode </td>
    <td class="alt">Moved to separate module</td>
  </tr>
</table>

\section LinearSolvers Linear solvers

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th><th>Notes</th></tr>
<tr><td>\code A.lu();\endcode</td>
<td>\code A.fullPivLu();\endcode</td>
<td class="alt">Now A.lu() returns a PartialPivLU</td></tr>
<tr><td>\code A.lu().solve(B,&X);\endcode</td>
<td>\code X = A.lu().solve(B);
 X = A.fullPivLu().solve(B);\endcode</td>
<td class="alt">The returned by value is fully optimized</td></tr>
<tr><td>\code A.llt().solve(B,&X);\endcode</td>
<td>\code X = A.llt().solve(B);
 X = A.selfadjointView<Lower>.llt().solve(B);
 X = A.selfadjointView<Upper>.llt().solve(B);\endcode</td>
<td class="alt">The returned by value is fully optimized and \n
the selfadjointView API allows you to select the \n
triangular part to work on (default is lower part)</td></tr>
<tr><td>\code A.llt().solveInPlace(B);\endcode</td>
<td>\code B = A.llt().solve(B);
 B = A.selfadjointView<Lower>.llt().solve(B);
 B = A.selfadjointView<Upper>.llt().solve(B);\endcode</td>
<td class="alt">In place solving</td></tr>
<tr><td>\code A.ldlt().solve(B,&X);\endcode</td>
<td>\code X = A.ldlt().solve(B);
 X = A.selfadjointView<Lower>.ldlt().solve(B);
 X = A.selfadjointView<Upper>.ldlt().solve(B);\endcode</td>
<td class="alt">The returned by value is fully optimized and \n
the selfadjointView API allows you to select the \n
triangular part to work on</td></tr>
</table>

\section GeometryModule Changes in the Geometry module

The Geometry module is the one that changed the most. If you rely heavily on it, it's probably a good idea to use the <a href="http://eigen.tuxfamily.org/dox-3.2/Eigen2SupportModes.html">"Eigen 2 support modes"</a> to perform your migration.

\section Transform The Transform class

In Eigen 2, the Transform class didn't really know whether it was a projective or affine transformation. In Eigen 3, it takes a new \a Mode template parameter, which indicates whether it's \a Projective or \a Affine transform. There is no default value.

The Transform3f (etc) typedefs are no more. In Eigen 3, the Transform typedefs explicitly refer to the \a Projective and \a Affine modes:

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th><th>Notes</th></tr>
<tr>
  <td> Transform3f </td>
  <td> Affine3f or Projective3f </td>
  <td> Of course 3f is just an example here </td>
</tr>
</table>


\section LazyVsNoalias Lazy evaluation and noalias

In Eigen all operations are performed in a lazy fashion except the matrix products which are always evaluated into a temporary by default.
In Eigen2, lazy evaluation could be enforced by tagging a product using the .lazy() function. However, in complex expressions it was not
easy to determine where to put the lazy() function. In Eigen3, the lazy() feature has been superseded by the MatrixBase::noalias() function
which can be used on the left hand side of an assignment when no aliasing can occur. Here is an example:
\code
MatrixXf a, b, c;
...
c.noalias() += 2 * a.transpose() * b;
\endcode
However, the noalias mechanism does not cover all the features of the old .lazy(). Indeed, in some extremely rare cases,
it might be useful to explicit request for a lay product, i.e., for a product which will be evaluated one coefficient at once, on request,
just like any other expressions. To this end you can use the MatrixBase::lazyProduct() function, however we strongly discourage you to
use it unless you are sure of what you are doing, i.e., you have rigourosly measured a speed improvement.

\section AlignMacros Alignment-related macros

The EIGEN_ALIGN_128 macro has been renamed to EIGEN_ALIGN16. Don't be surprised, it's just that we switched to counting in bytes ;-)

The \link TopicPreprocessorDirectivesPerformance EIGEN_DONT_ALIGN \endlink option still exists in Eigen 3, but it has a new cousin: \link TopicPreprocessorDirectivesPerformance  EIGEN_DONT_ALIGN_STATICALLY.\endlink It allows to get rid of all static alignment issues while keeping alignment of dynamic-size heap-allocated arrays. Vectorization of statically allocated arrays is still preserved (unless you define \link TopicPreprocessorDirectivesPerformance EIGEN_UNALIGNED_VECTORIZE \endlink =0), at the cost of unaligned memory stores.

\section AlignedMap Aligned Map objects

A common issue with Eigen 2 was that when mapping an array with Map, there was no way to tell Eigen that your array was aligned. There was a ForceAligned option but it didn't mean that; it was just confusing and has been removed.

New in Eigen3 is the #Aligned option. See the documentation of class Map. Use it like this:
\code
Map<Vector4f, Aligned> myMappedVector(some_aligned_array);
\endcode
There also are related convenience static methods, which actually are the preferred way as they take care of such things as constness:
\code
result = Vector4f::MapAligned(some_aligned_array);
\endcode

\section StdContainers STL Containers

In Eigen2, <tt>\#include\<Eigen/StdVector\></tt> tweaked std::vector to automatically align elements. The problem was that that was quite invasive. In Eigen3, we only override standard behavior if you use Eigen::aligned_allocator<T> as your allocator type. So for example, if you use std::vector<Matrix4f>, you need to do the following change (note that aligned_allocator is under namespace Eigen):

<table class="manual">
<tr><th>Eigen 2</th><th>Eigen 3</th></tr>
<tr>
  <td> \code std::vector<Matrix4f> \endcode </td>
  <td> \code std::vector<Matrix4f, aligned_allocator<Matrix4f> > \endcode </td>
</tr>
</table>

\section eiPrefix Internal ei_ prefix

In Eigen2, global internal functions and structures were prefixed by \c ei_. In Eigen3, they all have been moved into the more explicit \c internal namespace. So, e.g., \c ei_sqrt(x) now becomes \c internal::sqrt(x). Of course it is not recommended to rely on Eigen's internal features.



*/

}
