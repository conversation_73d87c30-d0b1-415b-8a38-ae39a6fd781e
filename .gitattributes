## 统一处理所有文本文件的行尾符
#* text=auto
#
## 二进制文件配置
#*.bin binary
#*.exe binary
#*.dll binary
#*.so binary
#*.a binary
#
## 忽略可执行权限的变化
#*.sh -x
#*.py -x
#
## 脚本文件配置
#*.sh text eol=lf
#*.bat text eol=crlf
#
## C++代码文件行尾
#*.cpp text eol=lf
#*.h text eol=lf
#*.hxx text eol=lf
#*.hpp text eol=lf

# C++代码文件编码
#*.cpp text working-tree-encoding=UTF-8-BOM
#*.h text working-tree-encoding=UTF-8-BOM
#*.hxx text working-tree-encoding=UTF-8-BOM
#*.hpp text working-tree-encoding=UTF-8-BOM
