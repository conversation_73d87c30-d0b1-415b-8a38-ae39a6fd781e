﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Mesh.h
//! <AUTHOR>
//! @brief 网格类
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_Mesh_
#define _basic_mesh_Mesh_

#include "basic/mesh/BaseMesh.h"

// 用于友元类的声明（暂时）
class DecomposeManager;
class AgglomerateManager;
class DualMesh;
class MeshSorting;
class WallDistanceBase;
class WallDistanceManager;
class MeshProcess;
class CgnsMeshBase;
class CgnsMesh;
class DlgMesh;
class FluentMeshBlock;
class MeshConverter;
class MeshConvertManager;
class SubMesh;
class OversetMesh;
class BackgroundGrid;

/**
 * @class Mesh
 * @brief 网格类，继承自BaseMesh，提供网格数据结构及相关操作方法
 * 
 * 该类封装了CFD计算中所需的网格数据结构，包括：
 * - 单元、面、节点信息
 * - 边界条件处理
 * - 并行计算相关数据结构
 * - 多重网格相关数据结构
 * - 重叠网格区域处理
 */
class Mesh: public BaseMesh
{
public:
    /**
     * @brief 默认构造函数
     */
    Mesh();
    
    /**
     * @brief 构造函数，从文件加载网格
     * @param MshFileName 网格文件名
     */
    Mesh(const std::string& MshFileName);

    // 获取并行计算相关的虚单元信息
    /**
     * @brief 获取并行计算相关的虚单元信息
     * @return 包含所有并行虚单元信息的二维向量
     */
    const std::vector<std::vector<GhostElement> > &GetGhostElementsParallel() const
    {
        return this->vv_ghostElement_parallel;
    }
    
    /**
     * @brief 获取多重网格相关的虚单元信息
     * @return 包含所有多重网格虚单元信息的二维向量
     */
    const std::vector<std::vector<GhostElement> > &GetGhostElementsMultigrid() const
    {
        return this->vv_ghostElement_multigrid;
    }
    
    /**
     * @brief 获取边界全局ID
     * @param localID 边界局部ID
     * @return 边界全局ID
     */
    const int &GetBoundaryIDGlobal(const int &localID) const
    {
        return this->v_boundaryIDGlobal[localID];
    }
    
    /**
     * @brief 获取单元到壁面的距离
     * @param elementID 单元ID
     * @return 单元到壁面的距离
     */
    const Scalar &GetNearWallDistance(const int &elementID) const
    {
        return this->v_nearWallDistance[elementID];
    }
    
    /**
     * @brief 获取共享面的相邻单元ID
     * @param elementID 单元ID
     * @return 相邻单元ID列表
     */
    const std::vector<int> &GetNeighborIDFaceAdjoin(const int &elementID) const
    {
        return this->vv_neighborID_faceAdjoin[elementID];
    }
    
    /**
     * @brief 获取边界单元对应的内部单元ID
     * @param patchID 边界块ID
     * @param index 边界单元索引
     * @return 对应的内部单元ID
     */
    const int &GetInnerElementIDForBoundaryElement(const int &patchID, const int &index) const
    {
        const int &index0 = this->vv_boundaryFaceIndexInDomain[patchID][index];
        return this->vv_innerElementID[patchID][index0];
    }

    // 设置类方法
    /**
     * @brief 设置边界单元对应的内部单元ID
     * @param patchID 边界块ID
     * @param index 边界单元索引
     * @param ID 要设置的内部单元ID
     */
    void SetInnerElementIDForBoundaryElement(const int &patchID, const int &index, const int &ID)
    {
        this->vv_innerElementID[patchID][index] = ID;
    }
    
    /**
     * @brief 设置单元到壁面的距离
     * @param elementID 单元ID
     * @param value 要设置的距离值
     */
    void SetNearWallDistance(const int &elementID, const Scalar &value)
    {
        this->v_nearWallDistance[elementID] = value;
    }
    
    // 判断类方法
    /**
     * @brief 判断面是否为边界面
     * @param faceID 面ID
     * @return true如果是边界面，false否则
     */
    bool JudgeBoundaryFace(const int &faceID) const
    {
        const auto &type = this->v_elem[v_face[faceID].n_neighbor].et_type;
        return (type == Element::ElemType::ghostBoundary);
    }
    
    /**
     * @brief 判断单元是否为真实单元
     * @param elementID 单元ID
     * @return true如果是真实单元，false否则
     */
    bool JudgeRealElement(const int &elementID) const
    {
        return this->v_elem[elementID].et_type == Element::ElemType::real;
    }
    
    /**
     * @brief 判断单元是否为边界单元
     * @param elementID 单元ID
     * @return true如果是边界单元，false否则
     */
    bool JudegBoundaryElemnt(const int &elementID) const
    {
        return this->v_boundaryElementFlag.empty() ? false : this->v_boundaryElementFlag[elementID];
    }

    /**
     * @brief 判断半面是否跨越对称边界
     * @param faceID 面ID
     * @return true如果是跨越对称边界，false否则
     */
    bool JudgeHalfFaceCrossSymmetryBoundary(const int &faceID) const
    {
        return this->halfFaceCrossSymmetryBoundaryFlag[faceID];
    }

    // 重叠网格区域相关方法
    /**
     * @brief 获取重叠网格区域
     * @return 重叠网格区域引用
     */
    OversetRegion &GetOversetRegion()
    {
        return this->oversetRegion;
    }
    
    // 域内信息获取方法
    /**
     * @brief 获取计算域内的单元数量
     * @return 计算域内的单元数量
     */
    const int GetElementNumberInDomain() const
    {
        return this->v_elementIDInDomain.size();
    }
    
    /**
     * @brief 获取计算域内指定索引的单元ID
     * @param index 索引
     * @return 单元ID
     */
    const int &GetElementIDInDomain(const int &index) const
    {
        return this->v_elementIDInDomain[index];
    }

    /**
     * @brief 获取计算域内的内部面数量
     * @return 计算域内的内部面数量
     */
    const int GetInnerFaceNumberInDomain() const
    {
        return this->v_innerFaceIDInDomain.size();
    }
    
    /**
     * @brief 获取计算域内指定索引的内部面ID
     * @param index 索引
     * @return 内部面ID
     */
    const int &GetInnerFaceIDInDomain(const int &index) const
    {
        return this->v_innerFaceIDInDomain[index];
    }

    /**
     * @brief 获取计算域内指定边界块的边界面数量
     * @param patchID 边界块ID
     * @return 边界面的数量
     */
    const int GetBoundaryFaceNumberInDomain(const int &patchID)const
    {
        return this->vv_boundaryFaceIndexInDomain[patchID].size();
    }
    
    /**
     * @brief 获取计算域内指定边界块的边界面ID
     * @param patchID 边界块ID
     * @param index 索引
     * @return 边界面ID
     */
    const int &GetBoundaryFaceIDInDomain(const int &patchID, const int &index)const
    {
        const int &index0 = vv_boundaryFaceIndexInDomain[patchID][index];
        return vv_boundaryFaceID[patchID][index0];
    }

    // 网格操作方法
    /**
     * @brief 设置边界单元对应的内部单元ID(批量设置)
     */
    void SetInnerElementIDForBoundaryElement();
    
    /**
     * @brief 创建边界虚单元
     */
    void CreateBoundaryGhostElement();
    
    /**
     * @brief 更新边界单元标志
     */
    void UpdateBoundaryElementFlag();
    
    /**
     * @brief 更新半面跨越对称边界标志
     * @param symmetryPatchID 对称边界块ID列表
     */
    void UpdateHalfFaceCrossSymmetryBoundaryFlag(const std::vector<int> &symmetryPatchID);
    
    /**
     * @brief 更新计算域内信息
     */
    void UpdateInDomainInfo();

    // 网格IO方法
    /**
     * @brief 写入网格到文件
     * @param file 文件流
     * @param binary 是否为二进制格式
     * @param fullFlag 是否写入完整网格信息
     */
    void WriteMesh(std::fstream &file, const bool &binary, const bool &fullFlag = true) const;
    
    /**
     * @brief 从文件读取网格
     * @param file 文件流
     * @param binary 是否为二进制格式
     * @param fullFlag 是否读取完整网格信息
     */
    void ReadMesh(std::fstream &file, const bool &binary, const bool &fullFlag = true);
    
protected:    
    int n_elemNum_ghostBoundary; ///< 物理边界虚单元数量
    int n_elemNum_ghostParallel; ///< 并行边界虚单元数量
    int n_elemNum_ghostOverlap;  ///< 嵌套边界虚单元数量

    std::vector<int> v_boundaryIDGlobal;                              ///< 边界全局编号容器
    std::vector<Scalar> v_nearWallDistance;                           ///< 壁面距离容器
    std::vector<std::vector<int>> vv_neighborID_faceAdjoin;           ///< 共享面的相邻单元ID容器
    std::vector<std::vector<int>> vv_innerElementID;                  ///< 边界节点相邻的内部节点ID容器(仅用于对偶网格)
    std::vector<bool> v_boundaryElementFlag;                          ///< 边界单元标志容器(仅用于对偶网格)
    std::vector<bool> halfFaceCrossSymmetryBoundaryFlag;              ///< 对称边界边标志容器(仅用于对偶网格)

    std::vector<GhostElement> v_ghostElement;                         ///< 其他块中的虚单元信息容器
    std::vector<std::vector<GhostElement> > vv_ghostElement_parallel; ///< 网格分区形成的细网格虚单元信息容器
    std::vector<std::vector<GhostElement>> vv_ghostElement_multigrid; ///< 网格聚合形成的细网格虚单元信息容器
    
    std::vector<int> v_elementIDInDomain;                             ///< 参与计算的真实单元ID列表
    std::vector<int> v_innerFaceIDInDomain;                           ///< 参与计算的内部面ID列表
    std::vector<std::vector<int>> vv_boundaryFaceIndexInDomain;       ///< 参与计算的边界面局部索引列表

private:
    OversetRegion oversetRegion; ///< 重叠网格区域指针

public:    
#if defined(_BaseParallelMPI_)
public:
    /**
     * @brief 序列化方法(用于MPI并行)
     * @tparam Archive 序列化类型
     * @param ar 序列化对象
     * @param version 版本号
     */
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & zoneID;
        ar & st_fileName;
        ar & st_meshName;
        ar & n_elemNum;
        ar & n_elemNum_all;
        ar & n_faceNum;
        ar & n_nodeNum;
        ar & md_meshDim;

        ar & est_shapeType;
        ar & v_elem;
        ar & v_face;
        ar & v_node;

        ar & v_boundaryIDGlobal;
        ar & v_boundaryName;
        ar & vv_boundaryFaceID;
        ar & v_ghostElement;
        ar & vv_ghostElement_parallel;
        ar & vv_ghostElement_multigrid;
    }
#endif

    // 友元类声明
    friend class DecomposeManager;    ///< 网格分解管理器
    friend class AgglomerateManager;  ///< 网格聚合管理器
    friend class DualMesh;            ///< 对偶网格类
    friend class MeshSorting;         ///< 网格排序类
    friend class WallDistanceBase;    ///< 壁面距离基类
    friend class WallDistanceManager; ///< 壁面距离管理器
    friend class MeshProcess;         ///< 网格处理类
    friend class CgnsMeshBase;        ///< CGNS网格基类
    friend class CgnsMesh;            ///< CGNS网格类
    friend class DlgMesh;             ///< DLG网格类
    friend class FluentMeshBlock;     ///< Fluent网格块类
    friend class MeshConverter;       ///< 网格转换器
    friend class MeshConvertManager;  ///< 网格转换管理器
    friend class SubMesh;             ///< 子网格类
    friend class OversetMesh;         ///< 重叠网格类
    friend class BackgroundGrid;      ///< 背景网格类
};

#endif // _basic_mesh_Mesh_
