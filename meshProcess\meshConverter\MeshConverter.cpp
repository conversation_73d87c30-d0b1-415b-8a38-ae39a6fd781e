﻿#include "meshProcess/meshConverter/MeshConverter.h"

MeshConverter::MeshConverter(const std::string &MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_)
    :mesh(mesh_), fullFileName(MshFileName), meshDimension(meshDimension_)
{
    mesh->st_fileName = fullFileName.substr(fullFileName.rfind("/") + 1, -1);
    mesh->st_meshName = mesh->st_fileName.substr(0, mesh->st_fileName.rfind("."));
}

int MeshConverter::CreateFaceByElemAndPoint()
{
    const int threadSize = omp_get_max_threads();

    const int nodeSize = mesh->v_node.size();
    const int elementSizeAll = mesh->v_elem.size();
    for (int i = 0; i < elementSizeAll; ++i) std::vector<int>().swap(mesh->v_elem[i].v_faceID);

    Print("Create Face List ...");

    int elementSizeAvg;
    std::vector<Element> elementList;
    std::vector<std::vector<std::vector<int>>> v_faceIDListVector(threadSize);
    std::vector<std::vector<Face>> v_faceVector(threadSize);
    ARI_OMP(parallel for schedule(static) private(elementSizeAvg, elementList))
    for (int index = 0; index < threadSize; ++index)
    {
        // 确认每个进程待处理的单元信息
        const int threadID = omp_get_thread_num();
        const int start = threadID * (elementSizeAll / threadSize);
        elementSizeAvg = elementSizeAll / threadSize;
        if (threadID == threadSize - 1) elementSizeAvg = elementSizeAll - start;

        v_faceIDListVector[threadID].resize(nodeSize);
        if (threadID == 0)
        {
            //mesh->v_face.clear(); //已包含边界面，不能清空
            mesh->v_face.reserve(mesh->v_face.size() + mesh->v_elem.size() * 3);
            CreateFace(mesh->v_elem, start, elementSizeAvg, mesh->v_face, v_faceIDListVector[threadID]);
        }
        else
        {
            v_faceVector[threadID].reserve(elementSizeAvg * 3);
            elementList.assign(mesh->v_elem.begin() + start, mesh->v_elem.begin() + start + elementSizeAvg);
            CreateFace(elementList, start, elementSizeAvg, v_faceVector[threadID], v_faceIDListVector[threadID]);
        }
    }

    if (threadSize > 1) Print("Merge Face Vector ...");
    for (int threadID = 1; threadID < threadSize; ++threadID)
    {
        MergeFaceVector(v_faceIDListVector[threadID], v_faceVector[threadID], v_faceIDListVector[0]);
        std::vector<std::vector<int>>().swap(v_faceIDListVector[threadID]);
        std::vector<Face>().swap(v_faceVector[threadID]);
    }

    for (int faceID = 0; faceID < mesh->v_face.size(); ++faceID)
    {
        const int &ownerID = mesh->v_face[faceID].GetOwnerID();
        const int &neighID = mesh->v_face[faceID].GetNeighborID();
        if (ownerID == -1 && neighID == -1)
        {
            FatalError("MeshConverter::CreateFaceByElemAndPoint: face connetction is wrong!");
            return 1;
        }
    }

    return 0;
}

void MeshConverter::SortNodeList(std::vector<int> &nodeList)
{
    int num = nodeList.size();
    std::vector<int> newList;
    
    int minID = nodeList[0];
    int position = 0;
    for (int i = 1; i < num; i++)
        if (nodeList[i] < minID)
        {
            minID = nodeList[i];
            position = i;
        }

    int delta;
    int left = (position - 1 + num) % num;
    int right = (position + 1) % num;
    if (nodeList[left] > nodeList[right]) delta = 1;
    else              delta = -1;

    if (delta < 0)
    {
        for (int i = position; i >= 0; i--)         newList.push_back(nodeList[i]);
        for (int i = num - 1; i > position; i--) newList.push_back(nodeList[i]);
    }
    else
    {
        for (int i = position; i < num; i++) newList.push_back(nodeList[i]);
        for (int i = 0; i < position; i++)   newList.push_back(nodeList[i]);
    }

    nodeList  = newList;
}

void MeshConverter::CreateInteriorFaceForTriangular(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;

    Face face0(pointList[0], pointList[1]);
    Face face1(pointList[1], pointList[2]);
    Face face2(pointList[2], pointList[0]);

    face0.n_owner = ownerID;
    face1.n_owner = ownerID;
    face2.n_owner = ownerID;

    vFace.push_back(face0);
    vFace.push_back(face1);
    vFace.push_back(face2);
}

void MeshConverter::CreateInteriorFaceForQuadrilateral(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;

    Face face0(pointList[0], pointList[1]);
    Face face1(pointList[1], pointList[2]);
    Face face2(pointList[2], pointList[3]);
    Face face3(pointList[3], pointList[0]);

    face0.n_owner = ownerID;
    face1.n_owner = ownerID;
    face2.n_owner = ownerID;
    face3.n_owner = ownerID;

    vFace.push_back(face0);
    vFace.push_back(face1);
    vFace.push_back(face2);
    vFace.push_back(face3);
}

void MeshConverter::CreateInteriorFaceForTetrahedral(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;

    Face face0(pointList[0], pointList[2], pointList[1]);
    Face face1(pointList[0], pointList[1], pointList[3]);
    Face face2(pointList[1], pointList[2], pointList[3]);
    Face face3(pointList[0], pointList[3], pointList[2]);

    face0.n_owner = ownerID;
    face1.n_owner = ownerID;
    face2.n_owner = ownerID;
    face3.n_owner = ownerID;

    vFace.push_back(face0);
    vFace.push_back(face1);
    vFace.push_back(face2);
    vFace.push_back(face3);
}

void MeshConverter::CreateInteriorFaceForHexahedral(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;

    Face face0(pointList[0], pointList[3], pointList[2], pointList[1]);
    Face face1(pointList[4], pointList[5], pointList[6], pointList[7]);

    Face face2(pointList[0], pointList[4], pointList[7], pointList[3]);
    Face face3(pointList[1], pointList[2], pointList[6], pointList[5]);

    Face face4(pointList[0], pointList[1], pointList[5], pointList[4]);
    Face face5(pointList[2], pointList[3], pointList[7], pointList[6]);

    face0.n_owner = ownerID;
    face1.n_owner = ownerID;
    face2.n_owner = ownerID;
    face3.n_owner = ownerID;
    face4.n_owner = ownerID;
    face5.n_owner = ownerID;

    vFace.push_back(face0);
    vFace.push_back(face1);
    vFace.push_back(face2);
    vFace.push_back(face3);
    vFace.push_back(face4);
    vFace.push_back(face5);
}

void MeshConverter::CreateInteriorFaceForWedge(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;

    Face face0(pointList[0], pointList[2], pointList[1]);
    Face face1(pointList[3], pointList[4], pointList[5]);

    Face face2(pointList[0], pointList[1], pointList[4], pointList[3]);
    Face face3(pointList[1], pointList[2], pointList[5], pointList[4]);
    Face face4(pointList[0], pointList[3], pointList[5], pointList[2]);

    face0.n_owner = ownerID;
    face1.n_owner = ownerID;
    face2.n_owner = ownerID;
    face3.n_owner = ownerID;
    face4.n_owner = ownerID;

    vFace.push_back(face0);
    vFace.push_back(face1);
    vFace.push_back(face2);
    vFace.push_back(face3);
    vFace.push_back(face4);
}

void MeshConverter::CreateInteriorFaceForPyramid(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;

    Face face0(pointList[0], pointList[4], pointList[3]);
    Face face1(pointList[0], pointList[1], pointList[4]);
    Face face2(pointList[1], pointList[2], pointList[4]);
    Face face3(pointList[2], pointList[3], pointList[4]);

    Face face4(pointList[0], pointList[3], pointList[2], pointList[1]);

    face0.n_owner = ownerID;
    face1.n_owner = ownerID;
    face2.n_owner = ownerID;
    face3.n_owner = ownerID;
    face4.n_owner = ownerID;

    vFace.push_back(face0);
    vFace.push_back(face1);
    vFace.push_back(face2);
    vFace.push_back(face3);
    vFace.push_back(face4);
}

void MeshConverter::CreateInteriorFaceForPolygon(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID)
{
    const std::vector<int> &pointList = CurElem.v_nodeID;
    const int size = pointList.size();
    for (int i = 0; i < size; i++)
    {
        const int index0 = i;
        const int index1 = (i + 1) % size;

        Face face(pointList[index0], pointList[index1]);
        face.n_owner = ownerID;
        vFace.push_back(face);
    }
}

void MeshConverter::PopulateCellNodes()
{
    for (int i = 0; i < (int)mesh->v_elem.size(); i++)
    {
        switch (mesh->v_elem[i].est_shapeType)
        {
        case Element::estTriangular:
            this->PopulateTriangleCell(i);
            break;

        case Element::estTetrahedral:
            this->PopulateTetraCell(i);
            break;

        case Element::estQuadrilateral:
            this->PopulateQuadCell(i);
            break;

        case Element::estHexahedral:
            this->PopulateHexahedronCell(i);
            break;

        case Element::estPyramid:
            this->PopulatePyramidCell(i);
            break;

        case Element::estWedge:
            this->PopulateWedgeCell(i);
            break;

        case Element::estPolyhedron:
            this->PopulatePolyhedronCell(i);
            break;

        case Element::estPolygon:
            this->PopulatePolygonCell(i);
            break;

        case Element::estNoType:
            WarningContinue(std::string("Element ElemShapeType is \"estNoType\"."));
            break;

        default:
            break;

        }
    }
}

void MeshConverter::PopulateTriangleCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() != 3)
    {
        std::stringstream info;
        info << "Current Triangle Cell ID is:" << i + 1 << std::endl;
        info << "Current Triangle Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize(3);

    int nFstFace = mesh->v_elem[i].v_faceID[0];
    int nSecFace = mesh->v_elem[i].v_faceID[1];

    if (mesh->v_face[nFstFace].n_owner == i)
    {
        v_nodeID[0] = mesh->v_face[nFstFace].v_nodeID[0];
        v_nodeID[1] = mesh->v_face[nFstFace].v_nodeID[1];
    }
    else
    {
        v_nodeID[1] = mesh->v_face[nFstFace].v_nodeID[0];
        v_nodeID[0] = mesh->v_face[nFstFace].v_nodeID[1];
    }

    if (mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[0] &&
        mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[1])
    {
        v_nodeID[2] = mesh->v_face[nSecFace].v_nodeID[0];
    }
    else
    {
        v_nodeID[2] = mesh->v_face[nSecFace].v_nodeID[1];
    }
}

void MeshConverter::PopulateTetraCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() != 4)
    {
        std::stringstream info;
        info << "Current Tetrahedral Cell ID is:" << i + 1 << std::endl;
        info << "Current Tetrahedral Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize(4);

    int nFstFace = mesh->v_elem[i].v_faceID[0];
    int nSecFace = mesh->v_elem[i].v_faceID[1];

    if (mesh->v_face[nFstFace].n_owner == i)
    {
        v_nodeID[0] = mesh->v_face[nFstFace].v_nodeID[0];
        v_nodeID[1] = mesh->v_face[nFstFace].v_nodeID[1];
        v_nodeID[2] = mesh->v_face[nFstFace].v_nodeID[2];
    }
    else
    {
        v_nodeID[2] = mesh->v_face[nFstFace].v_nodeID[0];
        v_nodeID[1] = mesh->v_face[nFstFace].v_nodeID[1];
        v_nodeID[0] = mesh->v_face[nFstFace].v_nodeID[2];
    }

    if (mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[0] &&
        mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[1] &&
        mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[2])
    {
        v_nodeID[3] = mesh->v_face[nSecFace].v_nodeID[0];
    }
    else if (mesh->v_face[nSecFace].v_nodeID[1] != v_nodeID[0] &&
        mesh->v_face[nSecFace].v_nodeID[1] != v_nodeID[1] &&
        mesh->v_face[nSecFace].v_nodeID[1] != v_nodeID[2])
    {
        v_nodeID[3] = mesh->v_face[nSecFace].v_nodeID[1];
    }
    else
    {
        v_nodeID[3] = mesh->v_face[nSecFace].v_nodeID[2];
    }
}

void MeshConverter::PopulateQuadCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() != 4)
    {
        std::stringstream info;
        info << "Current Quadrilateral Cell ID is:" << i + 1 << std::endl;
        info << "Current Quadrilateral Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize(4);

    int nFstFace = mesh->v_elem[i].v_faceID[0];
    int nSecFace = mesh->v_elem[i].v_faceID[1];
    int n3rdFace = mesh->v_elem[i].v_faceID[2];
    int n4thFace = mesh->v_elem[i].v_faceID[3];

    if (mesh->v_face[nFstFace].n_owner == i)
    {
        v_nodeID[0] = mesh->v_face[nFstFace].v_nodeID[0];
        v_nodeID[1] = mesh->v_face[nFstFace].v_nodeID[1];
    }
    else
    {
        v_nodeID[1] = mesh->v_face[nFstFace].v_nodeID[0];
        v_nodeID[0] = mesh->v_face[nFstFace].v_nodeID[1];
    }

    if ((mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[0] &&
        mesh->v_face[nSecFace].v_nodeID[0] != v_nodeID[1]) &&
        (mesh->v_face[nSecFace].v_nodeID[1] != v_nodeID[0] &&
        mesh->v_face[nSecFace].v_nodeID[1] != v_nodeID[1]))
    {
        if (mesh->v_face[nSecFace].n_owner == i)
        {
            v_nodeID[2] = mesh->v_face[nSecFace].v_nodeID[0];
            v_nodeID[3] = mesh->v_face[nSecFace].v_nodeID[1];
        }
        else
        {
            v_nodeID[3] = mesh->v_face[nSecFace].v_nodeID[0];
            v_nodeID[2] = mesh->v_face[nSecFace].v_nodeID[1];
        }
    }
    else if ((mesh->v_face[n3rdFace].v_nodeID[0] != v_nodeID[0] &&
        mesh->v_face[n3rdFace].v_nodeID[0] != v_nodeID[1]) &&
        (mesh->v_face[n3rdFace].v_nodeID[1] != v_nodeID[0] &&
        mesh->v_face[n3rdFace].v_nodeID[1] != v_nodeID[1]))
    {
        if (mesh->v_face[n3rdFace].n_owner == i)
        {
            v_nodeID[2] = mesh->v_face[n3rdFace].v_nodeID[0];
            v_nodeID[3] = mesh->v_face[n3rdFace].v_nodeID[1];
        }
        else
        {
            v_nodeID[3] = mesh->v_face[n3rdFace].v_nodeID[0];
            v_nodeID[2] = mesh->v_face[n3rdFace].v_nodeID[1];
        }
    }
    else
    {
        if (mesh->v_face[n4thFace].n_owner == i)
        {
            v_nodeID[2] = mesh->v_face[n4thFace].v_nodeID[0];
            v_nodeID[3] = mesh->v_face[n4thFace].v_nodeID[1];
        }
        else
        {
            v_nodeID[3] = mesh->v_face[n4thFace].v_nodeID[0];
            v_nodeID[2] = mesh->v_face[n4thFace].v_nodeID[1];
        }
    }
}

void MeshConverter::PopulateHexahedronCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() != 6)
    {
        std::stringstream info;
        info << "Current Hexahedral Cell ID is:" << i + 1 << std::endl;
        info << "Current Hexahedral Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize(8);

    int nFstFace = mesh->v_elem[i].v_faceID[0];

    if (mesh->v_face[nFstFace].n_owner == i)
    {
        for (int j = 0; j < 4; j++)
        {
            v_nodeID[j] = mesh->v_face[nFstFace].v_nodeID[j];
        }
    }
    else
    {
        for (int j = 3; j >= 0; j--)
        {
            v_nodeID[3 - j] = mesh->v_face[nFstFace].v_nodeID[j];
        }
    }

    //Look for opposite face of hexahedron
    for (int j = 1; j < 6; j++)
    {
        int flag = 0;
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        for (int k = 0; k < 4; k++)
        {
            if ((v_nodeID[0] == mesh->v_face[nCurFace].v_nodeID[k]) ||
                (v_nodeID[1] == mesh->v_face[nCurFace].v_nodeID[k]) ||
                (v_nodeID[2] == mesh->v_face[nCurFace].v_nodeID[k]) ||
                (v_nodeID[3] == mesh->v_face[nCurFace].v_nodeID[k]))
            {
                flag = 1;
            }
        }

        if (flag == 0)
        {
            if (mesh->v_face[nCurFace].n_owner == i)
            {
                for (int k = 7; k >= 4; k--)
                {
                    v_nodeID[k] = mesh->v_face[nCurFace].v_nodeID[7 - k];
                }
            }
            else
            {
                for (int k = 4; k < 8; k++)
                {
                    v_nodeID[k] = mesh->v_face[nCurFace].v_nodeID[k - 4];
                }
            }
        }
    }

    //  Find the face with points 0 and 1 in them.
    int f01[4] = { -1, -1, -1, -1 };
    for (int j = 1; j < 6; j++)
    {
        int flag0 = 0;
        int flag1 = 0;
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        for (int k = 0; k < 4; k++)
        {
            if (v_nodeID[0] == mesh->v_face[nCurFace].v_nodeID[k])
            {
                flag0 = 1;
            }

            if (v_nodeID[1] == mesh->v_face[nCurFace].v_nodeID[k])
            {
                flag1 = 1;
            }
        }

        if ((flag0 == 1) && (flag1 == 1))
        {
            if (mesh->v_face[nCurFace].n_owner == i)
            {
                for (int k = 0; k<4; k++)
                {
                    f01[k] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
            else
            {
                for (int k = 3; k >= 0; k--)
                {
                    f01[k] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
        }
    }

    //  Find the face with points 0 and 3 in them.
    int f03[4] = { -1, -1, -1, -1 };
    for (int j = 1; j < 6; j++)
    {
        int flag0 = 0;
        int flag1 = 0;
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        for (int k = 0; k < 4; k++)
        {
            if (v_nodeID[0] == mesh->v_face[nCurFace].v_nodeID[k])
            {
                flag0 = 1;
            }
            if (v_nodeID[3] == mesh->v_face[nCurFace].v_nodeID[k])
            {
                flag1 = 1;
            }
        }

        if ((flag0 == 1) && (flag1 == 1))
        {
            if (mesh->v_face[nCurFace].n_owner == i)
            {
                for (int k = 0; k<4; k++)
                {
                    f03[k] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
            else
            {
                for (int k = 3; k >= 0; k--)
                {
                    f03[k] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
        }
    }

    // What point is in f01 and f03 besides 0 ... this is point 4
    int p4 = 0;
    for (int k = 0; k < 4; k++)
    {
        if (f01[k] != v_nodeID[0])
        {
            for (int n = 0; n < 4; n++)
            {
                if (f01[k] == f03[n])
                {
                    p4 = f01[k];
                }
            }
        }
    }

    // Since we know point 4 now we check to see if points
    //  4, 5, 6, and 7 are in the correct positions.
    int t[8];
    t[4] = v_nodeID[4];
    t[5] = v_nodeID[5];
    t[6] = v_nodeID[6];
    t[7] = v_nodeID[7];
    if (p4 == v_nodeID[5])
    {
        v_nodeID[4] = t[5];
        v_nodeID[5] = t[6];
        v_nodeID[6] = t[7];
        v_nodeID[7] = t[4];
    }
    else if (p4 == v_nodeID[6])
    {
        v_nodeID[4] = t[6];
        v_nodeID[5] = t[7];
        v_nodeID[6] = t[4];
        v_nodeID[7] = t[5];
    }
    else if (p4 == v_nodeID[7])
    {
        v_nodeID[4] = t[7];
        v_nodeID[5] = t[4];
        v_nodeID[6] = t[5];
        v_nodeID[7] = t[6];
    }
}

void MeshConverter::PopulatePyramidCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() != 5)
    {
        std::stringstream info;
        info << "Current Pyramid Cell ID is:" << i + 1 << std::endl;
        info << "Current Pyramid Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize(5);

    //The quad face will be the base of the pyramid
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        if (mesh->v_face[nCurFace].GetNodeSize() == 4)
        {
            if (mesh->v_face[nCurFace].n_owner == i)
            {
                for (int k = 0; k < 4; k++)
                {
                    v_nodeID[k] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
            else
            {
                for (int k = 0; k < 4; k++)
                {
                    v_nodeID[3 - k] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
        }
    }

    //Just need to find point 4
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        if (mesh->v_face[nCurFace].GetNodeSize() == 3)
        {
            for (int k = 0; k < 3; k++)
            {
                if ((mesh->v_face[nCurFace].v_nodeID[k] != v_nodeID[0]) &&
                    (mesh->v_face[nCurFace].v_nodeID[k] != v_nodeID[1]) &&
                    (mesh->v_face[nCurFace].v_nodeID[k] != v_nodeID[2]) &&
                    (mesh->v_face[nCurFace].v_nodeID[k] != v_nodeID[3]))
                {
                    v_nodeID[4] = mesh->v_face[nCurFace].v_nodeID[k];
                }
            }
        }
    }
}

void MeshConverter::PopulateWedgeCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() != 5)
    {
        std::stringstream info;
        info << "Current Wedge Cell ID is:" << i + 1 << std::endl;
        info << "Current Wedge Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize(6);

    //  Find the first triangle face and make it the base.
    int base = 0;
    int first = 0;
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        if ((mesh->v_face[nCurFace].GetNodeSize() == 3) && (first == 0))
        {
            base = mesh->v_elem[i].v_faceID[j];
            first = 1;
        }
    }

    //  Find the second triangle face and make it the top.
    int top = 0;
    int second = 0;
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        if ((mesh->v_face[nCurFace].GetNodeSize() == 3) && (second == 0) && (mesh->v_elem[i].v_faceID[j] != base))
        {
            top = mesh->v_elem[i].v_faceID[j];
            second = 1;
        }
    }

    // Load Base nodes into the nodes std::vector
    if (mesh->v_face[base].n_owner == i)
    {
        for (int j = 0; j < 3; j++)
        {
            v_nodeID[j] = mesh->v_face[base].v_nodeID[j];
        }
    }
    else
    {
        for (int j = 2; j >= 0; j--)
        {
            v_nodeID[2 - j] = mesh->v_face[base].v_nodeID[j];
        }
    }

    // Load Top nodes into the nodes std::vector
    if (mesh->v_face[top].n_neighbor == i)
    {
        for (int j = 3; j < 6; j++)
        {
            v_nodeID[j] = mesh->v_face[top].v_nodeID[j - 3];
        }
    }
    else
    {
        for (int j = 3; j < 6; j++)
        {
            v_nodeID[j] = mesh->v_face[top].v_nodeID[5 - j];
        }
    }

    //  Find the quad face with points 0 and 1 in them.
    int w01[4] = { -1, -1, -1, -1 };
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        if (nCurFace != base && nCurFace != top)
        {
            int wf0 = 0;
            int wf1 = 0;
            for (int k = 0; k < 4; k++)
            {
                if (v_nodeID[0] == mesh->v_face[nCurFace].v_nodeID[k])
                {
                    wf0 = 1;
                }

                if (v_nodeID[1] == mesh->v_face[nCurFace].v_nodeID[k])
                {
                    wf1 = 1;
                }

                if ((wf0 == 1) && (wf1 == 1))
                {
                    for (int n = 0; n<4; n++)
                    {
                        w01[n] = mesh->v_face[nCurFace].v_nodeID[n];
                    }
                }
            }
        }
    }

    //  Find the quad face with points 0 and 2 in them.
    int w02[4] = { -1, -1, -1, -1 };
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int nCurFace = mesh->v_elem[i].v_faceID[j];

        if (nCurFace != base &&    nCurFace != top)
        {
            int wf0 = 0;
            int wf2 = 0;
            for (int k = 0; k < 4; k++)
            {
                if (v_nodeID[0] == mesh->v_face[nCurFace].v_nodeID[k])
                {
                    wf0 = 1;
                }
                if (v_nodeID[2] == mesh->v_face[nCurFace].v_nodeID[k])
                {
                    wf2 = 1;
                }
                if ((wf0 == 1) && (wf2 == 1))
                {
                    for (int n = 0; n<4; n++)
                    {
                        w02[n] = mesh->v_face[nCurFace].v_nodeID[n];
                    }
                }
            }
        }
    }

    // Point 3 is the point that is in both w01 and w02

    // What point is in f01 and f02 besides 0 ... this is point 3
    int p3 = 0;
    for (int k = 0; k < 4; k++)
    {
        if (w01[k] != v_nodeID[0])
        {
            for (int n = 0; n < 4; n++)
            {
                if (w01[k] == w02[n])
                {
                    p3 = w01[k];
                }
            }
        }
    }

    // Since we know point 3 now we check to see if points
    //  3, 4, and 5 are in the correct positions.
    int t[6];
    t[3] = v_nodeID[3];
    t[4] = v_nodeID[4];
    t[5] = v_nodeID[5];
    if (p3 == v_nodeID[4])
    {
        v_nodeID[3] = t[4];
        v_nodeID[4] = t[5];
        v_nodeID[5] = t[3];
    }
    else if (p3 == v_nodeID[5])
    {
        v_nodeID[3] = t[5];
        v_nodeID[4] = t[3];
        v_nodeID[5] = t[4];
    }
}

void MeshConverter::PopulatePolyhedronCell(const int &i)
{
    //if (mesh->v_elem[i].GetFaceSize() < 5)
    //{
    //    std::stringstream info;
    //    info << "Current Polyhedron Cell ID is:" << i + 1 << std::endl;
    //    info << "Current Polyhedron Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
    //    FatalError(info.str());
    //}

    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int CurFaceID(mesh->v_elem[i].v_faceID[j]);
        for (int k = 0; k < mesh->v_face[CurFaceID].GetNodeSize(); k++)
        {
            int flag(0);
            // Is the node already in the cell?
            for (int n = 0; n < v_nodeID.size(); n++)
            {
                if (v_nodeID[n] == mesh->v_face[CurFaceID].v_nodeID[k])
                {
                    flag = 1;
                }
            }
            if (flag == 0)
            {
                //No match - insert node into cell.
                v_nodeID.push_back(mesh->v_face[CurFaceID].v_nodeID[k]);
            }
        }
    }
}

void MeshConverter::PopulatePolygonCell(const int &i)
{
    if (mesh->v_elem[i].GetFaceSize() <= 3)
    {
        std::stringstream info;
        info << "Current Polygon Cell ID is:" << i + 1 << std::endl;
        info << "Current Polygon Cell Face Num is:" << mesh->v_elem[i].GetFaceSize() << std::endl;
        FatalError(info.str());
    }

    // Insert all elem line segment into mapLineSeg
    // First node id , Second node id
    std::vector<std::pair<int, int> > vpairLineSeg;
    for (int j = 0; j < mesh->v_elem[i].GetFaceSize(); j++)
    {
        int CurFaceID(mesh->v_elem[i].v_faceID[j]);

        vpairLineSeg.push_back(std::pair<int, int>(mesh->v_face[CurFaceID].v_nodeID[0], mesh->v_face[CurFaceID].v_nodeID[1]));
    }

    // Here because FaceNum = LineSegsNum = ElemNodeNum
    std::vector<int> &v_nodeID = mesh->v_elem[i].v_nodeID;
    v_nodeID.resize((int)mesh->v_elem[i].GetFaceSize());

    std::vector<std::pair<int, int> > v_orderedLineSeg;
    v_orderedLineSeg.push_back(vpairLineSeg[0]);
    for (int n = 0; n < vpairLineSeg.size() - 1; n++)
    {
        int CurRightID = v_orderedLineSeg[n].second;
        bool found = false;
        for (int m = 0; m < vpairLineSeg.size(); m++)
        {
            if ((vpairLineSeg[m].first == CurRightID && vpairLineSeg[m].second == v_orderedLineSeg[n].first) ||
                (vpairLineSeg[m].second == CurRightID && vpairLineSeg[m].first == v_orderedLineSeg[n].first))
            {
                continue;
            }

            if (vpairLineSeg[m].first == CurRightID)
            {
                v_orderedLineSeg.push_back(vpairLineSeg[m]);
                found = true;
                break;
            }
            else if (vpairLineSeg[m].second == CurRightID)
            {
                v_orderedLineSeg.push_back(std::pair<int, int>(vpairLineSeg[m].second, vpairLineSeg[m].first));

                found = true;
                break;
            }
        }

        if (!found)
        {
            FatalError("Element " + ToString(i) + ". Segment " + ToString(n) + "is not closed. Please check!!!");
        }
    }

    v_nodeID[0] = v_orderedLineSeg[0].first;
    for (int n = 0; n < v_orderedLineSeg.size() - 1; n++)
    {
        v_nodeID[n + 1] = v_orderedLineSeg[n].second;
    }

    // Check if tail node to head node face existence
    bool bExist(false);
    std::pair<int, int> pairTailToHead(mesh->v_elem[i].GetNodeID(mesh->v_elem[i].GetNodeSize() - 1),
        mesh->v_elem[i].GetNodeID(0));

    for (int n = 0; n < vpairLineSeg.size(); n++)
    {
        if ((vpairLineSeg[n].first == pairTailToHead.first && vpairLineSeg[n].second == pairTailToHead.second) ||
            (vpairLineSeg[n].second == pairTailToHead.first && vpairLineSeg[n].first == pairTailToHead.second))
        {
            bExist = true;
        }
    }

    if (bExist == false)
    {
        FatalError("Element " + ToString(i) + " tail node to head node face is not found in elem.v_face. Please check!!!");
    }
}

void MeshConverter::CalculateParameters()
{    
    mesh->n_elemNum = (int)mesh->v_elem.size();
    mesh->n_faceNum = (int)mesh->v_face.size();
    mesh->n_nodeNum = (int)mesh->v_node.size();
    mesh->n_elemNum_all = mesh->n_elemNum;
    mesh->n_elemNum_ghostBoundary = 0;
    mesh->n_elemNum_ghostParallel = 0;
    mesh->n_elemNum_ghostOverlap = 0;
}

void MeshConverter::CreateFace(const std::vector<Element> &elementList, 
                               const int &start,
                               const int &elementSize,
                               std::vector<Face> &v_faceTemp,
                               std::vector<std::vector<int>> &faceIDListVector)
{
    // 已经存在的面（边界面）
    for (int j = 0; j < v_faceTemp.size(); j++)
    {
        const int sumNodeIDList = SumNodeList(v_faceTemp[j].v_nodeID);
        faceIDListVector[sumNodeIDList].push_back(j);
    }

    int faceIndex = v_faceTemp.size() - 1;

    const bool mpiRank0Flag = (omp_get_thread_num() == 0);

    //遍历单元的点构成，形成面信息 
    for (int i = 0; i < elementSize; i++)
    {
        const int interval = std::max(elementSize / 10, 1);
        if (mpiRank0Flag && ((i + 1) % interval == 0))
        {
            std::ostringstream stringStream;
            if ((i + 1) / interval == 1) stringStream << "\t";
            stringStream << std::setprecision(3) << (Scalar)(i + 1) / elementSize * 100.0 << "%" << "  ";
            if ((i + 1) / interval == 10 || i + 1 == elementSize) stringStream << std::endl;
            Info::infoFile << stringStream.str();
            std::cout << stringStream.str();
        }

        const Element &CurElem = elementList[i];
        std::vector<Face> vFace;
        int elementID = start + i;

        switch (CurElem.est_shapeType)
        {
        case Element::estTriangular:
            CreateInteriorFaceForTriangular(CurElem, vFace, elementID);
            break;
        case Element::estQuadrilateral:
            CreateInteriorFaceForQuadrilateral(CurElem, vFace, elementID);
            break;
        case Element::estTetrahedral:
            CreateInteriorFaceForTetrahedral(CurElem, vFace, elementID);
            break;
        case Element::estHexahedral:
            CreateInteriorFaceForHexahedral(CurElem, vFace, elementID);
            break;
        case Element::estWedge:
            CreateInteriorFaceForWedge(CurElem, vFace, elementID);
            break;
        case Element::estPyramid:
            CreateInteriorFaceForPyramid(CurElem, vFace, elementID);
            break;
        case Element::estPolygon:
            CreateInteriorFaceForPolygon(CurElem, vFace, elementID);
            break;
        default:
            FatalError("MeshConverter::CreateFace: Element type is unknown!");
            break;
        }

        if (mpiRank0Flag) mesh->v_elem[elementID].v_faceID.resize(vFace.size());
        for (int j = 0; j < vFace.size(); j++)
        {
            const int sumNodeIDList = SumNodeList(vFace[j].v_nodeID);
            bool flagNewFace = true;
            const int faceIDListSize = faceIDListVector[sumNodeIDList].size();
            for (int k = 0; k < faceIDListSize; ++k)
            {
                const int &faceID = faceIDListVector[sumNodeIDList][k];
                if (JudgeNodeList(vFace[j], v_faceTemp[faceID])) //已经存在此面                    
                {
                    const int &adjacentID = elementID;
                    if (v_faceTemp[faceID].n_owner == -1) //边界面
                    {
                        v_faceTemp[faceID].n_owner = adjacentID;

                        //单元生成的边界面替换已有边界面，确保面法矢向外
                        v_faceTemp[faceID].v_nodeID = vFace[j].v_nodeID;
                    }
                    else //内部面
                    {
                        v_faceTemp[faceID].n_neighbor = adjacentID;
                    }

                    flagNewFace = false;

                    if (mpiRank0Flag) mesh->v_elem[elementID].v_faceID[j] = faceID;
                    break;
                }
            }
            if (flagNewFace) //新的面
            {
                faceIndex++;
                v_faceTemp.push_back(vFace[j]);
                faceIDListVector[sumNodeIDList].push_back(faceIndex);
                if (mpiRank0Flag) mesh->v_elem[elementID].v_faceID[j] = faceIndex;
            }
        }
    }
}

void MeshConverter::MergeFaceVector(const std::vector<std::vector<int>> &faceIDListTemp, 
                                    const std::vector<Face> &faceVectorTemp,
                                    std::vector<std::vector<int>> &faceIDListVector)
{
    int faceIndex = mesh->v_face.size() - 1;
    const int tempSize1 = faceIDListTemp.size();
    for (int j = 0; j < tempSize1; ++j)
    {
        // 遍历局部容器当前位置所有面，将与全局容器当前位置所有面均不相同的面放入全局容器
        const int faceIDListSize = faceIDListVector[j].size();
        const int tempSize2 = faceIDListTemp[j].size();
        for (int k = 0; k < tempSize2; ++k)
        {
            const int &faceIndexTemp = faceIDListTemp[j][k];
            const Face &faceTemp = faceVectorTemp[faceIndexTemp];

            // 遍历范围为之前已填入的面
            bool flagNewFace = true;
            if(faceTemp.n_neighbor == -1) //物理边界面或分块边界面，需要在已填入的面中进行查找
            {
                for (int m = 0; m < faceIDListSize; ++m)
                {
                    const int &faceID = faceIDListVector[j][m];
                    if(mesh->v_face[faceID].n_neighbor != -1) continue; //内部面，不需要判断
                    if (JudgeNodeList(faceTemp, mesh->v_face[faceID])) //已经存在此面                    
                    {
                        const int &adjacentID = faceTemp.n_owner;
                        if (mesh->v_face[faceID].n_owner == -1) //物理边界面
                        {
                            mesh->v_face[faceID].n_owner = adjacentID;

                            //单元生成的边界面替换已有边界面，确保面法矢向外
                            mesh->v_face[faceID].v_nodeID = faceTemp.v_nodeID;
                        }
                        else //内部面
                        {
                            mesh->v_face[faceID].n_neighbor = adjacentID;
                        }

                        mesh->v_elem[adjacentID].v_faceID.push_back(faceID);
                        flagNewFace = false;
                        break;
                    }
                }
            }
            if (flagNewFace) //新的面
            {
                const int &ownerID = faceTemp.n_owner;
                const int &neighID = faceTemp.n_neighbor;
                faceIndex++;
                faceIDListVector[j].push_back(faceIndex);
                mesh->v_face.push_back(faceTemp);
                mesh->v_elem[ownerID].v_faceID.push_back(faceIndex);
                if (neighID != -1) mesh->v_elem[neighID].v_faceID.push_back(faceIndex);
            }
        }
    }
}

int MeshConverter::SumNodeList(const std::vector<int> &nodeList)
{
    const int size = (int)nodeList.size();
    int sum = 0;
    for (int i = 0; i < size; ++i) sum += nodeList[i];
    return sum / size;
}

bool MeshConverter::JudgeNodeList(const Face &face1, const Face &face2)
{
    const int size1 = (int)face1.GetNodeSize();
    const int size2 = (int)face2.GetNodeSize();

    if (size1 != size2) return false;

    for (int i = 0; i < size1; ++i)
    {
        bool flag = false;
        for (int j = 0; j < size2; ++j)
        {
            if (face1.v_nodeID[i] == face2.v_nodeID[j])
            {
                flag = true;
                break;
            }
        }

        if (!flag) return false;        
    }
    return true;
}

