/*!
@file
Includes all the adaptors for external Boost libraries.

@copyright <PERSON> 2013-2016
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_HANA_EXT_BOOST_HPP
#define BOOST_HANA_EXT_BOOST_HPP

//! @ingroup group-ext
//! @defgroup group-ext-boost Other Boost adapters
//! Adapters for miscellaneous heterogeneous containers in Boost.

#include <boost/hana/ext/boost/fusion.hpp>
#include <boost/hana/ext/boost/mpl.hpp>
#include <boost/hana/ext/boost/tuple.hpp>

#endif // !BOOST_HANA_EXT_BOOST_HPP
