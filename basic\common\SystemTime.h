﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SystemTime.h
//! <AUTHOR>
//! @brief 系统时间相关功能.
//! @date 2022-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-07-22 乔龙
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_SystemTime_
#define _basic_common_SystemTime_

#include <string>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <stdlib.h>
#include <time.h>

using namespace std::chrono;

class SystemTime
{
public:
    /**
     * @brief 构造函数，初始化开始时间
     * @note 构造函数会自动记录对象创建时的时间点
     */
    SystemTime();

    /**
     * @brief 获取从开始时间到当前时间的流逝时间
     * @return 以秒为单位的流逝时间
     * @note 精度为毫秒级
     */
    double GetElapsedTime();

    /**
     * @brief 更新时间点
     * @note 更新end时间点为当前系统时间
     */
    void UpdateTime();

    /**
     * @brief 打印当前系统时间
     * @note 格式为"YYYY-MM-DD HH:MM:SS"
     */
    void PrintNowTime();
    
    /**
     * @brief 打印基于SystemTime的统计时间
     * @param[in] processDescription 过程描述信息
     * @param[in] startTime 起始时间(SystemTime类型)
     * @note 输出格式: "[过程描述] 耗时: X.XXX秒"
     * @note 相比clock_t版本精度更高
     */
    friend void PrintProcessTime(const std::string &processDescription, SystemTime &startTime);
    
private:
    time_point<system_clock> start;
    time_point<system_clock> end;
};

/**
 * @brief 打印当前系统时间
 * @note 全局函数，格式为"YYYY-MM-DD HH:MM:SS"
 */
void PrintSystemTime();

/**
 * @brief 打印基于clock_t的统计时间
 * @param[in] processDescription 过程描述信息
 * @param[in] startTime 起始时间(clock_t类型)
 * @note 输出格式: "[过程描述] 耗时: X.XXX秒"
 */
void PrintProcessTime(const std::string &processDescription, clock_t &startTime);

#endif