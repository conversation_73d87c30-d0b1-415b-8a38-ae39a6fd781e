/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/

#if FUSION_MAX_LIST_SIZE <= 10
#include <boost/fusion/container/list/detail/cpp03/preprocessed/list10_fwd.hpp>
#elif FUSION_MAX_LIST_SIZE <= 20
#include <boost/fusion/container/list/detail/cpp03/preprocessed/list20_fwd.hpp>
#elif FUSION_MAX_LIST_SIZE <= 30
#include <boost/fusion/container/list/detail/cpp03/preprocessed/list30_fwd.hpp>
#elif FUSION_MAX_LIST_SIZE <= 40
#include <boost/fusion/container/list/detail/cpp03/preprocessed/list40_fwd.hpp>
#elif FUSION_MAX_LIST_SIZE <= 50
#include <boost/fusion/container/list/detail/cpp03/preprocessed/list50_fwd.hpp>
#else
#error "FUSION_MAX_LIST_SIZE out of bounds for preprocessed headers"
#endif
