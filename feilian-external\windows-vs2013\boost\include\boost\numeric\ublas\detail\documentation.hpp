//
//  Copyright (c) 2000-2004
//  <PERSON><PERSON>, <PERSON>
//
//  Distributed under the Boost Software License, Version 1.0. (See
//  accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
//  The authors gratefully acknowledge the support of
//  GeNeSys mbH & Co. KG in producing this work.
//

// this file should not contain any code, but the documentation
// global to all files

/** \namespace boost::numeric::ublas
        \brief contains all important classes and functions of uBLAS

        all ublas definitions ...
        \todo expand this section
 */

/** \defgroup blas1 Level 1 BLAS 
        \brief level 1 basic linear algebra subroutines
*/

/** \defgroup blas2 Level 2 BLAS
        \brief level 2 basic linear algebra subroutines 
*/

/** \defgroup blas3 Level 3 BLAS
        \brief level 3 basic linear algebra subroutines 
*/
