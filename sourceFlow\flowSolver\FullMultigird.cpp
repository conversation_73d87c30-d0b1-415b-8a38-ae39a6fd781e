﻿#include "sourceFlow/flowSolver/FullMultigird.h"

FullMultigird::FullMultigird(SubMesh *subMesh_,
							 std::vector<Package::FlowPackage *> &flowPackageVector_,
							 std::vector<Time::Flow::FlowTimeManager *> &timeSchemeVector_,
							 FlowResultsProcess *resultProcess_)
							 : subMesh(subMesh_), flowPackageVector(flowPackageVector_),
							 timeSchemeVector(timeSchemeVector_),
							 flowConfigure(flowPackageVector_[0]->GetFlowConfigure()),
							 multigridSolver(subMesh_, flowPackageVector_, timeSchemeVector_)
{
	// 根据输入参数，确定多重网格总层数
	multigridLevel = flowConfigure.GetAcceleration().multigridSolver.level;

	// 创建残值容器
	resultProcessList.push_back(resultProcess_);
	for (int level = 1; level < multigridLevel; ++level)
		resultProcessList.push_back(new FlowResultsProcess(subMesh, flowPackageVector));
}

FullMultigird::~FullMultigird()
{
	for (int level = 1; level < multigridLevel; ++level)
	{
        if (resultProcessList[level] != nullptr)
        {
            delete resultProcessList[level];
            resultProcessList[level] = nullptr;
        }
	}
}

void FullMultigird::Solve()
{
	if (GetMPIRank() == 0) Print("\n    Caluculate residuals ...");
	timeSchemeVector[0]->SetFineGridLevel(0);

	timeSchemeVector[0]->CalculateResidual();
    CheckStatus(2201);

	resultProcessList[0]->CalculateMonitorValues();
    CheckStatus(2202);

    resultProcessList[0]->OutputMonitorValues(0);
    CheckStatus(2203);

	const Scalar &criteria = flowConfigure.GetControl().innerLoop.criteria;
	const int convergenceFlag = resultProcessList[0]->CheckConvergence(criteria);
    CheckStatus(2204);

	if (convergenceFlag == 1)
	{
		resultProcessList[0]->SaveFinalResults();
    	CheckStatus(2205);
		return;
	}

	for (int level = 0; level < multigridLevel - 1; ++level)
	{
		multigridSolver.RestrictionPrimary(level);
    	CheckStatus(2206);

		timeSchemeVector[level + 1]->UpdateBoundaryCondition();
    	CheckStatus(2207);

		timeSchemeVector[level + 1]->UpdateGradientAndMuT();
    	CheckStatus(2208);

		multigridSolver.RestrictionResidual(level);
		CheckStatus(2209);

		resultProcessList[level + 1]->Initialize(level + 1);
		CheckStatus(2210);

		resultProcessList[level + 1]->CalculateMonitorValues();
		CheckStatus(2211);
	}

	if (GetMPIRank() == 0) Print("\n    Begin full multigrid cycle ...");
	for (int currentSequence = multigridLevel - 1; currentSequence > 0; --currentSequence)
	{
		const int &startLevel = currentSequence;

		if (GetMPIRank() == 0) Print("\n      Solve at level =  " + ToString(startLevel));

		// 全多重循环总迭代步数及保存间隔
		const int &innerLoopSteps = flowConfigure.GetControl().initialization.fullMultigridSteps;

		// 多重网格求解器初始化
		multigridSolver.Initialize(startLevel, resultProcessList[startLevel]);
		CheckStatus(2212);

		// 流场迭代求解
		for (int innerStep = 0; innerStep < innerLoopSteps; ++innerStep)
		{
			// 多重网格求解
			multigridSolver.Solve(innerStep);
			CheckStatus(2213);

			// 结束判断
			const Scalar &criteria = flowConfigure.GetControl().initialization.fullMultigridCriteria;
			const int convergenceFlag = resultProcessList[startLevel]->CheckConvergence(criteria);
			CheckStatus(2214);

			if (innerStep + 1 == innerLoopSteps || convergenceFlag == 1)
			{
				for (int level = startLevel; level > 0; --level)
				{
					// 由粗网格插值计算细网格
					multigridSolver.InitializeFromCoarse(level - 1);
					CheckStatus(2215);

					// 更新细网格边界条件
					timeSchemeVector[level - 1]->UpdateBoundaryCondition();
					CheckStatus(2216);

					// 光顺细网格解
                    multigridSolver.SmoothField(level - 1);
                    CheckStatus(2217);
					
            		// 更新细网格边界条件
            		timeSchemeVector[level - 1]->UpdateBoundaryCondition();
                    CheckStatus(2218);

            		// 更新细网格梯度和湍流粘性系数
            		timeSchemeVector[level - 1]->UpdateGradientAndMuT();
                    CheckStatus(2219);
				}
				break;
			}
		}

		multigridSolver.InitializeFromCoarse(startLevel - 1);
		CheckStatus(2220);
		
		timeSchemeVector[startLevel - 1]->UpdateBoundaryCondition();
		CheckStatus(2221);

        multigridSolver.SmoothField(startLevel - 1);
        CheckStatus(2222);
	}
}
