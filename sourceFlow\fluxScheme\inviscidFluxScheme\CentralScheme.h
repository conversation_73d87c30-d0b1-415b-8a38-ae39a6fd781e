﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file CentralScheme.h
//! <AUTHOR>
//! @brief NS方程无粘项通量求解格式：中心格式并附加人工粘性项
//! @date 2021-03-29
//
//------------------------------修改日志----------------------------------------
// 2021-11-11 李艳亮
//    说明：进一步整合，并添加不同的方法：
//         (1)用于四阶项L(U)的计算方法选择：1) 用伪Laplacian算子（遍历周围单元）
//                                        2) 用二阶精度左右面值的差分替代
//         (2)添加几何权重因子theta的选择： 1) 常值   2) 按几何形状计算（仅构造函数调用1次，动网格需要修改）
//         (3)对细网格和粗网格设置不同的二阶与四阶系数，保障粗网格的鲁棒性
//         (4)二阶与四阶系数不同的计算方式： 1）来自UNSMB  2）参考书籍
//
// 2021-07-16 乔龙
//    说明：格式进一步修改完善，并添加湍流部分。
//
// 2021-03-29 乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_CentralScheme_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_CentralScheme_

#include "sourceFlow/fluxScheme/inviscidFluxScheme/InviscidFluxScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief NS方程无粘项通量中心格式求解类
 * 附加人工粘性项的中心格式
 * 
 */
class CentralScheme : public InviscidFluxScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
    CentralScheme(Package::FlowPackage &data,                  
                  Limiter::Limiter *limiter,
                  Flux::Flow::Precondition::Precondition *precondition);

    /**
     * @brief 析构函数
     * 
     */
    ~CentralScheme();

    /**
     * @brief 对流项平均残差累加
     * 
     */
    void AddAverageResidual();

    /**
     * @brief 对流项耗散残差累加
     * 
     */
    void AddDissipationResidual();

    /**
     * @brief 计算面通量
     * 
     * @param[in] face 当前面
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
    NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue);

    /**
     * @brief 采用一阶格式计算平均面通量
     * 
     * @param[in] faceID 当前面编号
     * @param[out] faceFlux 当前面的通量
     */
    void CalculateFaceFluxAverage(const int &faceID, NSFaceFlux &faceFlux);

private:    
    /**
     * @brief 计算人工粘性项的相关物理场（差分场）
     * 
     */
    void CalculateLaplace();        

    /**
     * @brief 不考虑预处理的面通量计算
     * 
     * @param[in] faceID 面编号
     * @param[in, out] faceFlux 面通量
     */
    void CalculateFaceFluxNonPrecondition(const int &faceID, NSFaceFlux &faceFlux);
    
    /**
     * @brief 考虑预处理的面通量计算
     * 
     * @param[in] faceID 面编号
     * @param[in, out] faceFlux 面通量
     */
    void CalculateFaceFluxPrecondition(const int &faceID, NSFaceFlux &faceFlux);
    
    /**
     * @brief 细网格计算面通量
     * 
     * @param[in] faceID 面编号
     * @param[in] deltaRho 面两侧密度差量
     * @param[in] deltaRhoU 面两侧动量差量
     * @param[in] deltaRhoE 面两侧能量差量
     * @param[in, out] faceFlux 面通量
     */
    void CalculateFaceFluxFine(const int &faceID, const Scalar &deltaRho, const Vector &deltaRhoU, const Scalar &deltaRhoE, NSFaceFlux &faceFlux);
    
    /**
     * @brief 粗网格计算面通量
     * 
     * @param[in] faceID 面编号
     * @param[in] deltaRho 面两侧密度差量
     * @param[in] deltaRhoU 面两侧动量差量
     * @param[in] deltaRhoE 面两侧能量差量
     * @param[in, out] faceFlux 面通量
     */
    void CalculateFaceFluxCoarse(const int &faceID, const Scalar &deltaRho, const Vector &deltaRhoU, const Scalar &deltaRhoE, NSFaceFlux &faceFlux);
    
    /**
     * @brief 计算单元面的谱半径，不考虑谱半径
     * 
     * @param[in] faceID 面编号
     * @return const Scalar
     */
    const Scalar CalculateSpectralRadiusSideNonPre(const int &faceID, const int &elementID);

    /**
     * @brief 计算单元面的谱半径，考虑谱半径
     * 
     * @param[in] faceID 面编号
     * @return const Scalar
     */
    const Scalar CalculateSpectralRadiusSidePre(const int &faceID, const int &elementID);

    /**
     * @brief 计算面两侧单元守恒量差量
     * 
     * @param[in] ownerID 单元编号
     * @param[in] neighID 单元编号
     * @param[out] deltaRho 密度差量
     * @param[out] deltaRhoU 速度差量
     * @param[out] deltaRhoE 能力差量
     */
    void CalculateDeltaValue(const int &ownerID, const int &neighID, Scalar &deltaRho, Vector &deltaRhoU, Scalar &deltaRhoE);

protected:
    const Scalar k0; ///< 简化人工粘性系数
    const Scalar k2; ///< 二阶人工粘性系数
    const Scalar k4; ///< 四阶人工粘性系数
    const Scalar maxK; ///< 最大粘性系数

    ElementField<Vector> *rhoU; ///< 动量
    ElementField<Scalar> *rhoE; ///< 能量
    
    ElementField<Scalar> *laplaceRho; ///< 质量二阶导数
    ElementField<Vector> *laplaceRhoU; ///< 动量二阶导数
    ElementField<Scalar> *laplaceRhoE; ///< 能量二阶导数
    
	ElementField<Scalar> *pressureSensor; ///< 压力探测器
	
	ElementField<Scalar> *elementInnerFaceSize; ///< 单元的内部面数量（对偶网格）

	/// 面通量计算函数指针
	void (CentralScheme::*CalculateDissipationFlux)(const int &, NSFaceFlux &);
    
	/// 不同层级网格面通量计算函数指针
	void (CentralScheme::*CalculateFaceFluxLevel)(const int &, const Scalar &, const Vector &, const Scalar &, NSFaceFlux &);
    
	/// 计算面谱半径的函数指针
	const Scalar (CentralScheme::*CalculateSpectralRadiusSide)(const int &, const int &);
    
    unsigned short nDim, nVar;
    Matrix Jacobian_i, Jacobian_j;
};

} // namespace Inviscid
} // namespace Flow
} // namespace Flux
#endif