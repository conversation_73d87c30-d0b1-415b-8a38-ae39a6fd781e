//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_LEXER_MAR_22_2007_1008PM)
#define BOOST_SPIRIT_LEXER_MAR_22_2007_1008PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/lexer/terminals.hpp>
#include <boost/spirit/home/<USER>/lexer/token_def.hpp>
#include <boost/spirit/home/<USER>/lexer/char_token_def.hpp>
#include <boost/spirit/home/<USER>/lexer/string_token_def.hpp>
#include <boost/spirit/home/<USER>/lexer/sequence.hpp>
#include <boost/spirit/home/<USER>/lexer/action.hpp>
#include <boost/spirit/home/<USER>/lexer/lexer.hpp>

#endif
