﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowBoundaryManager.h
//! <AUTHOR> 乔龙
//! @brief 边界条件的管理器类，根据各边界Patch的类型名称调用各边界条件类的更新方法
//! @date  2021-03-30
//
//------------------------------修改日志----------------------------------------
// 2024-10-31  尹强
// 说明：增加质量流量入口/出口、热通量/温度分布壁面边条的初始化;
//       增加质量流量结构体和归并函数的定义
//       修改短舱入口/出口面积、流量等归并函数，使其能够支持多个短舱入口/出口边界
//
// 2021-03-30  李艳亮 乔龙
// 说明：建立并规范化
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_FlowBoundaryManager_
#define _sourceFlow_boundaryCondition_FlowBoundaryManager_

#include "sourceFlow/boundaryCondition/Extrapolation.h"
#include "sourceFlow/boundaryCondition/FarField.h"
#include "sourceFlow/boundaryCondition/FlowBoundary.h"
#include "sourceFlow/boundaryCondition/InflowSpecify.h"
#include "sourceFlow/boundaryCondition/InflowTotalCondition.h"
#include "sourceFlow/boundaryCondition/NacelleExhaust.h"
#include "sourceFlow/boundaryCondition/NacelleInlet.h"
#include "sourceFlow/boundaryCondition/OutflowPressure.h"
#include "sourceFlow/boundaryCondition/MassFlow.h"
#include "sourceFlow/boundaryCondition/OversetBoundary.h"
#include "sourceFlow/boundaryCondition/ParallelBoundary.h"
#include "sourceFlow/boundaryCondition/Periodic.h"
#include "sourceFlow/boundaryCondition/Symmetry.h"
#include "sourceFlow/boundaryCondition/SyntheticJet.h"
#include "sourceFlow/boundaryCondition/WallAdiabatic.h"
#include "sourceFlow/boundaryCondition/WallIsothermal.h"
#include "sourceFlow/boundaryCondition/WallGivenHeatFlux.h"
#include "sourceFlow/boundaryCondition/WallGivenTemperature.h"
#include "sourceFlow/boundaryCondition/WallMoving.h"
#include "sourceFlow/boundaryCondition/WallRotate.h"
#include "sourceFlow/boundaryCondition/WallSlipping.h"
#include "sourceFlow/boundaryCondition/WallRiblets.h"
#include "sourceFlow/boundaryCondition/WindTunnelWallPressure.h"
#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"


/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场边界条件管理器类
 * 
 */
class FlowBoundaryManager
{
public:
    /**
     * @brief 构造函数，创建流场边界条件管理器对象
     * 
     * @param[in, out] data 包含各类场的数据包
     */
    FlowBoundaryManager(Package::FlowPackage &data);

    /**
     * @brief 析构函数
     * 删除流场边界条件管理器中创建的边界条件对象
     * 
     */
    ~FlowBoundaryManager();

    /**
     * @brief 初始化边界条件类
     * 根据各边界Patch的类型名称，初始化边界条件类并存储在flowBoundary
     * 
     * @param precondition 
     */
    void SetBoundaryCondition(Flux::Flow::Precondition::Precondition *precondition);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 更新边界条件
     * 逐边界调用各边界条件类的更新方法
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();

    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 更新边界残值
     * 
     */
    void UpdateBoundaryResidual();

    /**
     * @brief 返回边界类型容器
     * 
     * @return const std::vector<Boundary::Type>& 
     */
    const std::vector<Boundary::Type> &GetBoundaryVector();

    /**
    * @brief 计算发动机入口面积和出口流量
    *
     * @param[in] initializeFlag 是否初始化标识，true需要初始化
    */
    void CalculateNacelleInfo(const bool &initializeFlag);
    
    /**
     * @brief 设置边界条件调用的空间离散格式
     * 
     * @param[in] InviscidFluxScheme_ 对流通量指针
     */
    void SetFluxScheme(Flux::Flow::Inviscid::InviscidFluxScheme *InviscidFluxScheme_, Flux::Flow::Viscous::ViscousFluxScheme *viscousFluxScheme_);

	/**
	* @brief 计算质量流量入口/出口边界的实际质量流量
	*
	* @param[in] initializeFlag 是否初始化标识，true需要初始化
	*/
	void CalculateMassflow();

private:
    Package::FlowPackage &flowPackage; ///< 包含各类场的数据包
    const Configure::Flow::FlowConfigure &flowConfigure; ///< 控制参数
    
    int flowBoundarySize; ///< 流场物理边界数量
    std::vector<FlowBoundary*> flowBoundary; ///< 存放边界条件类指针的容器
    std::vector<Boundary::Type> flowBoundaryType; ///< 存放边界条件类型的容器    

    ParallelBoundary parallelB; ///< 并行边界
    OversetBoundary *oversetB; //重叠边界

    const int &level; ///< 当前网格层级
    
    bool hasNacelleInlet; ///< 发动机进口边界标识
    bool hasNacelleExhaust; ///< 发动机出口边界标识
	bool hasMassFlowInlet; ///< 质量流量入口边界标识
	bool hasMassFlowOutlet; ///< 质量流量出口边界标识   

    struct NacelleStruct
    {
        bool updateFlag; ///< 进口边界流量计算标识
        Scalar inArea; ///< 短舱进口边界的面积
        Scalar outMassFlow; ///< 短舱进口边界的流量（当匹配使用时由出口积分计算完成）

		NacelleStruct()
        {
            updateFlag = false;
            inArea = 0.0;
            outMassFlow = 0.0;
        }
    } ; ///< 短舱内外流一体化计算需要全局收集和交换的数据

	std::vector<NacelleStruct> nacelleInlet;
	std::vector<NacelleStruct> nacelleOutlet;

	struct MassFlowStruct
	{
		Scalar Area; ///< 边界面积，预留的，暂时不同
		Scalar massFlux; ///< 边界的质量通量 rho * V，预留的，暂时不用
		Scalar massFlowRate; ///< 边界的质量流率 rho * V * Area

		MassFlowStruct()
		{
			Area = false;
			massFlux = 0.0;
			massFlowRate = 0.0;
		}
	} ; ///< 质量流量流入和流出需要全局收集和交换的数据

	std::vector<MassFlowStruct> massFlowInlet;
	std::vector<MassFlowStruct> massFlowOutlet;
};

} // namespace Flow
} // namespace Boundary

# endif