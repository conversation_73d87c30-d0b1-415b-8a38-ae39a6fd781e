//  (C) Copyright <PERSON> 2008.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//
// Forwarding header for Borland C++:
//
#if !defined(BOOST_TR1_UNORDERED_H_INCLUDED)
#  define BOOST_TR1_UNORDERED_H_INCLUDED
#  include <unordered_set.>
#  include <unordered_map.>
#endif


