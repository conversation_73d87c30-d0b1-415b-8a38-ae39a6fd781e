//-----------------------------------------------------------------------------
// boost variant.hpp header file
// See http://www.boost.org/libs/variant for documentation.
//-----------------------------------------------------------------------------
//
// Copyright (c) 2003
// <PERSON>, Itay <PERSON>n
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_VARIANT_HPP
#define BOOST_VARIANT_HPP

// variant "main"
#include <boost/variant/variant.hpp>
#include <boost/variant/recursive_variant.hpp>
#include <boost/variant/recursive_wrapper.hpp>

// common applications
#include <boost/variant/get.hpp>
#include <boost/variant/apply_visitor.hpp>
#include <boost/variant/static_visitor.hpp>
#include <boost/variant/visitor_ptr.hpp>

#endif // BOOST_VARIANT_HPP
