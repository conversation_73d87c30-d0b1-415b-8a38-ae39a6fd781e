﻿#include "sourceFlow/resultsProcess/ResidualProcess.h"

ResidualProcess::ResidualProcess(std::vector<Package::FlowPackage *> flowPackageVector_)
    : flowPackageVector(flowPackageVector_)
{
    const auto &flowConfigure = flowPackageVector_[0]->GetFlowConfigure();
    
    // 判定是二维还是三维
    dimension3D = flowPackageVector_[0]->GetMeshStruct().dim3;

	unsteady = flowPackageVector_[0]->GetUnsteadyStatus().unsteadyFlag;

    // 续算标识
    restartFlag = flowConfigure.GetControl().initialization.type == Initialization::Type::RESTART;

    // 计算起始步
    beginStep = 0;
    if (restartFlag) beginStep = flowConfigure.GetControl().initialization.restartStep;

    const auto &residuals = flowConfigure.GetMonitor().residuals;
    residualsInLog10 = residuals.log10Flag;
    normalizedResiduals = residuals.normalizedFlag;

    const int nTurbulent = flowPackageVector_[0]->GetField().turbulence.size();
    nResidualTotal = 5 + nTurbulent;
    totalResidualStrings = {"ResMass", "ResMomX", "ResMomY", "ResMomZ", "ResEngy"};
    for (int k=0; k < nTurbulent; k++)
    {
        std::string temp = flowPackageVector_[0]->GetField().turbulence[k]->GetName();
        if(temp.size() > 4) temp = temp.substr(0, 4);
        for (int len = 1; len < temp.size(); len++) temp[len] = tolower(temp[len]);
        totalResidualStrings.push_back("Res" + temp);
    }

    monitorResidualName.clear();
    maximumResidualName.clear();
    for (int i = 0; i < nResidualTotal; i++)
    {
        monitorResidualName.push_back(totalResidualStrings[i]);
        maximumResidualName.push_back(totalResidualStrings[i] + "Max");
    }
    nMonitorResidual = monitorResidualName.size();
    nMaximumResidual = maximumResidualName.size();
    
    monitorResidual.resize(nMonitorResidual);
    maximumResidual.resize(nMaximumResidual);

    this->Initialize(0);
}

void ResidualProcess::Initialize(const int &level)
{
    // 初始化监测残值、参考残值、最大残值容器
    monitorResidual.clear();
    maximumResidual.clear();
    monitorResidualReference.clear();
    monitorResidual.resize(nMonitorResidual);
    maximumResidual.resize(nMaximumResidual);
    monitorResidualReference.resize(nMonitorResidual, -1.0);

    if (GetMPIRank() == 0 && restartFlag && normalizedResiduals && !unsteady)
        this->GetRestartResiduals(monitorResidual0, maximumResidual0);

    const auto &flowPackage = *flowPackageVector[level];
    residualMass = flowPackage.GetResidualField().residualMass;
    residualMomentum = flowPackage.GetResidualField().residualMomentum;
    residualEnergy = flowPackage.GetResidualField().residualEnergy;
    residualTurbulence = flowPackage.GetResidualField().residualTurbulence;

    // 计算全局网格单元数量
    Mesh *mesh = flowPackage.GetMeshStruct().mesh;
    nElementGlobal = mesh->GetElementNumberReal();
    
#if defined(_BaseParallelMPI_)
    SumAllProcessor(nElementGlobal, 0);
    MPIBroadcast(nElementGlobal, 0);
#endif
}

int ResidualProcess::CheckConvergence(const Scalar &criteria)
{
    int convergenceFlag = 0;

    //判断内循环是否达到收敛条件
    if (GetMPIRank() == 0)
    {
        if (residualsInLog10) convergenceFlag = monitorResidual[0] < log10(Max(criteria, SMALL)) ? 1 : 0;
        else                  convergenceFlag = monitorResidual[0] < criteria ? 1 : 0;

        if (convergenceFlag == 0)
        {
            for (int i = 0; i < nMonitorResidual; i++)
            {
                if ( ( residualsInLog10 && monitorResidual[i] > 30.0 ) ||
                     (!residualsInLog10 && monitorResidual[i] > 1.0E30 ) )
                {
                     convergenceFlag = -1;
                     break;
                }
            }
        }
    }
    
#if defined(_BaseParallelMPI_)
    MPIBroadcast(convergenceFlag, 0);
#endif

    if (convergenceFlag == -1)
    {
		if (GetMPIRank() == 0)
		{
			FatalError("InnerLoop::Solve: solver is diverged!");
			return convergenceFlag;
		}
    }

    return convergenceFlag;
}

void ResidualProcess::GetRestartResiduals(std::vector<Scalar> &monitorResidual0, std::vector<Scalar> &maximumResidual0)
{
    std::fstream outFile;
    const auto &flowConfigure = flowPackageVector[0]->GetFlowConfigure();
    const std::string &resultName = flowConfigure.GetCaseName();
    const std::string &resultPath = flowConfigure.GetControl().resultSavePath;
	outFile.open(resultPath + resultName + "_res" + ".dat", std::fstream::in);

	monitorResidual0.clear();
	maximumResidual0.clear();

    // 读取残差文件第一行（变量名），并填充到tileVector容器中
    std::string stringTemp;
    getline(outFile, stringTemp);
    std::istringstream stringStream(stringTemp);
    std::vector<std::string> tileVector;
    while(!stringStream.eof())
    {
        stringStream >> stringTemp;
        if (!stringTemp.empty()) tileVector.push_back(stringTemp);
    }
    if (!tileVector.empty())
        tileVector.erase(tileVector.begin(), tileVector.begin() + 2);
	else
	{
		FatalError("ResidualProcess::GetRestartResiduals: no results");
		return;
	}

    // 读取指定行（监测量），并将数值填充到valueVector中
    double step = 0;
    std::vector<Scalar> valueVector;
    while(getline(outFile, stringTemp))
    {
        if(!stringTemp.empty())
        {
            std::istringstream stringStream1(stringTemp);
            stringStream1 >> step;
			
            if(step == beginStep)
            {
                valueVector.push_back(step);
                while(!stringStream1.eof())
                {
                    Scalar value;
                    stringStream1 >> value;
                    valueVector.push_back(value);
                }
                break;
            }
        }
    }

    // 循环tileVector容器中的变量名，在所有监测变量名中查找器编号
    for (int j = 0; j < tileVector.size(); j++)
    {
        bool monitorFlag = false;
        for (int i = 0; i < nResidualTotal; i++)
            if (tileVector[j] == totalResidualStrings[i]) monitorFlag = true;

        if (monitorFlag)
        {
            if (residualsInLog10) monitorResidual0.push_back(pow(10.0, valueVector[j]));
            else                  monitorResidual0.push_back(valueVector[j]);
        }

        bool maximumFlag = false;
        for (int i = 0; i < nResidualTotal; i++)
            if (tileVector[j] == totalResidualStrings[i] + "Max") maximumFlag = true;

        if (maximumFlag)
        {
            if (residualsInLog10) maximumResidual0.push_back(pow(10.0, valueVector[j]));
            else                  maximumResidual0.push_back(valueVector[j]);
        }
    }

    outFile.close();
}

std::vector<Scalar> &ResidualProcess::CalculateMonitorResidual(const int &currentLevel)
{
    Mesh *currentMesh = flowPackageVector[currentLevel]->GetMeshStruct().mesh;

    // 监测残值置零
    for (int i = 0; i < nMonitorResidual; i++) monitorResidual[i] = Scalar0;

    // 当地网格上所有残值求和
    std::vector<Scalar> residualVector(nResidualTotal);
    const int elementNumber = currentMesh->GetElementNumberInDomain();
    const Scalar scalarTemp = 1.0 / (Scalar)nElementGlobal;
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = currentMesh->GetElementIDInDomain(index);
        
        residualVector[0] = residualMass->GetValue(elementID);
        residualVector[1] = residualMomentum->GetValue(elementID).X();
        residualVector[2] = residualMomentum->GetValue(elementID).Y();
        residualVector[3] = residualMomentum->GetValue(elementID).Z();
        residualVector[4] = residualEnergy->GetValue(elementID);
        for (int k = 0; k < flowPackageVector[currentLevel]->GetTurbulentStatus().nVariable; k++)
            residualVector[5+k] = residualTurbulence[k]->GetValue(elementID);
        
        const Scalar volumeInverse = 1.0 / currentMesh->GetElement(elementID).GetVolume();
        for (int i=0; i < nResidualTotal; i++)
        {
            const Scalar localResidual = fabs(residualVector[i]) * volumeInverse;
            monitorResidual[i] += scalarTemp * localResidual * localResidual;
        }
    }

#if defined(_BaseParallelMPI_)
    for (int i = 0; i < nMonitorResidual; i++) SumAllProcessor(monitorResidual[i], 0);
#endif
    
    // 0号进程监测残值计算
    if (GetMPIRank() == 0)
    {
        for (int i = 0; i < nMonitorResidual; i++)
        {
            monitorResidual[i] = sqrt(monitorResidual[i]);

            // 相对监测残值计算
            if(normalizedResiduals)
            {
                // 修改参考值
                if (monitorResidualReference[i] < 0.0 && monitorResidual[i] > SMALL)
                {
                    if(restartFlag && !unsteady) monitorResidualReference[i] = monitorResidual[i] /  Max(monitorResidual0[i], SMALL);
                    else                         monitorResidualReference[i] = monitorResidual[i];
                }

                // 计算相对残值
                if (monitorResidualReference[i] > SMALL) monitorResidual[i] /= monitorResidualReference[i];
                else                                     monitorResidual[i] = 1.0;
            }

            if(residualsInLog10) monitorResidual[i] = log10(Max(monitorResidual[i], SMALL));
        }
    }
    
    return monitorResidual;
}

std::vector<Scalar> &ResidualProcess::CalculateMaximumResidual(const int &currentLevel)
{
    Mesh *currentMesh = flowPackageVector[currentLevel]->GetMeshStruct().mesh;

    // 监测残值置零
    for (int i = 0; i < nMaximumResidual; i++) maximumResidual[i] = Scalar0;

    // 当地网格上所有残值求和
    std::vector<Scalar> residualVector(nResidualTotal);
    const int elementNumber = currentMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = currentMesh->GetElementIDInDomain(index);
        
        residualVector[0] = residualMass->GetValue(elementID);
        residualVector[1] = residualMomentum->GetValue(elementID).X();
        residualVector[2] = residualMomentum->GetValue(elementID).Y();
        residualVector[3] = residualMomentum->GetValue(elementID).Z();
        residualVector[4] = residualEnergy->GetValue(elementID);
        for (int k = 0; k < flowPackageVector[currentLevel]->GetTurbulentStatus().nVariable; k++)
            residualVector[5+k] = residualTurbulence[k]->GetValue(elementID);
        
        Scalar volumeInverse = 1.0 / currentMesh->GetElement(elementID).GetVolume();
        for (int i=0; i < nResidualTotal; i++)
        {
            const Scalar localResidual = fabs(residualVector[i]) * volumeInverse;
            if(localResidual > maximumResidual[i]) maximumResidual[i] = localResidual;
        }
    }

#if defined(_BaseParallelMPI_)
    for (int i = 0; i < nMaximumResidual; i++) MaxAllProcessor(maximumResidual[i], 0);
#endif
    
    // 0号进程监测残值计算
    if (GetMPIRank() == 0)
    {
        for (int i = 0; i < nMaximumResidual; i++)
        {
            // 相对监测残值计算
            if(normalizedResiduals) maximumResidual[i] /= Max(monitorResidualReference[i], SMALL);
            
            if(residualsInLog10) maximumResidual[i] = log10(Max(maximumResidual[i], SMALL));
        }
    }

    return maximumResidual;
}