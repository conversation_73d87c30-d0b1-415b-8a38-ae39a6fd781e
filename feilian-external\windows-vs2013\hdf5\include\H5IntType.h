// C++ informative line for the emacs editor: -*- C++ -*-
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * Copyright by the Board of Trustees of the University of Illinois.         *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://support.hdfgroup.org/ftp/HDF5/releases.  *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

#ifndef __H5IntType_H
#define __H5IntType_H

namespace H5 {

class PredType;

/*! \class IntType
    \brief IntType is a derivative of a DataType and operates on HDF5
    integer datatype.
*/
//  Inheritance: AtomType -> DataType -> H5Object -> H5Location -> IdComponent
class H5_DLLCPP IntType : public AtomType {
   public:
        // Creates an integer type using a predefined type
        IntType(const PredType& pred_type);

        // Gets the integer datatype of the specified dataset
        IntType(const DataSet& dataset);

        // Retrieves the sign type for an integer type
        H5T_sign_t getSign() const;

        // Sets the sign proprety for an integer type.
        void setSign(H5T_sign_t sign) const;

        ///\brief Returns this class name.
        virtual H5std_string fromClass () const { return("IntType"); }

        // Default constructor
        IntType();

        // Creates a integer datatype using an existing id
        IntType(const hid_t existing_id);

        // Copy constructor: makes copy of IntType object
        IntType(const IntType& original);

        // Noop destructor.
        virtual ~IntType();

}; // end of IntType
} // namespace H5

#endif // __H5IntType_H
