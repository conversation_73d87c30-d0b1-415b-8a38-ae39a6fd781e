﻿#include "sourceFlow/boundaryCondition/ExternalBoundary.h"

namespace Boundary
{
namespace Flow
{

ExternalBoundary::ExternalBoundary(const int &boundaryPatchID, Package::FlowPackage &data)
    :
    FlowBoundary(boundaryPatchID, data)
{  
}

void ExternalBoundary::AddDiffusiveResidual()
{
    if(!nodeCenter) this->AddDiffusiveResidualCellCenter();
    
    return;
}

void ExternalBoundary::AddConvectiveResidual()
{
    if (implicitFlag) this->AddConvectiveResidualExternal();
    else              this->AddConvectiveResidualAverage();
    
    this->AddMRFConvectiveResidualAverage();
}

void ExternalBoundary::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;
    
    return;
}

}// namespace Flow
}// namespace Boundary
