 #pragma once
#include "basicTypes.h"
#include "ItemAddress.h"
namespace tecplot { namespace ___3933 { class FESubzonePartitionerInterface { public: virtual ~FESubzonePartitionerInterface() {} virtual ___465                  numCellsInZone() const = 0; virtual ___2090::SubzoneOffset_t ___2783() const = 0; virtual ___2090::ItemOffset_t    ___2782(___2090::SubzoneOffset_t ___469) const = 0; virtual ___465                  ___4608(___2090 ___451) const = 0; virtual ___2090                  szCoordinateAtZoneCell(___465 zoneCell) const = 0; virtual ___2718                  numNodesInZone() const = 0; virtual ___2090::SubzoneOffset_t ___2823() const = 0; virtual ___2090::ItemOffset_t    ___2822(___2090::SubzoneOffset_t ___2734) const = 0; virtual ___2718                  ___4657(___2090 nodeAddress) const = 0; virtual ___2090                  ___3924(___2718 ___4656) const = 0; virtual void                         setNodeSubzoneCoordinate(___2718 ___4656, ___2090 ___2759) = 0; }; }}
