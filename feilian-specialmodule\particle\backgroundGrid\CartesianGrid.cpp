﻿#include "feilian-specialmodule/particle/backgroundGrid/CartesianGrid.h"

CartesianGrid::CartesianGrid(Mesh *mesh_) : mesh(mesh_)
{
    domMin = {INF, INF, INF}, domMax = -domMin;
    for (int i = 0; i < mesh->GetNodeNumber(); i++)
    {
        const Node &node = mesh->GetNode(i);
        domMin = Min(domMin, node);
        domMax = Max(domMax, node);
    }
    
    dim2 = mesh->GetMeshDimension() == Mesh::MeshDim::md2D;

    const Vector dmax = domMax - domMin;
    dX = dmax.X() / 10.0;
    dY = dmax.Y() / 10.0;
    dZ = dim2 ? 1.0 : dmax.Z() / 10.0;
    nGridsX = (int)(ceil((dmax.X() - SMALL) / dX));
    nGridsY = (int)(ceil((dmax.Y() - SMALL) / dY));
    nGridsZ = dim2 ? 1 : (int)(ceil((dmax.Z() - SMALL) / dZ));
    refSize = dim2 ? 4 : 8;
    refineSize = dim2 ? 4 : 8;
    maxLevel = 0;
    
    OctGrid.resize(nGridsX);
    for (int i = 0; i < nGridsX; i++)
    {
        OctGrid[i].resize(nGridsY);
        for (int j = 0; j < nGridsY; j++)
        {
            OctGrid[i][j].resize(nGridsZ);
            for (int k = 0; k < nGridsZ; k++)
            {
                OctGrid[i][j][k].center.SetX(domMin.X() + (i + 0.5) * dX);
                OctGrid[i][j][k].center.SetY(domMin.Y() + (j + 0.5) * dY);
                OctGrid[i][j][k].center.SetZ(dim2 ? Scalar0 : domMin.Z() + (k + 0.5) * dZ);
                OctGrid[i][j][k].level = 0;
            }
        }
    }
}

void CartesianGrid::CreateMapForMesh()
{
    for (int elemID = 0; elemID < mesh->GetElementNumberReal(); elemID++)
    {
        const Vector &center = mesh->GetElement(elemID).GetCenter();
        const int indexX = (int)((center.X() - domMin.X()) / dX);
        const int indexY = (int)((center.Y() - domMin.Y()) / dY);
        const int indexZ = (int)((center.Z() - domMin.Z()) / dZ);
        OctGrid[indexX][indexY][indexZ].elementIDList.push_back(elemID);
    }

    for (int i = 0; i < nGridsX; i++)
    {
        for (int j = 0; j < nGridsY; j++)
        {
            for (int k = 0; k < nGridsZ; k++)
            {
                Refine(&OctGrid[i][j][k]);
            }
        }
    }
}

void CartesianGrid::Refine(OctElement *parent)
{
    auto &elementIDList = parent->elementIDList;
    if (elementIDList.size() <= refSize) return;
    
    Vector lengthParent = Vector(dX, dY, dZ) * pow(2, -parent->level);
    Vector lengthChild = 0.5 * lengthParent;
    if (dim2)
    {
        lengthParent.SetZ(dZ);
        lengthChild.SetZ(dZ);
    }

    parent->children.resize(refineSize);
    for (int index = 0; index < refineSize; index++)
    {
        parent->children[index] = new OctElement(parent);
    }

    const int kSize = dim2 ? 1 : 2;
    for (int k = 0; k < kSize; k++)
    {
        for (int j = 0; j < 2; j++)
        {
            for (int i = 0; i < 2; i++)
            {
                const int index = i + 2 * j + 4 * k;
                OctElement *child0 = parent->children[index];
                child0->center.SetX(parent->center.X() + (i - 0.5) * lengthChild.X());
                child0->center.SetY(parent->center.Y() + (j - 0.5) * lengthChild.Y());
                child0->center.SetZ(dim2 ? Scalar0 : parent->center.Z() + (k - 0.5) * lengthChild.Z());
            }
        }
    }

    const Vector minParent = parent->center - lengthChild;
    for (int m = 0; m < elementIDList.size(); m++)
    {
        const int &elemID = elementIDList[m];
        const Vector &center = mesh->GetElement(elemID).GetCenter();
        const int indexX = (int)((center.X() - minParent.X()) / lengthChild.X());
        const int indexY = (int)((center.Y() - minParent.Y()) / lengthChild.Y());
        const int indexZ = dim2 ? 0 : (int)((center.Z() - minParent.Z()) / lengthChild.Z());
        const int index = indexX + 2 * indexY + 4 * indexZ;
        parent->children[index]->elementIDList.push_back(elemID);
    }

    elementIDList.clear();
    
    maxLevel = Max(maxLevel, parent->level + 1);
    
    for (int index = 0; index < refineSize; index++) Refine(parent->children[index]);
}

void CartesianGrid::GetAllElementIDSize(OctElement *parent, int &size)
{
    for (int i = 0; i < refineSize; i++)
    {
        const auto &child = parent->children[i];
		if (!child->elementIDList.empty())
		{
			size += child->elementIDList.size();
		}
		else if (!child->children.empty())
		{
			int size0 = 0;
			GetAllElementIDSize(child, size0);
			size += size0;
		}
    }
}

void CartesianGrid::GetAllElementID(OctElement *parent, std::vector<int> &elemIDList)
{
    for (int i = 0; i < refineSize; i++)
    {
        const auto &child = parent->children[i];
        if (!child->elementIDList.empty())
        {
            for (int j = 0; j < child->elementIDList.size(); j++)
                elemIDList.push_back(child->elementIDList[j]);
        }
        else if (!child->children.empty())
        {
            GetAllElementID(child, elemIDList);
        }
    }
}

std::vector<int> CartesianGrid::GetAdjanctElementID(const Vector &pos)
{
    const int i = (int)((pos.X() - domMin.X()) / dX);
    const int j = (int)((pos.Y() - domMin.Y()) / dY);
    const int k = (int)((pos.Z() - domMin.Z()) / dZ);

    if (!OctGrid[i][j][k].elementIDList.empty())
    {
        return OctGrid[i][j][k].elementIDList;
    }
    else if (!OctGrid[i][j][k].children.empty())
    {
        return GetAdjanctElementID(&OctGrid[i][j][k], pos);
    }
    else
    {
        FatalError("GetAdjanctElementID: ");
        return std::vector<int>();
    }
}

std::vector<int> CartesianGrid::GetAdjanctElementID(OctElement *parent, const Vector &pos)
{
    Vector lengthParent = Vector(dX, dY, dZ) * pow(2, -parent->level);
    Vector lengthChild = 0.5 * lengthParent;
    if (dim2)
    {
        lengthParent.SetZ(dZ);
        lengthChild.SetZ(dZ);
    }
    
    const Vector minParent = parent->center - lengthChild;
    const int indexX = (int)((pos.X() - minParent.X()) / lengthChild.X());
    const int indexY = (int)((pos.Y() - minParent.Y()) / lengthChild.Y());
    const int indexZ = dim2 ? 0 : (int)((pos.Z() - minParent.Z()) / lengthChild.Z());
    const int index = indexX + 2 * indexY + 4 * indexZ;

    const auto &child = parent->children[index];
    if (!child->elementIDList.empty())
    {
        return child->elementIDList;
    }
    else if (!child->children.empty())
    {
        return GetAdjanctElementID(child, pos);
    }
    else
	{
		int allElemSize = 0;
		GetAllElementIDSize(parent, allElemSize);

		std::vector<int> elemIDList;
		elemIDList.reserve(allElemSize);
        GetAllElementID(parent, elemIDList);

        return elemIDList;
    }
}