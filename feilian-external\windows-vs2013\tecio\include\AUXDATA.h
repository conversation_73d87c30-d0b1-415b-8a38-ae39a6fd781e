 #if !defined AUXDATA_h
 #define AUXDATA_h
 #if defined EXTERN
 #  undef EXTERN
 #endif
 #if defined ___260
 #  define EXTERN
 #else
 #  define EXTERN extern
 #endif
EXTERN ___372 ___250(char      ___472, ___372 ___2030); EXTERN ___372 ___249(const char *___2686); EXTERN ___264 ___231(___90 ___2893); EXTERN void ___236(___264 *___230); EXTERN ___372 ___251(void       *___2098, ___90  ___494); EXTERN ___264 ___234(___264 ___230, ___372  ConsiderRetain); EXTERN int32_t ___248(___264 ___230); EXTERN ___372 ___246(___264 ___230, const char *___2686, int32_t    *___2096); EXTERN void ___244(___264    ___230, int32_t       ___1926, const char    **___2686, ___90    *___4315, AuxDataType_e *___4236, ___372     *___3362); EXTERN ___372 ___245(___264    ___230, const char    *___2686, ___90    *___4315, AuxDataType_e *___4236, ___372     *___3362); EXTERN ___372 ___242(___264     ___230, const char    *___2686, ___372     *___4315, AuxDataType_e *___4236, ___372     *___3362); EXTERN ___372 ___267(___264    ___230, char const*   ___2686, ___90    ___4315, AuxDataType_e ___4236, ___372     ___3362); EXTERN ___372 ___238(___264 ___230, const char *___2686); EXTERN void AuxDataDeleteItems(___264 ___230); EXTERN ___372 ___232(___264 ___3947, ___264 ___3643); EXTERN void ___237(___264 ___230, int32_t    ___1926);
 #endif 
