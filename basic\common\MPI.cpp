﻿#include "basic/common/MPI.h"
#include "basic/common/Vector.h"

#include <time.h>

// #if defined(_BaseParallelMPI_)
// namespace mpi = boost::mpi;
// #endif

bool MPIInitialized()
{
#if defined(_BaseParallelMPI_)
    return boost::mpi::environment::initialized();
#endif
    return false;
}

void MPIAbort(const int errorCode)
{
#if defined(_BaseParallelMPI_)
    boost::mpi::environment::abort(errorCode);
#endif
}

int GetMPIRank()
{
#if defined(_BaseParallelMPI_)
    if(MPIInitialized()) return MPI::mpiWorld.rank();
#endif
    return 0;
}

int GetMPISize()
{
#if defined(_BaseParallelMPI_)
    if(MPIInitialized()) return MPI::mpiWorld.size();
#endif
    return 1;
}

void MPIBarrier()
{
#if defined(_BaseParallelMPI_)
    if(MPIInitialized()) MPI::mpiWorld.barrier();
#endif
}

void MPIIprobe(std::vector<std::pair<int, int>> &tags)
{
#if defined(_BaseParallelMPI_)
    if (!MPIInitialized()) return;

    clock_t time0 = clock();
    while (true)
    {
        bool stat = true;
        for (auto iter = tags.begin(); iter != tags.end(); ++iter)
        {
            if (MPI::mpiWorld.iprobe(iter->first, iter->second)) continue;
            stat = false;
            break;
        }
        if (stat) break;

        if ((clock() - time0) > 30 * CLOCKS_PER_SEC)
        {
            boost::mpi::environment::abort(1);
            boost::mpi::environment::finalized();
        }
    }
    tags.clear();
#endif
}

#if defined(_BaseParallelMPI_)
void MPIWaitAll(std::vector<boost::mpi::request> &requests)
{
    if (!MPIInitialized()) return;
    boost::mpi::wait_all(requests.begin(), requests.end());
    requests.clear();
}
#endif

template void MPIBroadcast(bool &phi, const int &rootID);
template void MPIBroadcast(int &phi, const int &rootID);
template void MPIBroadcast(double &phi, const int &rootID);
template void MPIBroadcast(std::vector<bool> &phi, const int &rootID);
template void MPIBroadcast(std::vector<int> &phi, const int &rootID);
template void MPIBroadcast(std::vector<double> &phi, const int &rootID);
template<class Type>
void MPIBroadcast(Type &phi, const int &rootID)
{
#if defined(_BaseParallelMPI_)
    if(!MPIInitialized()) return;
    broadcast(MPI::mpiWorld, phi, rootID);
#endif
}

template int MaxAllProcessor(int &phi, const int &rootID);
template int MaxAllProcessor(double &phi, const int &rootID);
template<class Type>
int MaxAllProcessor(Type &phi, const int &rootID)
{
    int maxProcessor = 0;

#if defined(_BaseParallelMPI_)
    if(!MPIInitialized()) return maxProcessor;
    
    if (MPI::mpiWorld.rank() == rootID)
    {
        std::vector<Type> phiVector(MPI::mpiWorld.size());
        boost::mpi::gather(MPI::mpiWorld, phi, phiVector, rootID);

        for (int j = 1; j < MPI::mpiWorld.size(); ++j)
        {
            if(phiVector[j] > phi)
            {
                phi = phiVector[j];
                maxProcessor = j;
            }
        }
    }
    else
    {
        boost::mpi::gather(MPI::mpiWorld, phi, rootID);
    }
#endif

    return maxProcessor;
}

template int MinAllProcessor(int &phi, const int &rootID);
template int MinAllProcessor(double &phi, const int &rootID);
template<class Type>
int MinAllProcessor(Type &phi, const int &rootID)
{
    int minProcessor = 0;

#if defined(_BaseParallelMPI_)
    if(!MPIInitialized()) return minProcessor;
    
    if (MPI::mpiWorld.rank() == rootID)
    {
        std::vector<Type> phiVector(MPI::mpiWorld.size());
        boost::mpi::gather(MPI::mpiWorld, phi, phiVector, rootID);

        for (int j = 1; j < MPI::mpiWorld.size(); ++j)
        {
            if(phiVector[j] < phi)
            {
                phi = phiVector[j];
                minProcessor = j;
            }
        }
    }
    else
    {
        boost::mpi::gather(MPI::mpiWorld, phi, rootID);
    }
#endif

    return minProcessor;
}

template void SumAllProcessor(int &phi, const int &rootID);
template void SumAllProcessor(double &phi, const int &rootID);
template void SumAllProcessor(Vector &phi, const int &rootID);
template<class Type>
void SumAllProcessor(Type &phi, const int &rootID)
{
#if defined(_BaseParallelMPI_)
    if(!MPIInitialized()) return;

    if (MPI::mpiWorld.rank() == rootID)
    {
        std::vector<Type> phiVector;
        boost::mpi::gather(MPI::mpiWorld, phi, phiVector, rootID);

        for (int j = 1; j < MPI::mpiWorld.size(); ++j)
            phi += phiVector[j];
    }
    else
    {
        boost::mpi::gather(MPI::mpiWorld, phi, rootID);
    }
#endif

    return;
}
