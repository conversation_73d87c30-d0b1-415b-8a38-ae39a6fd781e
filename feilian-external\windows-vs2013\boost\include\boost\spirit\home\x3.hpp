/*=============================================================================
    Copyright (c) 2001-2013 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_X3_MARCH_04_2007_0852PM)
#define BOOST_SPIRIT_X3_MARCH_04_2007_0852PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/auxiliary.hpp>
#include <boost/spirit/home/<USER>/char.hpp>
#include <boost/spirit/home/<USER>/directive.hpp>
#include <boost/spirit/home/<USER>/nonterminal.hpp>
#include <boost/spirit/home/<USER>/numeric.hpp>
#include <boost/spirit/home/<USER>/operator.hpp>
#include <boost/spirit/home/<USER>/core.hpp>
#include <boost/spirit/home/<USER>/string.hpp>

#endif
