
//  (C) Copyright <PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON> 2000.
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).
//
//  See http://www.boost.org/libs/type_traits for most recent version including documentation.

#ifndef BOOST_TT_BROKEN_SPEC_HPP_INCLUDED
#define BOOST_TT_BROKEN_SPEC_HPP_INCLUDED

//
// This header is deprecated and no longer used by type_traits:
//
#if defined(__GNUC__) || defined(_MSC_VER)
# pragma message("NOTE: Use of this header (boost/type_traits/broken_compiler_spec.hpp) is deprecated")
#endif

#endif // BOOST_TT_CONFIG_HPP_INCLUDED


