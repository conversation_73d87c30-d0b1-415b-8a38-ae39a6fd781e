﻿#include "meshProcess/wallDistance/WallDistanceManager.h"
#include "basic/field/ElementField.h"

WallDistanceManager::WallDistanceManager(SubMesh* subMesh_, const int &nLevel_, 
                                         const Turbulence::WallDistance &type_,
                                         const bool &dualMeshFlag_)
                                         : subMesh(subMesh_), type(type_), 
                                         wallDistancePointer(nullptr),
                                         dualMeshFlag(dualMeshFlag_)
{
    nLevel = Min(nLevel_, subMesh->GetTotalLevel());
}

WallDistanceManager::~WallDistanceManager()
{
    if (wallDistancePointer != nullptr) { delete wallDistancePointer; wallDistancePointer = nullptr; }
}

void WallDistanceManager::Calculate(const std::vector<int> &wallPatchIDList)
{
    // 收集壁面信息
    CollectWallFace(wallPatchIDList);
    
    // 计算壁面距离
    for (int level = 0; level < nLevel; level++)
    {
        // 选择计算方法
        SetPointer(level);
        
        // 计算细网格壁面距离
        wallDistancePointer->Calculate();
    }

    // 检查网格的壁面距离
    CheckDistance();
}

void WallDistanceManager::CheckDistance()
{
    MPIBarrier();
    
    for (int level = 0; level < nLevel; ++level)
    {
        Mesh* mesh = subMesh->GetMultiGrid(level);
        for (int iElem = 0; iElem < mesh->GetElementNumberReal(); ++iElem)
        {
            const Scalar &d = mesh->GetNearWallDistance(iElem);
            if (d < Scalar0 || d > INF - 1)
            {
				Scalar distMin = INF;
				for (int index = 0; index < wallBoundaryFace.size(); ++index)
				{
					const Vector &elementCenter = mesh->GetElement(iElem).GetCenter();
					const Face &face = wallBoundaryFace[index].first;
					distMin = Min(distMin, (face.GetCenter() - elementCenter).Mag());
				}
				mesh->SetNearWallDistance(iElem, distMin);
			}
        }
    }
}

void WallDistanceManager::CollectWallFace(const std::vector<int> &wallPatchIDList)
{
    Mesh *mesh = subMesh->GetMultiGrid(0);
    int wallSize = 0;
    for (int i = 0; i < wallPatchIDList.size(); i++)
    {
        wallSize += mesh->GetBoundaryFaceSize(wallPatchIDList[i]);
    }

    std::vector<Vector> wallFaceCenterList(wallSize), wallFaceNormalList(wallSize);
    
    int count = 0;
    for (int i = 0; i < wallPatchIDList.size(); i++)
    {
        const int &patchID = wallPatchIDList[i];
        for (int index = 0; index < mesh->GetBoundaryFaceSize(patchID); index++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(patchID, index);
            wallFaceCenterList[count] = mesh->GetFace(faceID).GetCenter();
            wallFaceNormalList[count] = mesh->GetFace(faceID).GetNormal();
            count++;
        }
    }

    std::vector<std::vector<Vector>> wallFaceCenterListTotal;
    std::vector<std::vector<Vector>> wallFaceNormalListTotal;

    if (GetMPISize() == 1)
    {
        wallFaceCenterListTotal.push_back(wallFaceCenterList);
        wallFaceNormalListTotal.push_back(wallFaceNormalList);
    }
    else
    {
#if defined(_BaseParallelMPI_)
        boost::mpi::all_gather(MPI::mpiWorld, wallFaceCenterList, wallFaceCenterListTotal);
        boost::mpi::all_gather(MPI::mpiWorld, wallFaceNormalList, wallFaceNormalListTotal);
#endif
    }

    wallSize = 0;
    for (int i = 0; i < wallFaceCenterListTotal.size(); ++i) wallSize += wallFaceCenterListTotal[i].size();
    wallBoundaryFace.reserve(wallSize);
    for (int i = 0; i < wallFaceCenterListTotal.size(); ++i)
    {
        for (int j = 0; j < wallFaceCenterListTotal[i].size(); ++j)
        {
            Face face;
            face.center = wallFaceCenterListTotal[i][j];
            face.normal = wallFaceNormalListTotal[i][j];
            face.areaMag = Scalar0;
            std::vector<Node> faceNode;
            wallBoundaryFace.push_back(std::make_pair(face, faceNode));
        }
    }
}

void WallDistanceManager::SetPointer(const int &level)
{
    Mesh *mesh = subMesh->GetMultiGrid(level);

    switch (type)
    {
    case Turbulence::WallDistance::TRAVERSE:
        wallDistancePointer = new WallDistanceTraverse(mesh, wallBoundaryFace);
        break;
    case Turbulence::WallDistance::ADT:
        wallDistancePointer = new WallDistanceADT(mesh, wallBoundaryFace);
        break;
    case Turbulence::WallDistance::KDT:
        wallDistancePointer = new WallDistanceKDT(mesh, wallBoundaryFace);
        break;
    default:
        FatalError("WallDistanceManager::SetPointer: input method isnot supported!\n");
        break;
    }
}