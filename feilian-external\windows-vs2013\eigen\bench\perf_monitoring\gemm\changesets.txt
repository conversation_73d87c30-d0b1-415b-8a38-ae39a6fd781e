#3.0.1
#3.1.1
#3.2.0
3.2.4
#5745:37f59e65eb6c
5891:d8652709345d  # introduce AVX
#5893:24b4dc92c6d3  # merge
5895:997c2ef9fc8b  # introduce FMA
#5904:e1eafd14eaa1  # complex and AVX
5908:f8ee3c721251  # improve packing with ptranspose
#5921:ca808bb456b0  # merge
#5927:8b1001f9e3ac
5937:5a4ca1ad8c53  # New gebp kernel handling up to 3 packets x 4 register-level blocks
#5949:f3488f4e45b2  # merge
#5969:e09031dccfd9  # Disable 3pX4 kernel on Altivec
#5992:4a429f5e0483  # merge
before-evaluators
#6334:f6a45e5b8b7c  # Implement evaluator for sparse outer products
#6639:c9121c60b5c7
#6655:06f163b5221f  # Properly detect FMA support on ARM
#6677:700e023044e7   # FMA has been wrongly disabled
#6681:11d31dafb0e3
#6699:5e6e8e10aad1   # merge default to tensors
#6726:ff2d2388e7b9   # merge default to tensors
#6742:0cbd6195e829   # merge default to tensors
#6747:853d2bafeb8f   # Generalized the gebp apis
6765:71584fd55762   # Made the blocking computation aware of the l3 cache; Also optimized the blocking parameters to take into account the number of threads used for a computation
#6781:9cc5a931b2c6   # generalized gemv
#6792:f6e1daab600a   # ensured that contractions that can be reduced to a matrix vector product
#6844:039efd86b75c   # merge tensor
6845:7333ed40c6ef   # change prefetching in gebp
#6856:b5be5e10eb7f   # merge index conversion
#6893:c3a64aba7c70   # clean blocking size computation
#6898:6fb31ebe6492   # rotating kernel for ARM
6899:877facace746   # rotating kernel for ARM only
#6904:c250623ae9fa   # result_of
6921:915f1b1fc158   # fix prefetching change for ARM
6923:9ff25f6dacc6   # prefetching
6933:52572e60b5d3   # blocking size strategy
6937:c8c042f286b2   # avoid redundant pack_rhs
6981:7e5d6f78da59   # dynamic loop swapping
6984:45f26866c091   # rm dynamic loop swapping, adjust lhs's micro panel height to fully exploit L1 cache
6986:a675d05b6f8f   # blocking heuristic: block on the rhs in L1 if the lhs fit in L1.
7013:f875e75f07e5   # organize a little our default cache sizes, and use a saner default L1 outside of x86 (10% faster on Nexus 5)
7015:8aad8f35c955   # Refactor computeProductBlockingSizes to make room for the possibility of using lookup tables
7016:a58d253e8c91   # Polish lookup tables generation
7018:9b27294a8186   # actual_panel_rows computation should always be resilient to parameters not consistent with the known L1 cache size, see comment
7019:c758b1e2c073   # Provide a empirical lookup table for blocking sizes measured on a Nexus 5. Only for float, only for Android on ARM 32bit for now.
7085:627e039fba68   # Bug 986: add support for coefficient-based product with 0 depth.
7098:b6f1db9cf9ec   # Bug 992: don't select a 3p GEMM path with non-vectorizable scalar types, this hits unsupported paths in symm/triangular products code
7591:09a8e2186610   # 3.3-alpha1
7650:b0f3c8f43025   # help clang inlining
#8744:74b789ada92a   # Improved the matrix multiplication blocking in the case where mr is not a power of 2 (e.g on Haswell CPUs)
8789:efcb912e4356   # Made the index type a template parameter to evaluateProductBlockingSizes. Use numext::mini and numext::maxi instead of std::min/std::max to compute blocking sizes
8972:81d53c711775   # Don't optimize the processing of the last rows of a matrix matrix product in cases that violate the assumptions made by the optimized code path
8985:d935df21a082   # Remove the rotating kernel.
8988:6c2dc56e73b3   # Bug 256: enable vectorization with unaligned loads/stores.
9148:b8b8c421e36c   # Relax mixing-type constraints for binary coefficient-wise operators
9174:d228bc282ac9   # merge
9212:c90098affa7b   # Fix performance regression introduced in changeset 8aad8f35c955
9213:9f1c14e4694b   # Fix performance regression in dgemm introduced by changeset 81d53c711775
