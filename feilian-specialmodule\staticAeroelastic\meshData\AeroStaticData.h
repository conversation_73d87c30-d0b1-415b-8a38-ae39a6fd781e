﻿//! @file 
//! <AUTHOR>
//! @brief
//! @date 

#ifndef _specialModule_staticAeroelastic_meshData_AeroStaticData_
#define _specialModule_staticAeroelastic_meshData_AeroStaticData_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <array>
#include <algorithm>

#include "meshProcess/meshConverter/CgnsMesh.h"
#include "feilian-specialmodule/staticAeroelastic/meshData/MeshData.h"
#include "sourceFlow/configure/FlowConfigure.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif



class AeroStaticData
{

    public:

    AeroStaticData(SubMesh *subMesh_ ,Configure::Flow::FlowConfigure &flowConfig_,Vector &scale_);
    ~AeroStaticData();


    //**初始化气动数据结构体，包括部件名称，物面点数目、初始坐标、初始面积、ID
    void FindAeroMeshData();
    
    //**初始化结构数据结构体，包括部件名称，加载点数目、坐标、ID、自然编号
    void ReadSolidMeshData();

    std::vector<int> GetV_wallboundaryID(){return this->v_wallboundaryID;}

    std::multimap<int, std::vector<int>> Getmapdata(){return this->map_data;}

    std::vector<struct Solver::AeroMeshData> GetAeroMeshData(){return this->v_aeroMeshData;}

    std::vector<struct Solver::SolidMeshData> GetSolidMeshData(){return this->v_solidMeshData;}

    int GetNcsdt(){return this->ncsdt;}

    std::string trim(std::string str);
    std::string getSubstringAfterChar(std::string str, char ch);

    int ReadNumFromLine(std::string line);
    void checkdat();

    void HandleAeroMeshData();

#ifdef _Supports_CXX11_
    std::unordered_map<int, int> GetSymData();
#else
    std::map<int, int> GetSymData();
#endif

    private:	
      	
    SubMesh *subMesh; 

    Vector &scale; 
    std::vector<struct Solver::AeroMeshData> v_aeroMeshData; //气动数据结构体数组
    std::vector<struct Solver::SolidMeshData> v_solidMeshData; //结构数据结构体数组

   /* std::vector<Solver::AeroMeshData> aeroMeshData; //气动数据结构体数组
    std::vector<Solver::SolidMeshData> solidMeshData; //结构数据结构体数组*/
    


    Configure::Flow::FlowConfigure &flowConfig;

    std::vector<int> v_wallboundaryID; 
    std::multimap<int, std::vector<int>> map_data; //当前分区下，所有部件点(globalID)所属流场数据包的位置    
    int npart;
    int processorID;
    bool flag;

    int ncsdt;


#if defined(_BaseParallelMPI_)
    mpi::communicator mpi_world;
#endif 

};

#endif