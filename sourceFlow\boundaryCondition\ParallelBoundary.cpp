﻿#include "sourceFlow/boundaryCondition/ParallelBoundary.h"

namespace Boundary
{
namespace Flow
{

ParallelBoundary::ParallelBoundary(Package::FlowPackage &data)
    :
    flowPackage(data), mesh(data.GetMeshStruct().mesh),
    material(data.GetMaterial()),
    rho(*data.GetField().density),
    U(*data.GetField().velocity),
    p(*data.GetField().pressure),
    T(*data.GetField().temperature),
    A(*data.GetField().soundSpeed),
    H(*data.GetField().enthalpy),
#if defined(_EnableMultiSpecies_)
    massFraction(data.GetField().massFraction),
#endif
    muLaminar(data.GetField().muLaminar),
    residualMass(*data.GetResidualField().residualMass),
    residualMomentum(*data.GetResidualField().residualMomentum),
    residualEnergy(*data.GetResidualField().residualEnergy),
    nodeCenter(data.GetFlowConfigure().GetPreprocess().dualMeshFlag)
{                
}

void ParallelBoundary::UpdateBoundaryCondition()
{
    rho.SetGhostlValueParallel();
    U.SetGhostlValueParallel();
    p.SetGhostlValueParallel();
#if defined(_EnableMultiSpecies_)
	for (int m = 0; m < massFraction.size(); ++m) massFraction[m]->SetGhostlValueParallel();
#endif
    
    const auto &vv_ghostElement = mesh->GetGhostElementsParallel();
    for (int i = 0; i < vv_ghostElement.size(); ++i)
    {
        for (int j = 0; j < vv_ghostElement[i].size(); ++j)
        {
            const int &faceID = vv_ghostElement[i][j].GetID();
            const int &neighborID = mesh->GetFace(faceID).GetNeighborID();

            const Scalar &pTemp = p.GetValue(neighborID);
            const Scalar &rhoTemp = rho.GetValue(neighborID);
            const Scalar TTemp = material.GetTemperature(pTemp, rhoTemp);
            T.SetValue(neighborID, TTemp);
            A.SetValue(neighborID, material.GetSoundSpeed(TTemp));
            H.SetValue(neighborID, material.Enthalpy(TTemp));
            if (muLaminar != nullptr) muLaminar->SetValue(neighborID, material.Mu(TTemp));
        }
    }

    return;
}

void ParallelBoundary::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;

    return;
}

}// namespace Flow
}// namespace Boundary
