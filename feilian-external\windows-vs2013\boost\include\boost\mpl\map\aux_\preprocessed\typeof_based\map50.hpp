
// Copyright <PERSON><PERSON><PERSON> G<PERSON>ovoy 2000-2004
// Copyright <PERSON> 2003-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/map/map50.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40
    >
struct map41
    : m_item<
          typename P40::first
        , typename P40::second
        , map40< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39 >
        >
{
    typedef map41 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41
    >
struct map42
    : m_item<
          typename P41::first
        , typename P41::second
        , map41< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40 >
        >
{
    typedef map42 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42
    >
struct map43
    : m_item<
          typename P42::first
        , typename P42::second
        , map42< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41 >
        >
{
    typedef map43 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43
    >
struct map44
    : m_item<
          typename P43::first
        , typename P43::second
        , map43< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42 >
        >
{
    typedef map44 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43, typename P44
    >
struct map45
    : m_item<
          typename P44::first
        , typename P44::second
        , map44< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42,P43 >
        >
{
    typedef map45 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43, typename P44
    , typename P45
    >
struct map46
    : m_item<
          typename P45::first
        , typename P45::second
        , map45< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42,P43,P44 >
        >
{
    typedef map46 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43, typename P44
    , typename P45, typename P46
    >
struct map47
    : m_item<
          typename P46::first
        , typename P46::second
        , map46< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42,P43,P44,P45 >
        >
{
    typedef map47 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43, typename P44
    , typename P45, typename P46, typename P47
    >
struct map48
    : m_item<
          typename P47::first
        , typename P47::second
        , map47< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42,P43,P44,P45,P46 >
        >
{
    typedef map48 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43, typename P44
    , typename P45, typename P46, typename P47, typename P48
    >
struct map49
    : m_item<
          typename P48::first
        , typename P48::second
        , map48< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42,P43,P44,P45,P46,P47 >
        >
{
    typedef map49 type;
};

template<
      typename P0, typename P1, typename P2, typename P3, typename P4
    , typename P5, typename P6, typename P7, typename P8, typename P9
    , typename P10, typename P11, typename P12, typename P13, typename P14
    , typename P15, typename P16, typename P17, typename P18, typename P19
    , typename P20, typename P21, typename P22, typename P23, typename P24
    , typename P25, typename P26, typename P27, typename P28, typename P29
    , typename P30, typename P31, typename P32, typename P33, typename P34
    , typename P35, typename P36, typename P37, typename P38, typename P39
    , typename P40, typename P41, typename P42, typename P43, typename P44
    , typename P45, typename P46, typename P47, typename P48, typename P49
    >
struct map50
    : m_item<
          typename P49::first
        , typename P49::second
        , map49< P0,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20,P21,P22,P23,P24,P25,P26,P27,P28,P29,P30,P31,P32,P33,P34,P35,P36,P37,P38,P39,P40,P41,P42,P43,P44,P45,P46,P47,P48 >
        >
{
    typedef map50 type;
};

}}
