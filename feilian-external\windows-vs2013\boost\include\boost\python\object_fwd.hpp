// Copyright <PERSON> 2002.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
#ifndef OBJECT_FWD_DWA2002724_HPP
# define OBJECT_FWD_DWA2002724_HPP

# include <boost/python/detail/prefix.hpp>

namespace boost { namespace python { 
namespace api
{
  class object;
}
using api::object;
}} // namespace boost::python

#endif // OBJECT_FWD_DWA2002724_HPP
