 #pragma once
#include "FESubzonePartitionerInterface.h"
#include "basicTypes.h"
namespace tecplot { namespace ___3933 { class ___37; class NoOpFESubzonePartitioner : public FESubzonePartitionerInterface { public: NoOpFESubzonePartitioner( ___37& ___36, ___4636 zone) { ___1844 ___1843; ___36.___4615(zone + 1, ___1843); m_numCells = static_cast<___465>(___1843.___2105()); m_numNodes = static_cast<___2718>(___1843.i()); } virtual ~NoOpFESubzonePartitioner() {} virtual ___465                  numCellsInZone() const { return m_numCells; } virtual ___2090::SubzoneOffset_t ___2783() const { return 0; } virtual ___2090::ItemOffset_t    ___2782(___2090::SubzoneOffset_t  ) const { ___478(___1305); return 0; } virtual ___465                  ___4608(___2090  ) const { ___478(___1305); return 0; } virtual ___2090                  szCoordinateAtZoneCell(___465  ) const { ___478(___1305); return ___2090(); } virtual ___2718                  numNodesInZone() const { return m_numNodes; } virtual ___2090::SubzoneOffset_t ___2823() const { return 0; } virtual ___2090::ItemOffset_t    ___2822(___2090::SubzoneOffset_t  ) const { ___478(___1305); return 0; } virtual ___2718                  ___4657(___2090  ) const { ___478(___1305); return 0; } virtual ___2090                  ___3924(___2718  ) const { ___478(___1305); return ___2090(); } virtual void                         setNodeSubzoneCoordinate(___2718  , ___2090 /*___2759*/) { ___478(___1305); } private: ___465 m_numCells; ___2718 m_numNodes; }; }}
