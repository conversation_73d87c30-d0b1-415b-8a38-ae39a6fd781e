﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MRFModel.h
//! <AUTHOR>
//! @brief 六自由度运动类
//! @date  2024-10-20
//

# ifndef _basic_MRF_MRFModel_
# define _basic_MRF_MRFModel_

#include "basic/common/ListTools.h"
#include "basic/field/ElementField.h"

namespace Boundary{
	namespace Flow{
		class FlowBoundaryManager;
	}
}



class MRFZONE{
public:

    MRFZONE();

	MRFZONE (std::string name,  Vector origin, Vector axis, Scalar omega, Mesh* mesh);

	MRFZONE& operator=(const MRFZONE& other);

	~MRFZONE();

    inline const std::string& get_name() const{
		return name;
	}				

	inline const Vector& get_origin() const{
		return origin;
	}

	inline const Vector& get_axis() const{
		return axis;
	}

	inline const Scalar& get_omega() const{
		return omega;
	}

	inline const Vector& get_oumu() const{
		return oumu;
	}
	
	//对面进行种类分类
	//  0 -> 不在此域的面
    //  1 -> 内部面或者内部转动边界面
    //  2 -> 交界面或者内部非转动面
	void divideFaces();

	//边界速度变换为相对速度
	void make_relative(ElementField<Vector> & V, Boundary::Flow::FlowBoundaryManager & fb) const;

	//边界速度变换为绝对速度
	void make_absolute(ElementField<Vector>&V, Boundary::Flow::FlowBoundaryManager& fb)	const;

	//动量方程需要增加科氏力
    void addCoriolis(ElementField<Vector>* residualMomentum,ElementField<Scalar> &rho,ElementField<Vector> &U);

	//内部对流项的通量修正	
	void RelativeFlux_Inerternal(const Face &face, const Vector &faceArea,const Scalar &gamma1,
								Scalar& massFlux, Vector& momentumFlux,Scalar& energyFlux,
								const Scalar &rhoLeft,const Vector &ULeft,const Scalar &pLeft,
								const Scalar &rhoRight,const Vector &URight,const Scalar &pRight);

	//边界对流项的通量修正
	void RelativeFlux_Patch(Face &face,Scalar& massFlux,Vector& momentumFlux,Scalar& energyFlux,
								Scalar& rhoFace,Vector& UFace,Scalar& rhoEFace,const Vector &faceArea);

	//网格
	Mesh* mesh;

    //Name of MRF region
	std::string name;
	             
	//Origin of axis
	Vector origin;

	//axis vector
	Vector axis;

	//angular speed     
	Scalar omega;

	//vector omega
	Vector oumu;

};

#endif