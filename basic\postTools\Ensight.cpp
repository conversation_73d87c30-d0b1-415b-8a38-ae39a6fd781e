﻿#include "basic/postTools/Ensight.h"

namespace Post
{
Ensight::Ensight(Mesh *mesh_, const bool dualFlag_, const Position outputPosition_, const bool exportInteriorFlag_, const std::string caseName_)
	: BasePost(mesh_, dualFlag_, outputPosition_, exportInteriorFlag_, caseName_), numChar(80)
{
    doubleFlag = false;
    thirdDOutFlag = true;
    boundaryNodeID.resize(mesh->GetBoundarySize());
}

void Ensight::WriteFile()
{
    if (caseName.size() == 0) FatalError("Ensight::WriteFile: case name is missing!");

    this->WriteCase();

    this->WriteGeo();

    if (0 != scalarFieldSize) this->WriteScalarField();

    if (0 != vectorFieldSize) this->WriteVectorField();

    if (0 != tensorFieldSize) this->WriteTensorField();
    
    std::size_t pos = caseName.rfind("_");
    if (pos == caseName.npos) Print("\nEnsight文件路径: " + caseName);
    else                      Print("\nEnsight文件路径: " + caseName.substr(0, pos));

    return;
}

void Ensight::WriteCase()
{
    // 确定文件名称与文件对象
    std::string fileName = caseName + ".case";
    std::fstream file;        

    // 打开文件
    file.open(fileName, std::ios::out);

    // 写标题
    file << "#" << std::endl;
    file << "# Solver : ARI-CFD" << std::endl;
    file << "# Case file: " << localCaseName + ".case" << std::endl;

    // 确定为文本格式和版本
    file << "FORMAT" << std::endl;
    file << "type: ensight gold" << std::endl;

    // 确定几何文件名称
    file << "GEOMETRY" << std::endl;
    file << "model: " << localCaseName + ".geo" << std::endl;

    // 确定是否写变量场标识
    if (0 != scalarFieldSize ||    0 != vectorFieldSize ||    0 != tensorFieldSize)
        file << "VARIABLE" << std::endl;

    std::string positionName = " per element: ";
    if (!outputAtCenter) positionName = " per node: ";

    // 确定标量场文件名称    
    for (int i = 0; i < scalarFieldSize; ++i)
    {
        std::string scalarName = GetScalarFieldName(scalarField[i]);
        std::string type = "scalar" + positionName;
        file << type << scalarName
             << " " + localCaseName + "." + scalarName << std::endl;
    }

    // 确定矢量场文件名称    
    for (int i = 0; i < vectorFieldSize; ++i)
    {
        std::string vectorName = GetVectorFieldName(vectorField[i]);
        std::string type = "vector" + positionName;
        file << type << vectorName
             << " " + localCaseName + "." + vectorName << std::endl;
    }

    // 确定张量场文件名称    
    for (int i = 0; i < tensorFieldSize; ++i)
    {
        std::string tensorName = GetTensorFieldName(tensorField[i]);
        std::string type = "tensor symm" + positionName;
        file << type << tensorName
             << " " + localCaseName + "." + tensorName << std::endl;
    }

    file.close();
    return;
}

void Ensight::WriteGeo()
{
    // 用于写入字符串数据、整型数据常量
    std::string string;
    int num;

    // 确定文件名称与文件对象
    std::string fileName = caseName + ".geo";
    std::fstream file;    

    // 打开文件
    file.open(fileName, std::ios::out | std::ios::binary);

     // C或C++生成的二进制文件
    string = "C Binary";
    WriteString(file, string);

    // 版本号
    string = "EnSight 10.1.1";
    WriteString(file, string);

    // 网格点和单元从1开始编号，采用自动编号方式(assign)
    string = "node id assign";
    WriteString(file, string);
    string = "element id assign";
    WriteString(file, string);

    // 第1步：写体网格几何信息
    // 第1.1步：体网格基本信息
    string = "part";
    WriteString(file, string);
    num = 1;
    IO::Write(file, num);
    string = "volume";
    WriteString(file, string);
    string = "coordinates";
    WriteString(file, string);
    num = mesh->GetNodeNumber();
    IO::Write(file, num);

    // 第1.2步：体网格坐标点信息
    WriteVolumeCoordinate(file);

    // 第1.3步：体网格单元构成关系
    WriteVolumeElement(file);

    // 第2步：写边界网格几何信息
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
    {
        WriteBoundaryMesh(patchID, file);
    }

    file.close();

    return;
}

void Ensight::WriteScalarField()
{
    // 用于写入字符串数据、整型数据常量
    std::string string;
    int num;

    for (int i = 0; i < scalarFieldSize; ++i)
    {
        std::string scalarName = GetScalarFieldName(scalarField[i]);

        // 确定文件名称、文件对象并打开文件
        std::string fileName = caseName + "." + scalarName;
        std::fstream file(fileName, std::ios::out | std::ios::binary);

        // 写说明
        WriteString(file, scalarName);

        // 写体场数据
        string = "part";
        WriteString(file, string);
        num = 1;
        IO::Write(file, num);
        string = this->GetVolumeElementType();
        WriteString(file, string);// 写单元类型
        WriteScalarValue(file, scalarField[i], -1, true);
        
        // 写边界数据
        for (int j = 0; j < mesh->GetBoundarySize(); ++j)
        {
            string = "part";
            WriteString(file, string);
            num = j + 2;
            IO::Write(file, num);
            string = this->GetBoundaryElementType();
            WriteString(file, string);// 写边界单元类型
            WriteScalarValue(file, scalarField[i], j, true);
        }

        file.close();
    }
    return;
}

void Ensight::WriteVectorField()
{
    // 用于写入字符串数据、整型数据常量
    std::string string;
    int num;

    for (int i = 0; i < vectorFieldSize; ++i)
    {
        std::string vectorName = GetVectorFieldName(vectorField[i]);

        // 确定文件名称、文件对象并打开文件
        std::string fileName = caseName + "." + vectorName;
        std::fstream file(fileName, std::ios::out | std::ios::binary);

        // 写说明
        WriteString(file, vectorName);

        // 写体场数据
        string = "part";
        WriteString(file, string);
        num = 1;
        IO::Write(file, num);
        string = this->GetVolumeElementType();
        WriteString(file, string);// 写单元类型
        WriteVectorValue(file, vectorField[i], -1, true);
        
        // 写边界数据
        for (int j = 0; j < mesh->GetBoundarySize(); ++j)
        {
            string = "part";
            WriteString(file, string);
            num = j + 2;
            IO::Write(file, num);
            string = this->GetBoundaryElementType();
            WriteString(file, string);// 写边界单元类型
            WriteVectorValue(file, vectorField[i], j, true);
        }

        file.close();
    }
    return;
}

void Ensight::WriteTensorField()
{
    // 用于写入字符串数据、整型数据常量
    std::string string;
    int num;

    for (int i = 0; i < tensorFieldSize; ++i)
    {
        std::string tensorName = GetTensorFieldName(tensorField[i]);

        // 确定文件名称、文件对象并打开文件
        std::string fileName = caseName + "." + tensorName;
        std::fstream file(fileName, std::ios::out | std::ios::binary);

        // 写说明
        WriteString(file, tensorName);

        // 写体场数据,认为是对称张量
        string = "part";
        WriteString(file, string);
        num = 1;
        IO::Write(file, num);
        string = this->GetVolumeElementType();
        WriteString(file, string);// 写单元类型
        WriteTensorValue(file, tensorField[i], -1, true);

        // 写边界数据
        for (int j = 0; j < mesh->GetBoundarySize(); ++j)
        {
            string = "part";
            WriteString(file, string);
            num = j + 2;
            IO::Write(file, num);
            string = this->GetBoundaryElementType();
            WriteString(file, string);// 写边界单元类型
            WriteTensorValue(file, tensorField[i], j, true);
        }

        file.close();
    }
    return;
}

void Ensight::WriteVolumeCoordinate(std::fstream &file)
{
    const int &numNode = mesh->GetNodeNumber();

    // Ensight必须是单精度
    float value;

    // 三个方向的坐标，对于二维z方向为0
    for (int i = 0; i < numNode; ++i)
    {
        value = (float)mesh->GetNode(i).X();
        IO::Write(file, value);
    }
    for (int i = 0; i < numNode; i++)
    {
        value = (float)mesh->GetNode(i).Y();
        IO::Write(file, value);
    }
    for (int i = 0; i < numNode; i++)
    {
        if (dim2) value = 0.0;
        else value = (float)mesh->GetNode(i).Z();
        IO::Write(file, value);
    }
    return;
}

void Ensight::WriteVolumeElement(std::fstream &file)
{
    // 按照多面体(多边形)类型来写，从10版本后开始支持
    std::string  string;
    if (dim2) string = "nsided";
    else      string = "nfaced";
    WriteString(file, string);
    
    // 体单元的数量
    const int &numElement = mesh->GetElementNumberReal();
    IO::Write(file, numElement);

    if (dim2) //二维网格
    {
        // 每个单元的点数量
        for (int i = 0; i < numElement; ++i)
        {
            int numNode = mesh->GetElement(i).GetNodeSize();
            IO::Write(file, numNode);
        }

        // 每个单元的点构成关系
        for (int i = 0; i < numElement; ++i)
        {
            int numNode = mesh->GetElement(i).GetNodeSize();
            for (int k = 0; k < numNode; ++k)
            {
                // Ensight从1开始编号
                int nodeID = mesh->GetElement(i).GetNodeID(k) + 1;
                IO::Write(file, nodeID);
            }
        }
    }
    else //三维网格
    {
        // 每个单元的面数量
        for (int i = 0; i < numElement; ++i)
        {
            int numFace = mesh->GetElement(i).GetFaceSize();
            IO::Write(file, numFace);
        }

        //  每个单元每个面的点数量
        for (int i = 0; i < numElement; ++i)
        {
            int numFace = mesh->GetElement(i).GetFaceSize();
            for (int j = 0; j < numFace; ++j)
            {
                const int &faceID = mesh->GetElement(i).GetFaceID(j);
                int numNode = mesh->GetFace(faceID).GetNodeSize();
                IO::Write(file, numNode);
            }
        }

        // 每个单元每个面的点构成关系
        for (int i = 0; i < numElement; ++i)
        {
            int numFace = mesh->GetElement(i).GetFaceSize();
            for (int j = 0; j < numFace; ++j)
            {
                const int &faceID = mesh->GetElement(i).GetFaceID(j);
                int numNode = mesh->GetFace(faceID).GetNodeSize();
                for (int k = 0; k < numNode; ++k)
                {
                    // Ensight从1开始编号
                    int nodeID = mesh->GetFace(faceID).GetNodeID(k) + 1;
                    IO::Write(file, nodeID);
                }
            }
        }
    }    
    return;
}

void Ensight::WriteBoundaryMesh(const int &patchID, std::fstream &file)
{
    // 用于写入字符串数据、整型数据常量
    std::string string;
    int num;

    // 写part
    string = "part";
    WriteString(file, string);
    num = patchID + 2;
    IO::Write(file, num);
    string = mesh->GetBoundaryName(patchID);
    WriteString(file, string);
    string = "coordinates";
    WriteString(file, string);

    auto &list = nodeListVector[patchID];

    num = nodeMapVector[patchID].size();
    IO::Write(file, num);

    // 写坐标
    float value;
    const int listSize = (int)list.size();
    for (int i = 0; i < listSize; ++i)
    {
        const int &nodeID = list[i];
        value = (float)mesh->GetNode(nodeID).X();
        IO::Write(file, value);    
    }
    for (int i = 0; i < listSize; ++i)
    {
        const int &nodeID = list[i];
        value = (float)mesh->GetNode(nodeID).Y();
        IO::Write(file, value);
    }
    for (int i = 0; i < listSize; ++i)
    {
        if (dim2)
        {
            value = 0.0;
        }
        else
        {
            const int &nodeID = list[i];
            value = (float)mesh->GetNode(nodeID).Z();
        }
        IO::Write(file, value);
    }

    // 该边界的面数量,二维：每个面为线段，三维：每个面为多边形
    if (dim2) string = "bar2";
    else      string = "nsided";
    WriteString(file, string);
    int numFace = mesh->GetBoundaryFaceSize(patchID);
    IO::Write(file, numFace);

    // 三维网格，需要写每个面的点数量
    if (!dim2)
    {   
        for (int j = 0; j < numFace; ++j)
        {
            const int &faceID = mesh->GetBoundaryFaceID(patchID, j);
            int numNode = mesh->GetFace(faceID).GetNodeSize();
            IO::Write(file, numNode);
        }
    }    

    // 每个面的点构成关系
    for (int j = 0; j < numFace; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceID(patchID, j);
        int numNode = mesh->GetFace(faceID).GetNodeSize();
        for (int k = 0; k < numNode; ++k)
        {
            const int &nodeID = mesh->GetFace(faceID).GetNodeID(k);
            int localID = nodeMapVector[patchID].find(nodeID)->second + 1;
            IO::Write(file, localID);            
        }
    }
    return;
}

void Ensight::WriteString(std::fstream &file, const std::string &s)
{
    file.write(s.c_str(), sizeof(char)*numChar);
}

std::string Ensight::GetVolumeElementType()
{
    if (outputAtCenter)
    {
        if (dim2) return "nsided";
        else      return "nfaced";
    }
    else
    {
        return "coordinates";
    }
}

std::string Ensight::GetBoundaryElementType()
{
    if (outputAtCenter)
    {
        if (dim2) return "bar2";
        else      return "nsided";
    }
    else
    {
        return "coordinates";
    }
}

}// namespace Post