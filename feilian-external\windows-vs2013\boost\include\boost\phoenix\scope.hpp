/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#ifndef BOOST_PHOENIX_SCOPE_HPP
#define BOOST_PHOENIX_SCOPE_HPP

#include <boost/phoenix/version.hpp>
#include <boost/phoenix/scope/scoped_environment.hpp>
#include <boost/phoenix/scope/lambda.hpp>
#include <boost/phoenix/scope/let.hpp>
#include <boost/phoenix/scope/local_variable.hpp>

#endif
