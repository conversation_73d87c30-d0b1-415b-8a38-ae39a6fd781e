﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file mainFlowSolver.cpp
//! <AUTHOR>
//! @brief 流场解算器主流程.
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释
//
// 2021-03-29 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#include "sourceFlow/flowSolver/OuterLoop.h"
#include "meshProcess/wallDistance/WallDistanceManager.h"

int main(int argc, char **argv)
{
	if (argc <= 1)
	{
		FatalError("参数错误");
		return -1;
	}

    // 建立并行环境
    InitializeMPI(argc, argv);

#if defined(_EnablePETSC_)
    char help[] = "FVM with PETSc \n";
	PetscInitialize(&argc, &argv, NULL, help);
#endif

    // 输出软件相关信息
    SetInfoFile("AriCFD_Solver.info");
    if (GetMPIRank() == 0)
    {
        PrintTitleInfo(" ARI_FeiLian ");
        PrintSystemTime();
    }

    if (GetMPIRank() == 0) Print("Read control file ...");

    // 定义参数文件对象
    Configure::Flow::FlowConfigure flowConfigure;

    // 读取参数文件
    flowConfigure.ReadCaseXml(argv[1]);

    // 打印计算参数
    if (GetMPIRank() == 0)
    {
        flowConfigure.PrintInformation();
        flowConfigure.PrintReferenceValues();
    }
    
    // 读网格
    if (GetMPIRank() == 0) Print("\nRead mesh file ...");
    const std::string meshName = flowConfigure.GetPreprocess().outputPath + flowConfigure.GetCaseName();
    SubMesh *localMesh = new SubMesh(meshName,
                                     flowConfigure.GetAcceleration().multigridSolver.level,
                                     flowConfigure.GetPreprocess().binaryFileFlag);
    CheckStatus(9400);

    // 更新当地边界信息
    flowConfigure.UpdateLocalBoundary(localMesh);

    // 网格参数预计算
    const int nLevel = Min(localMesh->GetTotalLevel(), flowConfigure.GetAcceleration().multigridSolver.level);
    std::vector<std::vector<int>> symmetryPatchID(nLevel);
    for (int level = 0; level < nLevel; ++level)
    {
        for (int patchID = 0; patchID < localMesh->GetMultiGrid(level)->GetBoundarySize(); ++patchID)
            if (flowConfigure.JudgeSymmetryLocal(level, patchID)) symmetryPatchID[level].push_back(patchID);
    }
    const bool &dualMeshFlag = flowConfigure.GetPreprocess().dualMeshFlag;
    localMesh->PreCalculate(dualMeshFlag, symmetryPatchID);
    CheckStatus(9500);

    //计算壁面距离
    const auto &wallDistanceMethod = flowConfigure.GetPreprocess().wallDistanceMethod;
    if (wallDistanceMethod != Turbulence::WallDistance::NONE_WALL_DISTANCE)
    {
        if (GetMPIRank() == 0) Print("\nCalculate wall distance ...");
        WallDistanceManager wallDistanceManager(localMesh, nLevel, wallDistanceMethod, dualMeshFlag);
        
        std::vector<int> wallPatchIDList;
        for (int patchID = 0; patchID < localMesh->GetBoundarySize(); ++patchID)
            if (flowConfigure.JudgeWallLocal(0, patchID)) wallPatchIDList.push_back(patchID);
        wallDistanceManager.Calculate(wallPatchIDList);
        CheckStatus(9600);
    }

    // 创建流场解算器对象
    OuterLoop *outerLoop = new OuterLoop(localMesh, flowConfigure);
	CheckStatus(2000);

    // 流场初始化
    outerLoop->Initialize();
    CheckStatus(2100);

    // 流场迭代求解外循环
    outerLoop->Solve();

    // 删除求解器
    delete outerLoop;
    outerLoop = nullptr;

    // 删除网格
    localMesh->ClearMesh();
    delete localMesh;
    localMesh = nullptr;

    // 计算完成
    if (GetMPIRank() == 0) Print("\nEnd to iterate successfully!");
    CloseInfoFile();

    FinalizeMPI();

    return 0;
}
