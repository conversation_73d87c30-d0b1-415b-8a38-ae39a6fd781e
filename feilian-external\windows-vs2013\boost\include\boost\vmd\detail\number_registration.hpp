
//  (C) Copyright <PERSON> 2011-2015
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).

#if !defined(BOOST_VMD_DETAIL_IS_NUMBER_REGISTRATION_HPP)
#define BOOST_VMD_DETAIL_IS_NUMBER_REGISTRATION_HPP

#define BOOST_VMD_REGISTER_0 (0)
#define BOOST_VMD_REGISTER_1 (1)
#define BOOST_VMD_REGISTER_2 (2)
#define BOOST_VMD_REGISTER_3 (3)
#define BOOST_VMD_REGISTER_4 (4)
#define BOOST_VMD_REGISTER_5 (5)
#define BOOST_VMD_REGISTER_6 (6)
#define BOOST_VMD_REGISTER_7 (7)
#define BOOST_VMD_REGISTER_8 (8)
#define BOOST_VMD_REGISTER_9 (9)
#define BOOST_VMD_REGISTER_10 (10)
#define BOOST_VMD_REGISTER_11 (11)
#define BOOST_VMD_REGISTER_12 (12)
#define BOOST_VMD_REGISTER_13 (13)
#define BOOST_VMD_REGISTER_14 (14)
#define BOOST_VMD_REGISTER_15 (15)
#define BOOST_VMD_REGISTER_16 (16)
#define BOOST_VMD_REGISTER_17 (17)
#define BOOST_VMD_REGISTER_18 (18)
#define BOOST_VMD_REGISTER_19 (19)
#define BOOST_VMD_REGISTER_20 (20)
#define BOOST_VMD_REGISTER_21 (21)
#define BOOST_VMD_REGISTER_22 (22)
#define BOOST_VMD_REGISTER_23 (23)
#define BOOST_VMD_REGISTER_24 (24)
#define BOOST_VMD_REGISTER_25 (25)
#define BOOST_VMD_REGISTER_26 (26)
#define BOOST_VMD_REGISTER_27 (27)
#define BOOST_VMD_REGISTER_28 (28)
#define BOOST_VMD_REGISTER_29 (29)
#define BOOST_VMD_REGISTER_30 (30)
#define BOOST_VMD_REGISTER_31 (31)
#define BOOST_VMD_REGISTER_32 (32)
#define BOOST_VMD_REGISTER_33 (33)
#define BOOST_VMD_REGISTER_34 (34)
#define BOOST_VMD_REGISTER_35 (35)
#define BOOST_VMD_REGISTER_36 (36)
#define BOOST_VMD_REGISTER_37 (37)
#define BOOST_VMD_REGISTER_38 (38)
#define BOOST_VMD_REGISTER_39 (39)
#define BOOST_VMD_REGISTER_40 (40)
#define BOOST_VMD_REGISTER_41 (41)
#define BOOST_VMD_REGISTER_42 (42)
#define BOOST_VMD_REGISTER_43 (43)
#define BOOST_VMD_REGISTER_44 (44)
#define BOOST_VMD_REGISTER_45 (45)
#define BOOST_VMD_REGISTER_46 (46)
#define BOOST_VMD_REGISTER_47 (47)
#define BOOST_VMD_REGISTER_48 (48)
#define BOOST_VMD_REGISTER_49 (49)
#define BOOST_VMD_REGISTER_50 (50)
#define BOOST_VMD_REGISTER_51 (51)
#define BOOST_VMD_REGISTER_52 (52)
#define BOOST_VMD_REGISTER_53 (53)
#define BOOST_VMD_REGISTER_54 (54)
#define BOOST_VMD_REGISTER_55 (55)
#define BOOST_VMD_REGISTER_56 (56)
#define BOOST_VMD_REGISTER_57 (57)
#define BOOST_VMD_REGISTER_58 (58)
#define BOOST_VMD_REGISTER_59 (59)
#define BOOST_VMD_REGISTER_60 (60)
#define BOOST_VMD_REGISTER_61 (61)
#define BOOST_VMD_REGISTER_62 (62)
#define BOOST_VMD_REGISTER_63 (63)
#define BOOST_VMD_REGISTER_64 (64)
#define BOOST_VMD_REGISTER_65 (65)
#define BOOST_VMD_REGISTER_66 (66)
#define BOOST_VMD_REGISTER_67 (67)
#define BOOST_VMD_REGISTER_68 (68)
#define BOOST_VMD_REGISTER_69 (69)
#define BOOST_VMD_REGISTER_70 (70)
#define BOOST_VMD_REGISTER_71 (71)
#define BOOST_VMD_REGISTER_72 (72)
#define BOOST_VMD_REGISTER_73 (73)
#define BOOST_VMD_REGISTER_74 (74)
#define BOOST_VMD_REGISTER_75 (75)
#define BOOST_VMD_REGISTER_76 (76)
#define BOOST_VMD_REGISTER_77 (77)
#define BOOST_VMD_REGISTER_78 (78)
#define BOOST_VMD_REGISTER_79 (79)
#define BOOST_VMD_REGISTER_80 (80)
#define BOOST_VMD_REGISTER_81 (81)
#define BOOST_VMD_REGISTER_82 (82)
#define BOOST_VMD_REGISTER_83 (83)
#define BOOST_VMD_REGISTER_84 (84)
#define BOOST_VMD_REGISTER_85 (85)
#define BOOST_VMD_REGISTER_86 (86)
#define BOOST_VMD_REGISTER_87 (87)
#define BOOST_VMD_REGISTER_88 (88)
#define BOOST_VMD_REGISTER_89 (89)
#define BOOST_VMD_REGISTER_90 (90)
#define BOOST_VMD_REGISTER_91 (91)
#define BOOST_VMD_REGISTER_92 (92)
#define BOOST_VMD_REGISTER_93 (93)
#define BOOST_VMD_REGISTER_94 (94)
#define BOOST_VMD_REGISTER_95 (95)
#define BOOST_VMD_REGISTER_96 (96)
#define BOOST_VMD_REGISTER_97 (97)
#define BOOST_VMD_REGISTER_98 (98)
#define BOOST_VMD_REGISTER_99 (99)
#define BOOST_VMD_REGISTER_100 (100)
#define BOOST_VMD_REGISTER_101 (101)
#define BOOST_VMD_REGISTER_102 (102)
#define BOOST_VMD_REGISTER_103 (103)
#define BOOST_VMD_REGISTER_104 (104)
#define BOOST_VMD_REGISTER_105 (105)
#define BOOST_VMD_REGISTER_106 (106)
#define BOOST_VMD_REGISTER_107 (107)
#define BOOST_VMD_REGISTER_108 (108)
#define BOOST_VMD_REGISTER_109 (109)
#define BOOST_VMD_REGISTER_110 (110)
#define BOOST_VMD_REGISTER_111 (111)
#define BOOST_VMD_REGISTER_112 (112)
#define BOOST_VMD_REGISTER_113 (113)
#define BOOST_VMD_REGISTER_114 (114)
#define BOOST_VMD_REGISTER_115 (115)
#define BOOST_VMD_REGISTER_116 (116)
#define BOOST_VMD_REGISTER_117 (117)
#define BOOST_VMD_REGISTER_118 (118)
#define BOOST_VMD_REGISTER_119 (119)
#define BOOST_VMD_REGISTER_120 (120)
#define BOOST_VMD_REGISTER_121 (121)
#define BOOST_VMD_REGISTER_122 (122)
#define BOOST_VMD_REGISTER_123 (123)
#define BOOST_VMD_REGISTER_124 (124)
#define BOOST_VMD_REGISTER_125 (125)
#define BOOST_VMD_REGISTER_126 (126)
#define BOOST_VMD_REGISTER_127 (127)
#define BOOST_VMD_REGISTER_128 (128)
#define BOOST_VMD_REGISTER_129 (129)
#define BOOST_VMD_REGISTER_130 (130)
#define BOOST_VMD_REGISTER_131 (131)
#define BOOST_VMD_REGISTER_132 (132)
#define BOOST_VMD_REGISTER_133 (133)
#define BOOST_VMD_REGISTER_134 (134)
#define BOOST_VMD_REGISTER_135 (135)
#define BOOST_VMD_REGISTER_136 (136)
#define BOOST_VMD_REGISTER_137 (137)
#define BOOST_VMD_REGISTER_138 (138)
#define BOOST_VMD_REGISTER_139 (139)
#define BOOST_VMD_REGISTER_140 (140)
#define BOOST_VMD_REGISTER_141 (141)
#define BOOST_VMD_REGISTER_142 (142)
#define BOOST_VMD_REGISTER_143 (143)
#define BOOST_VMD_REGISTER_144 (144)
#define BOOST_VMD_REGISTER_145 (145)
#define BOOST_VMD_REGISTER_146 (146)
#define BOOST_VMD_REGISTER_147 (147)
#define BOOST_VMD_REGISTER_148 (148)
#define BOOST_VMD_REGISTER_149 (149)
#define BOOST_VMD_REGISTER_150 (150)
#define BOOST_VMD_REGISTER_151 (151)
#define BOOST_VMD_REGISTER_152 (152)
#define BOOST_VMD_REGISTER_153 (153)
#define BOOST_VMD_REGISTER_154 (154)
#define BOOST_VMD_REGISTER_155 (155)
#define BOOST_VMD_REGISTER_156 (156)
#define BOOST_VMD_REGISTER_157 (157)
#define BOOST_VMD_REGISTER_158 (158)
#define BOOST_VMD_REGISTER_159 (159)
#define BOOST_VMD_REGISTER_160 (160)
#define BOOST_VMD_REGISTER_161 (161)
#define BOOST_VMD_REGISTER_162 (162)
#define BOOST_VMD_REGISTER_163 (163)
#define BOOST_VMD_REGISTER_164 (164)
#define BOOST_VMD_REGISTER_165 (165)
#define BOOST_VMD_REGISTER_166 (166)
#define BOOST_VMD_REGISTER_167 (167)
#define BOOST_VMD_REGISTER_168 (168)
#define BOOST_VMD_REGISTER_169 (169)
#define BOOST_VMD_REGISTER_170 (170)
#define BOOST_VMD_REGISTER_171 (171)
#define BOOST_VMD_REGISTER_172 (172)
#define BOOST_VMD_REGISTER_173 (173)
#define BOOST_VMD_REGISTER_174 (174)
#define BOOST_VMD_REGISTER_175 (175)
#define BOOST_VMD_REGISTER_176 (176)
#define BOOST_VMD_REGISTER_177 (177)
#define BOOST_VMD_REGISTER_178 (178)
#define BOOST_VMD_REGISTER_179 (179)
#define BOOST_VMD_REGISTER_180 (180)
#define BOOST_VMD_REGISTER_181 (181)
#define BOOST_VMD_REGISTER_182 (182)
#define BOOST_VMD_REGISTER_183 (183)
#define BOOST_VMD_REGISTER_184 (184)
#define BOOST_VMD_REGISTER_185 (185)
#define BOOST_VMD_REGISTER_186 (186)
#define BOOST_VMD_REGISTER_187 (187)
#define BOOST_VMD_REGISTER_188 (188)
#define BOOST_VMD_REGISTER_189 (189)
#define BOOST_VMD_REGISTER_190 (190)
#define BOOST_VMD_REGISTER_191 (191)
#define BOOST_VMD_REGISTER_192 (192)
#define BOOST_VMD_REGISTER_193 (193)
#define BOOST_VMD_REGISTER_194 (194)
#define BOOST_VMD_REGISTER_195 (195)
#define BOOST_VMD_REGISTER_196 (196)
#define BOOST_VMD_REGISTER_197 (197)
#define BOOST_VMD_REGISTER_198 (198)
#define BOOST_VMD_REGISTER_199 (199)
#define BOOST_VMD_REGISTER_200 (200)
#define BOOST_VMD_REGISTER_201 (201)
#define BOOST_VMD_REGISTER_202 (202)
#define BOOST_VMD_REGISTER_203 (203)
#define BOOST_VMD_REGISTER_204 (204)
#define BOOST_VMD_REGISTER_205 (205)
#define BOOST_VMD_REGISTER_206 (206)
#define BOOST_VMD_REGISTER_207 (207)
#define BOOST_VMD_REGISTER_208 (208)
#define BOOST_VMD_REGISTER_209 (209)
#define BOOST_VMD_REGISTER_210 (210)
#define BOOST_VMD_REGISTER_211 (211)
#define BOOST_VMD_REGISTER_212 (212)
#define BOOST_VMD_REGISTER_213 (213)
#define BOOST_VMD_REGISTER_214 (214)
#define BOOST_VMD_REGISTER_215 (215)
#define BOOST_VMD_REGISTER_216 (216)
#define BOOST_VMD_REGISTER_217 (217)
#define BOOST_VMD_REGISTER_218 (218)
#define BOOST_VMD_REGISTER_219 (219)
#define BOOST_VMD_REGISTER_220 (220)
#define BOOST_VMD_REGISTER_221 (221)
#define BOOST_VMD_REGISTER_222 (222)
#define BOOST_VMD_REGISTER_223 (223)
#define BOOST_VMD_REGISTER_224 (224)
#define BOOST_VMD_REGISTER_225 (225)
#define BOOST_VMD_REGISTER_226 (226)
#define BOOST_VMD_REGISTER_227 (227)
#define BOOST_VMD_REGISTER_228 (228)
#define BOOST_VMD_REGISTER_229 (229)
#define BOOST_VMD_REGISTER_230 (230)
#define BOOST_VMD_REGISTER_231 (231)
#define BOOST_VMD_REGISTER_232 (232)
#define BOOST_VMD_REGISTER_233 (233)
#define BOOST_VMD_REGISTER_234 (234)
#define BOOST_VMD_REGISTER_235 (235)
#define BOOST_VMD_REGISTER_236 (236)
#define BOOST_VMD_REGISTER_237 (237)
#define BOOST_VMD_REGISTER_238 (238)
#define BOOST_VMD_REGISTER_239 (239)
#define BOOST_VMD_REGISTER_240 (240)
#define BOOST_VMD_REGISTER_241 (241)
#define BOOST_VMD_REGISTER_242 (242)
#define BOOST_VMD_REGISTER_243 (243)
#define BOOST_VMD_REGISTER_244 (244)
#define BOOST_VMD_REGISTER_245 (245)
#define BOOST_VMD_REGISTER_246 (246)
#define BOOST_VMD_REGISTER_247 (247)
#define BOOST_VMD_REGISTER_248 (248)
#define BOOST_VMD_REGISTER_249 (249)
#define BOOST_VMD_REGISTER_250 (250)
#define BOOST_VMD_REGISTER_251 (251)
#define BOOST_VMD_REGISTER_252 (252)
#define BOOST_VMD_REGISTER_253 (253)
#define BOOST_VMD_REGISTER_254 (254)
#define BOOST_VMD_REGISTER_255 (255)
#define BOOST_VMD_REGISTER_256 (256)

#define BOOST_VMD_DETECT_0_0
#define BOOST_VMD_DETECT_1_1
#define BOOST_VMD_DETECT_2_2
#define BOOST_VMD_DETECT_3_3
#define BOOST_VMD_DETECT_4_4
#define BOOST_VMD_DETECT_5_5
#define BOOST_VMD_DETECT_6_6
#define BOOST_VMD_DETECT_7_7
#define BOOST_VMD_DETECT_8_8
#define BOOST_VMD_DETECT_9_9
#define BOOST_VMD_DETECT_10_10
#define BOOST_VMD_DETECT_11_11
#define BOOST_VMD_DETECT_12_12
#define BOOST_VMD_DETECT_13_13
#define BOOST_VMD_DETECT_14_14
#define BOOST_VMD_DETECT_15_15
#define BOOST_VMD_DETECT_16_16
#define BOOST_VMD_DETECT_17_17
#define BOOST_VMD_DETECT_18_18
#define BOOST_VMD_DETECT_19_19
#define BOOST_VMD_DETECT_20_20
#define BOOST_VMD_DETECT_21_21
#define BOOST_VMD_DETECT_22_22
#define BOOST_VMD_DETECT_23_23
#define BOOST_VMD_DETECT_24_24
#define BOOST_VMD_DETECT_25_25
#define BOOST_VMD_DETECT_26_26
#define BOOST_VMD_DETECT_27_27
#define BOOST_VMD_DETECT_28_28
#define BOOST_VMD_DETECT_29_29
#define BOOST_VMD_DETECT_30_30
#define BOOST_VMD_DETECT_31_31
#define BOOST_VMD_DETECT_32_32
#define BOOST_VMD_DETECT_33_33
#define BOOST_VMD_DETECT_34_34
#define BOOST_VMD_DETECT_35_35
#define BOOST_VMD_DETECT_36_36
#define BOOST_VMD_DETECT_37_37
#define BOOST_VMD_DETECT_38_38
#define BOOST_VMD_DETECT_39_39
#define BOOST_VMD_DETECT_40_40
#define BOOST_VMD_DETECT_41_41
#define BOOST_VMD_DETECT_42_42
#define BOOST_VMD_DETECT_43_43
#define BOOST_VMD_DETECT_44_44
#define BOOST_VMD_DETECT_45_45
#define BOOST_VMD_DETECT_46_46
#define BOOST_VMD_DETECT_47_47
#define BOOST_VMD_DETECT_48_48
#define BOOST_VMD_DETECT_49_49
#define BOOST_VMD_DETECT_50_50
#define BOOST_VMD_DETECT_51_51
#define BOOST_VMD_DETECT_52_52
#define BOOST_VMD_DETECT_53_53
#define BOOST_VMD_DETECT_54_54
#define BOOST_VMD_DETECT_55_55
#define BOOST_VMD_DETECT_56_56
#define BOOST_VMD_DETECT_57_57
#define BOOST_VMD_DETECT_58_58
#define BOOST_VMD_DETECT_59_59
#define BOOST_VMD_DETECT_60_60
#define BOOST_VMD_DETECT_61_61
#define BOOST_VMD_DETECT_62_62
#define BOOST_VMD_DETECT_63_63
#define BOOST_VMD_DETECT_64_64
#define BOOST_VMD_DETECT_65_65
#define BOOST_VMD_DETECT_66_66
#define BOOST_VMD_DETECT_67_67
#define BOOST_VMD_DETECT_68_68
#define BOOST_VMD_DETECT_69_69
#define BOOST_VMD_DETECT_70_70
#define BOOST_VMD_DETECT_71_71
#define BOOST_VMD_DETECT_72_72
#define BOOST_VMD_DETECT_73_73
#define BOOST_VMD_DETECT_74_74
#define BOOST_VMD_DETECT_75_75
#define BOOST_VMD_DETECT_76_76
#define BOOST_VMD_DETECT_77_77
#define BOOST_VMD_DETECT_78_78
#define BOOST_VMD_DETECT_79_79
#define BOOST_VMD_DETECT_80_80
#define BOOST_VMD_DETECT_81_81
#define BOOST_VMD_DETECT_82_82
#define BOOST_VMD_DETECT_83_83
#define BOOST_VMD_DETECT_84_84
#define BOOST_VMD_DETECT_85_85
#define BOOST_VMD_DETECT_86_86
#define BOOST_VMD_DETECT_87_87
#define BOOST_VMD_DETECT_88_88
#define BOOST_VMD_DETECT_89_89
#define BOOST_VMD_DETECT_90_90
#define BOOST_VMD_DETECT_91_91
#define BOOST_VMD_DETECT_92_92
#define BOOST_VMD_DETECT_93_93
#define BOOST_VMD_DETECT_94_94
#define BOOST_VMD_DETECT_95_95
#define BOOST_VMD_DETECT_96_96
#define BOOST_VMD_DETECT_97_97
#define BOOST_VMD_DETECT_98_98
#define BOOST_VMD_DETECT_99_99
#define BOOST_VMD_DETECT_100_100
#define BOOST_VMD_DETECT_101_101
#define BOOST_VMD_DETECT_102_102
#define BOOST_VMD_DETECT_103_103
#define BOOST_VMD_DETECT_104_104
#define BOOST_VMD_DETECT_105_105
#define BOOST_VMD_DETECT_106_106
#define BOOST_VMD_DETECT_107_107
#define BOOST_VMD_DETECT_108_108
#define BOOST_VMD_DETECT_109_109
#define BOOST_VMD_DETECT_110_110
#define BOOST_VMD_DETECT_111_111
#define BOOST_VMD_DETECT_112_112
#define BOOST_VMD_DETECT_113_113
#define BOOST_VMD_DETECT_114_114
#define BOOST_VMD_DETECT_115_115
#define BOOST_VMD_DETECT_116_116
#define BOOST_VMD_DETECT_117_117
#define BOOST_VMD_DETECT_118_118
#define BOOST_VMD_DETECT_119_119
#define BOOST_VMD_DETECT_120_120
#define BOOST_VMD_DETECT_121_121
#define BOOST_VMD_DETECT_122_122
#define BOOST_VMD_DETECT_123_123
#define BOOST_VMD_DETECT_124_124
#define BOOST_VMD_DETECT_125_125
#define BOOST_VMD_DETECT_126_126
#define BOOST_VMD_DETECT_127_127
#define BOOST_VMD_DETECT_128_128
#define BOOST_VMD_DETECT_129_129
#define BOOST_VMD_DETECT_130_130
#define BOOST_VMD_DETECT_131_131
#define BOOST_VMD_DETECT_132_132
#define BOOST_VMD_DETECT_133_133
#define BOOST_VMD_DETECT_134_134
#define BOOST_VMD_DETECT_135_135
#define BOOST_VMD_DETECT_136_136
#define BOOST_VMD_DETECT_137_137
#define BOOST_VMD_DETECT_138_138
#define BOOST_VMD_DETECT_139_139
#define BOOST_VMD_DETECT_140_140
#define BOOST_VMD_DETECT_141_141
#define BOOST_VMD_DETECT_142_142
#define BOOST_VMD_DETECT_143_143
#define BOOST_VMD_DETECT_144_144
#define BOOST_VMD_DETECT_145_145
#define BOOST_VMD_DETECT_146_146
#define BOOST_VMD_DETECT_147_147
#define BOOST_VMD_DETECT_148_148
#define BOOST_VMD_DETECT_149_149
#define BOOST_VMD_DETECT_150_150
#define BOOST_VMD_DETECT_151_151
#define BOOST_VMD_DETECT_152_152
#define BOOST_VMD_DETECT_153_153
#define BOOST_VMD_DETECT_154_154
#define BOOST_VMD_DETECT_155_155
#define BOOST_VMD_DETECT_156_156
#define BOOST_VMD_DETECT_157_157
#define BOOST_VMD_DETECT_158_158
#define BOOST_VMD_DETECT_159_159
#define BOOST_VMD_DETECT_160_160
#define BOOST_VMD_DETECT_161_161
#define BOOST_VMD_DETECT_162_162
#define BOOST_VMD_DETECT_163_163
#define BOOST_VMD_DETECT_164_164
#define BOOST_VMD_DETECT_165_165
#define BOOST_VMD_DETECT_166_166
#define BOOST_VMD_DETECT_167_167
#define BOOST_VMD_DETECT_168_168
#define BOOST_VMD_DETECT_169_169
#define BOOST_VMD_DETECT_170_170
#define BOOST_VMD_DETECT_171_171
#define BOOST_VMD_DETECT_172_172
#define BOOST_VMD_DETECT_173_173
#define BOOST_VMD_DETECT_174_174
#define BOOST_VMD_DETECT_175_175
#define BOOST_VMD_DETECT_176_176
#define BOOST_VMD_DETECT_177_177
#define BOOST_VMD_DETECT_178_178
#define BOOST_VMD_DETECT_179_179
#define BOOST_VMD_DETECT_180_180
#define BOOST_VMD_DETECT_181_181
#define BOOST_VMD_DETECT_182_182
#define BOOST_VMD_DETECT_183_183
#define BOOST_VMD_DETECT_184_184
#define BOOST_VMD_DETECT_185_185
#define BOOST_VMD_DETECT_186_186
#define BOOST_VMD_DETECT_187_187
#define BOOST_VMD_DETECT_188_188
#define BOOST_VMD_DETECT_189_189
#define BOOST_VMD_DETECT_190_190
#define BOOST_VMD_DETECT_191_191
#define BOOST_VMD_DETECT_192_192
#define BOOST_VMD_DETECT_193_193
#define BOOST_VMD_DETECT_194_194
#define BOOST_VMD_DETECT_195_195
#define BOOST_VMD_DETECT_196_196
#define BOOST_VMD_DETECT_197_197
#define BOOST_VMD_DETECT_198_198
#define BOOST_VMD_DETECT_199_199
#define BOOST_VMD_DETECT_200_200
#define BOOST_VMD_DETECT_201_201
#define BOOST_VMD_DETECT_202_202
#define BOOST_VMD_DETECT_203_203
#define BOOST_VMD_DETECT_204_204
#define BOOST_VMD_DETECT_205_205
#define BOOST_VMD_DETECT_206_206
#define BOOST_VMD_DETECT_207_207
#define BOOST_VMD_DETECT_208_208
#define BOOST_VMD_DETECT_209_209
#define BOOST_VMD_DETECT_210_210
#define BOOST_VMD_DETECT_211_211
#define BOOST_VMD_DETECT_212_212
#define BOOST_VMD_DETECT_213_213
#define BOOST_VMD_DETECT_214_214
#define BOOST_VMD_DETECT_215_215
#define BOOST_VMD_DETECT_216_216
#define BOOST_VMD_DETECT_217_217
#define BOOST_VMD_DETECT_218_218
#define BOOST_VMD_DETECT_219_219
#define BOOST_VMD_DETECT_220_220
#define BOOST_VMD_DETECT_221_221
#define BOOST_VMD_DETECT_222_222
#define BOOST_VMD_DETECT_223_223
#define BOOST_VMD_DETECT_224_224
#define BOOST_VMD_DETECT_225_225
#define BOOST_VMD_DETECT_226_226
#define BOOST_VMD_DETECT_227_227
#define BOOST_VMD_DETECT_228_228
#define BOOST_VMD_DETECT_229_229
#define BOOST_VMD_DETECT_230_230
#define BOOST_VMD_DETECT_231_231
#define BOOST_VMD_DETECT_232_232
#define BOOST_VMD_DETECT_233_233
#define BOOST_VMD_DETECT_234_234
#define BOOST_VMD_DETECT_235_235
#define BOOST_VMD_DETECT_236_236
#define BOOST_VMD_DETECT_237_237
#define BOOST_VMD_DETECT_238_238
#define BOOST_VMD_DETECT_239_239
#define BOOST_VMD_DETECT_240_240
#define BOOST_VMD_DETECT_241_241
#define BOOST_VMD_DETECT_242_242
#define BOOST_VMD_DETECT_243_243
#define BOOST_VMD_DETECT_244_244
#define BOOST_VMD_DETECT_245_245
#define BOOST_VMD_DETECT_246_246
#define BOOST_VMD_DETECT_247_247
#define BOOST_VMD_DETECT_248_248
#define BOOST_VMD_DETECT_249_249
#define BOOST_VMD_DETECT_250_250
#define BOOST_VMD_DETECT_251_251
#define BOOST_VMD_DETECT_252_252
#define BOOST_VMD_DETECT_253_253
#define BOOST_VMD_DETECT_254_254
#define BOOST_VMD_DETECT_255_255
#define BOOST_VMD_DETECT_256_256

#define BOOST_VMD_SUBTYPE_REGISTER_0 (BOOST_VMD_TYPE_NUMBER,0)
#define BOOST_VMD_SUBTYPE_REGISTER_1 (BOOST_VMD_TYPE_NUMBER,1)
#define BOOST_VMD_SUBTYPE_REGISTER_2 (BOOST_VMD_TYPE_NUMBER,2)
#define BOOST_VMD_SUBTYPE_REGISTER_3 (BOOST_VMD_TYPE_NUMBER,3)
#define BOOST_VMD_SUBTYPE_REGISTER_4 (BOOST_VMD_TYPE_NUMBER,4)
#define BOOST_VMD_SUBTYPE_REGISTER_5 (BOOST_VMD_TYPE_NUMBER,5)
#define BOOST_VMD_SUBTYPE_REGISTER_6 (BOOST_VMD_TYPE_NUMBER,6)
#define BOOST_VMD_SUBTYPE_REGISTER_7 (BOOST_VMD_TYPE_NUMBER,7)
#define BOOST_VMD_SUBTYPE_REGISTER_8 (BOOST_VMD_TYPE_NUMBER,8)
#define BOOST_VMD_SUBTYPE_REGISTER_9 (BOOST_VMD_TYPE_NUMBER,9)
#define BOOST_VMD_SUBTYPE_REGISTER_10 (BOOST_VMD_TYPE_NUMBER,10)
#define BOOST_VMD_SUBTYPE_REGISTER_11 (BOOST_VMD_TYPE_NUMBER,11)
#define BOOST_VMD_SUBTYPE_REGISTER_12 (BOOST_VMD_TYPE_NUMBER,12)
#define BOOST_VMD_SUBTYPE_REGISTER_13 (BOOST_VMD_TYPE_NUMBER,13)
#define BOOST_VMD_SUBTYPE_REGISTER_14 (BOOST_VMD_TYPE_NUMBER,14)
#define BOOST_VMD_SUBTYPE_REGISTER_15 (BOOST_VMD_TYPE_NUMBER,15)
#define BOOST_VMD_SUBTYPE_REGISTER_16 (BOOST_VMD_TYPE_NUMBER,16)
#define BOOST_VMD_SUBTYPE_REGISTER_17 (BOOST_VMD_TYPE_NUMBER,17)
#define BOOST_VMD_SUBTYPE_REGISTER_18 (BOOST_VMD_TYPE_NUMBER,18)
#define BOOST_VMD_SUBTYPE_REGISTER_19 (BOOST_VMD_TYPE_NUMBER,19)
#define BOOST_VMD_SUBTYPE_REGISTER_20 (BOOST_VMD_TYPE_NUMBER,20)
#define BOOST_VMD_SUBTYPE_REGISTER_21 (BOOST_VMD_TYPE_NUMBER,21)
#define BOOST_VMD_SUBTYPE_REGISTER_22 (BOOST_VMD_TYPE_NUMBER,22)
#define BOOST_VMD_SUBTYPE_REGISTER_23 (BOOST_VMD_TYPE_NUMBER,23)
#define BOOST_VMD_SUBTYPE_REGISTER_24 (BOOST_VMD_TYPE_NUMBER,24)
#define BOOST_VMD_SUBTYPE_REGISTER_25 (BOOST_VMD_TYPE_NUMBER,25)
#define BOOST_VMD_SUBTYPE_REGISTER_26 (BOOST_VMD_TYPE_NUMBER,26)
#define BOOST_VMD_SUBTYPE_REGISTER_27 (BOOST_VMD_TYPE_NUMBER,27)
#define BOOST_VMD_SUBTYPE_REGISTER_28 (BOOST_VMD_TYPE_NUMBER,28)
#define BOOST_VMD_SUBTYPE_REGISTER_29 (BOOST_VMD_TYPE_NUMBER,29)
#define BOOST_VMD_SUBTYPE_REGISTER_30 (BOOST_VMD_TYPE_NUMBER,30)
#define BOOST_VMD_SUBTYPE_REGISTER_31 (BOOST_VMD_TYPE_NUMBER,31)
#define BOOST_VMD_SUBTYPE_REGISTER_32 (BOOST_VMD_TYPE_NUMBER,32)
#define BOOST_VMD_SUBTYPE_REGISTER_33 (BOOST_VMD_TYPE_NUMBER,33)
#define BOOST_VMD_SUBTYPE_REGISTER_34 (BOOST_VMD_TYPE_NUMBER,34)
#define BOOST_VMD_SUBTYPE_REGISTER_35 (BOOST_VMD_TYPE_NUMBER,35)
#define BOOST_VMD_SUBTYPE_REGISTER_36 (BOOST_VMD_TYPE_NUMBER,36)
#define BOOST_VMD_SUBTYPE_REGISTER_37 (BOOST_VMD_TYPE_NUMBER,37)
#define BOOST_VMD_SUBTYPE_REGISTER_38 (BOOST_VMD_TYPE_NUMBER,38)
#define BOOST_VMD_SUBTYPE_REGISTER_39 (BOOST_VMD_TYPE_NUMBER,39)
#define BOOST_VMD_SUBTYPE_REGISTER_40 (BOOST_VMD_TYPE_NUMBER,40)
#define BOOST_VMD_SUBTYPE_REGISTER_41 (BOOST_VMD_TYPE_NUMBER,41)
#define BOOST_VMD_SUBTYPE_REGISTER_42 (BOOST_VMD_TYPE_NUMBER,42)
#define BOOST_VMD_SUBTYPE_REGISTER_43 (BOOST_VMD_TYPE_NUMBER,43)
#define BOOST_VMD_SUBTYPE_REGISTER_44 (BOOST_VMD_TYPE_NUMBER,44)
#define BOOST_VMD_SUBTYPE_REGISTER_45 (BOOST_VMD_TYPE_NUMBER,45)
#define BOOST_VMD_SUBTYPE_REGISTER_46 (BOOST_VMD_TYPE_NUMBER,46)
#define BOOST_VMD_SUBTYPE_REGISTER_47 (BOOST_VMD_TYPE_NUMBER,47)
#define BOOST_VMD_SUBTYPE_REGISTER_48 (BOOST_VMD_TYPE_NUMBER,48)
#define BOOST_VMD_SUBTYPE_REGISTER_49 (BOOST_VMD_TYPE_NUMBER,49)
#define BOOST_VMD_SUBTYPE_REGISTER_50 (BOOST_VMD_TYPE_NUMBER,50)
#define BOOST_VMD_SUBTYPE_REGISTER_51 (BOOST_VMD_TYPE_NUMBER,51)
#define BOOST_VMD_SUBTYPE_REGISTER_52 (BOOST_VMD_TYPE_NUMBER,52)
#define BOOST_VMD_SUBTYPE_REGISTER_53 (BOOST_VMD_TYPE_NUMBER,53)
#define BOOST_VMD_SUBTYPE_REGISTER_54 (BOOST_VMD_TYPE_NUMBER,54)
#define BOOST_VMD_SUBTYPE_REGISTER_55 (BOOST_VMD_TYPE_NUMBER,55)
#define BOOST_VMD_SUBTYPE_REGISTER_56 (BOOST_VMD_TYPE_NUMBER,56)
#define BOOST_VMD_SUBTYPE_REGISTER_57 (BOOST_VMD_TYPE_NUMBER,57)
#define BOOST_VMD_SUBTYPE_REGISTER_58 (BOOST_VMD_TYPE_NUMBER,58)
#define BOOST_VMD_SUBTYPE_REGISTER_59 (BOOST_VMD_TYPE_NUMBER,59)
#define BOOST_VMD_SUBTYPE_REGISTER_60 (BOOST_VMD_TYPE_NUMBER,60)
#define BOOST_VMD_SUBTYPE_REGISTER_61 (BOOST_VMD_TYPE_NUMBER,61)
#define BOOST_VMD_SUBTYPE_REGISTER_62 (BOOST_VMD_TYPE_NUMBER,62)
#define BOOST_VMD_SUBTYPE_REGISTER_63 (BOOST_VMD_TYPE_NUMBER,63)
#define BOOST_VMD_SUBTYPE_REGISTER_64 (BOOST_VMD_TYPE_NUMBER,64)
#define BOOST_VMD_SUBTYPE_REGISTER_65 (BOOST_VMD_TYPE_NUMBER,65)
#define BOOST_VMD_SUBTYPE_REGISTER_66 (BOOST_VMD_TYPE_NUMBER,66)
#define BOOST_VMD_SUBTYPE_REGISTER_67 (BOOST_VMD_TYPE_NUMBER,67)
#define BOOST_VMD_SUBTYPE_REGISTER_68 (BOOST_VMD_TYPE_NUMBER,68)
#define BOOST_VMD_SUBTYPE_REGISTER_69 (BOOST_VMD_TYPE_NUMBER,69)
#define BOOST_VMD_SUBTYPE_REGISTER_70 (BOOST_VMD_TYPE_NUMBER,70)
#define BOOST_VMD_SUBTYPE_REGISTER_71 (BOOST_VMD_TYPE_NUMBER,71)
#define BOOST_VMD_SUBTYPE_REGISTER_72 (BOOST_VMD_TYPE_NUMBER,72)
#define BOOST_VMD_SUBTYPE_REGISTER_73 (BOOST_VMD_TYPE_NUMBER,73)
#define BOOST_VMD_SUBTYPE_REGISTER_74 (BOOST_VMD_TYPE_NUMBER,74)
#define BOOST_VMD_SUBTYPE_REGISTER_75 (BOOST_VMD_TYPE_NUMBER,75)
#define BOOST_VMD_SUBTYPE_REGISTER_76 (BOOST_VMD_TYPE_NUMBER,76)
#define BOOST_VMD_SUBTYPE_REGISTER_77 (BOOST_VMD_TYPE_NUMBER,77)
#define BOOST_VMD_SUBTYPE_REGISTER_78 (BOOST_VMD_TYPE_NUMBER,78)
#define BOOST_VMD_SUBTYPE_REGISTER_79 (BOOST_VMD_TYPE_NUMBER,79)
#define BOOST_VMD_SUBTYPE_REGISTER_80 (BOOST_VMD_TYPE_NUMBER,80)
#define BOOST_VMD_SUBTYPE_REGISTER_81 (BOOST_VMD_TYPE_NUMBER,81)
#define BOOST_VMD_SUBTYPE_REGISTER_82 (BOOST_VMD_TYPE_NUMBER,82)
#define BOOST_VMD_SUBTYPE_REGISTER_83 (BOOST_VMD_TYPE_NUMBER,83)
#define BOOST_VMD_SUBTYPE_REGISTER_84 (BOOST_VMD_TYPE_NUMBER,84)
#define BOOST_VMD_SUBTYPE_REGISTER_85 (BOOST_VMD_TYPE_NUMBER,85)
#define BOOST_VMD_SUBTYPE_REGISTER_86 (BOOST_VMD_TYPE_NUMBER,86)
#define BOOST_VMD_SUBTYPE_REGISTER_87 (BOOST_VMD_TYPE_NUMBER,87)
#define BOOST_VMD_SUBTYPE_REGISTER_88 (BOOST_VMD_TYPE_NUMBER,88)
#define BOOST_VMD_SUBTYPE_REGISTER_89 (BOOST_VMD_TYPE_NUMBER,89)
#define BOOST_VMD_SUBTYPE_REGISTER_90 (BOOST_VMD_TYPE_NUMBER,90)
#define BOOST_VMD_SUBTYPE_REGISTER_91 (BOOST_VMD_TYPE_NUMBER,91)
#define BOOST_VMD_SUBTYPE_REGISTER_92 (BOOST_VMD_TYPE_NUMBER,92)
#define BOOST_VMD_SUBTYPE_REGISTER_93 (BOOST_VMD_TYPE_NUMBER,93)
#define BOOST_VMD_SUBTYPE_REGISTER_94 (BOOST_VMD_TYPE_NUMBER,94)
#define BOOST_VMD_SUBTYPE_REGISTER_95 (BOOST_VMD_TYPE_NUMBER,95)
#define BOOST_VMD_SUBTYPE_REGISTER_96 (BOOST_VMD_TYPE_NUMBER,96)
#define BOOST_VMD_SUBTYPE_REGISTER_97 (BOOST_VMD_TYPE_NUMBER,97)
#define BOOST_VMD_SUBTYPE_REGISTER_98 (BOOST_VMD_TYPE_NUMBER,98)
#define BOOST_VMD_SUBTYPE_REGISTER_99 (BOOST_VMD_TYPE_NUMBER,99)
#define BOOST_VMD_SUBTYPE_REGISTER_100 (BOOST_VMD_TYPE_NUMBER,100)
#define BOOST_VMD_SUBTYPE_REGISTER_101 (BOOST_VMD_TYPE_NUMBER,101)
#define BOOST_VMD_SUBTYPE_REGISTER_102 (BOOST_VMD_TYPE_NUMBER,102)
#define BOOST_VMD_SUBTYPE_REGISTER_103 (BOOST_VMD_TYPE_NUMBER,103)
#define BOOST_VMD_SUBTYPE_REGISTER_104 (BOOST_VMD_TYPE_NUMBER,104)
#define BOOST_VMD_SUBTYPE_REGISTER_105 (BOOST_VMD_TYPE_NUMBER,105)
#define BOOST_VMD_SUBTYPE_REGISTER_106 (BOOST_VMD_TYPE_NUMBER,106)
#define BOOST_VMD_SUBTYPE_REGISTER_107 (BOOST_VMD_TYPE_NUMBER,107)
#define BOOST_VMD_SUBTYPE_REGISTER_108 (BOOST_VMD_TYPE_NUMBER,108)
#define BOOST_VMD_SUBTYPE_REGISTER_109 (BOOST_VMD_TYPE_NUMBER,109)
#define BOOST_VMD_SUBTYPE_REGISTER_110 (BOOST_VMD_TYPE_NUMBER,110)
#define BOOST_VMD_SUBTYPE_REGISTER_111 (BOOST_VMD_TYPE_NUMBER,111)
#define BOOST_VMD_SUBTYPE_REGISTER_112 (BOOST_VMD_TYPE_NUMBER,112)
#define BOOST_VMD_SUBTYPE_REGISTER_113 (BOOST_VMD_TYPE_NUMBER,113)
#define BOOST_VMD_SUBTYPE_REGISTER_114 (BOOST_VMD_TYPE_NUMBER,114)
#define BOOST_VMD_SUBTYPE_REGISTER_115 (BOOST_VMD_TYPE_NUMBER,115)
#define BOOST_VMD_SUBTYPE_REGISTER_116 (BOOST_VMD_TYPE_NUMBER,116)
#define BOOST_VMD_SUBTYPE_REGISTER_117 (BOOST_VMD_TYPE_NUMBER,117)
#define BOOST_VMD_SUBTYPE_REGISTER_118 (BOOST_VMD_TYPE_NUMBER,118)
#define BOOST_VMD_SUBTYPE_REGISTER_119 (BOOST_VMD_TYPE_NUMBER,119)
#define BOOST_VMD_SUBTYPE_REGISTER_120 (BOOST_VMD_TYPE_NUMBER,120)
#define BOOST_VMD_SUBTYPE_REGISTER_121 (BOOST_VMD_TYPE_NUMBER,121)
#define BOOST_VMD_SUBTYPE_REGISTER_122 (BOOST_VMD_TYPE_NUMBER,122)
#define BOOST_VMD_SUBTYPE_REGISTER_123 (BOOST_VMD_TYPE_NUMBER,123)
#define BOOST_VMD_SUBTYPE_REGISTER_124 (BOOST_VMD_TYPE_NUMBER,124)
#define BOOST_VMD_SUBTYPE_REGISTER_125 (BOOST_VMD_TYPE_NUMBER,125)
#define BOOST_VMD_SUBTYPE_REGISTER_126 (BOOST_VMD_TYPE_NUMBER,126)
#define BOOST_VMD_SUBTYPE_REGISTER_127 (BOOST_VMD_TYPE_NUMBER,127)
#define BOOST_VMD_SUBTYPE_REGISTER_128 (BOOST_VMD_TYPE_NUMBER,128)
#define BOOST_VMD_SUBTYPE_REGISTER_129 (BOOST_VMD_TYPE_NUMBER,129)
#define BOOST_VMD_SUBTYPE_REGISTER_130 (BOOST_VMD_TYPE_NUMBER,130)
#define BOOST_VMD_SUBTYPE_REGISTER_131 (BOOST_VMD_TYPE_NUMBER,131)
#define BOOST_VMD_SUBTYPE_REGISTER_132 (BOOST_VMD_TYPE_NUMBER,132)
#define BOOST_VMD_SUBTYPE_REGISTER_133 (BOOST_VMD_TYPE_NUMBER,133)
#define BOOST_VMD_SUBTYPE_REGISTER_134 (BOOST_VMD_TYPE_NUMBER,134)
#define BOOST_VMD_SUBTYPE_REGISTER_135 (BOOST_VMD_TYPE_NUMBER,135)
#define BOOST_VMD_SUBTYPE_REGISTER_136 (BOOST_VMD_TYPE_NUMBER,136)
#define BOOST_VMD_SUBTYPE_REGISTER_137 (BOOST_VMD_TYPE_NUMBER,137)
#define BOOST_VMD_SUBTYPE_REGISTER_138 (BOOST_VMD_TYPE_NUMBER,138)
#define BOOST_VMD_SUBTYPE_REGISTER_139 (BOOST_VMD_TYPE_NUMBER,139)
#define BOOST_VMD_SUBTYPE_REGISTER_140 (BOOST_VMD_TYPE_NUMBER,140)
#define BOOST_VMD_SUBTYPE_REGISTER_141 (BOOST_VMD_TYPE_NUMBER,141)
#define BOOST_VMD_SUBTYPE_REGISTER_142 (BOOST_VMD_TYPE_NUMBER,142)
#define BOOST_VMD_SUBTYPE_REGISTER_143 (BOOST_VMD_TYPE_NUMBER,143)
#define BOOST_VMD_SUBTYPE_REGISTER_144 (BOOST_VMD_TYPE_NUMBER,144)
#define BOOST_VMD_SUBTYPE_REGISTER_145 (BOOST_VMD_TYPE_NUMBER,145)
#define BOOST_VMD_SUBTYPE_REGISTER_146 (BOOST_VMD_TYPE_NUMBER,146)
#define BOOST_VMD_SUBTYPE_REGISTER_147 (BOOST_VMD_TYPE_NUMBER,147)
#define BOOST_VMD_SUBTYPE_REGISTER_148 (BOOST_VMD_TYPE_NUMBER,148)
#define BOOST_VMD_SUBTYPE_REGISTER_149 (BOOST_VMD_TYPE_NUMBER,149)
#define BOOST_VMD_SUBTYPE_REGISTER_150 (BOOST_VMD_TYPE_NUMBER,150)
#define BOOST_VMD_SUBTYPE_REGISTER_151 (BOOST_VMD_TYPE_NUMBER,151)
#define BOOST_VMD_SUBTYPE_REGISTER_152 (BOOST_VMD_TYPE_NUMBER,152)
#define BOOST_VMD_SUBTYPE_REGISTER_153 (BOOST_VMD_TYPE_NUMBER,153)
#define BOOST_VMD_SUBTYPE_REGISTER_154 (BOOST_VMD_TYPE_NUMBER,154)
#define BOOST_VMD_SUBTYPE_REGISTER_155 (BOOST_VMD_TYPE_NUMBER,155)
#define BOOST_VMD_SUBTYPE_REGISTER_156 (BOOST_VMD_TYPE_NUMBER,156)
#define BOOST_VMD_SUBTYPE_REGISTER_157 (BOOST_VMD_TYPE_NUMBER,157)
#define BOOST_VMD_SUBTYPE_REGISTER_158 (BOOST_VMD_TYPE_NUMBER,158)
#define BOOST_VMD_SUBTYPE_REGISTER_159 (BOOST_VMD_TYPE_NUMBER,159)
#define BOOST_VMD_SUBTYPE_REGISTER_160 (BOOST_VMD_TYPE_NUMBER,160)
#define BOOST_VMD_SUBTYPE_REGISTER_161 (BOOST_VMD_TYPE_NUMBER,161)
#define BOOST_VMD_SUBTYPE_REGISTER_162 (BOOST_VMD_TYPE_NUMBER,162)
#define BOOST_VMD_SUBTYPE_REGISTER_163 (BOOST_VMD_TYPE_NUMBER,163)
#define BOOST_VMD_SUBTYPE_REGISTER_164 (BOOST_VMD_TYPE_NUMBER,164)
#define BOOST_VMD_SUBTYPE_REGISTER_165 (BOOST_VMD_TYPE_NUMBER,165)
#define BOOST_VMD_SUBTYPE_REGISTER_166 (BOOST_VMD_TYPE_NUMBER,166)
#define BOOST_VMD_SUBTYPE_REGISTER_167 (BOOST_VMD_TYPE_NUMBER,167)
#define BOOST_VMD_SUBTYPE_REGISTER_168 (BOOST_VMD_TYPE_NUMBER,168)
#define BOOST_VMD_SUBTYPE_REGISTER_169 (BOOST_VMD_TYPE_NUMBER,169)
#define BOOST_VMD_SUBTYPE_REGISTER_170 (BOOST_VMD_TYPE_NUMBER,170)
#define BOOST_VMD_SUBTYPE_REGISTER_171 (BOOST_VMD_TYPE_NUMBER,171)
#define BOOST_VMD_SUBTYPE_REGISTER_172 (BOOST_VMD_TYPE_NUMBER,172)
#define BOOST_VMD_SUBTYPE_REGISTER_173 (BOOST_VMD_TYPE_NUMBER,173)
#define BOOST_VMD_SUBTYPE_REGISTER_174 (BOOST_VMD_TYPE_NUMBER,174)
#define BOOST_VMD_SUBTYPE_REGISTER_175 (BOOST_VMD_TYPE_NUMBER,175)
#define BOOST_VMD_SUBTYPE_REGISTER_176 (BOOST_VMD_TYPE_NUMBER,176)
#define BOOST_VMD_SUBTYPE_REGISTER_177 (BOOST_VMD_TYPE_NUMBER,177)
#define BOOST_VMD_SUBTYPE_REGISTER_178 (BOOST_VMD_TYPE_NUMBER,178)
#define BOOST_VMD_SUBTYPE_REGISTER_179 (BOOST_VMD_TYPE_NUMBER,179)
#define BOOST_VMD_SUBTYPE_REGISTER_180 (BOOST_VMD_TYPE_NUMBER,180)
#define BOOST_VMD_SUBTYPE_REGISTER_181 (BOOST_VMD_TYPE_NUMBER,181)
#define BOOST_VMD_SUBTYPE_REGISTER_182 (BOOST_VMD_TYPE_NUMBER,182)
#define BOOST_VMD_SUBTYPE_REGISTER_183 (BOOST_VMD_TYPE_NUMBER,183)
#define BOOST_VMD_SUBTYPE_REGISTER_184 (BOOST_VMD_TYPE_NUMBER,184)
#define BOOST_VMD_SUBTYPE_REGISTER_185 (BOOST_VMD_TYPE_NUMBER,185)
#define BOOST_VMD_SUBTYPE_REGISTER_186 (BOOST_VMD_TYPE_NUMBER,186)
#define BOOST_VMD_SUBTYPE_REGISTER_187 (BOOST_VMD_TYPE_NUMBER,187)
#define BOOST_VMD_SUBTYPE_REGISTER_188 (BOOST_VMD_TYPE_NUMBER,188)
#define BOOST_VMD_SUBTYPE_REGISTER_189 (BOOST_VMD_TYPE_NUMBER,189)
#define BOOST_VMD_SUBTYPE_REGISTER_190 (BOOST_VMD_TYPE_NUMBER,190)
#define BOOST_VMD_SUBTYPE_REGISTER_191 (BOOST_VMD_TYPE_NUMBER,191)
#define BOOST_VMD_SUBTYPE_REGISTER_192 (BOOST_VMD_TYPE_NUMBER,192)
#define BOOST_VMD_SUBTYPE_REGISTER_193 (BOOST_VMD_TYPE_NUMBER,193)
#define BOOST_VMD_SUBTYPE_REGISTER_194 (BOOST_VMD_TYPE_NUMBER,194)
#define BOOST_VMD_SUBTYPE_REGISTER_195 (BOOST_VMD_TYPE_NUMBER,195)
#define BOOST_VMD_SUBTYPE_REGISTER_196 (BOOST_VMD_TYPE_NUMBER,196)
#define BOOST_VMD_SUBTYPE_REGISTER_197 (BOOST_VMD_TYPE_NUMBER,197)
#define BOOST_VMD_SUBTYPE_REGISTER_198 (BOOST_VMD_TYPE_NUMBER,198)
#define BOOST_VMD_SUBTYPE_REGISTER_199 (BOOST_VMD_TYPE_NUMBER,199)
#define BOOST_VMD_SUBTYPE_REGISTER_200 (BOOST_VMD_TYPE_NUMBER,200)
#define BOOST_VMD_SUBTYPE_REGISTER_201 (BOOST_VMD_TYPE_NUMBER,201)
#define BOOST_VMD_SUBTYPE_REGISTER_202 (BOOST_VMD_TYPE_NUMBER,202)
#define BOOST_VMD_SUBTYPE_REGISTER_203 (BOOST_VMD_TYPE_NUMBER,203)
#define BOOST_VMD_SUBTYPE_REGISTER_204 (BOOST_VMD_TYPE_NUMBER,204)
#define BOOST_VMD_SUBTYPE_REGISTER_205 (BOOST_VMD_TYPE_NUMBER,205)
#define BOOST_VMD_SUBTYPE_REGISTER_206 (BOOST_VMD_TYPE_NUMBER,206)
#define BOOST_VMD_SUBTYPE_REGISTER_207 (BOOST_VMD_TYPE_NUMBER,207)
#define BOOST_VMD_SUBTYPE_REGISTER_208 (BOOST_VMD_TYPE_NUMBER,208)
#define BOOST_VMD_SUBTYPE_REGISTER_209 (BOOST_VMD_TYPE_NUMBER,209)
#define BOOST_VMD_SUBTYPE_REGISTER_210 (BOOST_VMD_TYPE_NUMBER,210)
#define BOOST_VMD_SUBTYPE_REGISTER_211 (BOOST_VMD_TYPE_NUMBER,211)
#define BOOST_VMD_SUBTYPE_REGISTER_212 (BOOST_VMD_TYPE_NUMBER,212)
#define BOOST_VMD_SUBTYPE_REGISTER_213 (BOOST_VMD_TYPE_NUMBER,213)
#define BOOST_VMD_SUBTYPE_REGISTER_214 (BOOST_VMD_TYPE_NUMBER,214)
#define BOOST_VMD_SUBTYPE_REGISTER_215 (BOOST_VMD_TYPE_NUMBER,215)
#define BOOST_VMD_SUBTYPE_REGISTER_216 (BOOST_VMD_TYPE_NUMBER,216)
#define BOOST_VMD_SUBTYPE_REGISTER_217 (BOOST_VMD_TYPE_NUMBER,217)
#define BOOST_VMD_SUBTYPE_REGISTER_218 (BOOST_VMD_TYPE_NUMBER,218)
#define BOOST_VMD_SUBTYPE_REGISTER_219 (BOOST_VMD_TYPE_NUMBER,219)
#define BOOST_VMD_SUBTYPE_REGISTER_220 (BOOST_VMD_TYPE_NUMBER,220)
#define BOOST_VMD_SUBTYPE_REGISTER_221 (BOOST_VMD_TYPE_NUMBER,221)
#define BOOST_VMD_SUBTYPE_REGISTER_222 (BOOST_VMD_TYPE_NUMBER,222)
#define BOOST_VMD_SUBTYPE_REGISTER_223 (BOOST_VMD_TYPE_NUMBER,223)
#define BOOST_VMD_SUBTYPE_REGISTER_224 (BOOST_VMD_TYPE_NUMBER,224)
#define BOOST_VMD_SUBTYPE_REGISTER_225 (BOOST_VMD_TYPE_NUMBER,225)
#define BOOST_VMD_SUBTYPE_REGISTER_226 (BOOST_VMD_TYPE_NUMBER,226)
#define BOOST_VMD_SUBTYPE_REGISTER_227 (BOOST_VMD_TYPE_NUMBER,227)
#define BOOST_VMD_SUBTYPE_REGISTER_228 (BOOST_VMD_TYPE_NUMBER,228)
#define BOOST_VMD_SUBTYPE_REGISTER_229 (BOOST_VMD_TYPE_NUMBER,229)
#define BOOST_VMD_SUBTYPE_REGISTER_230 (BOOST_VMD_TYPE_NUMBER,230)
#define BOOST_VMD_SUBTYPE_REGISTER_231 (BOOST_VMD_TYPE_NUMBER,231)
#define BOOST_VMD_SUBTYPE_REGISTER_232 (BOOST_VMD_TYPE_NUMBER,232)
#define BOOST_VMD_SUBTYPE_REGISTER_233 (BOOST_VMD_TYPE_NUMBER,233)
#define BOOST_VMD_SUBTYPE_REGISTER_234 (BOOST_VMD_TYPE_NUMBER,234)
#define BOOST_VMD_SUBTYPE_REGISTER_235 (BOOST_VMD_TYPE_NUMBER,235)
#define BOOST_VMD_SUBTYPE_REGISTER_236 (BOOST_VMD_TYPE_NUMBER,236)
#define BOOST_VMD_SUBTYPE_REGISTER_237 (BOOST_VMD_TYPE_NUMBER,237)
#define BOOST_VMD_SUBTYPE_REGISTER_238 (BOOST_VMD_TYPE_NUMBER,238)
#define BOOST_VMD_SUBTYPE_REGISTER_239 (BOOST_VMD_TYPE_NUMBER,239)
#define BOOST_VMD_SUBTYPE_REGISTER_240 (BOOST_VMD_TYPE_NUMBER,240)
#define BOOST_VMD_SUBTYPE_REGISTER_241 (BOOST_VMD_TYPE_NUMBER,241)
#define BOOST_VMD_SUBTYPE_REGISTER_242 (BOOST_VMD_TYPE_NUMBER,242)
#define BOOST_VMD_SUBTYPE_REGISTER_243 (BOOST_VMD_TYPE_NUMBER,243)
#define BOOST_VMD_SUBTYPE_REGISTER_244 (BOOST_VMD_TYPE_NUMBER,244)
#define BOOST_VMD_SUBTYPE_REGISTER_245 (BOOST_VMD_TYPE_NUMBER,245)
#define BOOST_VMD_SUBTYPE_REGISTER_246 (BOOST_VMD_TYPE_NUMBER,246)
#define BOOST_VMD_SUBTYPE_REGISTER_247 (BOOST_VMD_TYPE_NUMBER,247)
#define BOOST_VMD_SUBTYPE_REGISTER_248 (BOOST_VMD_TYPE_NUMBER,248)
#define BOOST_VMD_SUBTYPE_REGISTER_249 (BOOST_VMD_TYPE_NUMBER,249)
#define BOOST_VMD_SUBTYPE_REGISTER_250 (BOOST_VMD_TYPE_NUMBER,250)
#define BOOST_VMD_SUBTYPE_REGISTER_251 (BOOST_VMD_TYPE_NUMBER,251)
#define BOOST_VMD_SUBTYPE_REGISTER_252 (BOOST_VMD_TYPE_NUMBER,252)
#define BOOST_VMD_SUBTYPE_REGISTER_253 (BOOST_VMD_TYPE_NUMBER,253)
#define BOOST_VMD_SUBTYPE_REGISTER_254 (BOOST_VMD_TYPE_NUMBER,254)
#define BOOST_VMD_SUBTYPE_REGISTER_255 (BOOST_VMD_TYPE_NUMBER,255)
#define BOOST_VMD_SUBTYPE_REGISTER_256 (BOOST_VMD_TYPE_NUMBER,256)

#endif /* BOOST_VMD_DETAIL_IS_NUMBER_REGISTRATION_HPP */
