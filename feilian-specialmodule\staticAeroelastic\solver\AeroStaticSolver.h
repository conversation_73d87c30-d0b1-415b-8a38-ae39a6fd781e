﻿//! @file 
//! <AUTHOR>
//! @brief
//! @date 

#ifndef _specialModule_staticAeroelastic_solver_AeroStaticSolver_
#define _specialModule_staticAeroelastic_solver_AeroStaticSolver_

#include "sourceFlow/flowSolver/FullMultigird.h"
#include "feilian-specialmodule/staticAeroelastic/meshData/AeroStaticData.h"
#include "feilian-specialmodule/staticAeroelastic/method/AeroStaticMethod.h"
#include "feilian-specialmodule/staticAeroelastic/meshData/MeshData.h"
#include "sourceFlow/flowSolver/OuterLoop.h"
#include "meshProcess/meshConverter/MeshConvertManager.h"

class AeroStaticSolver
{

public:
    /*
     * @param[in] subMesh_ 当地网格，含细网格和所有粗网格
     * @param[in] flowConfig_ 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
     */
    AeroStaticSolver(SubMesh *subMesh_, Configure::Flow::FlowConfigure &flowConfig_);
    //*析构函数
    ~AeroStaticSolver();
    
    //*主流程函数
    void Process();
    //*初始化
    void Initialize();

private:
   
  
    /// 当地网格，含细网格和所有粗网格
    SubMesh *subMesh;

    SubMesh *globalMesh;

    /// 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
    Configure::Flow::FlowConfigure &flowConfigure;

    
    //流场解算器对象
    OuterLoop *outerLoop;

    AeroStaticData *aeroStatic_Data;

    AeroStaticMethod *aeroStatic_Method;






};


#endif