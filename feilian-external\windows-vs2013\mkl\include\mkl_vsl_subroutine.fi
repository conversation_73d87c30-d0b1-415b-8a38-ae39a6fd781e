! file: mkl_vsl_subroutine.fi
!===============================================================================
! Copyright 2006-2018 Intel Corporation.
!
! This software and the related documents are Intel copyrighted  materials,  and
! your use of  them is  governed by the  express license  under which  they were
! provided to you (License).  Unless the License provides otherwise, you may not
! use, modify, copy, publish, distribute,  disclose or transmit this software or
! the related documents without Intel's prior written permission.
!
! This software and the related documents  are provided as  is,  with no express
! or implied  warranties,  other  than those  that are  expressly stated  in the
! License.
!===============================================================================

!++
!  Fortran 90 old VSL interface.
!--

      MODULE MKL_VSL_TYPE


!++
!  Definitions for VSL functions return values (errors, warnings)
!--

!  "No error" status
      INTEGER(KIND=4) VSL_STATUS_OK
      INTEGER(KIND=4) VSL_ERROR_OK
      PARAMETER (VSL_STATUS_OK = 0)
      PARAMETER (VSL_ERROR_OK  = 0)

!  Common errors (-1..-999)
      INTEGER(KIND=4) VSL_ERROR_FEATURE_NOT_IMPLEMENTED
      INTEGER(KIND=4) VSL_ERROR_UNKNOWN
      INTEGER(KIND=4) VSL_ERROR_BADARGS
      INTEGER(KIND=4) VSL_ERROR_MEM_FAILURE
      INTEGER(KIND=4) VSL_ERROR_NULL_PTR
      PARAMETER (VSL_ERROR_FEATURE_NOT_IMPLEMENTED = -1)
      PARAMETER (VSL_ERROR_UNKNOWN                 = -2)
      PARAMETER (VSL_ERROR_BADARGS                 = -3)
      PARAMETER (VSL_ERROR_MEM_FAILURE             = -4)
      PARAMETER (VSL_ERROR_NULL_PTR                = -5)

!  RNG errors (-1000..-1999)
!  brng errors
      INTEGER(KIND=4) VSL_RNG_ERROR_INVALID_BRNG_INDEX
      INTEGER(KIND=4) VSL_RNG_ERROR_LEAPFROG_UNSUPPORTED
      INTEGER(KIND=4) VSL_RNG_ERROR_SKIPAHEAD_UNSUPPORTED
      INTEGER(KIND=4) VSL_RNG_ERROR_BRNGS_INCOMPATIBLE
      INTEGER(KIND=4) VSL_RNG_ERROR_BAD_STREAM
      INTEGER(KIND=4) VSL_RNG_ERROR_BRNG_TABLE_FULL
      INTEGER(KIND=4) VSL_RNG_ERROR_BAD_STREAM_STATE_SIZE
      INTEGER(KIND=4) VSL_RNG_ERROR_BAD_WORD_SIZE
      INTEGER(KIND=4) VSL_RNG_ERROR_BAD_NSEEDS
      INTEGER(KIND=4) VSL_RNG_ERROR_BAD_NBITS
      INTEGER(KIND=4) VSL_RNG_QRNG_PERIOD_ELAPSED
      INTEGER(KIND=4) VSL_RNG_ERROR_LEAPFROG_NSTREAMS_TOO_BIG
      PARAMETER (VSL_RNG_ERROR_INVALID_BRNG_INDEX        = -1000)
      PARAMETER (VSL_RNG_ERROR_LEAPFROG_UNSUPPORTED      = -1002)
      PARAMETER (VSL_RNG_ERROR_SKIPAHEAD_UNSUPPORTED     = -1003)
      PARAMETER (VSL_RNG_ERROR_BRNGS_INCOMPATIBLE        = -1005)
      PARAMETER (VSL_RNG_ERROR_BAD_STREAM                = -1006)
      PARAMETER (VSL_RNG_ERROR_BRNG_TABLE_FULL           = -1007)
      PARAMETER (VSL_RNG_ERROR_BAD_STREAM_STATE_SIZE     = -1008)
      PARAMETER (VSL_RNG_ERROR_BAD_WORD_SIZE             = -1009)
      PARAMETER (VSL_RNG_ERROR_BAD_NSEEDS                = -1010)
      PARAMETER (VSL_RNG_ERROR_BAD_NBITS                 = -1011)
      PARAMETER (VSL_RNG_QRNG_PERIOD_ELAPSED             = -1012)
      PARAMETER (VSL_RNG_ERROR_LEAPFROG_NSTREAMS_TOO_BIG = -1013)

!++
!  BASIC RANDOM NUMBER GENERATOR (BRNG) RELATED MACRO DEFINITIONS
!--


!  MAX NUMBER OF BRNGS CAN BE REGISTERED IN VSL
!  No more than VSL_MAX_REG_BRNGS basic generators can be registered in VSL
!  (including predefined basic generators).
!
!  Change this number to increase/decrease number of BRNGs can be registered.
      INTEGER(KIND=4) VSL_MAX_REG_BRNGS
      PARAMETER (VSL_MAX_REG_BRNGS = 512)

!  PREDEFINED BRNG NAMES
      INTEGER(KIND=4) VSL_BRNG_SHIFT
      INTEGER(KIND=4) VSL_BRNG_INC

      INTEGER(KIND=4) VSL_BRNG_MCG31
      INTEGER(KIND=4) VSL_BRNG_R250
      INTEGER(KIND=4) VSL_BRNG_MRG32K3A
      INTEGER(KIND=4) VSL_BRNG_MCG59
      INTEGER(KIND=4) VSL_BRNG_WH
      INTEGER(KIND=4) VSL_BRNG_SOBOL
      INTEGER(KIND=4) VSL_BRNG_NIEDERR
      INTEGER(KIND=4) VSL_BRNG_MT19937
      INTEGER(KIND=4) VSL_BRNG_MT2203
      INTEGER(KIND=4) VSL_BRNG_IABSTRACT
      INTEGER(KIND=4) VSL_BRNG_DABSTRACT
      INTEGER(KIND=4) VSL_BRNG_SABSTRACT

      PARAMETER (VSL_BRNG_SHIFT=20)
      PARAMETER (VSL_BRNG_INC=ISHFT(1, VSL_BRNG_SHIFT))

      PARAMETER (VSL_BRNG_MCG31    =VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_R250     =VSL_BRNG_MCG31    +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_MRG32K3A =VSL_BRNG_R250     +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_MCG59    =VSL_BRNG_MRG32K3A +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_WH       =VSL_BRNG_MCG59    +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_SOBOL    =VSL_BRNG_WH       +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_NIEDERR  =VSL_BRNG_SOBOL    +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_MT19937  =VSL_BRNG_NIEDERR  +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_MT2203   =VSL_BRNG_MT19937  +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_IABSTRACT=VSL_BRNG_MT2203   +VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_DABSTRACT=VSL_BRNG_IABSTRACT+VSL_BRNG_INC)
      PARAMETER (VSL_BRNG_SABSTRACT=VSL_BRNG_DABSTRACT+VSL_BRNG_INC)

!  LEAPFROG METHOD FOR GRAY-CODE BASED QUASI-RANDOM NUMBER BASIC GENERATORS
!  VSL_BRNG_SOBOL and VSL_BRNG_NIEDERR are Gray-code based quasi-random number
!  basic generators. In contrast to pseudorandom number basic generators,
!  quasi-random ones take the dimension as initialization parameter.
!
!  Suppose that quasi-random number generator (QRNG) dimension is S. QRNG
!  sequence is a sequence of S-dimensional vectors:
!
!     x0=(x0[0],x0[1],...,x0[S-1]),x1=(x1[0],x1[1],...,x1[S-1]),...
!
!  VSL treats the output of any basic generator as 1-dimensional, however:
!
!     x0[0],x0[1],...,x0[S-1],x1[0],x1[1],...,x1[S-1],...
!
!  Because of nature of VSL_BRNG_SOBOL and VSL_BRNG_NIEDERR QRNGs,
!  the only S-stride Leapfrog method is supported for them. In other words,
!  user can generate subsequences, which consist of fixed elements of
!  vectors x0,x1,... For example, if 0 element is fixed, the following
!  subsequence is generated:
!
!     x0[1],x1[1],x2[1],...
!
!  To use the s-stride Leapfrog method with given QRNG, user should call
!  vslLeapfrogStream function with parameter k equal to element to be fixed
!  (0<=k<S) and parameter nstreams equal to VSL_QRNG_LEAPFROG_COMPONENTS.
      INTEGER(KIND=4) VSL_QRNG_LEAPFROG_COMPONENTS
      PARAMETER (VSL_QRNG_LEAPFROG_COMPONENTS = Z"7FFFFFFF")


!  INITIALIZATION METHODS FOR USER-DESIGNED BASIC RANDOM NUMBER GENERATORS.
!  Each BRNG must support at least VSL_INIT_METHOD_STANDARD initialization
!  method. In addition, VSL_INIT_METHOD_LEAPFROG and VSL_INIT_METHOD_SKIPAHEAD
!  initialization methods can be supported.
!
!  If VSL_INIT_METHOD_LEAPFROG is not supported then initialization routine
!  must return VSL_RNG_ERROR_LEAPFROG_UNSUPPORTED error code.
!
!  If VSL_INIT_METHOD_SKIPAHEAD is not supported then initialization routine
!  must return VSL_RNG_ERROR_SKIPAHEAD_UNSUPPORTED error code.
!
!  If there is no error during initialization, the initialization routine must
!  return VSL_ERROR_OK code.
      INTEGER(KIND=4) VSL_INIT_METHOD_STANDARD
      INTEGER(KIND=4) VSL_INIT_METHOD_LEAPFROG
      INTEGER(KIND=4) VSL_INIT_METHOD_SKIPAHEAD
      PARAMETER (VSL_INIT_METHOD_STANDARD  = 0)
      PARAMETER (VSL_INIT_METHOD_LEAPFROG  = 1)
      PARAMETER (VSL_INIT_METHOD_SKIPAHEAD = 2)

!++
!  TRANSFORMATION METHOD NAMES FOR DISTRIBUTION RANDOM NUMBER GENERATORS
!  VSL interface allows more than one generation method in a distribution
!  transformation subroutine. Following macro definitions are used to
!  specify generation method for given distribution generator.
!
!  Method name macro is constructed as
!
!     VSL_METHOD_<Precision><Distribution>_<Method>
!
!  where
!
!     <Precision> - S (single precision) or D (double precision)
!     <Distribution> - probability distribution
!     <Method> - method name
!
!  VSL_METHOD_<Precision><Distribution>_<Method> should be used with
!  vsl<precision>Rng<Distribution> function only, where
!
!     <precision> - s (single) or d (double)
!     <Distribution> - probability distribution
!--

! Uniform
!
! <Method>   <Short Description>
! STD        standard method. Currently there is only one method for this
!            distribution generator
      INTEGER VSL_RNG_METHOD_SUNIFORM_STD
      INTEGER VSL_RNG_METHOD_DUNIFORM_STD
      INTEGER VSL_RNG_METHOD_IUNIFORM_STD
      PARAMETER (VSL_RNG_METHOD_SUNIFORM_STD = 0)
      PARAMETER (VSL_RNG_METHOD_DUNIFORM_STD = 0)
      PARAMETER (VSL_RNG_METHOD_IUNIFORM_STD = 0)

! Uniform Bits
!
! <Method>   <Short Description>
! STD        standard method. Currently there is only one method for this
!            distribution generator
      INTEGER VSL_RNG_METHOD_IUNIFORMBITS_STD
      PARAMETER (VSL_RNG_METHOD_IUNIFORMBITS_STD = 0)

! Gaussian
!
! <Method>   <Short Description>
! BOXMULLER  generates normally distributed random number x thru the pair of
!            uniformly distributed numbers u1 and u2 according to the formula:
!
!               x=sqrt(-ln(u1))*sin(2*Pi*u2)
!
! BOXMULLER2 generates pair of normally distributed random numbers x1 and x2
!            thru the pair of uniformly dustributed numbers u1 and u2
!            according to the formula
!
!               x1=sqrt(-ln(u1))*sin(2*Pi*u2)
!               x2=sqrt(-ln(u1))*cos(2*Pi*u2)
!
!            NOTE: implementation correctly works with odd vector lengths
!
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SGAUSSIAN_BOXMULLER
      INTEGER VSL_RNG_METHOD_SGAUSSIAN_BOXMULLER2
      INTEGER VSL_RNG_METHOD_SGAUSSIAN_ICDF
      INTEGER VSL_RNG_METHOD_DGAUSSIAN_BOXMULLER
      INTEGER VSL_RNG_METHOD_DGAUSSIAN_BOXMULLER2
      INTEGER VSL_RNG_METHOD_DGAUSSIAN_ICDF
      PARAMETER (VSL_RNG_METHOD_SGAUSSIAN_BOXMULLER  = 0)
      PARAMETER (VSL_RNG_METHOD_SGAUSSIAN_BOXMULLER2 = 1)
      PARAMETER (VSL_RNG_METHOD_SGAUSSIAN_ICDF       = 2)
      PARAMETER (VSL_RNG_METHOD_DGAUSSIAN_BOXMULLER  = 0)
      PARAMETER (VSL_RNG_METHOD_DGAUSSIAN_BOXMULLER2 = 1)
      PARAMETER (VSL_RNG_METHOD_DGAUSSIAN_ICDF       = 2)

! GaussianMV - multivariate (correlated) normal
! Multivariate (correlated) normal random number generator is based on
! uncorrelated Gaussian random number generator (see vslsRngGaussian and
! vsldRngGaussian functions):
!
! <Method>   <Short Description>
! BOXMULLER  generates normally distributed random number x thru the pair of
!            uniformly distributed numbers u1 and u2 according to the formula:
!
!               x=sqrt(-ln(u1))*sin(2*Pi*u2)
!
! BOXMULLER2 generates pair of normally distributed random numbers x1 and x2
!            thru the pair of uniformly dustributed numbers u1 and u2
!            according to the formula
!
!               x1=sqrt(-ln(u1))*sin(2*Pi*u2)
!               x2=sqrt(-ln(u1))*cos(2*Pi*u2)
!
!            NOTE: implementation correctly works with odd vector lengths
!
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SGAUSSIANMV_BOXMULLER
      INTEGER VSL_RNG_METHOD_SGAUSSIANMV_BOXMULLER2
      INTEGER VSL_RNG_METHOD_SGAUSSIANMV_ICDF
      INTEGER VSL_RNG_METHOD_DGAUSSIANMV_BOXMULLER
      INTEGER VSL_RNG_METHOD_DGAUSSIANMV_BOXMULLER2
      INTEGER VSL_RNG_METHOD_DGAUSSIANMV_ICDF
      PARAMETER (VSL_RNG_METHOD_SGAUSSIANMV_BOXMULLER  = 0)
      PARAMETER (VSL_RNG_METHOD_SGAUSSIANMV_BOXMULLER2 = 1)
      PARAMETER (VSL_RNG_METHOD_SGAUSSIANMV_ICDF       = 2)
      PARAMETER (VSL_RNG_METHOD_DGAUSSIANMV_BOXMULLER  = 0)
      PARAMETER (VSL_RNG_METHOD_DGAUSSIANMV_BOXMULLER2 = 1)
      PARAMETER (VSL_RNG_METHOD_DGAUSSIANMV_ICDF       = 2)

! Exponential
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SEXPONENTIAL_ICDF
      INTEGER VSL_RNG_METHOD_DEXPONENTIAL_ICDF
      PARAMETER (VSL_RNG_METHOD_SEXPONENTIAL_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DEXPONENTIAL_ICDF = 0)

! Laplace
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
!
! ICDF - inverse cumulative distribution function method:
!
!           x=+/-ln(u) with probability 1/2,
!
!        where
!
!           x - random number with Laplace distribution,
!           u - uniformly distributed random number
      INTEGER VSL_RNG_METHOD_SLAPLACE_ICDF
      INTEGER VSL_RNG_METHOD_DLAPLACE_ICDF
      PARAMETER (VSL_RNG_METHOD_SLAPLACE_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DLAPLACE_ICDF = 0)

! Weibull
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SWEIBULL_ICDF
      INTEGER VSL_RNG_METHOD_DWEIBULL_ICDF
      PARAMETER (VSL_RNG_METHOD_SWEIBULL_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DWEIBULL_ICDF = 0)

! Cauchy
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SCAUCHY_ICDF
      INTEGER VSL_RNG_METHOD_DCAUCHY_ICDF
      PARAMETER (VSL_RNG_METHOD_SCAUCHY_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DCAUCHY_ICDF = 0)

! Rayleigh
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SRAYLEIGH_ICDF
      INTEGER VSL_RNG_METHOD_DRAYLEIGH_ICDF
      PARAMETER (VSL_RNG_METHOD_SRAYLEIGH_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DRAYLEIGH_ICDF = 0)

! Lognormal
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SLOGNORMAL_ICDF
      INTEGER VSL_RNG_METHOD_DLOGNORMAL_ICDF
      PARAMETER (VSL_RNG_METHOD_SLOGNORMAL_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DLOGNORMAL_ICDF = 0)

! Gumbel
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_SGUMBEL_ICDF
      INTEGER VSL_RNG_METHOD_DGUMBEL_ICDF
      PARAMETER (VSL_RNG_METHOD_SGUMBEL_ICDF = 0)
      PARAMETER (VSL_RNG_METHOD_DGUMBEL_ICDF = 0)

! Bernoulli
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_IBERNOULLI_ICDF
      PARAMETER (VSL_RNG_METHOD_IBERNOULLI_ICDF = 0)

! Geometric
!
! <Method>   <Short Description>
! ICDF       inverse cumulative distribution function method
      INTEGER VSL_RNG_METHOD_IGEOMETRIC_ICDF
      PARAMETER (VSL_RNG_METHOD_IGEOMETRIC_ICDF = 0)

! Binomial
!
! <Method>   <Short Description>
! BTPE       for ntrial*min(p,1-p)>30 acceptance/rejection method with
!            decomposition onto 4 regions:
!
!               * 2 parallelograms;
!               * triangle;
!               * left exponential tail;
!               * right exponential tail.
!
!            othewise table lookup method is used
      INTEGER VSL_RNG_METHOD_IBINOMIAL_BTPE
      PARAMETER (VSL_RNG_METHOD_IBINOMIAL_BTPE = 0)

! Hypergeometric
!
! <Method>   <Short Description>
! H2PE       if mode of distribution is large, acceptance/rejection method is
!            used with decomposition onto 3 regions:
!
!               * rectangular;
!               * left exponential tail;
!               * right exponential tail.
!
!            othewise table lookup method is used
      INTEGER VSL_RNG_METHOD_IHYPERGEOMETRIC_H2PE
      PARAMETER (VSL_RNG_METHOD_IHYPERGEOMETRIC_H2PE = 0)

! Poisson
!
! <Method>   <Short Description>
! PTPE       if lambda>=27, acceptance/rejection method is used with
!            decomposition onto 4 regions:
!
!               * 2 parallelograms;
!               * triangle;
!               * left exponential tail;
!               * right exponential tail.
!
!            othewise table lookup method is used
!
! POISNORM   for lambda>=1 method is based on Poisson inverse CDF
!            approximation by Gaussian inverse CDF; for lambda<1
!            table lookup method is used.
      INTEGER VSL_RNG_METHOD_IPOISSON_PTPE
      INTEGER VSL_RNG_METHOD_IPOISSON_POISNORM
      PARAMETER (VSL_RNG_METHOD_IPOISSON_PTPE     = 0)
      PARAMETER (VSL_RNG_METHOD_IPOISSON_POISNORM = 1)

! Poisson
!
! <Method>   <Short Description>
! POISNORM   for lambda>=1 method is based on Poisson inverse CDF
!            approximation by Gaussian inverse CDF; for lambda<1
!            ICDF method is used.
      INTEGER VSL_RNG_METHOD_IPOISSONV_POISNORM
      PARAMETER (VSL_RNG_METHOD_IPOISSONV_POISNORM = 0)

! Negbinomial
!
! <Method>   <Short Description>
! NBAR       if (a-1)*(1-p)/p>=100, acceptance/rejection method is used with
!            decomposition onto 5 regions:
!
!               * rectangular;
!               * 2 trapezoid;
!               * left exponential tail;
!               * right exponential tail.
!
!            othewise table lookup method is used.
      INTEGER VSL_RNG_METHOD_INEGBINOMIAL_NBAR
      PARAMETER (VSL_RNG_METHOD_INEGBINOMIAL_NBAR = 0)

!++
!  MATRIX STORAGE SCHEMES
!--

! Some multivariate random number generators, e.g. GaussianMV, operate
! with matrix parameters. To optimize matrix parameters usage VSL offers
! following matrix storage schemes. (See VSL documentation for more details).
!
! FULL     - whole matrix is stored
! PACKED   - lower/higher triangular matrix is packed in 1-dimensional array
! DIAGONAL - diagonal elements are packed in 1-dimensional array
      INTEGER(KIND=4) VSL_MATRIX_STORAGE_FULL
      INTEGER(KIND=4) VSL_MATRIX_STORAGE_PACKED
      INTEGER(KIND=4) VSL_MATRIX_STORAGE_DIAGONAL
      PARAMETER (VSL_MATRIX_STORAGE_FULL     = 0)
      PARAMETER (VSL_MATRIX_STORAGE_PACKED   = 1)
      PARAMETER (VSL_MATRIX_STORAGE_DIAGONAL = 2)

!++
!  TYPEDEFS
!--

!  VSL STREAM STATE POINTER
!  This structure is to store VSL stream state address allocated by
!  VSLNEWSTREAM subroutine.
      TYPE VSL_STREAM_STATE
          INTEGER(KIND=4) descriptor1
          INTEGER(KIND=4) descriptor2
      END TYPE VSL_STREAM_STATE

!  BASIC RANDOM NUMBER GENERATOR PROPERTIES STRUCTURE
!  The structure describes the properties of given basic generator, e.g. size
!  of the stream state structure, pointers to function implementations, etc.
!
!  BRNG properties structure fields:
!  StreamStateSize - size of the stream state structure (in bytes)
!  WordSize        - size of base word (in bytes). Typically this is 4 bytes.
!  NSeeds          - number of words necessary to describe generator's state
!  NBits           - number of bits actually used in base word. For example,
!                    only 31 least significant bits are actually used in
!                    basic random number generator MCG31m1 with 4-byte base
!                    word. NBits field is useful while interpreting random
!                    words as a sequence of random bits.
!  IncludesZero    - FALSE if 0 cannot be generated in integer-valued
!                    implementation; TRUE if 0 can be potentially generated in
!                    integer-valued implementation.
!  InitStream      - pointer to stream state initialization function
!  sBRng           - pointer to single precision implementation
!  dBRng           - pointer to double precision implementation
!  iBRng           - pointer to integer-value implementation
      TYPE VSL_BRNG_PROPERTIES
          INTEGER(KIND=4) streamstatesize
          INTEGER(KIND=4) nseeds
          INTEGER(KIND=4) includeszero
          INTEGER(KIND=4) wordsize
          INTEGER(KIND=4) nbits
          INTEGER(KIND=4) initstream
          INTEGER(KIND=4) sbrng
          INTEGER(KIND=4) dbrng
          INTEGER(KIND=4) ibrng
      END TYPE VSL_BRNG_PROPERTIES

      END MODULE MKL_VSL_TYPE

      MODULE MKL_VSL

      USE MKL_VSL_TYPE

!++
!  VSL CONTINUOUS DISTRIBUTION GENERATOR FUNCTION INTERFACES.
!--

!  Uniform distribution
      INTERFACE
        SUBROUTINE vsrnguniform( method, stream, n, r, a, b )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: b
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrnguniform( method, stream, n, r, a, b )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: b
        END SUBROUTINE
      END INTERFACE

!  Gaussian distribution
      INTERFACE
        SUBROUTINE vsrnggaussian( method, stream, n, r, a, sigma )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: sigma
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrnggaussian( method, stream, n, r, a, sigma )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: sigma
        END SUBROUTINE
      END INTERFACE

!  GaussianMV distribution
      INTERFACE
        SUBROUTINE vsrnggaussianmv( method, stream, n, r, dimen,        &
     &                              mstorage, a, t )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)         :: method
          TYPE(VSL_STREAM_STATE)     :: stream
          INTEGER,INTENT(IN)         :: n
          INTEGER(KIND=4),INTENT(IN) :: dimen
          REAL(KIND=4),INTENT(OUT)   :: r(dimen,n)
          INTEGER(KIND=4),INTENT(IN) :: mstorage
          REAL(KIND=4),INTENT(IN)    :: a(dimen)
          REAL(KIND=4),INTENT(IN)    :: t(dimen,dimen)
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrnggaussianmv( method, stream, n, r, dimen,        &
     &                              mstorage, a, t )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)         :: method
          TYPE(VSL_STREAM_STATE)     :: stream
          INTEGER,INTENT(IN)         :: n
          REAL(KIND=8),INTENT(OUT)   :: r(n)
          INTEGER(KIND=4),INTENT(IN) :: dimen
          INTEGER(KIND=4),INTENT(IN) :: mstorage
          REAL(KIND=8),INTENT(IN)    :: a(dimen)
          REAL(KIND=8),INTENT(IN)    :: t(dimen,dimen)
        END SUBROUTINE
      END INTERFACE

!  Exponential distribution
      INTERFACE
        SUBROUTINE vsrngexponential( method, stream, n, r, a,           &
     &                                     beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrngexponential( method, stream, n, r, a,           &
     &                                     beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!  Laplace distribution
      INTERFACE
        SUBROUTINE vsrnglaplace( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrnglaplace( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!  Weibull distribution
      INTERFACE
        SUBROUTINE vsrngweibull( method, stream, n, r, alpha, a,        &
     &                                 beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: alpha
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrngweibull( method, stream, n, r, alpha, a,        &
     &                                 beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: alpha
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!  Cauchy distribution
      INTERFACE
        SUBROUTINE vsrngcauchy( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrngcauchy( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!  Rayleigh distribution
      INTERFACE
        SUBROUTINE vsrngrayleigh( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrngrayleigh( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!  Lognormal distribution
      INTERFACE
        SUBROUTINE vsrnglognormal( method, stream, n, r, a, sigma,      &
     &                             b, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: sigma
          REAL(KIND=4),INTENT(IN)  :: b
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrnglognormal( method, stream, n, r, a, sigma,      &
     &                             b, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: sigma
          REAL(KIND=8),INTENT(IN)  :: b
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!  Gumbel distribution
      INTERFACE
        SUBROUTINE vsrnggumbel( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=4),INTENT(IN)  :: a
          REAL(KIND=4),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

      INTERFACE
        SUBROUTINE vdrnggumbel( method, stream, n, r, a, beta )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)       :: method
          TYPE(VSL_STREAM_STATE)   :: stream
          INTEGER,INTENT(IN)       :: n
          REAL(KIND=8),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)  :: a
          REAL(KIND=8),INTENT(IN)  :: beta
        END SUBROUTINE
      END INTERFACE

!++
!  VSL DISCRETE DISTRIBUTION GENERATOR FUNCTION INTERFACES.
!--

!  Uniform distribution
      INTERFACE
        SUBROUTINE virnguniform( method, stream, n, r, a, b )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          INTEGER(KIND=4),INTENT(IN)  :: a
          INTEGER(KIND=4),INTENT(IN)  :: b
        END SUBROUTINE
      END INTERFACE

!  UniformBits distribution
      INTERFACE
        SUBROUTINE virnguniformbits( method, stream, n, r )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
        END SUBROUTINE
      END INTERFACE

!  Bernoulli distribution
      INTERFACE
        SUBROUTINE virngbernoulli( method, stream, n, r, p )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)     :: p
        END SUBROUTINE
      END INTERFACE

!  Geometric distribution
      INTERFACE
        SUBROUTINE virnggeometric( method, stream, n, r, p )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)     :: p
        END SUBROUTINE
      END INTERFACE

!  Binomial distribution
      INTERFACE
        SUBROUTINE virngbinomial(method, stream, n, r, ntrial, p)
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          INTEGER(KIND=4),INTENT(IN)  :: ntrial
          REAL(KIND=8),INTENT(IN)     :: p
        END SUBROUTINE
      END INTERFACE

!  Hypergeometric distribution
      INTERFACE
        SUBROUTINE virnghypergeometric( method, stream, n, r, l,        &
     &                                        s, m )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          INTEGER(KIND=4),INTENT(IN)  :: l
          INTEGER(KIND=4),INTENT(IN)  :: s
          INTEGER(KIND=4),INTENT(IN)  :: m
        END SUBROUTINE
      END INTERFACE

!  Poisson distribution
      INTERFACE
        SUBROUTINE virngpoisson( method, stream, n, r, lambda )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)     :: lambda
        END SUBROUTINE
      END INTERFACE

!  PoissonV distribution
      INTERFACE
        SUBROUTINE virngpoissonv( method, stream, n, r, lambda )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)     :: lambda(n)
        END SUBROUTINE
      END INTERFACE

!  Negbinomial distribution
      INTERFACE
        SUBROUTINE virngnegbinomial( method, stream, n, r, a, p )
            USE MKL_VSL_TYPE
          INTEGER,INTENT(IN)          :: method
          TYPE(VSL_STREAM_STATE)      :: stream
          INTEGER,INTENT(IN)          :: n
          INTEGER(KIND=4),INTENT(OUT) :: r(n)
          REAL(KIND=8),INTENT(IN)     :: a
          REAL(KIND=8),INTENT(IN)     :: p
        END SUBROUTINE
      END INTERFACE

!++
!  VSL SERVICE FUNCTION INTERFACES.
!--

! NewStream - stream creation/initialization
      INTERFACE
        SUBROUTINE vslnewstream( stream, brng, seed )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE)     :: stream
          INTEGER(KIND=4),INTENT(IN) :: brng
          INTEGER(KIND=4),INTENT(IN) :: seed
        END SUBROUTINE
      END INTERFACE

! NewStreamEx - advanced stream creation/initialization
      INTERFACE
        SUBROUTINE vslnewstreamex( stream, brng, n, params )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE)     :: stream
          INTEGER(KIND=4),INTENT(IN) :: brng
          INTEGER(KIND=4),INTENT(IN) :: n
          INTEGER(KIND=4),INTENT(IN) :: params(n)
        END SUBROUTINE
      END INTERFACE

! DeleteStream - delete stream
      INTERFACE
        SUBROUTINE vsldeletestream( stream )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE) :: stream
        END SUBROUTINE
      END INTERFACE

! CopyStream - copy all stream information
      INTERFACE
        SUBROUTINE vslcopystream( newstream, srcstream )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE) :: newstream
          TYPE(VSL_STREAM_STATE) :: srcstream
        END SUBROUTINE
      END INTERFACE

! CopyStreamState - copy stream state only
      INTERFACE
        SUBROUTINE vslcopystreamstate( deststream, srcstream )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE) :: deststream
          TYPE(VSL_STREAM_STATE) :: srcstream
        END SUBROUTINE
      END INTERFACE

! LeapfrogStream - leapfrog method
      INTERFACE
        SUBROUTINE vslleapfrogstream( stream, k, nstreams )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE)     :: stream
          INTEGER(KIND=4),INTENT(IN) :: k
          INTEGER(KIND=4),INTENT(IN) :: nstreams
        END SUBROUTINE
      END INTERFACE

! SkipAheadStream - skip-ahead method
      INTERFACE
        SUBROUTINE vslskipaheadstream( stream, nskip )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE)     :: stream
          INTEGER(KIND=8),INTENT(IN) :: nskip
        END SUBROUTINE
      END INTERFACE

! GetBrngProperties - get BRNG properties
      INTERFACE
        SUBROUTINE vslgetbrngproperties( brng, properties )
            USE MKL_VSL_TYPE
          INTEGER(KIND=4),INTENT(IN)            :: brng
          TYPE(VSL_BRNG_PROPERTIES),INTENT(OUT) :: properties
        END SUBROUTINE
      END INTERFACE

! GetNumRegBrngs - get number of registered BRNGs
      INTERFACE
        INTEGER FUNCTION vslgetnumregbrngs( )
        END FUNCTION
      END INTERFACE

! GetStreamStateBrng - get BRNG associated with given stream
      INTERFACE
        INTEGER FUNCTION vslgetstreamstatebrng( stream )
            USE MKL_VSL_TYPE
          TYPE(VSL_STREAM_STATE) :: stream
        END FUNCTION
      END INTERFACE

! RegisterBrng - register new BRNG
      INTERFACE
        INTEGER FUNCTION vslregisterbrng( properties )
            USE MKL_VSL_TYPE
          TYPE(VSL_BRNG_PROPERTIES) :: properties
        END FUNCTION
      END INTERFACE

      END MODULE MKL_VSL
