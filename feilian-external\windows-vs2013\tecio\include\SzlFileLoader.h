 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <algorithm>
#include <cfloat>
 #define _USE_MATH_DEFINES
#include <cmath> 
 #if defined MSWIN
 #define _CRTDBG_MAP_ALLOC
#include <crtdbg.h>
 #pragma warning (disable:4996) 
 #endif
#include <cstdarg>
#include <cstdlib>
#include <cstring>
#include <set>
#include <string>
#include <utility>
#include <vector>
#include "ThirdPartyHeadersEnd.h"
#include "MASTER.h"
#include "GLOBAL.h"
#include "ClassMacros.h"
#include "MinMax.h"
#include "basicTypes.h"
#include "IJK.h"
#include "ItemAddress.h"
#include "LightweightVector.h"
#include "AnyTypeLightweightVector.h"
#include "RawArray.h"
#include "showMessage.h"
#include "xyz.h"
namespace tecplot { namespace ___3933 { class CszConnectivity;
 #define MAX_ERROR_MESSAGE_SIZE 500
 #define countOf(staticArray) ( sizeof((staticArray))/sizeof((staticArray)[0]) )
 #define numItemsOf(someArray) ( sizeof((someArray))/sizeof(*(someArray)) )
static ___2090::ItemOffset_t const DEFAULT_SUBZONE_MAX_I_DIM = 6; static ___2090::ItemOffset_t const DEFAULT_SUBZONE_MAX_J_DIM = 6; static ___2090::ItemOffset_t const DEFAULT_SUBZONE_MAX_K_DIM = 6;
 #define DEFAULT_SUBZONE_MAX_IJK_DIM ___1844(DEFAULT_SUBZONE_MAX_I_DIM, DEFAULT_SUBZONE_MAX_J_DIM, DEFAULT_SUBZONE_MAX_K_DIM)
static ___2090::ItemOffset_t const DEFAULT_SUBZONE_MAX_FE_SIZE = 256; static bool const DEFAULT_MINIMIZE_IJK_NUM_SUBZONES = false; static bool const DEFAULT_MINIMIZE_SYNTHETIC_FE_NUM_SUBZONES = false; static ___4636 const NOT_SHARED = static_cast<___4636>(-1); typedef ___2240<___2090>                 ItemAddressArray; typedef ___2240<___2090::SubzoneAddress> SubzoneAddressArray; template<typename T> inline bool reserveVectorSpace(std::vector<T>& vectorToReserve, size_t newSize) { REQUIRE(newSize>0); bool ___2039 = false; try { vectorToReserve.reserve(newSize); ___2039 = true; } catch (...) { ___2039 = ___1305; } ENSURE(IMPLICATION(___2039, vectorToReserve.capacity()>=newSize)); return ___2039; } template<typename T> inline bool resizeVector(std::vector<T>& vectorToResize, size_t newSize) { REQUIRE(newSize>0); bool ___2039 = false; try { vectorToResize.resize(newSize); ___2039 = true; } catch (...) { ___2039 = ___1305; } ENSURE(IMPLICATION(___2039, vectorToResize.size()==newSize)); return ___2039; } template<typename T> inline bool resizeVector(std::vector<T>& vectorToSize, size_t newSize, T padValue) { REQUIRE(newSize>0); bool ___2039 = false; try { vectorToSize.resize(newSize, padValue); ___2039 = true; } catch (...) { ___2039 = ___1305; } ENSURE(IMPLICATION(___2039, vectorToSize.size()==newSize)); return ___2039; } template<typename T> inline bool resize2DVector(std::vector<std::vector<T> >& vector2D, size_t newDim1, size_t newDim2) { REQUIRE(newDim1>0); REQUIRE(newDim2>0); bool ___2039 = false; try { vector2D.resize(newDim1); for ( size_t ___1841 = 0; ___1841 < newDim1; ___1841++ ) vector2D[___1841].resize(newDim2); ___2039 = true; } catch (...) { ___2039 = ___1305; } ENSURE(IMPLICATION(___2039, vector2D.size()==newDim1)); ENSURE(IMPLICATION(___2039, vector2D[0].size()==newDim2)); ENSURE(IMPLICATION(___2039, vector2D[newDim1/2].size()==newDim2)); ENSURE(IMPLICATION(___2039, vector2D[newDim1-1].size()==newDim2)); return ___2039; } template<typename T> inline bool resize2DVector(std::vector<std::vector<T> >& vector2D, size_t newDim1, size_t newDim2, T padValue) { REQUIRE(newDim1>0); REQUIRE(newDim2>0); bool ___2039 = false; try { vector2D.resize(newDim1); for ( size_t ___1841 = 0; ___1841 < newDim1; ___1841++ ) vector2D[___1841].resize(newDim2, padValue); ___2039 = true; } catch (...) { ___2039 = ___1305; } ENSURE(IMPLICATION(___2039, vector2D.size()==newDim1)); ENSURE(IMPLICATION(___2039, vector2D[0].size()==newDim2)); ENSURE(IMPLICATION(___2039, vector2D[newDim1/2].size()==newDim2)); ENSURE(IMPLICATION(___2039, vector2D[newDim1-1].size()==newDim2)); return ___2039; } template<typename T> inline void clearAndDeallocStdSet(std::set<T>& setToClear) { std::set<T>().swap(setToClear); } template <class ForwardIterator> bool isSorted(ForwardIterator first, ForwardIterator last) { if ( first!=last ) { ForwardIterator next = first; while (++next!=last) { if (*next<*first) return false; ++first; } } return true; } typedef ___2240<char>     ___473; typedef ___2240<uint8_t>  UInt8Array; typedef ___2240<uint16_t> UInt16Array; typedef ___2240<uint32_t> UInt32Array; typedef ___2240<uint64_t> UInt64Array; typedef ___2240<int32_t>  ___1965; typedef ___2240<double>   ___1105; typedef ___2240<float>    ___1435; typedef ___2240<___465>                  ___464; typedef ___2240<___2718>                  ___2717; typedef ___2240<___4636>                  ___4635; typedef ___2240<___2090::SubzoneOffset_t> SubzoneOffsetArray; typedef ___2240<___2090::ItemOffset_t>    ItemOffsetArray; typedef ___2240<___682>                CornerIndexArray; typedef ___2240<___372>                    BooleanArray; typedef ___2240<std::string>                  ___3816; typedef ___2240<___2090::___2980>                       PartitionArray; typedef std::pair<___2090::___2980, ___2090::SubzoneOffset_t> PartitionSubzone; typedef ___2240<PartitionSubzone>                               PartitionSubzoneArray; typedef std::set<PartitionSubzone>                                        PartitionSubzoneSet; typedef ___2240<PartitionSubzoneSet>                            PartitionSubzoneSetArray; typedef std::set<___2090::SubzoneOffset_t> SubzoneOffsetSet; typedef ___2240<SubzoneOffsetSet>    SubzoneOffsetSetArray; typedef ___2240<UInt8Array>       VarZoneUInt8Array; typedef ___2240<___1393>        ___1392; typedef ___2240<___1392>     FileLoc2DArray; typedef ___2240<FileLoc2DArray>   FileLoc3DArray; typedef ___2240<FileLoc3DArray>   FileLoc4DArray;
 #if 0
typedef ___1392                             SzFileLocArray; typedef ___2240<SzFileLocArray>        PtnSzFileLocArray; typedef ___2240<PtnSzFileLocArray>     ZonePtnSzFileLocArray; typedef ___2240<ZonePtnSzFileLocArray> VarZonePtnSzFileLocArray; typedef ___1392                             PtnFileLocArray; typedef ___2240<PtnFileLocArray>       ZnPtnFileLocArray; typedef ___1392                             VarFileLocArray; typedef ___2240<VarFileLocArray>       PtnVarFileLocArray; typedef ___2240<PtnVarFileLocArray>    ZonePtnVarFileLocArray; typedef ___1392                             ZoneFileLocArray; typedef ___2240<ZoneFileLocArray>      VarZoneFileLocArray;
 #endif
typedef AnyTypeLightweightVector              FieldDataArray; typedef ___2240<FieldDataArray>     SzFieldData; typedef ___2240<SzFieldData>        PtnSzFieldData; typedef ___2240<PtnSzFieldData>     ZonePtnSzFieldData; typedef ___2240<ZonePtnSzFieldData> VarZonePtnSzFieldData; typedef uint16_t InterpCount_t; static InterpCount_t const MAX_INTERP_COUNT = InterpCount_t(-1); typedef ___2240<InterpCount_t>             InterpCountArray; typedef ___2240<InterpCountArray>          SzInterpCountArray; typedef ___2240<SzInterpCountArray>        PtnSzInterpCountArray; typedef ___2240<PtnSzInterpCountArray>     ZonePtnSzInterpCountArray; typedef ___2240<ZonePtnSzInterpCountArray> VarZonePtnSzInterpCountArray; typedef ___2240<BooleanArray>         PtnBooleanArray; typedef ___2240<PtnBooleanArray>      ZonePtnBooleanArray; typedef ___2240<ZonePtnBooleanArray>  VarZonePtnBooleanArray; typedef ___2240<uint8_t>                ValueLocationArray; typedef ___2240<ValueLocationArray>     ___4391; typedef ___2240<uint8_t>                FieldDataTypeArray; typedef ___2240<FieldDataTypeArray>     ___4382; typedef ___2240<uint8_t>        IsPassiveArray; typedef ___2240<IsPassiveArray> ___4385; typedef ___2240<___4635> ___4388; typedef ___2240<___1105> VarZoneDoubleArray; typedef ___2240<___2090::___2980> PartitionArray; struct ___230 { std::string ___2495; std::string ___2667; AuxDataLocation_e m_location; ___1172 m_entity; AuxDataType_e m_type; ___230() {} ___230( std::string const& ___2685, std::string const& ___4314, AuxDataLocation_e location, ___1172 entity, AuxDataType_e type) : ___2495(___2685) , ___2667(___4314) , m_location(location) , m_entity(entity) , m_type(type) {} }; struct ___1556 { double ___2617[3]; CoordSys_e ___2616; ___372 ___2484; ___1172 ___2677; ___516 ___2395; ___516 ___2462; ___372 ___2486; GeomType_e ___2466; LinePattern_e ___2489; double ___2615; double ___2490; uint16_t ___2502; ArrowheadStyle_e ___2343; ArrowheadAttachment_e ___2341; double ___2342; double ___2340; Scope_e ___2619; Clipping_e ___2394; std::vector<std::vector<class ___4580> > ___2464; std::string ___2491; ___1556() {} ___1556( double ___4574, double ___4591, double ___4715, CoordSys_e ___3159, ___372 ___2004, ___1172 zone, ___516 color, ___516 ___1411, ___372 ___2022, GeomType_e ___1651, LinePattern_e ___2263, double ___2986, double ___2289, uint16_t ___2793, ArrowheadStyle_e arrowheadStyle, ArrowheadAttachment_e arrowheadAttachment, double arrowheadSize, double arrowheadAngle, Scope_e ___3442, Clipping_e ___495, std::vector<std::vector<___4580> > const& ___1572, std::string const& ___2330) : ___2616(___3159) , ___2484(___2004) , ___2677(zone) , ___2395(color) , ___2462(___1411) , ___2486(___2022) , ___2466(___1651) , ___2489(___2263) , ___2615(___2986) , ___2490(___2289) , ___2502(___2793) , ___2343(arrowheadStyle) , ___2341(arrowheadAttachment) , ___2342(arrowheadSize) , ___2340(arrowheadAngle) , ___2619(___3442) , ___2394(___495) , ___2464(___1572) , ___2491(___2330) { ___2617[0] = ___4574; ___2617[1] = ___4591; ___2617[2] = ___4715; } }; struct Text { ___516 ___2628; ___516 ___2629; double ___2630; double ___2631; TextBox_e ___2632; TextAnchor_e ___2625; double ___2626[3]; double ___2627; Clipping_e ___2633; ___516 ___2634; double ___2635; double ___2637; std::string ___2638; CoordSys_e ___2639; Scope_e ___2641; Units_e ___2642; std::string ___2643; std::string ___2644; ___372 ___2645; ___372 ___2646; ___1172 ___2647; ___372 ___2636; Text() {} Text( double ___4574, double ___4591, double ___4713, ___516 ___4059, ___516 ___4061, double ___4071, double ___4073, TextBox_e ___4078, TextAnchor_e ___4043, double ___4056, Clipping_e ___4079, ___516 ___4080, double ___4103, double ___4107, std::string ___4109, CoordSys_e ___4115, Scope_e ___4119, Units_e ___4124, std::string ___4126, std::string ___4129, ___372 ___4132, ___372 ___4134, ___1172 ___4138, ___372 ___4105) : ___2628(___4059) , ___2629(___4061)
, ___2630(___4071) , ___2631(___4073) , ___2632(___4078) , ___2625(___4043) , ___2627(___4056) , ___2633(___4079) , ___2634(___4080) , ___2635(___4103) , ___2637(___4107) , ___2638(___4109) , ___2639(___4115) , ___2641(___4119) , ___2642(___4124) , ___2643(___4126) , ___2644(___4129) , ___2645(___4132) , ___2646(___4134) , ___2647(___4138) , ___2636(___4105) { ___2626[0] = ___4574; ___2626[1] = ___4591; ___2626[2] = ___4713; } }; struct CustomLabelSet { uint32_t m_numLabels; ___3816 m_labels; }; typedef ___2240<___230> AuxDataArray; typedef ___2240<___1556> GeomArray; typedef ___2240<Text> TextArray; typedef ___2240<CustomLabelSet> CustomLabelsArray; typedef ___2240<RefSubzoneOffset_t> RefSubzoneOffsetArray; class NszConnectivity { UNCOPYABLE_CLASS(NszConnectivity); public: SubzoneAddressArray m_refCszs; RefSubzoneOffset_t   m_numRefCszs; NszConnectivity() : m_numRefCszs(0) { } ~NszConnectivity() { ___937(); } uint64_t numBytesAllocated(void) const { return m_refCszs.numBytesAllocated(m_numRefCszs); } void ___937() { m_numRefCszs = 0; m_refCszs.___937(); ENSURE(empty()); } bool setNumRszs(RefSubzoneOffset_t numRszs) { REQUIRE(___2067()); REQUIRE(numRszs != BAD_REFSZ_INDEX); bool ___2039; if ( numRszs == 0 ) { m_numRefCszs = numRszs; ___478(m_refCszs.empty()); ___2039 = true; } else { ___2039 = m_refCszs.alloc(numRszs); if ( ___2039 ) m_numRefCszs = numRszs; else ___937(); } ENSURE(___2067()); return ___2039; } inline bool ___2067() const { ___478(EQUIVALENCE(m_refCszs.empty(), m_numRefCszs == 0)); ___478(IMPLICATION(!m_refCszs.empty(), m_refCszs.size() == size_t(m_numRefCszs))); return true; } inline bool empty() const { REQUIRE(___2067()); return m_numRefCszs==0; } inline RefSubzoneOffset_t getNumRszs() const { REQUIRE(___2067()); return m_numRefCszs; } inline ___2090::SubzoneAddress getRszAddress(RefSubzoneOffset_t refSubzoneOffset) const { REQUIRE(___2067()); REQUIRE(refSubzoneOffset<getNumRszs()); return m_refCszs[refSubzoneOffset]; } inline bool getRszAddresses(___3269<___2090::SubzoneAddress>& rszAddresses) const { REQUIRE(___2067()); REQUIRE(rszAddresses.empty()); bool ___2039 = true; try { ___478(m_numRefCszs>0); rszAddresses.reserve(m_numRefCszs); rszAddresses.___3503(m_numRefCszs); memcpy(&rszAddresses[0], &m_refCszs[0], m_numRefCszs*sizeof(___2090::SubzoneAddress)); } catch (...) { ___2039 = ___1186("Out of memory in getRefCszAddresses"); } return ___2039; } }; typedef ___2240<CszConnectivity>           CszConnectivityArray; typedef ___2240<CszConnectivityArray>      PtnCszConnectivity; typedef ___2240<PtnCszConnectivity>        ZonePtnCszConnectivity; typedef ___2240<RefSubzoneOffsetArray>     PtnRefSubzoneOffsetArray; typedef ___2240<PtnRefSubzoneOffsetArray>  ZonePtnRefSubzoneOffsetArray; typedef ___2240<NszConnectivity>           NszConnectivityArray; typedef ___2240<NszConnectivityArray>      PtnNszConnectivity; typedef ___2240<PtnNszConnectivity>        ZonePtnNszConnectivity; typedef ___2240<ItemOffsetArray>           ZoneSzSizeArray; typedef ___2240<___465>               CellSubzoneFirstItemArray; typedef ___2240<CellSubzoneFirstItemArray> ZoneCszFirstItemArray; typedef ___2240<___2718>               NodeSubzoneFirstItemArray; typedef ___2240<NodeSubzoneFirstItemArray> ZoneNszFirstItemArray; typedef ___2240<___81>                AnySzFirstItemArray; typedef ___2240<AnySzFirstItemArray>       ZoneAnySzFirstItemArray; typedef ___2240<___2479>                 ___2481; typedef ___2240<___2481>            VarZoneMinMaxArray; }}
