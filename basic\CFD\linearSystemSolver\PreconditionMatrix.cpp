﻿#include "basic/CFD/linearSystemSolver/PreconditionMatrix.h"

PreconditionMatrix::PreconditionMatrix(const int &rowSize1, const int &nVariable1)
{
	rowSize = rowSize1;
	nVariable = nVariable1;
	nVariable2 = nVariable * nVariable;
	BSMatrix = nullptr;
}

PreconditionMatrix::~PreconditionMatrix()
{
	BSMatrix = nullptr;
}

void PreconditionMatrix::Initialize(const Time::LinearSolverPreconditionerType &type)
{
	kind_of_precondition = type;//初始化
	diagMatrixInv.resize(rowSize * nVariable2);//初始化
}

void PreconditionMatrix::Build(BlockSparseMatrixSelf &BSMatrix_)
{
	BSMatrix = &BSMatrix_;

	if (kind_of_precondition != Time::LinearSolverPreconditionerType::JACOBI && matrixValue.empty())
	{
		matrixValue.resize(BSMatrix->GetBlockSize() * nVariable2); // 最好放到稀疏矩阵的initialize中
	}

	// 计算对角块阵的逆矩阵
	std::vector<Scalar> block(nVariable2);
	for (int row = 0; row < rowSize; ++row)
	{
		const Scalar *p = BSMatrix->GetBlock(row, row);
		for (int i = 0; i < nVariable2; ++i) block[i] = p[i];
		BSMatrix->MatrixInverse(&block[0], &(diagMatrixInv[row * nVariable2]));
	}

	// 采用ILU0预处理
	if (kind_of_precondition != Time::LinearSolverPreconditionerType::JACOBI)
	{
		Scalar weight[64], aux_block[64];

		// 复制一份传入的稀疏矩阵
		const int valueSize = BSMatrix->GetBlockSize() * nVariable2;
		for (int iVar = 0; iVar < valueSize; ++iVar) matrixValue[iVar] = BSMatrix->GetValue(iVar);

		// 从第二行开始循环
		for (auto row = 1; row < rowSize; row++)
		{
			// 对于这一行，循环下矩阵
			for (auto index = BSMatrix->GetBlockIndexRowBegin(row); index < BSMatrix->GetBlockIndexRowDiag(row); index++)
			{
				const auto jPoint = BSMatrix->GetBlockColIndex(index);

				const auto Block_ij = &matrixValue[index * nVariable2];
				MatrixMatrixProduct(Block_ij, &diagMatrixInv[jPoint * nVariable2], weight);

				// weight计算了Aij*inv(Ajj)，下面计算上矩阵的值
				for (auto index_ = BSMatrix->GetBlockIndexRowDiag(jPoint) + 1; index_ < BSMatrix->GetBlockIndexRowBegin(jPoint + 1); index_++)
				{
					/*--- 定位列(kPoint > jPoint). ---*/
					const auto kPoint = BSMatrix->GetBlockColIndex(index_);

					// 获得Aik
					Scalar *Block_ik = nullptr;
					for (unsigned long index1 = BSMatrix->GetBlockIndexRowBegin(row); index1 < BSMatrix->GetBlockIndexRowBegin(row+1); index1++)
					{
						if (BSMatrix->GetBlockColIndex(index1) == kPoint)
						{
							Block_ik = &matrixValue[index1*nVariable2];
							break;
						}
					}

					/*--- 如果Aik存在, 更新: Aik -= Aij*inv(Ajj)*Ajk ---*/
					if (Block_ik != nullptr)
					{
						const auto Block_jk = &matrixValue[index_ * nVariable2];
						MatrixMatrixProduct(weight, Block_jk, aux_block);
						
						for(unsigned long iVar = 0; iVar < nVariable2; iVar++) Block_ik[iVar] -= aux_block[iVar];
					}
				}

				/*--- 最后存储weight值，该值即为下三角矩阵中的非零值. ---*/
				for (auto iVar = 0ul; iVar < nVariable2; ++iVar) Block_ij[iVar] = weight[iVar];
			}
		}
	}
}

void PreconditionMatrix::MatrixVectorProduct(const std::vector<Scalar> &vec, std::vector<Scalar> &prod)const
{
	if (kind_of_precondition == Time::LinearSolverPreconditionerType::JACOBI)
	{
		for (int row = 0; row < rowSize; ++row)
		{
			const int begin_matrix = row * nVariable2;
			const int begin_vec = row * nVariable;

			for (int i = 0; i < nVariable; ++i)
			{
				prod[begin_vec + i] = 0.0;
				const auto valueI = &diagMatrixInv[begin_matrix + i * nVariable];
				for (int j = 0; j < nVariable; ++j)
					prod[begin_vec + i] += valueI[j] * vec[begin_vec + j];
			}
		}
	}
	else
	{
		Scalar aux_vec[8];

		/*--- 将vec复制一份作为prod的初始化 ---*/
		for (auto iVar = 0; iVar < rowSize * nVariable; iVar++) prod[iVar] = vec[iVar];

		/*--- 用之前ILU分解获得的下三角阵进行前向求解 ---*/
		for (auto iPoint = 1; iPoint < rowSize; iPoint++)
		{
			const auto prodI = &prod[iPoint * nVariable];
			for (auto index = BSMatrix->GetBlockIndexRowBegin(iPoint); index < BSMatrix->GetBlockIndexRowDiag(iPoint); index++)
			{
				const auto jPoint = BSMatrix->GetBlockColIndex(index);
				const auto block = &matrixValue[index * nVariable2];
				const auto prodJ = &prod[jPoint * nVariable];
				for (unsigned long i = 0; i < nVariable; i++)
				{
					const auto blockI = &block[i*nVariable];
					for (unsigned long j = 0; j < nVariable; j++) prodI[i] -= blockI[j] * prodJ[j];
				}
			}
		}

		/*--- 向后回代 ---*/
		for (auto iPoint = rowSize; iPoint > 0;)
		{
			iPoint--;
			const auto prodI = &prod[iPoint * nVariable];
			for (auto iVar = 0ul; iVar < nVariable; iVar++) aux_vec[iVar] = prodI[iVar];

			for (auto index = BSMatrix->GetBlockIndexRowDiag(iPoint) + 1; index < BSMatrix->GetBlockIndexRowBegin(iPoint + 1); index++)
			{
				const auto jPoint = BSMatrix->GetBlockColIndex(index);
				const auto block = &matrixValue[index * nVariable2];
				const auto prodJ = &prod[jPoint * nVariable];
				for (unsigned long i = 0; i < nVariable; i++)
				{
					const auto blockI = &block[i*nVariable];
					for (unsigned long j = 0; j < nVariable; j++) aux_vec[i] -= blockI[j] * prodJ[j];
				}
			}

			const auto block = &diagMatrixInv[iPoint * nVariable2];
			for (unsigned long i = 0; i < nVariable; i++)
			{
				prodI[i] = 0.0;
				const auto blockI = &block[i*nVariable];
				for (unsigned long j = 0; j < nVariable; j++) prodI[i] += blockI[j] * aux_vec[j];
			}
		}
	}
}

void PreconditionMatrix::MatrixMatrixProduct(const Scalar *matrix_a, const Scalar *matrix_b, Scalar *prod)
{
	int i, j, k;
	for (i = 0; i < nVariable; i++)
	{
		for (j = 0; j < nVariable; j++)
		{
			prod[i * nVariable + j] = 0.0;
			for (k = 0; k < nVariable; k++)
				prod[i * nVariable + j] += matrix_a[i * nVariable + k] * matrix_b[k * nVariable + j];
		}
	}
}