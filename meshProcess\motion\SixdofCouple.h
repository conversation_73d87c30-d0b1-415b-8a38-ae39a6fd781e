﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SixdofCouple.h
//! <AUTHOR>
//! @brief 六自由度运动耦合类
//! @date  2024-1-20
//
#ifndef _meshProcess_Motion_SixdofCouple_
#define _meshProcess_Motion_SixdofCouple_
#include "Sixdof.h"

class SixdofCouple
{
public:

	SixdofCouple(Sixdof&,int extrapolType=0, double relaxFactor=1.0, double subitConvThreshold=0.001);

	~SixdofCouple();
	
	//参数更新
	void InitializeTimestep();

private:
	Sixdof* DOF;
	int extrapolator;
	double relaxFacFixed;
	double subitConvThreshold;

	//time level "n+1"
	var motion_data;

	//time level "n"
	var motion_data_old;

	//time level "n-1"
	var motion_data_oold;

	//time level "n-2"
	var motion_data_ooold;
};

#endif