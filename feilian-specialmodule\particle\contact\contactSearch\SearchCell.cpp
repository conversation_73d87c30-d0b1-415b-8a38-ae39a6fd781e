﻿#include "feilian-specialmodule/particle/contact/contactSearch/SearchCell.h"

namespace Particle
{
namespace Contact
{

SearchCell::SearchCell(const Vector &minDomain, const Vector &maxDomain,
                       const Scalar &minDiameter, const Scalar &maxDiameter,
                       const int &max_nPrtcl_, const int &numParticles_,
					   std::vector<Particle *> &particles_,
					   ElementField<Scalar> *volumeFraction_,
					   const bool &clc_minmax_, const Scalar &ratio_)
					   : SimulationWorld(minDomain, maxDomain, minDiameter, maxDiameter, max_nPrtcl_, numParticles_, particles_, volumeFraction_), clc_minmax(clc_minmax_), ratio(ratio_)
{
	dim2 = fabs(maxDomain.Z() - minDomain.Z()) < SMALL;
}

void SearchCell::Initialize()
{
    // 颗粒索引容器
    this->box_index.resize(this->GetParticleNumberMax(), IndexVector());
	
    // 计算域最小坐标点
	minDomainCurrent = this->GetMinDomain();

    // 计算域最大坐标点
	maxDomainCurrent = this->GetMaxDomain();

    // 搜索单元边长
    this->dx = this->ratio * this->GetMinMaxDiameter().second;

    // 三个方向的搜索单元数量
	this->CalculateSearchCellNumber();
}

void SearchCell::CalculateSearchCellNumber()
{
	// 三个方向的搜索单元数量
	this->num.x = Max(1, int(std::round((maxDomainCurrent.X() - minDomainCurrent.X()) / this->dx * 1.000001)) + 1);
	this->num.y = Max(1, int(std::round((maxDomainCurrent.Y() - minDomainCurrent.Y()) / this->dx * 1.000001)) + 1);
	this->num.z = Max(1, int(std::round((maxDomainCurrent.Z() - minDomainCurrent.Z()) / this->dx * 1.000001)) + 1);
}

void SearchCell::BoxIndex()
{
    // 获得颗粒数量
    const int &nPrtcl = this->GetParticleNumber();
    
    // 获得最小最大坐标点
    minDomainCurrent = this->GetMinDomain();
    maxDomainCurrent = this->GetMaxDomain();

	contactSearch = true;
	if (volumeFraction != nullptr)
	{
		Vector minDomainTemp = Vector(INF, INF, INF);
		Vector maxDomainTemp = Vector(-INF, -INF, -INF);
		Mesh *mesh = volumeFraction->GetMesh();
		const int elemNum = mesh->GetElementNumberReal();
		for (int elemID = 0; elemID < elemNum; elemID++)
		{
			const Scalar &fraction = volumeFraction->GetValue(elemID);
			if (fraction > 0.01)
			{
				const Element &elem = mesh->GetElement(elemID);
				for (int i = 0; i < elem.GetNodeSize(); i++)
				{
					const Vector &node = mesh->GetNode(elem.GetNodeID(i));
					minDomainTemp = Min(minDomainTemp, node);
					maxDomainTemp = Max(maxDomainTemp, node);
				}
			}
		}

		if (dim2)
		{
			minDomainTemp.SetZ(Scalar0);
			maxDomainTemp.SetZ(Scalar0);
		}

		minDomainCurrent = Max(minDomainCurrent, minDomainTemp);
		maxDomainCurrent = Min(maxDomainCurrent, maxDomainTemp);

		const Vector distanceMax = maxDomainCurrent - minDomainCurrent;
		if (distanceMax.X() < SMALL || distanceMax.Y() < SMALL ||
			(!dim2 && distanceMax.Z() < SMALL))
		{
			contactSearch = false;
		}
	}

	if (contactSearch) this->UpdateBoxIndex();
}

void SearchCell::UpdateBoxIndex()
{
	// 获得颗粒数量
	const int &nPrtcl = this->GetParticleNumber();

	// 三个方向的搜索单元数量
	this->CalculateSearchCellNumber();

	// 遍历所有颗粒
	for (int n = 0; n < nPrtcl; ++n)
	{
		// 判断所有在计算域的颗粒
		if (this->particles[n] != nullptr)
		{
			// 根据颗粒位置得到所在整数坐标
			const Vector position = (this->particles[n]->position - minDomainCurrent);
			IndexVector ind;
			if (dim2) ind = IndexVector(position / this->dx + Vector(1, 1, 0));
			else      ind = IndexVector(position / this->dx + Vector(1, 1, 1));

			// 检查颗粒是否已在搜索范围外
			if (ind.x < 0 || ind.x >= this->num.x ||
				ind.y < 0 || ind.y >= this->num.y ||
				((!dim2) && (ind.z < 0 || ind.z >= this->num.z)))
			{
				this->box_index[n] = IndexVector(-1, -1, -1);
			}
			else
			{
				// 更新当前颗粒索引
				this->box_index[n] = ind;
			}
		}
	}
}

} // namespace Contact

} // namespace Particle
