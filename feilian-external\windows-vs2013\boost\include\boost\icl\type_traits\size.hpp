/*-----------------------------------------------------------------------------+    
Copyright (c) 2008-2009: <PERSON>
+------------------------------------------------------------------------------+
   Distributed under the Boost Software License, Version 1.0.
      (See accompanying file LICENCE.txt or copy at
           http://www.boost.org/LICENSE_1_0.txt)
+-----------------------------------------------------------------------------*/
#ifndef BOOST_ICL_TYPE_TRAITS_SIZE_HPP_JOFA_080911
#define BOOST_ICL_TYPE_TRAITS_SIZE_HPP_JOFA_080911

namespace boost{ namespace icl
{
    template <class Type> struct size{ typedef std::size_t type; };
}} // namespace boost icl

#endif


