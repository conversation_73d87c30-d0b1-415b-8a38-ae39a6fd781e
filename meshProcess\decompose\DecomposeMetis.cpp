﻿#include "meshProcess/decompose/DecomposeMetis.h"

DecomposeMetis::DecomposeMetis(Mesh* pmesh, const int &nPros)
    :
    DecomposeBase(pmesh,nPros)
{
}

void DecomposeMetis::Decompose(const std::vector<int> &cellWeights_, std::vector<int> &decomposeIDList)
{
    Mesh* pmesh = this->p_mesh;
    const int elementSize = pmesh->GetElementNumberReal();
    const int faceSize = pmesh->GetFaceNumber();

    // 获取相邻单元数量
    std::vector<int> elemAdjncySize(elementSize, 0);
    for (int faceID = 0; faceID < faceSize; ++faceID)
    {
        const int &ownerID = pmesh->GetFace(faceID).GetOwnerID();
        const int &neighID = pmesh->GetFace(faceID).GetNeighborID();
        if (neighID != -1)
        {
            elemAdjncySize[ownerID]++;
            elemAdjncySize[neighID]++;
        }
    }

    // 分配空间
    std::vector<std::vector<int> > elemAdjncy(elementSize);
    for (int elementID = 0; elementID < elementSize; ++elementID)
        elemAdjncy[elementID].reserve(elemAdjncySize[elementID]);
    
    // 生成相邻单元列表
    for (int faceID = 0; faceID < faceSize; ++faceID)
    {
        const int &ownerID = pmesh->GetFace(faceID).GetOwnerID();
        const int &neighID = pmesh->GetFace(faceID).GetNeighborID();
        if (neighID != -1)
        {
            elemAdjncy[ownerID].push_back(neighID);
            elemAdjncy[neighID].push_back(ownerID);
        }
    }

    // 相邻单元编号排序
    for (int elementID = 0; elementID < elementSize; ++elementID)
        std::sort(elemAdjncy[elementID].begin(), elemAdjncy[elementID].end());

    // 计算相邻信息大小
    int adjncySize = 0;
    for (int elementID = 0; elementID < elementSize; ++elementID)
        adjncySize += elemAdjncySize[elementID];

    // 形成单元相邻信息一维数组
    std::vector<idx_t> xadj, adjncy;
    xadj.reserve(elementSize + 1); adjncy.reserve(adjncySize);
    for (int elementID = 0; elementID < elementSize; ++elementID)
    {
        xadj.push_back(adjncy.size());
        for (int index = 0; index < elemAdjncySize[elementID]; ++index)
            adjncy.push_back(elemAdjncy[elementID][index]);
    }
    xadj.push_back((int)adjncy.size());

    std::vector<int>().swap(elemAdjncySize);
    std::vector<std::vector<int> >().swap(elemAdjncy);

    idx_t Vertices = elementSize;
    idx_t ncon = 1;
    idx_t nparts = this->n_pros;
    idx_t options[METIS_NOPTIONS];
    idx_t edgecut = 0;
    std::vector<idx_t> Part_(elementSize);
	
    std::vector<idx_t> cellWeights(elementSize, 0);
	for (int elementID = 0; elementID < elementSize; ++elementID)
        cellWeights[elementID] = cellWeights_[elementID];
	
    std::vector<real_t> processorWeights(nparts, 1.0 / nparts);
    METIS_SetDefaultOptions(options);

    std::ostringstream ostring;
    if (nparts > 8)
    {
        ostring << "Using K-way Partitioning!" << std::endl;
        ostring << "METIS_PartGraphKway status:\t"
                << METIS_PartGraphKway(
                    &Vertices,
                    &ncon,
                    &xadj[0],
                    &adjncy[0],
                    &cellWeights[0],
                    0,
                    nullptr,
                    &nparts,
                    &processorWeights[0],
                    nullptr,
                    &options[0],
                    &edgecut,
                    &Part_[0]
                    )
                << std::endl;
        Print(ostring.str());
    }
    else
    {
        ostring << "Using Recursive Partitioning!" << std::endl;
        ostring << "METIS_PartGraphRecursive status:\t"
                << METIS_PartGraphRecursive(
                    &Vertices,
                    &ncon,
                    &xadj[0],
                    &adjncy[0],
                    &cellWeights[0],
                    0,
                    nullptr,
                    &nparts,
                    &processorWeights[0],
                    nullptr,
                    &options[0],
                    &edgecut,
                    &Part_[0]
                    )
                << std::endl;
        PrintFile(ostring.str());
    }

    decomposeIDList.resize(elementSize);
    for (int i = 0; i < elementSize; ++i) decomposeIDList[i] = (int)Part_[i];
}