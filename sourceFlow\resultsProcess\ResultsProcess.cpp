﻿#include "sourceFlow/resultsProcess/ResultsProcess.h"

FlowResultsProcess::FlowResultsProcess(SubMesh *localMesh_, std::vector<Package::FlowPackage *> flowPackageVector_)
	:
	localMesh(localMesh_),
	flowPackageVector(flowPackageVector_),
	residualProcess(flowPackageVector_),
	forceProcess(flowPackageVector_)
{
	const auto &flowConfigure = flowPackageVector_[0]->GetFlowConfigure();

	// 获取参数文件中的算例名称
	resultName = flowConfigure.GetCaseName();
	resultPath = flowConfigure.GetControl().resultSavePath;
    if (resultPath.find_last_of("/") != resultPath.length() - 1) resultPath = resultPath + "/";
	MakeDirectory(resultPath);

	// 获取非定常标识
	unsteady = flowPackageVector[0]->GetUnsteadyStatus().unsteadyFlag;

	// 续算标识
	restartFlag = flowConfigure.GetControl().initialization.type == Initialization::Type::RESTART;

	// 计算起始步
	startStep = restartFlag ? flowConfigure.GetControl().initialization.restartStep : 0;

	// 当前步取起始步
	currentStep = restartFlag ? (unsteady ? this->GetInnerStep() : startStep) : 0;

	// 起始时间及时间步
	timeStep = unsteady ? startStep : 0;
	meanStep = 0;
	startTime = 0.0;
	startMeanStep = 0;
	if (restartFlag && unsteady)
	{
		this->GetReStartValues();
		for (int i = 0; i < flowPackageVector.size(); i++)flowPackageVector[i]->SetStartTime(startTime);
	}

	// 新定常监控文件标识
	newMonitorFileFlag = restartFlag ? false : true;

	// 清空旧监控文件
	if (newMonitorFileFlag && currentLevel == 0)
	{
		outFile.open(resultPath + resultName + "_res" + ".dat", std::ios::out);
		outFile.close();
	}

    const int nTurbulent = flowPackageVector_[0]->GetField().turbulence.size();
    const int nResidualTotal = 5 + nTurbulent;
	monitorResidualFlag.clear();
	maximumResidualFlag.clear();
    monitorResidualFlag.resize(nResidualTotal, false);
    maximumResidualFlag.resize(nResidualTotal, false);
    const auto &residuals = flowConfigure.GetMonitor().residuals;
    if(residuals.massFlag) monitorResidualFlag[0] = true;
    if(residuals.momentumFlag) for (int k=1; k <= 3; k++) monitorResidualFlag[k] = true;
    if(residuals.energyFlag) monitorResidualFlag[4] = true;
    if(residuals.turbulenceFlag) for (int k=0; k < nTurbulent; k++) monitorResidualFlag[5+k] = true;
    if(residuals.maxMassFlag) maximumResidualFlag[0] = true;
    if(residuals.maxTurbulenceFlag) for (int k=0; k < nTurbulent; k++) maximumResidualFlag[5+k] = true;

    dimension3D = flowPackageVector_[0]->GetMeshStruct().dim3;
    if(!dimension3D) monitorResidualFlag[3] = false;

    const auto &monitorForce = flowConfigure.GetMonitor().forces;
	monitorForceFlag.clear();
    monitorForceFlag.resize(5, false);
    if(monitorForce.ClFlag) monitorForceFlag[0] = true;
    if(monitorForce.CdFlag) monitorForceFlag[1] = true;
    if(monitorForce.CmFlag)
    {
        if (dimension3D) { monitorForceFlag[2] = true; monitorForceFlag[3] = true; }
        monitorForceFlag[4] = true;
    }

	// 新非定常监控文件标识
	newUnsteadyMonitorFileFlag = false;
	if (unsteady)
	{
		if (!restartFlag) newUnsteadyMonitorFileFlag = true;

		if (newUnsteadyMonitorFileFlag && currentLevel == 0)
		{
			outFile.open(resultPath + resultName + "_unsteady" + ".dat", std::ios::out);
			outFile.close();
		}
	}

	// 湍流标识
	turbulentFlag = flowPackageVector[0]->GetTurbulentStatus().turbulenceType > Turbulence::Model::LAMINAR;

	//获取多域管理器
	zoneManager = flowPackageVector[0]->GetZoneManager();

	// 采用双时间步长计算标识
	dualTimeFlag = flowConfigure.GetTimeScheme().outerLoopType == Time::UnsteadyType::DUAL_TIME_STEP;

	// 全局边界面数量，容器大小为全局边界编号数量
	boundaryFaceSizeGlobal.resize(flowConfigure.GetGlobalBoundarySize());

	// 收集全局网格信息
	this->GatherGlobalInfo();

	// 全局耗时初始化
	CPUTimeTotal.UpdateTime();
}

FlowResultsProcess::~FlowResultsProcess()
{
	if (outFile.is_open()) outFile.close();
}

void FlowResultsProcess::Initialize(const int &level)
{
	currentLevel = level;
	residualProcess.Initialize(level);
	forceProcess.Initialize();
	CPUTimePerStep.UpdateTime();
}

int FlowResultsProcess::CheckConvergence(const Scalar &criteria)
{
	return residualProcess.CheckConvergence(criteria);
}

void FlowResultsProcess::OutputForceCoefficient()
{
	forceProcess.OutputForceCoefficient();
}

void FlowResultsProcess::UpdateTime()
{
	CPUTimePerStep.UpdateTime();
}

void FlowResultsProcess::SaveIntervalResults()
{
	std::string fileName = unsteady ? ToString(timeStep) : ToString(currentStep);
	if (restartFlag)
	{
		if (unsteady && timeStep == startStep) return;
		if (!unsteady && currentStep == startStep) return;
	}

	this->SaveFlowFields(fileName);
}

void FlowResultsProcess::SaveFinalResults()
{
	this->SaveIntervalResults();
	this->OutputForceCoefficient();
}

void FlowResultsProcess::SaveFlowFields(const std::string stepString)
{
	const auto &flowPackage = *flowPackageVector[0];

	if (flowPackage.GetMeshStruct().level > 0)
	{
		WarningContinue("FlowResultsProcess::SaveFlowFields: field at coarse mesh isnot supported!");
		return;
	}

	const std::string fieldPath = resultPath + stepString + "/";
	MakeDirectory(fieldPath);

	this->MergeAndWriteField(flowPackage.GetField().density, stepString);
	this->MergeAndWriteField(flowPackage.GetField().velocity, stepString);
	this->MergeAndWriteField(flowPackage.GetField().pressure, stepString);

	for (int i = 0; i < flowPackage.GetField().turbulence.size(); i++)
		this->MergeAndWriteField(flowPackage.GetField().turbulence[i], stepString);

	if (flowPackage.GetField().turbulence.size() > 0)
		this->MergeAndWriteField(flowPackage.GetField().muTurbulent, stepString);

	if (flowPackage.GetFlowConfigure().JudgeEnableOversetMesh())   //启动重叠网格时，输出重叠网格单元类型场
	{
		this->MergeAndWriteField(flowPackage.GetField().oversetElemType, stepString);
	}

	if (flowPackage.GetFlowConfigure().JudgeEnableMotion())	//启动运动时
	{
		//输出运动物理量场
		this->MergeAndWriteField(flowPackage.GetField().meshVelocity, stepString);
		//输出新网格点坐标
		this->MergeAndWriteNodes(stepString);
	}

	if (flowPackage.GetTurbulentStatus().desFlag)
	{
		this->MergeAndWriteField(flowPackage.GetField().ldOverlr, stepString);
		if (flowPackage.GetFlowConfigure().GetModel().type == Turbulence::Model::SPALART_ALLMARAS_DDES ||
			flowPackage.GetFlowConfigure().GetModel().type == Turbulence::Model::MENTER_SST_DDES)
			this->MergeAndWriteField(flowPackage.GetField().shieldingFunction, stepString);
	}

#if defined(_EnableMLTurbModel_)
	for (int i = 0; i < flowPackage.GetField().features.size(); i++)
		this->MergeAndWriteField(flowPackage.GetField().features[i], stepString);
#endif

	if (unsteady)
	{
		if (turbulentFlag)
		{
			this->MergeAndWriteField(flowPackage.GetField().meanMuTurbulent, stepString);
		}
	    if (flowPackage.GetField().muLaminar != nullptr)
		    this->MergeAndWriteField(flowPackage.GetField().meanMu, stepString);
		this->MergeAndWriteField(flowPackage.GetField().meanPressure, stepString);
		this->MergeAndWriteField(flowPackage.GetField().meanDensity, stepString);
		this->MergeAndWriteField(flowPackage.GetField().meanVelocity, stepString);
#if defined(_EnableMultiSpecies_)
		for (int k = 0; k < flowPackage.GetMultiSpeciesStatus().speciesSize; k++)
		{
			this->MergeAndWriteField(flowPackage.GetField().massFraction[k], stepString);
		}
#endif
	}
}

void FlowResultsProcess::CalculateMeanFields(int computeAveragedStep)
{
	const auto &flowPackage = *flowPackageVector[0];
	meanStep = computeAveragedStep + startMeanStep;
	if (turbulentFlag)
		this->CalculateMeanValues(meanStep, *flowPackage.GetField().muTurbulent, *flowPackage.GetField().meanMuTurbulent);

	if (flowPackage.GetField().muLaminar != nullptr)
		this->CalculateMeanValues(meanStep, *flowPackage.GetField().muLaminar, *flowPackage.GetField().meanMu);
	this->CalculateMeanValues(meanStep, *flowPackage.GetField().pressure, *flowPackage.GetField().meanPressure);
	this->CalculateMeanValues(meanStep, *flowPackage.GetField().density, *flowPackage.GetField().meanDensity);
	this->CalculateMeanValues(meanStep, *flowPackage.GetField().velocity, *flowPackage.GetField().meanVelocity);
}

template void FlowResultsProcess::CalculateMeanValues(int computeAveragedStep, ElementField<Scalar> &value, ElementField<Scalar> &meanValue);
template void FlowResultsProcess::CalculateMeanValues(int computeAveragedStep, ElementField<Vector> &value, ElementField<Vector> &meanValue);
template<class Type>
void FlowResultsProcess::CalculateMeanValues(int computeAveragedStep, ElementField<Type> &value, ElementField<Type> &meanValue)
{
	const auto &flowPackage = *flowPackageVector[0];
	Mesh *mesh = flowPackage.GetMeshStruct().mesh;
	const int elementNumber = mesh->GetElementNumberInDomain();

	for (int elementID = 0; elementID < elementNumber; ++elementID)
	{
		const Type valueTemp = value.GetValue(elementID);
		const Type meanValueTemp = meanValue.GetValue(elementID);
		const Type meanValuenew = meanValueTemp + (valueTemp - meanValueTemp) / (computeAveragedStep + 1);
		meanValue.SetValue(elementID, meanValuenew);
	}
	meanValue.SetGhostlValueBoundary();
}


void FlowResultsProcess::GatherGlobalInfo()
{
	int zoneID = localMesh->GetMeshZoneID();//修改增加对多域的支持
	int zoneMpiRank = 0;
	int zoneMpiSize = 1;

	MPIBarrier();
#if defined(_BaseParallelMPI_)
	boost::mpi::communicator zoneMpiWorld;
	if (GetMPISize() > 1) zoneMpiWorld = MPI::mpiWorld.split(zoneID);//修改增加对多域的支持,曾凯
	zoneMpiRank = zoneMpiWorld.rank();
	zoneMpiSize = zoneMpiWorld.size();
#endif

	// 统计全局网格每个边界的总面数
	const int boundarySize = localMesh->GetBoundarySize();
	for (int patchID = 0, index1 = 0; patchID < boundarySize; ++patchID)
	{
		const int &patchIDGlobal = localMesh->GetBoundaryGlobalID(0, patchID);
		boundaryFaceSizeGlobal[patchIDGlobal] = localMesh->GetBoundaryFaceSize(patchID);
	}

#if defined(_BaseParallelMPI_)
	if (GetMPISize() > 1)
	{
		std::vector<std::vector<int>> boundaryFaceSizeGlobalVector;
		boost::mpi::gather(zoneMpiWorld, boundaryFaceSizeGlobal, boundaryFaceSizeGlobalVector, 0);
		if (zoneMpiWorld.rank() == 0)
		{
			for (int j = 0; j < boundaryFaceSizeGlobal.size(); ++j)
			{
				for (int i = 1; i < zoneMpiWorld.size(); ++i)
				{
					boundaryFaceSizeGlobal[j] += boundaryFaceSizeGlobalVector[i][j];
				}
			}
		}
		boost::mpi::broadcast(zoneMpiWorld, boundaryFaceSizeGlobal, 0);
	}
#endif
	MPIBarrier();

	std::vector<int> elementIDlocalToGlobal(localMesh->GetElementNumberReal());
	for (int i = 0; i < elementIDlocalToGlobal.size(); ++i)
		elementIDlocalToGlobal[i] = localMesh->GetElementGlobalID(i);

	int boundaryFaceSizeLocal = 0;
	for (int patchID = 0; patchID < boundarySize; ++patchID)
		boundaryFaceSizeLocal += localMesh->GetBoundaryFaceSize(patchID);

	std::vector<int> boundaryFaceIDLocalToGlobal(boundaryFaceSizeLocal);
	for (int patchID = 0, index1 = 0; patchID < boundarySize; ++patchID)
	{
		const int &patchIDGlobal = localMesh->GetBoundaryGlobalID(0, patchID);

		int index0 = 0;
		for (int i = 0; i < patchIDGlobal; ++i) index0 += boundaryFaceSizeGlobal[i];

		for (int index = 0; index < localMesh->GetBoundaryFaceSize(patchID); ++index)
		{
			const int &globalFaceIndex = localMesh->GetBoundaryFaceGlobalIndex(patchID, index);
			boundaryFaceIDLocalToGlobal[index1++] = index0 + globalFaceIndex;
		}
	}

	if (GetMPISize() == 1)
	{
		elementIDLocalToGlobalTotal.push_back(elementIDlocalToGlobal);
		boundaryFaceIDLocalToGlobalTotal.push_back(boundaryFaceIDLocalToGlobal);
	}
	else
	{
#if defined(_BaseParallelMPI_)
		zoneMpiWorld = MPI::mpiWorld.split(zoneID);//修改增加对多域的支持,曾凯
		boost::mpi::gather(zoneMpiWorld, elementIDlocalToGlobal, elementIDLocalToGlobalTotal, 0);
		boost::mpi::gather(zoneMpiWorld, boundaryFaceIDLocalToGlobal, boundaryFaceIDLocalToGlobalTotal, 0);
		boost::mpi::gather(zoneMpiWorld, boundaryFaceIDLocalToGlobal, boundaryFaceIDLocalToGlobalTotal, 0);
#endif
	}

	elementSize = 0;
	boundaryFaceSizeTotal = 0;
	if (zoneMpiRank == 0)
	{
		for (int partID = 0; partID < zoneMpiSize; ++partID)
		{
			elementSize += elementIDLocalToGlobalTotal[partID].size();
			boundaryFaceSizeTotal += boundaryFaceIDLocalToGlobalTotal[partID].size();
		}
	}
}

template void FlowResultsProcess::MergeAndWriteField(ElementField<Int> *phi, const std::string &stepString);
template void FlowResultsProcess::MergeAndWriteField(ElementField<Scalar> *phi, const std::string &stepString);
template void FlowResultsProcess::MergeAndWriteField(ElementField<Vector> *phi, const std::string &stepString);
template<class Type>
void FlowResultsProcess::MergeAndWriteField(ElementField<Type> *phi, const std::string &stepString)
{
	std::vector<Type> elementValue(localMesh->GetElementNumberReal());
	for (int i = 0; i < localMesh->GetElementNumberReal(); ++i)
		elementValue[i] = phi->GetValue(i);

	const int boundarySize = localMesh->GetBoundarySize();

	int boundaryFaceSizeLocal = 0;
	for (int i = 0; i < boundarySize; ++i)
		boundaryFaceSizeLocal += localMesh->GetBoundaryFaceSize(i);

	// 存储边界面面心物理量
	std::vector<Type> boundaryValue(boundaryFaceSizeLocal);
	for (int patchID = 0, index1 = 0; patchID < boundarySize; ++patchID)
	{
		for (int index = 0; index < localMesh->GetBoundaryFaceSize(patchID); ++index)
		{
			const int &faceID = localMesh->GetBoundaryFaceID(patchID, index);
			boundaryValue[index1++] = 0.5 * (phi->GetValue(localMesh->GetFace(faceID).GetNeighborID())
				+ phi->GetValue(localMesh->GetFace(faceID).GetOwnerID()));
		}
	}

	int zoneID = localMesh->GetMeshZoneID();//修改增加对多域的支持
	int myZoneRootRank = zoneManager->GetZoneStartRank(zoneID);
	//int myZoneEndRank = zoneManager->GetZoneEndRank(zoneID);

	const int MPISize = GetMPISize();
	const int MPIRank = GetMPIRank();

	// 初始化全局场容器，物理边界面在面列表开始位置
	std::vector<Type> phiGlobal;
	if (MPIRank == myZoneRootRank) phiGlobal.resize(elementSize + boundaryFaceSizeTotal);

	if (MPISize == 1)
	{
		// 填充单元值
		for (int j = 0; j < elementValue.size(); j++) phiGlobal[j] = elementValue[j];

		// 填充边界值
		for (int i = 0, index = elementValue.size(); i < boundaryValue.size(); ++i)
			phiGlobal[index++] = boundaryValue[i];
	}
	else
	{
		std::vector<std::vector<Type>> elementValueVector;
		std::vector<std::vector<Type>> boundaryValueVector;

		int zoneMpiRank = 0;
		int zoneMpiSize = 1;

#if defined(_BaseParallelMPI_)
		boost::mpi::communicator mpiWorld;
		boost::mpi::communicator zoneMpiWorld = mpiWorld.split(zoneID);//修改增加对多域的支持
		boost::mpi::gather(zoneMpiWorld, elementValue, elementValueVector, 0);
		boost::mpi::gather(zoneMpiWorld, boundaryValue, boundaryValueVector, 0);
		zoneMpiRank = zoneMpiWorld.rank();
		zoneMpiSize = zoneMpiWorld.size();
#endif

		std::vector<Type>().swap(elementValue);
		std::vector<Type>().swap(boundaryValue);

		if (zoneMpiRank == 0)
		{
			for (int i = 0; i < zoneMpiSize; i++)
			{
				// 填充单元值
				for (int j = 0; j < elementIDLocalToGlobalTotal[i].size(); j++)
				{
					const int &position = elementIDLocalToGlobalTotal[i][j];
					phiGlobal[position] = elementValueVector[i][j];
				}

				// 填充边界值
				for (int j = 0; j < boundaryFaceIDLocalToGlobalTotal[i].size(); ++j)
				{
					// 物理边界面在面列表开始位置
					const int position = elementSize + boundaryFaceIDLocalToGlobalTotal[i][j];
					phiGlobal[position] = boundaryValueVector[i][j];
				}
			}
		}
	}

	if (!phiGlobal.empty() && GetMPIRank() == myZoneRootRank)//修改为按域输出
	{
		std::fstream file;
		const std::string fileName = resultPath + stepString + "/" + phi->GetName() + "_zone" + ToString(zoneID) + ".Field"; //修改为按域输出
		file.open(fileName, std::ios::out | std::ios::binary);
		IO::Write(file, phiGlobal, true);

		file.close();
	}

	MPIBarrier();
}

void FlowResultsProcess::MergeAndWriteNodes(const std::string &stepString)
{
	int zoneID = localMesh->GetMeshZoneID();//多域支持
	int myZoneRootRank = zoneManager->GetZoneStartRank(zoneID);
	//int myZoneEndRank = zoneManager->GetZoneEndRank(zoneID);

	//获取当地网格点坐标及全局编号
	std::vector<Vector> localNodes;
	std::vector<int> NodeIDLocaltoGlobal;
	int nodesNum = localMesh->GetNodeNumber();
	localNodes.resize(nodesNum);
	NodeIDLocaltoGlobal.resize(nodesNum);
	for (int i = 0; i < nodesNum; ++i)
	{
		localNodes[i] = localMesh->GetNode(i);
		NodeIDLocaltoGlobal[i] = localMesh->GetNodeGlobalID(i);
	}

	std::vector<Vector> nodesGlobal;
	if (GetMPISize() == 1)
	{
		nodesGlobal = localNodes;
	}
	else
	{
		//合并各进程坐标点
		std::vector<std::vector<Vector>> nodesAllProc;
		std::vector<std::vector<int>> nodeIDLocalToGlobalAllProc;

		int zoneMpiRank = 0;

#if defined(_BaseParallelMPI_)
		boost::mpi::communicator mpiWorld;
		boost::mpi::communicator zoneMpiWorld = mpiWorld.split(zoneID);//多域支持
		boost::mpi::gather(zoneMpiWorld, localNodes, nodesAllProc, 0);
		boost::mpi::gather(zoneMpiWorld, NodeIDLocaltoGlobal, nodeIDLocalToGlobalAllProc, 0);
		zoneMpiRank = zoneMpiWorld.rank();
#endif

		std::vector<Vector>().swap(localNodes);
		std::vector<int>().swap(NodeIDLocaltoGlobal);

		int nodesNumTotal = 0;
		for (int i = 0; i < nodeIDLocalToGlobalAllProc.size(); i++)
		{
			int n = *std::max_element(nodeIDLocalToGlobalAllProc[i].begin(),
				nodeIDLocalToGlobalAllProc[i].end());
			if (n + 1 > nodesNumTotal)
			{
				nodesNumTotal = n + 1;
			}
		}
		nodesGlobal.resize(nodesNumTotal);

		if (zoneMpiRank == 0)
		{
			for (int i = 0; i < nodeIDLocalToGlobalAllProc.size(); i++)
			{
				for (int j = 0; j < nodeIDLocalToGlobalAllProc[i].size(); j++)
				{
					const int &position = nodeIDLocalToGlobalAllProc[i][j];
					nodesGlobal[position] = nodesAllProc[i][j];
				}
			}

		}
	}

	if (!nodesGlobal.empty() && GetMPIRank() == myZoneRootRank)//按域输出
	{
		std::fstream file;
		const std::string fileName = resultPath + stepString + "/" + "nodes" + "_zone" + ToString(zoneID) + ".XYZ"; //按域输出
		file.open(fileName, std::ios::out | std::ios::binary);
		IO::Write(file, nodesGlobal, true);

		file.close();
	}
}

void FlowResultsProcess::CalculateMonitorValues()
{
	this->residualProcess.CalculateMonitorResidual(currentLevel);
	this->residualProcess.CalculateMaximumResidual(currentLevel);
	this->forceProcess.CalculateMonitorForce(currentLevel);
}

std::string PrintString(const Scalar &value, const int &size_, const int &precision_, const bool &scientific_)
{
    std::ostringstream stringStream;

	const int precision = Max(precision_, 0);
	const bool scientific = scientific_ || value > pow(10, size_ - precision - 2);
	const int size = scientific ? Max(size_, 7) : Max(size_, 2);

    stringStream << std::setprecision(precision);
    stringStream << std::fixed << std::setw(size);
	if (scientific) stringStream << std::scientific;
    stringStream << value;

    return stringStream.str();
}

void FlowResultsProcess::OutputMonitorValuesScreen(const int &innerStep)
{
	auto *flowPackage = flowPackageVector[currentLevel];

	const Scalar &currentTime = flowPackageVector[0]->GetUnsteadyStatus().currentTime;
	const int monitorInterval = unsteady ? 1 : flowPackage->GetFlowConfigure().GetControl().innerLoop.monitorInterval;

	// 0号进程输出
	if (flowPackage->GetMeshStruct().processorID == 0)
	{
		// 获取监测残值、最大残值、力系数的数值和名称
		const auto &monitorResidual = residualProcess.GetMonitorResidual();
		const auto &residualName = residualProcess.GetMonitorResidualName();
		const auto &maximumResidual = residualProcess.GetMaximumResidual();
		const auto &maximumResidualName = residualProcess.GetMaximumResidualName();
		const auto &monitorForce = forceProcess.GetMonitorForce();
		const auto &forceName = forceProcess.GetMonitorForceName();

		const bool &residualsInLog10 = flowPackage->GetFlowConfigure().GetMonitor().residuals.log10Flag;
		const bool &residualsNormalized = flowPackage->GetFlowConfigure().GetMonitor().residuals.normalizedFlag;

		const bool scientific = !residualsInLog10 || !residualsNormalized;
		const int residualLength = 16;
		const int forceLength = 16;

		// 输出监测量名称
		std::ostringstream stringStream;
		std::ostringstream stringStreamInfoFile;
		std::ostringstream stringStreamScreen;
		bool titleFlag;
		if (unsteady) titleFlag = (innerStep == 0);
		else          titleFlag = ((currentStep > 10 * monitorInterval) && currentStep % (10 * monitorInterval) == monitorInterval) || currentStep <= startStep + 1;
		if (titleFlag)
		{
			for (int i = 0; i < residualName.size(); i++) if (monitorResidualFlag[i]) stringStream << std::setw(residualLength) << residualName[i];
			for (int i = 0; i < maximumResidualName.size(); i++) if (maximumResidualFlag[i]) stringStream << std::setw(residualLength) << maximumResidualName[i];
			for (int i = 0; i < forceName.size(); i++) if (monitorForceFlag[i]) stringStream << std::setw(forceLength) << forceName[i];

			if (unsteady) stringStreamInfoFile << std::endl << std::string(stringStream.str().length() + 26, '=');
			stringStreamInfoFile << std::endl << std::setw(12) << "InnerStep" << std::setw(12) << "CFL" << stringStream.str();
			PrintFile(stringStreamInfoFile.str());

			if (unsteady) stringStreamScreen << std::endl << std::string(stringStream.str().length() + 54, '=');
			stringStreamScreen << std::endl << std::setw(12) << "InnerStep"
			                                << std::setw(12) << "CFL"
											<< stringStream.str()
											<< std::setw(12) << "timePerStep"
											<< std::setw(16) << "totalTime";
			PrintScreen(stringStreamScreen.str());
		}

		// 屏幕输出监测量
		stringStream.str("");
		for (int i = 0; i < monitorResidual.size(); i++) if (monitorResidualFlag[i]) stringStream << PrintString(monitorResidual[i], residualLength, 6, scientific);
		for (int i = 0; i < maximumResidual.size(); i++) if (maximumResidualFlag[i]) stringStream << PrintString(maximumResidual[i], residualLength, 6, scientific);
		for (int i = 0; i < monitorForce.size(); i++) if (monitorForceFlag[i]) stringStream << PrintString(monitorForce[i], forceLength, 6, true);

		stringStreamInfoFile.str("");
		stringStreamInfoFile << std::setw(12) << (unsteady ? innerStep : currentStep);
		stringStreamInfoFile << std::setw(12) << flowPackage->GetCFLNumber();
		stringStreamInfoFile << stringStream.str();
		PrintFile(stringStreamInfoFile.str());

		stringStreamScreen.str("");
		stringStreamScreen << std::setw(12) << (unsteady ? innerStep : currentStep)
			               << PrintString(flowPackage->GetCFLNumber(), 12, 6, false)
					       << stringStream.str()
						   << PrintString(CPUTimePerStep.GetElapsedTime(), 12, 6, false)
						   << PrintString(CPUTimeTotal.GetElapsedTime(), 16, 6, true);
		PrintScreen(stringStreamScreen.str());

		CPUTimePerStep.UpdateTime();
	}

	return;
}

void FlowResultsProcess::OutputMonitorValuesFile(const int &innerStep)
{
	if (currentLevel > 0) return;

	auto *flowPackage = flowPackageVector[currentLevel];
	const int &monitorInterval = flowPackage->GetFlowConfigure().GetControl().innerLoop.monitorInterval;

	const Scalar &currentTime = flowPackageVector[0]->GetUnsteadyStatus().currentTime;

	// 0号进程输出
	if (flowPackage->GetMeshStruct().processorID == 0)
	{
		// 获取监测残值、最大残值、力系数的数值和名称
		const auto &monitorResidual = residualProcess.GetMonitorResidual();
		const auto &residualName = residualProcess.GetMonitorResidualName();
		const auto &maximumResidual = residualProcess.GetMaximumResidual();
		const auto &maximumResidualName = residualProcess.GetMaximumResidualName();
		const auto &monitorForce = forceProcess.GetMonitorForce();
		const auto &forceName = forceProcess.GetMonitorForceName();

		const bool &residualsInLog10 = flowPackage->GetFlowConfigure().GetMonitor().residuals.log10Flag;
		const bool &residualsNormalized = flowPackage->GetFlowConfigure().GetMonitor().residuals.normalizedFlag;

		const bool scientific = !residualsInLog10 || !residualsNormalized;
		const int residualLength = 16;
		const int forceLength = 16;

		// 输出监测量名称
		std::ostringstream stringStream;
		std::ostringstream stringStreamInfoFile;
		std::ostringstream stringStreamScreen;
		if (newMonitorFileFlag)
		{
			for (int i = 0; i < residualName.size(); i++) stringStream << std::setw(residualLength) << residualName[i];
			for (int i = 0; i < maximumResidualName.size(); i++) stringStream << std::setw(residualLength) << maximumResidualName[i];
			for (int i = 0; i < forceName.size(); i++) stringStream << std::setw(forceLength) << forceName[i];

			outFile.open(resultPath + resultName + "_res" + ".dat", std::ios::out);
			outFile << "variables = iter"
					<< std::setw(16) << "CPUTimeTot"
					<< std::setw(12) << "CFL" << stringStream.str() << std::endl;
			outFile.close();

			outForceFile.open(resultPath + resultName + "_force" + ".dat", std::ios::out);
			stringStream.str("");
			stringStream
				<< std::setw(15) << "BoundaryID"
				<< std::setw(15) << "Fx_Pressure"
				<< std::setw(15) << "Fy_Pressure"
				<< std::setw(15) << "Fz_Pressure"
				<< std::setw(15) << "Fx_Viscous"
				<< std::setw(15) << "Fy_Viscous"
				<< std::setw(15) << "Fz_Viscous"
				<< std::setw(15) << "Mx_Pressure"
				<< std::setw(15) << "My_Pressure"
				<< std::setw(15) << "Mz_Pressure"
				<< std::setw(15) << "Mx_Viscous"
				<< std::setw(15) << "My_Viscous"
				<< std::setw(15) << "Mz_Viscous"
				<< std::setw(15) << "AreaSum_x"
				<< std::setw(15) << "AreaSum_y"
				<< std::setw(15) << "AreaSum_z";

			outForceFile << "variables = iter" << stringStream.str() << std::endl;
			outForceFile.close();

			newMonitorFileFlag = false;
		}

		// 屏幕输出监测量
		stringStream.str("");
		for (int i = 0; i < monitorResidual.size(); i++) stringStream << PrintString(monitorResidual[i], residualLength, 6, scientific);
		for (int i = 0; i < maximumResidual.size(); i++) stringStream << PrintString(maximumResidual[i], residualLength, 6, scientific);
		for (int i = 0; i < monitorForce.size(); i++) stringStream << PrintString(monitorForce[i], forceLength, 6, true);

		outFile.open(resultPath + resultName + "_res" + ".dat", std::ios::out | std::ios::app);
		outFile << std::setw(16) << currentStep;
		outFile << PrintString(CPUTimeTotal.GetElapsedTime(), 16, 6, true);
		outFile << PrintString(flowPackage->GetCFLNumber(), 12, 6, false);
		outFile << stringStream.str() << std::endl;
		outFile.close();

		//文件输出原始力数据
		outForceFile.open(resultPath + resultName + "_force" + ".dat", std::ios::out | std::ios::app);
		this->forceProcess.OutputForceDimensional(outForceFile, currentStep);
		outForceFile.close();
	}

	return;
}

void FlowResultsProcess::OutputMonitorValues(const int &innerStep)
{
	this->OutputMonitorValuesScreen(innerStep);
	this->OutputMonitorValuesFile(innerStep);
}

void FlowResultsProcess::OutputMonitorValuesOutLoop()
{
	auto *flowPackage = flowPackageVector[currentLevel];

	// 0号进程输出
	if (flowPackage->GetMeshStruct().processorID == 0)
	{
		// 获取监测残值、最大残值、力系数的数值和名称
		const auto &monitorResidual = residualProcess.GetMonitorResidual();
		const auto &residualName = residualProcess.GetMonitorResidualName();
		const auto &maximumResidual = residualProcess.GetMaximumResidual();
		const auto &maximumResidualName = residualProcess.GetMaximumResidualName();
		const auto &monitorForce = forceProcess.GetMonitorForce();
		const auto &forceName = forceProcess.GetMonitorForceName();

		const bool &residualsInLog10 = flowPackage->GetFlowConfigure().GetMonitor().residuals.log10Flag;
		const bool &residualsNormalized = flowPackage->GetFlowConfigure().GetMonitor().residuals.normalizedFlag;

		const bool scientific = !residualsInLog10 || !residualsNormalized;
		const int residualLength = 16;
		const int forceLength = 16;

		// 输出监测量名称
		std::ostringstream stringStream;
		std::ostringstream stringStreamInfoFile;
		std::ostringstream stringStreamScreen;
		if (dualTimeFlag || timeStep % 10 == 1)
		{
			for (int i = 0; i < monitorResidualFlag.size(); i++) if (monitorResidualFlag[i]) stringStream << std::setw(residualLength) << residualName[i];
			for (int i = 0; i < maximumResidualFlag.size(); i++) if (maximumResidualFlag[i]) stringStream << std::setw(residualLength) << maximumResidualName[i];
			for (int i = 0; i < monitorForceFlag.size(); i++)    if (monitorForceFlag[i]) stringStream << std::setw(forceLength) << forceName[i];

			if (newUnsteadyMonitorFileFlag && currentLevel == 0)
			{
				outFile.open(resultPath + resultName + "_unsteady" + ".dat", std::ios::out);
				outFile << "variables = TimeStep    PhyTime(s)    MeanStep" << stringStream.str() << std::endl;
				outFile.close();
				newUnsteadyMonitorFileFlag = false;
			}

			stringStreamInfoFile.str("");
			if (dualTimeFlag) stringStreamInfoFile << std::string(stringStream.str().length() + 26, '-') << std::endl;
			else              stringStreamInfoFile << std::endl;
			stringStreamInfoFile << std::setw(12) << "TimeStep";
			stringStreamInfoFile << std::setw(12) << "PhyTime(s)";
			stringStreamInfoFile << stringStream.str();
			PrintFile(stringStreamInfoFile.str());

			stringStreamScreen.str("");
			if (dualTimeFlag) stringStreamScreen << std::string(stringStream.str().length() + 54, '-') << std::endl;
			else              stringStreamScreen << std::endl;
			stringStreamScreen << std::setw(12) << "TimeStep";
			stringStreamScreen << std::setw(12) << "PhyTime(s)";
			stringStreamScreen << stringStream.str();
			stringStreamScreen << std::setw(12) << "timePerStep";
			stringStreamScreen << std::setw(16) << "totalTime";

			PrintScreen(stringStreamScreen.str());
		}

		// 屏幕输出监测量
		stringStream.str("");
		for (int i = 0; i < monitorResidual.size(); i++) if (monitorResidualFlag[i]) stringStream << PrintString(monitorResidual[i], residualLength, 6, scientific);
		for (int i = 0; i < maximumResidual.size(); i++) if (maximumResidualFlag[i]) stringStream << PrintString(maximumResidual[i], residualLength, 6, scientific);
		for (int i = 0; i < monitorForce.size(); i++) if (monitorForceFlag[i]) stringStream << PrintString(monitorForce[i], forceLength, 6, true);

		if (currentLevel == 0)
		{
			outFile.open(resultPath + resultName + "_unsteady" + ".dat", std::ios::out | std::ios::app);
			outFile << std::setw(20) << timeStep;
			outFile << std::setw(14) << std::scientific << std::setprecision(4) << currentTime;
			outFile << std::setw(12) << meanStep;

			outFile << std::setw(22) << std::scientific << std::setprecision(6);
			outFile << stringStream.str() << std::endl;
			outFile.close();
		}

		stringStreamInfoFile.str("");
		stringStreamInfoFile << std::setw(12) << timeStep;
		stringStreamInfoFile << std::setw(12) << std::scientific << std::setprecision(4) << currentTime;

		stringStreamInfoFile << stringStream.str();
		if (dualTimeFlag) stringStreamInfoFile << std::endl << std::string(stringStream.str().length() + 26, '=');
		PrintFile(stringStreamInfoFile.str());

		stringStreamScreen.str("");
		stringStreamScreen << std::setw(12) << timeStep;
		stringStreamScreen << std::setw(12) << std::scientific << std::setprecision(4) << currentTime;
		stringStreamScreen << stringStream.str();
		stringStreamScreen << std::setw(12) << std::setprecision(6) << std::fixed << CPUTimeOutLoop.GetElapsedTime();
		stringStreamScreen << std::setw(16) << std::setprecision(6) << std::fixed << CPUTimeTotal.GetElapsedTime();

		if (dualTimeFlag) stringStreamScreen << std::endl << std::string(stringStream.str().length() + 54, '=');
		PrintScreen(stringStreamScreen.str());

		CPUTimeOutLoop.UpdateTime();
	}

	return;
}

void FlowResultsProcess::MonitorPerStep(const int &innerStep)
{
	// 更新currentStep
	currentStep++;

	// 输出第一步及满足整除间隔的计算步
	const int &monitorInterval = unsteady ? 1 : flowPackageVector[currentLevel]->GetFlowConfigure().GetControl().innerLoop.monitorInterval;
	if (currentStep % monitorInterval == 0 || currentStep == startStep + 1)
	{
		this->CalculateMonitorValues();
		this->OutputMonitorValues(innerStep);
	}

	return;
}

void FlowResultsProcess::MonitorPerStepOutLoop()
{
	// 更新currentStep
	timeStep++;

	const Scalar  &currentTimeTemp = flowPackageVector[0]->GetUnsteadyStatus().currentTime;
	currentTime = currentTimeTemp + startTime;

	this->CalculateMonitorValues();
	this->OutputMonitorValuesOutLoop();

	return;
}

int FlowResultsProcess::GetStartMeanStep()
{
	std::fstream outFile;
	const auto &flowConfigure = flowPackageVector[0]->GetFlowConfigure();
	const std::string &resultName = flowConfigure.GetCaseName();
	const std::string &resultPath = flowConfigure.GetControl().resultSavePath;
	outFile.open(resultPath + resultName + "_unsteady" + ".dat", std::fstream::in);
	if (!outFile.is_open()) return 0;

	// 读取残差文件第一行（变量名），并填充到tileVector容器中
	std::string stringTemp;
	getline(outFile, stringTemp);
	std::istringstream stringStream(stringTemp);
	std::vector<std::string> tileVector;
	while (!stringStream.eof())
	{
		stringStream >> stringTemp;
		if (!stringTemp.empty()) tileVector.push_back(stringTemp);
	}
	if (!tileVector.empty())
		tileVector.erase(tileVector.begin(), tileVector.begin() + 2);
	else
		FatalError("ResidualProcess::GetStartMeanStep: no results");

	// 读取指定行（监测量），并将数值填充到valueVector中
	int step = 0;
	double restartTime;
	int restartMeanStep = 0;
	std::vector<Scalar> valueVector;
	while (getline(outFile, stringTemp))
	{
		if (!stringTemp.empty())
		{
			std::istringstream stringStream1(stringTemp);
			stringStream1 >> step;
			stringStream1 >> restartTime;
			stringStream1 >> restartMeanStep;

			if (step == startStep) break;
		}
	}
	return restartMeanStep + 1;
}

void FlowResultsProcess::GetReStartValues()
{
	std::fstream outFile;
	const auto &flowConfigure = flowPackageVector[0]->GetFlowConfigure();
	const std::string &resultName = flowConfigure.GetCaseName();
	const std::string &resultPath = flowConfigure.GetControl().resultSavePath;
	outFile.open(resultPath + resultName + "_unsteady" + ".dat", std::fstream::in);
	if (!outFile.is_open()) return;

	// 读取残差文件第一行（变量名），并填充到tileVector容器中
	std::string stringTemp;
	getline(outFile, stringTemp);
	std::istringstream stringStream(stringTemp);
	std::vector<std::string> tileVector;
	while (!stringStream.eof())
	{
		stringStream >> stringTemp;
		if (!stringTemp.empty()) tileVector.push_back(stringTemp);
	}
	if (!tileVector.empty())
		tileVector.erase(tileVector.begin(), tileVector.begin() + 2);
	else
		FatalError("ResidualProcess::GetReStartValues: no results");

	// 读取指定行（监测量），并将数值填充到valueVector中
	int step = 0;
	double restartTime;
	int restartMeanStep = 0;
	std::vector<Scalar> valueVector;
	while (getline(outFile, stringTemp))
	{
		if (!stringTemp.empty())
		{
			std::istringstream stringStream1(stringTemp);
			stringStream1 >> step;
			stringStream1 >> restartTime;
			stringStream1 >> restartMeanStep;

			if (step == startStep) break;
		}
	}
	startTime = restartTime;
	startMeanStep = restartMeanStep + 1;

	//如果重置时均值，startMeanStep置为0
	if (flowConfigure.GetControl().outerLoop.resetAverage) startMeanStep = 0;
}

int FlowResultsProcess::GetInnerStep()
{
	std::fstream outFile;
	const auto &flowConfigure = flowPackageVector[0]->GetFlowConfigure();
	const std::string &resultName = flowConfigure.GetCaseName();
	const std::string &resultPath = flowConfigure.GetControl().resultSavePath;
	outFile.open(resultPath + resultName + "_res" + ".dat", std::fstream::in);

	// 读取残差文件第一行（变量名），并填充到tileVector容器中
	std::string stringTemp;
	getline(outFile, stringTemp);
	std::istringstream stringStream(stringTemp);
	std::vector<std::string> tileVector;
	while (!stringStream.eof())
	{
		stringStream >> stringTemp;
		if (!stringTemp.empty()) tileVector.push_back(stringTemp);
	}
	if (!tileVector.empty())
		tileVector.erase(tileVector.begin(), tileVector.begin() + 2);
	else
		FatalError("ResidualProcess::GetInnerStep: no results");

	// 读取指定行（监测量），并将数值填充到valueVector中
	int step = 0;
	std::vector<Scalar> valueVector;
	while (getline(outFile, stringTemp))
	{
		if (!stringTemp.empty())
		{
			std::istringstream stringStream1(stringTemp);
			stringStream1 >> step;
		}
	}
	return step;
}