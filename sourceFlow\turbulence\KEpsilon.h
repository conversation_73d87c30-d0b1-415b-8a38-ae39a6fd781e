﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file K_Epsilon.h
//! <AUTHOR>
//! @brief 湍流类：K_Epsilon
//! @date 2021-04-05
//
//------------------------------修改日志----------------------------------------
// 2021-04-05 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_KEpsilon_
#define _sourceFlow_turbulence_KEpsilon_

#include "sourceFlow/turbulence/BaseTurbulence.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流K_Epsilon类
 * 
 */
class  KEpsilon: public BaseTurbulence
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
    KEpsilon(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
    ~KEpsilon();

    /**
     * @brief 计算湍流粘性系数
     * 
     */
    void CalculateMuTurbulent();

    /**
     * @brief 各项残值计算前的准备工作
     * 
     */
	void PreCalculate(){};
    
    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();
    
    /**
     * @brief 湍流量限制
     * 
     * @return int 
     */
    int CheckAndLimit();    

    /**
     * @brief 初始化湍流流场
     * 
     */
    void InitializeTurbulentField();

protected:
    /**
     * @brief 计算扩散项面心处扩散系数
     * 
     * @param[in] faceID 面编号
     * @return std::vector<std::pair<Scalar, Scalar>> 
     */
    std::vector<std::pair<Scalar, Scalar>> CalculateGammaFace(const int &faceID);
    
protected:
    ElementField<Scalar> &k; ///< 湍动能
    ElementField<Scalar> &epsilon; ///< 耗散率

private:
    ElementField<Vector> *gradientK; ///< 湍动能梯度
    ElementField<Vector> *gradientEpsilon; ///< 耗散率梯度

    ElementField<Scalar> &residualK; ///< 湍动能残值
    ElementField<Scalar> &residualEpsilon; ///< 耗散率残值

    const Scalar Cmu; ///< 涡黏系数参数
    const Scalar Ce1; ///< 计算耗散生成项常数
    const Scalar Ce2; ///< 计算耗散破坏项常数
    
    const Scalar two3; ///< 常量：2/3

    const Scalar sigmaK; ///< 湍动能耗散项系数
    const Scalar sigmaE; ///< 耗散方程扩散项系数

    Scalar kFree; ///< 远场湍动能
    Scalar epsilonFree; ///< 远场耗散率

};

} // namespace Turbulence
#endif 
