namespace Eigen {

/** \page GettingStarted Getting started

\eigenAutoToc

This is a very short guide on how to get started with Eigen. It has a dual purpose. It serves as a minimal introduction to the Eigen library for people who want to start coding as soon as possible. You can also read this page as the first part of the Tutorial, which explains the library in more detail; in this case you will continue with \ref TutorialMatrixClass.

\section GettingStartedInstallation How to "install" Eigen?

In order to use Eigen, you just need to download and extract <PERSON><PERSON><PERSON>'s source code (see <a href="http://eigen.tuxfamily.org/index.php?title=Main_Page#Download">the wiki</a> for download instructions). In fact, the header files in the \c Eigen subdirectory are the only files required to compile programs using Eigen. The header files are the same for all platforms. It is not necessary to use CMake or install anything.


\section GettingStartedFirstProgram A simple first program

Here is a rather simple program to get you started.

\include QuickStart_example.cpp

We will explain the program after telling you how to compile it.


\section GettingStartedCompiling Compiling and running your first program

There is no library to link to. The only thing that you need to keep in mind when compiling the above program is that the compiler must be able to find the Eigen header files. The directory in which you placed <PERSON>ige<PERSON>'s source code must be in the include path. With GCC you use the -I option to achieve this, so you can compile the program with a command like this:

\code g++ -I /path/to/eigen/ my_program.cpp -o my_program \endcode

On Linux or Mac OS X, another option is to symlink or copy the Eigen folder into /usr/local/include/. This way, you can compile the program with:

\code g++ my_program.cpp -o my_program \endcode

When you run the program, it produces the following output:

\include QuickStart_example.out


\section GettingStartedExplanation Explanation of the first program

The Eigen header files define many types, but for simple applications it may be enough to use only the \c MatrixXd type. This represents a matrix of arbitrary size (hence the \c X in \c MatrixXd), in which every entry is a \c double (hence the \c d in \c MatrixXd). See the \ref QuickRef_Types "quick reference guide" for an overview of the different types you can use to represent a matrix.

The \c Eigen/Dense header file defines all member functions for the MatrixXd type and related types (see also the \ref QuickRef_Headers "table of header files"). All classes and functions defined in this header file (and other Eigen header files) are in the \c Eigen namespace. 

The first line of the \c main function declares a variable of type \c MatrixXd and specifies that it is a matrix with 2 rows and 2 columns (the entries are not initialized). The statement <tt>m(0,0) = 3</tt> sets the entry in the top-left corner to 3. You need to use round parentheses to refer to entries in the matrix. As usual in computer science, the index of the first index is 0, as opposed to the convention in mathematics that the first index is 1.

The following three statements sets the other three entries. The final line outputs the matrix \c m to the standard output stream.


\section GettingStartedExample2 Example 2: Matrices and vectors

Here is another example, which combines matrices with vectors. Concentrate on the left-hand program for now; we will talk about the right-hand program later.

<table class="manual">
<tr><th>Size set at run time:</th><th>Size set at compile time:</th></tr>
<tr><td>
\include QuickStart_example2_dynamic.cpp
</td>
<td>
\include QuickStart_example2_fixed.cpp
</td></tr></table>

The output is as follows:

\include QuickStart_example2_dynamic.out


\section GettingStartedExplanation2 Explanation of the second example

The second example starts by declaring a 3-by-3 matrix \c m which is initialized using the \link DenseBase::Random(Index,Index) Random() \endlink method with random values between -1 and 1. The next line applies a linear mapping such that the values are between 10 and 110. The function call \link DenseBase::Constant(Index,Index,const Scalar&) MatrixXd::Constant\endlink(3,3,1.2) returns a 3-by-3 matrix expression having all coefficients equal to 1.2. The rest is standard arithmetics.

The next line of the \c main function introduces a new type: \c VectorXd. This represents a (column) vector of arbitrary size. Here, the vector \c v is created to contain \c 3 coefficients which are left unitialized. The one but last line uses the so-called comma-initializer, explained in \ref TutorialAdvancedInitialization, to set all coefficients of the vector \c v to be as follows:

\f[
v =
\begin{bmatrix}
  1 \\
  2 \\
  3
\end{bmatrix}.
\f]

The final line of the program multiplies the matrix \c m with the vector \c v and outputs the result.

Now look back at the second example program. We presented two versions of it. In the version in the left column, the matrix is of type \c MatrixXd which represents matrices of arbitrary size. The version in the right column is similar, except that the matrix is of type \c Matrix3d, which represents matrices of a fixed size (here 3-by-3). Because the type already encodes the size of the matrix, it is not necessary to specify the size in the constructor; compare <tt>MatrixXd m(3,3)</tt> with <tt>Matrix3d m</tt>. Similarly, we have \c VectorXd on the left (arbitrary size) versus \c Vector3d on the right (fixed size). Note that here the coefficients of vector \c v are directly set in the constructor, though the same syntax of the left example could be used too.

The use of fixed-size matrices and vectors has two advantages. The compiler emits better (faster) code because it knows the size of the matrices and vectors. Specifying the size in the type also allows for more rigorous checking at compile-time. For instance, the compiler will complain if you try to multiply a \c Matrix4d (a 4-by-4 matrix) with a \c Vector3d (a vector of size 3). However, the use of many types increases compilation time and the size of the executable. The size of the matrix may also not be known at compile-time. A rule of thumb is to use fixed-size matrices for size 4-by-4 and smaller.


\section GettingStartedConclusion Where to go from here?

It's worth taking the time to read the  \ref TutorialMatrixClass "long tutorial".

However if you think you don't need it, you can directly use the classes documentation and our \ref QuickRefPage.

\li \b Next: \ref TutorialMatrixClass

*/

}

