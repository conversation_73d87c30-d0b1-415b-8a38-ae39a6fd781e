// The Boost Sort library cumulative header.

//          Copyright <PERSON> 2014
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

// See http://www.boost.org/libs/sort/ for library home page.

#ifndef BOOST_SORT_HPP
#define BOOST_SORT_HPP

/*
Cumulative include for the Boost Sort library
*/

#include <boost/sort/spreadsort/spreadsort.hpp>

#endif
