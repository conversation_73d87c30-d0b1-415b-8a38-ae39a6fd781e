 #pragma once
#include "CodeContract.h"
#include "StandardIntegralTypes.h"
namespace tecplot {
 #define SIZE_IN_BITS(___1837) (sizeof(___1837)*8)
 #define VALID_ITEM_ADDRESS_PARTITION(itemAddressPartition) \
 ((itemAddressPartition) != tecplot::___2090::INVALID_PARTITION && \
 (SIZE_IN_BITS(tecplot::___2090::___2980) == tecplot::___2090::PartitionBitSize || \
 (SIZE_IN_BITS(tecplot::___2090::___2980) > tecplot::___2090::PartitionBitSize && \
 uint64_t(itemAddressPartition) >> tecplot::___2090::PartitionBitSize == uint64_t(0))))
 #define VALID_ITEM_ADDRESS_SUBZONE_OFFSET(itemAddressSubzoneOffset) \
 ((itemAddressSubzoneOffset) != tecplot::___2090::INVALID_SUBZONE_OFFSET && \
 (SIZE_IN_BITS(tecplot::___2090::SubzoneOffset_t) == tecplot::___2090::SubzoneOffsetBitSize || \
 (SIZE_IN_BITS(tecplot::___2090::SubzoneOffset_t) > tecplot::___2090::SubzoneOffsetBitSize && \
 uint64_t(itemAddressSubzoneOffset) >> tecplot::___2090::SubzoneOffsetBitSize == uint64_t(0))))
 #define VALID_ITEM_ADDRESS_ITEM_OFFSET(itemAddressItemOffset) \
 ((itemAddressItemOffset) != tecplot::___2090::INVALID_ITEM_OFFSET && \
 (SIZE_IN_BITS(tecplot::___2090::ItemOffset_t) == tecplot::___2090::ItemOffsetBitSize || \
 (SIZE_IN_BITS(tecplot::___2090::ItemOffset_t) > tecplot::___2090::ItemOffsetBitSize && \
 uint64_t(itemAddressItemOffset) >> tecplot::___2090::ItemOffsetBitSize == uint64_t(0))))
 #define VALID_UNIFORM_ITEM_ADDRESS(___2089) \
 ((___2089).___14() == tecplot::___2090::UniformAddressType)
 #define VALID_SZL_ITEM_ADDRESS(___2089) \
 ((___2089).___14() == tecplot::___2090::SzlAddressType && \
 VALID_ITEM_ADDRESS_PARTITION((___2089).___2977()) && \
 VALID_ITEM_ADDRESS_SUBZONE_OFFSET((___2089).subzoneOffset()) && \
 VALID_ITEM_ADDRESS_ITEM_OFFSET((___2089).itemOffset()))
 #define VALID_ITEM_ADDRESS(___2089) \
 (VALID_UNIFORM_ITEM_ADDRESS(___2089) || \
 VALID_SZL_ITEM_ADDRESS(___2089))
 #define VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(subzoneAddress) \
 (VALID_ITEM_ADDRESS_PARTITION((subzoneAddress).___2977()) && \
 VALID_ITEM_ADDRESS_SUBZONE_OFFSET((subzoneAddress).subzoneOffset()))
class ___2090 { public: typedef uint32_t ___2980; typedef uint64_t SubzoneIndex_t; typedef uint32_t SubzoneOffset_t; typedef uint16_t ItemOffset_t; typedef int64_t ___4261; static uint32_t const ___15 = 1U; static uint32_t const PartitionBitSize = 23U; static uint32_t const SubzoneOffsetBitSize = 32U; static uint32_t const ItemOffsetBitSize = 8U; static uint32_t const ___4259 = 63U; static uint32_t const SzlAddressType     = 0U; static uint32_t const UniformAddressType = 1U; static ___2980 const INVALID_PARTITION = static_cast<___2980>((uint64_t(1) << PartitionBitSize) - uint64_t(1)); static ___2980 const UNKNOWN_PARTITION = 0; static ___2980 const MAX_PARTITION = INVALID_PARTITION - static_cast<___2980>(1); static SubzoneOffset_t const INVALID_SUBZONE_OFFSET = static_cast<SubzoneOffset_t>((uint64_t(1) << SubzoneOffsetBitSize) - uint64_t(1)); static SubzoneOffset_t const MAX_SUBZONE_OFFSET = INVALID_SUBZONE_OFFSET - static_cast<SubzoneOffset_t>(1); static ItemOffset_t const INVALID_ITEM_OFFSET = static_cast<ItemOffset_t>((uint64_t(1) << ItemOffsetBitSize)); static ItemOffset_t const MAX_ITEM_OFFSET = INVALID_ITEM_OFFSET - static_cast<ItemOffset_t>(1); class SubzoneAddress { public: inline SubzoneAddress() { INVARIANT(SIZE_IN_BITS(*this) == 64U); } inline SubzoneAddress( ___2980     ___2977, SubzoneOffset_t subzoneOffset) : m_partition(___2977) , m_subzoneOffset(subzoneOffset) { INVARIANT(SIZE_IN_BITS(*this) == 64U); REQUIRE(VALID_ITEM_ADDRESS_PARTITION(m_partition)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_OFFSET(m_subzoneOffset)); } inline ___2980 ___2977() const { REQUIRE(VALID_ITEM_ADDRESS_PARTITION(m_partition)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_OFFSET(m_subzoneOffset)); return m_partition; } inline SubzoneOffset_t subzoneOffset() const { REQUIRE(VALID_ITEM_ADDRESS_PARTITION(m_partition)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_OFFSET(m_subzoneOffset)); return m_subzoneOffset; } inline bool operator==(SubzoneAddress const& ___2888) const { REQUIRE(VALID_ITEM_ADDRESS_PARTITION(m_partition)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_OFFSET(m_subzoneOffset)); return m_partition == ___2888.m_partition && m_subzoneOffset == ___2888.m_subzoneOffset; } private: ___2980     m_partition; SubzoneOffset_t m_subzoneOffset; }; inline ___2090() { INVARIANT(___4306()); } inline ___2090( ___2980     ___2977, SubzoneOffset_t subzoneOffset, ItemOffset_t    itemOffset) { INVARIANT(___4306()); REQUIRE(VALID_ITEM_ADDRESS_PARTITION(___2977)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_OFFSET(subzoneOffset)); REQUIRE(VALID_ITEM_ADDRESS_ITEM_OFFSET(itemOffset)); ___478(___15 + ___4259 == SIZE_IN_BITS(___2090)); ___478(___15 + PartitionBitSize + SubzoneOffsetBitSize + ItemOffsetBitSize == SIZE_IN_BITS(___2090)); m.___14                  = SzlAddressType; m.szlItemAddress.___2977     = ___2977; m.szlItemAddress.subzoneOffset = subzoneOffset; m.szlItemAddress.itemOffset    = itemOffset; } inline ___2090( SubzoneAddress subzoneAddress, ItemOffset_t   itemOffset) { INVARIANT(___4306()); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(subzoneAddress)); REQUIRE(VALID_ITEM_ADDRESS_ITEM_OFFSET(itemOffset)); ___478(___15 + ___4259 == SIZE_IN_BITS(___2090)); ___478(___15 + PartitionBitSize + SubzoneOffsetBitSize + ItemOffsetBitSize == SIZE_IN_BITS(___2090)); m.___14                  = SzlAddressType; m.szlItemAddress.___2977     = subzoneAddress.___2977(); m.szlItemAddress.subzoneOffset = subzoneAddress.subzoneOffset(); m.szlItemAddress.itemOffset    = itemOffset; } inline explicit ___2090(___4261 ___4258) { INVARIANT(___4306()); ___478(___15 + ___4259 == SIZE_IN_BITS(___2090)); ___478(___15 + PartitionBitSize + SubzoneOffsetBitSize + ItemOffsetBitSize == SIZE_IN_BITS(___2090)); m.___14           = UniformAddressType; m.___4256.___2865 = ___4258; } inline bool ___2065() const { ENSURE(m.___14 == SzlAddressType || m.___14 == UniformAddressType); return m.___14 == UniformAddressType; } inline bool isSzlItem() const { ENSURE(m.___14 == SzlAddressType || m.___14 == UniformAddressType); return m.___14 == SzlAddressType; } inline uint32_t ___14() const { ENSURE(m.___14 == SzlAddressType || m.___14 == UniformAddressType); return m.___14; } inline SubzoneAddress subzoneAddress() const { REQUIRE(m.___14 == SzlAddressType); return SubzoneAddress(m.szlItemAddress.___2977, m.szlItemAddress.subzoneOffset); } inline ___2980 ___2977() const { REQUIRE(m.___14 == SzlAddressType); ENSURE(VALID_ITEM_ADDRESS_PARTITION(m.szlItemAddress.___2977)); return m.szlItemAddress.___2977; } inline SubzoneOffset_t subzoneOffset() const { REQUIRE(m.___14 == SzlAddressType); ENSURE(VALID_ITEM_ADDRESS_SUBZONE_OFFSET(m.szlItemAddress.subzoneOffset));
return m.szlItemAddress.subzoneOffset; } inline ItemOffset_t itemOffset() const { REQUIRE(m.___14 == SzlAddressType); ENSURE(VALID_ITEM_ADDRESS_ITEM_OFFSET(m.szlItemAddress.itemOffset)); return m.szlItemAddress.itemOffset; } inline ___4261 ___4258() const { REQUIRE(___2065()); return m.___4256.___2865; } inline uint64_t toRawBits() const { return m.___3270; } static ___2090 fromRawBits(uint64_t ___3270) { ___2090 ___3358; ___3358.m.___3270 = ___3270; ENSURE(VALID_ITEM_ADDRESS(___3358)); return ___3358; } inline bool operator==(___2090 const& ___2888) const { REQUIRE(VALID_ITEM_ADDRESS(___2888)); return m.___3270 == ___2888.m.___3270; } private:
 #if !defined NO_ASSERTS
static bool ___4306() { return (SIZE_IN_BITS(___2090) <= 64U && SIZE_IN_BITS(___2980)     >= PartitionBitSize     && SIZE_IN_BITS(SubzoneOffset_t) >= SubzoneOffsetBitSize && SIZE_IN_BITS(ItemOffset_t)    >  ItemOffsetBitSize    && SIZE_IN_BITS(___4261) >= ___4259 && ___15 + ___4259 == SIZE_IN_BITS(___2090) && ___15 + PartitionBitSize + SubzoneOffsetBitSize + ItemOffsetBitSize == SIZE_IN_BITS(___2090)); }
 #endif
struct SzlItemAddress_s { uint64_t ___14:___15; uint64_t ___2977:PartitionBitSize; uint64_t subzoneOffset:SubzoneOffsetBitSize; uint64_t itemOffset:ItemOffsetBitSize; }; struct ___4257 { int64_t ___14:___15; int64_t ___2865:___4259; }; union { uint64_t         ___14:___15; SzlItemAddress_s szlItemAddress; ___4257 ___4256; uint64_t         ___3270; } m; }; inline bool operator<( ___2090 const& ___2229, ___2090 const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS(___3392)); uint32_t const ___2230 = ___2229.___14(); uint32_t const ___3393 = ___3392.___14(); if (___2230 != ___3393) { return ___2230 < ___3393; } else if (___2230 == tecplot::___2090::UniformAddressType) { return ___2229.___4258() < ___3392.___4258(); } else if (___2229.___2977() == ___3392.___2977()) { if (___2229.subzoneOffset() == ___3392.subzoneOffset()) return ___2229.itemOffset() < ___3392.itemOffset(); else return ___2229.subzoneOffset() < ___3392.subzoneOffset(); } else { return ___2229.___2977() < ___3392.___2977(); } } inline bool operator!=( ___2090 const& ___2229, ___2090 const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS(___3392)); return !(___2229 == ___3392); } inline bool operator<=( ___2090 const& ___2229, ___2090 const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS(___3392)); return ___2229 < ___3392 || ___2229 == ___3392; } inline bool operator>( ___2090 const& ___2229, ___2090 const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS(___3392)); return !(___2229 < ___3392) && !(___2229 == ___3392); } inline bool operator>=( ___2090 const& ___2229, ___2090 const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS(___3392)); return !(___2229 < ___3392); } inline bool operator<( ___2090::SubzoneAddress const& ___2229, ___2090::SubzoneAddress const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___3392)); if (___2229.___2977() == ___3392.___2977()) return ___2229.subzoneOffset() < ___3392.subzoneOffset(); else return ___2229.___2977() < ___3392.___2977(); } inline bool operator!=( ___2090::SubzoneAddress const& ___2229, ___2090::SubzoneAddress const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___3392)); return !(___2229 == ___3392); } inline bool operator<=( ___2090::SubzoneAddress const& ___2229, ___2090::SubzoneAddress const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___3392)); return ___2229 < ___3392 || ___2229 == ___3392; } inline bool operator>( ___2090::SubzoneAddress const& ___2229, ___2090::SubzoneAddress const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___3392)); return !(___2229 < ___3392) && !(___2229 == ___3392); } inline bool operator>=( ___2090::SubzoneAddress const& ___2229, ___2090::SubzoneAddress const& ___3392) { REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___2229)); REQUIRE(VALID_ITEM_ADDRESS_SUBZONE_ADDRESS(___3392)); return !(___2229 < ___3392); } }
