﻿#include "sourceFlow/turbulence/SpalartAllmarasDDES.h"

namespace Turbulence
{

SpalartAllmarasDDES::SpalartAllmarasDDES(Package::FlowPackage &flowPackage)
    :SpalartAllmaras(flowPackage),
	rKappa2(5.948839976)
{
	Cdes = flowPackage.GetFlowConfigure().GetModel().DESconstant;
	Cdt = flowPackage.GetFlowConfigure().GetModel().Cdt;
	ldOverlr = flowPackage.GetField().ldOverlr;
	shieldingFunction = flowPackage.GetField().shieldingFunction;

	CalculateFilterWidth();

	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar lrans = Max(mesh->GetNearWallDistance(elementID), SMALL);
		const Scalar deltaMaxTemp = deltaMax->GetValue(elementID);
		const Scalar ldes = Min(lrans, Cdes*deltaMaxTemp);

		ldOverlr->SetValue(elementID, ldes / lrans);
	}
}

SpalartAllmarasDDES::~SpalartAllmarasDDES()
{
}


void SpalartAllmarasDDES::AddSourceResidual()
{
	// 仅细网格计算湍流方程的源项
	// if (currentLevel > 0) return;

	//源项残差按单元循环
	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar lrans = Max(mesh->GetNearWallDistance(elementID), SMALL);
		const Scalar rdw2 = 1.0 / (lrans * lrans);
                const Scalar &rhoTemp = rho.GetValue(elementID);
		const Scalar &muI = muLaminar.GetValue(elementID);
		const Scalar &mutI = muTurbulent->GetValue(elementID);
		const Tensor &gradUI = gradientU.GetValue(elementID);
		const Scalar gradUM = sqrt(gradUI && gradUI);
		const Scalar rd = Cdt * (muI + mutI) / rhoTemp / (gradUM + SMALL) * rdw2 * rKappa2;
		const Scalar fd = 1 - tanh(rd * rd * rd);
		const Scalar deltaTemp = deltaMax->GetValue(elementID);
		const Scalar dDeltaTemp = Max(0.0, lrans - Cdes * deltaTemp);
		const Scalar ldes = lrans - fd * dDeltaTemp;
		const Scalar ldOverlrTemp = ldes / lrans;

		shieldingFunction->SetValue(elementID, fd);
		ldOverlr->SetValue(elementID, ldOverlrTemp);
		SpalartAllmaras::AddSourceResidual(elementID, ldOverlrTemp, 1.0);
	}
}


}//namespace Turbulence
