// Copyright 2002 The Trustees of Indiana University.

// Use, modification and distribution is subject to the Boost Software 
// License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

//  Boost.MultiArray Library
//  Authors: <AUTHORS>
//           <PERSON>
//           <PERSON>
//  See http://www.boost.org/libs/multi_array for documentation.

#ifndef COLLECTION_CONCEPT_RG103101_HPP
#define COLLECTION_CONCEPT_RG103101_HPP

#include "boost/concept_check.hpp"

namespace boost {
namespace detail {
namespace multi_array { // Old location for this
  using boost::CollectionConcept;
}
}

}
#endif // COLLECTION_CONCEPT_RG103101_HPP
