﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Tecplot.h
//! <AUTHOR>
//! @brief 用于生成Tecplot后处理格式文件的类
//! @date 2021-04-18
//
//------------------------------修改日志----------------------------------------
// 2021-04-18 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _basic_postTools_Tecplot_
#define _basic_postTools_Tecplot_

#if defined(_EnableTecio_)
#include "TECIO.h"
#endif

#include "basic/postTools/BasePost.h"

/**
 * @brief 后处理命名空间
 * 
 */
namespace Post
{
/**
 * @brief Tecplot文件输出类
 * 
 */
class  Tecplot :public BasePost
{
public:    
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 网格指针
     * @param[in] dualFlag_ 对偶标识
     * @param[in] outputPosition_ 物理量输出位置
     * @param[in] caseName_ 模型名称
     */
    Tecplot(Mesh *mesh_,
            const bool dualFlag_ = false,
			const Position outputPosition_ = Position::CELL_CENTER,
			const bool exportInteriorFlag_ = false,
            const std::string caseName_ = "");

    /**
     * @brief 生成Tecplot格式文件
     * 根据是否包含场生成纯网格文件或包含场的文件
     * 
     */
    void WriteFile();    

    /**
     * @brief 输出物面信息
     * 
     * @param[in] boundaryID 边界编号
     * @param[in] nodeVector 新的边界点坐标
     */ 
    void WriteBoundaryFile(const std::vector<int> &boundaryID, std::vector< std::vector<Node>> *nodeVector = nullptr);
	
	void WriteBoundaryFile_AeroDynamic(std::vector<int> boundaryID,std::vector< std::vector<Node>> nodeVector, std::vector<std::vector<int>> nodeID);
    
#if defined(_EnableTecio_)
    /**
     * @brief 调用tecio库生成Tecplot格式文件
     * 根据是否包含场生成纯网格文件或包含场的文件
     * 
     * @param[in] caseName 算例名称名
     */
	void WriteFileTecIO(const std::string &caseName);

	void WriteFileTecIOVolume();

	void WriteFileTecIOBoundary(const int &patchID);

#endif

protected:
	
    /**
     * @brief 获取变量名称
     * 
     */
	std::vector<std::string> GetVariableNames();
	
    /**
     * @brief 生成文件题头
     * 
     * @param[in] file 文件对象
     */
    void WriteTitle(std::fstream &file);

    /**
     * @brief 生成体信息
     * 
     * @param[in] file 文件对象
     */
    void WriteVolume(std::fstream &file);

    /**
     * @brief 生成边界信息
     * 
     * @param[in] file 文件对象
     */
    void WriteBoundary(std::fstream &file);

    /**
     * @brief 把字符串转换成ASCII格式并应二进制格式写进文件
     * 
     * @param[in] currentString 字符串
     * @param[in] file 文件对象
     */
    void WriteStringASCII(std::string currentString, std::fstream &file);

    /**
     * @brief 写Tecplot文件的题头
     * 
     * @param[in] file 文件对象
     */
    void WriteTitleBinary(std::fstream &file);

    /**
     * @brief 输出内部几何信息
     * 
     * @param[in] file 文件对象
     */
    void WriteVolumeZoneBinary(std::fstream &file);

    /**
     * @brief 输出边界几何信息
     * 
     * @param[in] boundaryID 边界编号
     * @param[in] file 文件对象
     */
    void WriteBoundaryZoneBinary(int boundaryID, std::fstream &file);

    /**
     * @brief 写物理场内部值
     * 
     * @param[in] file 文件对象
     */
    void WriteVolumeDataBinary(std::fstream &file);
    
    /**
     * @brief 写物理场边界值
     * 
     * @param[in] boundaryID 边界编号
     * @param[in] file 文件对象
     */
    void WriteBoundaryDataBinary(int boundaryID, std::fstream &file);

    /**
     * @brief 获取物理场指定位置物理值
     * 
     * @tparam Type 物理场类型，标量或矢量
     * @param[in] phi 物理场
     * @param[in] ID 位置编号
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 
     */
    template<class Type>
    Type GetFieldValue(const ElementField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 获取物理场指定位置物理值
     * 
     * @tparam Type 物理场类型，标量或矢量
     * @param[in] phi 物理场
     * @param[in] ID 位置编号
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 
     */
    template<class Type>
    Type GetFieldValue(const NodeField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 输出物理场最小和最大值
     * 
     * @tparam[in] Type 物理量类型，包括标量和矢量
     * @param[in] file 文件对象
     * @param[in] phi 物理场
     * @param[in] boundaryID 边界编号，取-1时为内部值
     */
    template<class Type>
    void WriteMinMax(std::fstream &file, const ElementField<Type> *phi, const int boundaryID = -1);

    /**
     * @brief 输出物理场最小和最大值
     * 
     * @tparam[in] Type 物理量类型，包括标量和矢量
     * @param[in] file 文件对象
     * @param[in] phi 物理场
     * @param[in] boundaryID 边界编号，取-1时为内部值
     */
    template<class Type>
    void WriteMinMax(std::fstream &file, const NodeField<Type> *phi, const int boundaryID = -1);

    /**
     * @brief 输出物理场最小和最大值
     * 
     * @tparam[in] Type 物理量类型，包括标量和矢量
     * @param[in] file 文件对象
     * @param[in] min 最小值
     * @param[in] max 最大值
     */
    template<class Type>
    void WriteMinMax(std::fstream &file, const Type &min, const Type &max);

    /**
     * @brief 输出区域数据信息
     * 
     * @param[in] file 文件对象
     */
    void WriteZoneDataInfo(std::fstream &file);
    
private:
    /**
     * @brief 建立三维网格边界面所有边的容器
     * 
     */
    void CreatBoundaryEdge();

    /**
     * @brief 建立边界区域的点局部编号和全局编号的对应关系
     * 
     * @param[in, out] vbe 三维流场边界面所有边的容器
     * @param[in, out] map 包含边界点全局编号和局部编号的图：->first表示全局编号，->second表示局部编号
     * @param[in, out] list 包含边界点全局编号的容器：list[i]表示局部编号为i的点，全局编号为list[i]
     * @param[in] patchID 边界编号
     */
    void SetBoundaryTopology(std::vector<BoundaryEdge> &vbe ,std::map<int, int> &map, std::vector<int> &list, const int &patchID);

private:
    /// 二进制输出标识，true为二进制
    bool binary;    

    std::vector<std::vector<BoundaryEdge>> vv_boundaryEdge;
    
    /// 标量数量（= 坐标数量 + 标量场数量 + 矢量场数量*dim）
    int numValue;

	/// 单元类型映射关系
	std::vector<int> zoneElementTypeMap;

	/// 类型名称映射关系
	std::vector<std::string> zoneNameMap;

	/// 单元点的数量映射关系
	std::vector<int> cellNodeSizeMap;

	/// 输出变量名称
	std::vector<std::string> variableNames;
};

} // namespace Post
#endif 