﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MPI.h
//! <AUTHOR>
//! @brief MPI相关操作功能.
//! @date 2022-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-07-22 乔龙
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_MPI_
#define _basic_common_MPI_

#include <string>
#include <vector>

#include "basic/common/BoostLib.h"
#if defined(_BaseParallelMPI_)
#include <mpi.h>
#endif

#if defined(_BaseParallelMPI_)
namespace MPI
{
    /// @brief 全局MPI通信器
    static boost::mpi::communicator mpiWorld;
}
#endif

#if defined(_BaseParallelMPI_)
/**
 * @def InitializeMPI(argc, argv)
 * @brief 初始化MPI环境
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 */
#define InitializeMPI(argc, argv) boost::mpi::environment env(argc, argv);
#else
/// @brief 空MPI初始化宏(非MPI模式)
#define InitializeMPI(argc, argv)
#endif

#if defined(_BaseParallelMPI_)
/**
 * @def FinalizeMPI()
 * @brief 终止MPI环境
 */
#define FinalizeMPI() boost::mpi::environment::finalized();
#else
/// @brief 空MPI终止宏(非MPI模式)
#define FinalizeMPI()
#endif

/**
 * @brief 检查MPI是否已初始化
 * @return true 如果MPI已初始化
 * @return false 如果MPI未初始化
 */
bool MPIInitialized();

/**
 * @brief MPI终止
 * 
 * @param[in] errorCode 错误代码
 */
void MPIAbort(const int errorCode);

/**
 * @brief 获取当前进程的MPI秩(rank)
 * @return int 当前进程的MPI秩
 * @note 在非MPI模式下返回0
 */
int GetMPIRank();

/**
 * @brief 获取MPI通信器中的进程总数
 * @return int MPI进程总数
 * @note 在非MPI模式下返回1
 */
int GetMPISize();

/**
 * @brief MPI同步屏障
 * @details 阻塞所有进程直到所有进程都到达此调用点
 */
void MPIBarrier();

/**
 * @brief 非阻塞探测MPI消息
 * @param[out] tags 探测到的消息标签对列表(源进程ID, 消息标签)
 */
void MPIIprobe(std::vector<std::pair<int, int>> &tags);

#if defined(_BaseParallelMPI_)
/**
 * @brief 等待所有MPI请求完成
 * @param[in,out] requests MPI请求向量
 */
void MPIWaitAll(std::vector<boost::mpi::request> &requests);
#endif

/**
 * @brief 广播数据到所有进程
 * @tparam Type 数据类型
 * @param[in,out] phi 要广播的数据
 * @param[in] rootID 广播根进程ID(默认为0)
 */
template<class Type>
void MPIBroadcast(Type &phi, const int &rootID = 0);

/**
 * @brief 在所有进程中查找最大值
 * @tparam Type 数据类型
 * @param[in,out] phi 要比较的数据
 * @param[in] rootID 结果收集进程ID(默认为0)
 * @return int 错误码(0表示成功)
 */
template<class Type>
int MaxAllProcessor(Type &phi, const int &rootID = 0);

/**
 * @brief 在所有进程中查找最小值
 * @tparam Type 数据类型
 * @param[in,out] phi 要比较的数据
 * @param[in] rootID 结果收集进程ID(默认为0)
 * @return int 错误码(0表示成功)
 */
template<class Type>
int MinAllProcessor(Type &phi, const int &rootID = 0);

/**
 * @brief 在所有进程中求和
 * @tparam Type 数据类型
 * @param[in,out] phi 要求和的数据
 * @param[in] rootID 结果收集进程ID(默认为0)
 */
template<class Type>
void SumAllProcessor(Type &phi, const int &rootID = 0);

#endif