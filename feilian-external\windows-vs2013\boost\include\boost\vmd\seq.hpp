
//  (C) Copyright <PERSON> 2015
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).

#if !defined(BOOST_VMD_SEQ_HPP)
#define BOOST_VMD_SEQ_HPP

#include <boost/vmd/detail/setup.hpp>

#if BOOST_PP_VARIADICS

#include <boost/vmd/seq/is_vmd_seq.hpp>
#include <boost/vmd/seq/pop_back.hpp>
#include <boost/vmd/seq/pop_front.hpp>
#include <boost/vmd/seq/push_back.hpp>
#include <boost/vmd/seq/push_front.hpp>
#include <boost/vmd/seq/remove.hpp>
#include <boost/vmd/seq/size.hpp>
#include <boost/vmd/seq/to_array.hpp>
#include <boost/vmd/seq/to_list.hpp>
#include <boost/vmd/seq/to_tuple.hpp>

#endif /* BOOST_PP_VARIADICS */
#endif /* BOOST_VMD_SEQ_HPP */
