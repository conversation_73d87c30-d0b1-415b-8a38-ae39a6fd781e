﻿#include "sourceFlow/timeScheme/CFLNumberManager.h"

namespace Time
{
namespace Flow
{

CFLNumberManager::CFLNumberManager(Package::FlowPackage &flowPackage_)
	:
    flowPackage(flowPackage_),
	flowConfigure(flowPackage_.GetFlowConfigure()),
	mesh(flowPackage_.GetMeshStruct().mesh),
    currentLevel(flowPackage_.GetMeshStruct().level),
    CFL(flowPackage_.GetCFLNumber()),
    rho(flowPackage_.GetField().density),
    rho0(flowPackage_.GetField0().density),
    residualMass(flowPackage_.GetResidualField().residualMass)
{
	// CFL数的设置
    coarseRatio = flowConfigure.GetTimeScheme().CFL.coarseMeshRatio;
    CFL = flowConfigure.GetTimeScheme().CFL.value * pow(coarseRatio, currentLevel);

    CFLType = flowConfigure.GetTimeScheme().CFL.variableFlag;
    CFLGrowthStep = 0;
    currrentCFLStep = 0;

    //自动CFL数初始值设为1
    monitorResidualCurrent=0;
    if (CFLType!=0)
    {
        CFLGrowthStep = flowConfigure.GetTimeScheme().CFL.growthStep;
        CFL0=flowConfigure.GetTimeScheme().CFL.value;
        CFLGrowthRatio = flowConfigure.GetTimeScheme().CFL.growthRatio;
        AutoCFLMin=flowConfigure.GetTimeScheme().CFL.min;
        AutoCFLMax=flowConfigure.GetTimeScheme().CFL.max;       
    }

    startStep = 0;                              // 起始步
    epsilon = 0.05;                             // 定义阈值，该值越小收敛越严格
    delta1 = 5;                                 // 以残值判断CFL增大的阈值
    delta2 = 5;                                 // 以守恒量比值判断CFL增大的阈值
    CFLFactor = Max(CFLGrowthRatio - 1.0, 0.0); // 初始增长系数

    // 总单元数
    nElementGlobal = mesh->GetElementNumberReal();
#if defined(_BaseParallelMPI_)
    SumAllProcessor(nElementGlobal, 0);
    MPIBroadcast(nElementGlobal, 0);
#endif

}

CFLNumberManager::~CFLNumberManager()
{
}

void CFLNumberManager::Calculate()
{
    // 自动CFl数只在细网格层中调用
    if (currentLevel > 0 || CFLType == 0) return;

    if (CFLType == 1)
    {
        if (currrentCFLStep < CFLGrowthStep)
        {
            CFL = Min(Max(CFL * CFLGrowthRatio, AutoCFLMin), AutoCFLMax) * pow(coarseRatio, currentLevel);
            currrentCFLStep++;
        }
    }
    else if (CFLType == 2)
    {
        currrentCFLStep++;

        // 储存上一步残值
        monitorResidualOld = monitorResidualCurrent;
        if (monitorResidualOld == 0) monitorResidualOld = SMALL;
        monitorResidualCurrent = this->CalculateMonitorMassResidual();

        // 保留第一步残值，用于计算R_0/R_k
        if (startStep == 0)
        {
            initial_residual_0 = monitorResidualCurrent;
            CFL = CFL0;
            startStep++;
        }
        else
        {
            // 定义阈值大小，控制CFL数变化
            const Scalar m0 = log10(monitorResidualCurrent / monitorResidualOld);
            const Scalar m1 = log10(initial_residual_0 / monitorResidualCurrent);
            const Scalar m2 = -log10(this->CalculateDeltaQDivideQ());

            // 计算CFL数，控制CFL上限
            CFL = Min(CFL * (1.0 + CFLFactor), AutoCFLMax);

            // 若有发散趋势，CFL缩减为原来的0.8，并控制CFL数的下限
            if (m0 > epsilon)
            {
                if (CFL >= AutoCFLMin) CFL = Max(0.8 * CFL, AutoCFLMin);
                else                   CFL = Max(0.8 * CFL, CFL0);
            }

            // 若收敛趋于稳定，指数增大CFL
            if (m1 > delta1 && m2 > delta2) CFLFactor = CFLFactor * CFLGrowthRatio;
        }
    }
}

Scalar CFLNumberManager::CalculateMonitorMassResidual()
{
    // 监测残值置零
    monitorMassResidual = Scalar0;
    Scalar residualMassTmp;

    // 当地网格上所有残值求和
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        
        residualMassTmp = residualMass->GetValue(elementID);

        const Scalar volumeInverse = 1.0 / mesh->GetElement(elementID).GetVolume();
        Scalar localResidual = fabs(residualMassTmp) * volumeInverse;
        monitorMassResidual += localResidual * localResidual;
    }
    monitorMassResidual/= (Scalar)nElementGlobal;

#if defined(_BaseParallelMPI_)
    SumAllProcessor(monitorMassResidual, 0);
#endif

    // 0号进程监测残值计算
    if (GetMPIRank() == 0) monitorMassResidual=sqrt(monitorMassResidual);
    
    return monitorMassResidual;
}

Scalar CFLNumberManager::CalculateDeltaQDivideQ()
{
    DeltaQ_Divide_Q = Scalar0;
    Scalar QTmp;

    // 当地网格上所有残值求和
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        // 计算ΔQ/Q
        QTmp = (rho->GetValue(elementID) - rho0->GetValue(elementID)) / rho->GetValue(elementID);
        DeltaQ_Divide_Q += QTmp * QTmp;
    }
    DeltaQ_Divide_Q /= (Scalar)nElementGlobal;

#if defined(_BaseParallelMPI_)
    SumAllProcessor(DeltaQ_Divide_Q, 0);
    MPIBroadcast(DeltaQ_Divide_Q, 0);
#endif

    DeltaQ_Divide_Q = sqrt(DeltaQ_Divide_Q);

    return DeltaQ_Divide_Q;
}

}//namespace Flow
}//namespace Time
