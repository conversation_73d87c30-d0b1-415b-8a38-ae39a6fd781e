﻿#include "meshProcess/agglomeration/AgglomerateSeed.h"

AgglomerateSeed::AgglomerateSeed(const int &nLayer_, const int &coarseRationLayer_,
								 const int &coarseRationTang_,
								 const int &minsize_, const int &maxsize_,
								 const bool &structured_ )
	:nLayer(nLayer_), coarseRationLayer(coarseRationLayer_),
	 coarseRationTang(coarseRationTang_),
	 minsize(minsize_), maxsize(maxsize_), structured(structured_)
{
}

AgglomerateSeed::~AgglomerateSeed()
{
    std::vector<int>().swap(wallPatchIDList);
    std::vector<int>().swap(fineCVBoundFlag);
    std::vector<std::vector<int>>().swap(fineCVBound);
    std::vector<Scalar>().swap(fineCVAera);
    std::vector<bool>().swap(agglomFlag);
    std::vector<int>().swap(bPrior);
    std::vector<int>().swap(faceIntTemp);
}

void  AgglomerateSeed::Agglomerate(Mesh* mesh, const std::vector<int> &wallPatchIDList_, const int &coarseLevel_, std::vector<int> &finalDecomp, int& nCoarseCells)
{
	//step0: 准备工作
	fineMesh = mesh;
	coarseLevel = coarseLevel_;
	wallPatchIDList = wallPatchIDList_;
	if (wallPatchIDList.size() == 0) //无物面边界
		FatalError("AgglomerateSeed::Agglomerate: wall size == 0...");

	nDim = (mesh->GetMeshDimension() == Mesh::MeshDim::md2D) ? 2 : 3;
	cRatio = (minsize + maxsize) / 2;
	if (structured)
	{
		cRatio = 4;
		coarseRationTang = 2;
	}
	
	//step1: 基本信息初始化
	nBound = fineMesh->GetBoundarySize();
	const int nCVFine = fineMesh->GetElementNumberReal();
	agglomFlag.clear();
    faceIntTemp.clear();
	agglomFlag.resize(nCVFine, false); //true表示已经被聚合
	faceIntTemp.resize(fineMesh->GetFaceNumber(), -1);	

	//step2: 初始化细网格的控制体
	this->InitCV(); 

	//step3: 开始聚合
    finalDecomp.clear();
	finalDecomp.resize(nCVFine, -1);
    nCoarseCells = 0;
	this->Agglom(finalDecomp, nCoarseCells);
}

void  AgglomerateSeed::InitCV()
{
    // 所有单元初始均按照内部单元进行初始化处理，对应fineCVBound[0]，不包含边界标识
    fineCVBound.push_back(std::vector<int>{});
    fineCVBoundFlag.clear();
    fineCVBoundFlag.resize(fineMesh->GetElementNumberReal(), 0);

	// 遍历边界单元，修改边界单元的边界标识信息
	for (int patchID = 0; patchID < fineMesh->GetBoundarySize(); ++patchID)
	{
        const int boundaryFaceSize = fineMesh->GetBoundaryFaceSize(patchID);
		for (int index = 0; index < boundaryFaceSize; ++index)
		{
			const int &faceID = fineMesh->GetBoundaryFaceID(patchID, index);
			const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();			

            const int index0 = fineCVBoundFlag[ownerID];
            if (index0 == 0) //首次添加
            {
                fineCVBound.push_back(std::vector<int>{patchID});
                fineCVBoundFlag[ownerID] = fineCVBound.size() - 1;
            }
            else
            {
                bool flag = false;
                auto &bList = fineCVBound[index0];
                for (int k = 0; k < bList.size(); ++k)
                {
                    if (bList[k] == patchID)
                    {
                        flag = true;
                        break;
                    }
                }
                if (!flag) fineCVBound[index0].push_back(patchID);
            }
		}
	}

	//处理面积信息
    fineCVAera.clear();
	fineCVAera.resize(fineMesh->GetElementNumberReal(), Scalar0);
	for (int faceID = 0; faceID < fineMesh->GetFaceNumber(); ++faceID)
	{
		const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();
		const int &neighID = fineMesh->GetFace(faceID).GetNeighborID();
		if (neighID < 0) continue;
		
		fineCVAera[ownerID] += fineMesh->GetFace(faceID).GetArea();
		fineCVAera[neighID] += fineMesh->GetFace(faceID).GetArea();
	}
}

void  AgglomerateSeed::Agglom(std::vector<int> &finalDecomp, int &nCoarseCells)
{	
	//step1: 对网格单元进行分层标记
	this->SetLayerNumber();

	//step2: 通过种子单元开始聚合
	const int seedID = this->GetSeed(); //获得初始聚合的种子单元
	agglomFlag[seedID] = true;
	auto CVCoarse = this->AgglomCV(seedID);
	for (int j = 0; j < CVCoarse.elementIDList.size(); ++j)
	{
		const int &fineID = CVCoarse.elementIDList[j];
		finalDecomp[fineID] = nCoarseCells;
	}
	nCoarseCells++;

	//step3: 阵面推进，继续聚合
	std::vector<int> front;
	int frocand = 0;
	this->InitFront(front); // 获得初始推进阵面
	
	while (frocand >= 0)
	{
		this->FindNextID(front, frocand);
		if (frocand == -1) break;
		agglomFlag[front[frocand]] = true;		
		auto CVCoarse = this->AgglomCV(front[frocand]);
	    for (int j = 0; j < CVCoarse.elementIDList.size(); ++j)
	    {
	    	const int &fineID = CVCoarse.elementIDList[j];
	    	finalDecomp[fineID] = nCoarseCells;
	    }
	    nCoarseCells++;
	}

	//step4: 检查是否存在遗漏的单元
	for (int i = 0; i < fineMesh->GetElementNumberReal(); ++i)	
		if (!agglomFlag[i]) WarningContinue("AgglomerateSeed::Agglom: 孤立单元 ID =  " + ToString(i));
	bPrior.clear();		
}

void  AgglomerateSeed::SetLayerNumber()
{
	const int nvcFine = fineMesh->GetElementNumberReal();
    bPrior.clear();
	bPrior.resize(nvcFine, -1);//对于物面 BPRIOR = 1, 靠近物面 BPRIOR = 2，依次类推

	//step 1: 对于物面控制体的标识bPrior =1 (如没有物面，取其他边界控制体进行标识)
	std::vector<int> front; //用于阵面推进的单元数量    

    //边界单元的处理
    for (int i = 0; i < wallPatchIDList.size(); ++i)
    {
        const int &patchID = wallPatchIDList[i];
        const int boundaryFaceSize = fineMesh->GetBoundaryFaceSize(patchID);

        for (int index = 0; index < boundaryFaceSize; ++index)
        {
            const int &faceID = fineMesh->GetBoundaryFaceID(patchID, index);
            const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();

            for (int j = 0; j < front.size(); ++j)
                if (front[j] == ownerID) continue;

            bPrior[ownerID] = 1;
            front.push_back(ownerID);
        }
    }

	//step 2: 对剩余控制体进行标识
	int prior = 1; //从物面单元（或其他边界）起始的网格层数
	while (front.size() > 0)
	{
		prior++;
		std::vector<int> xFront;//下一层阵面

		for (int iFront = 0; iFront < front.size(); ++iFront)
		{	
			const int &ID = front[iFront];
			const Element &element = fineMesh->GetElement(ID);
			for (int j = 0; j < element.GetFaceSize(); ++j) //遍历周围单元
			{
				const int &faceID = element.GetFaceID(j);				
				const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();
				const int &neighID = fineMesh->GetFace(faceID).GetNeighborID();
				if (neighID < 0)  continue;

				int adjoinID = neighID;
				if (neighID == ID) adjoinID = ownerID;

				if (bPrior[adjoinID] < 0) //未标记层数
				{
					bPrior[adjoinID] = prior;
					xFront.push_back(adjoinID);
				}
			}
		}
		front.swap(xFront);
	}
}

int AgglomerateSeed::GetSeed()
{
	if (wallPatchIDList.size() > 0) //存在物面边界
	{
		for (int i = 0; i < wallPatchIDList.size(); ++i)
		{
			const int &patchID = wallPatchIDList[i];
            const int boundaryFaceSize = fineMesh->GetBoundaryFaceSize(patchID);
			for (int j = 0; j < boundaryFaceSize; ++j)
			{
				const int &faceID = fineMesh->GetBoundaryFaceID(patchID, j);
				return fineMesh->GetFace(faceID).GetOwnerID();
			}
		}
	}
	else if (nBound > 0) //不存在物面边界，但是存在其他边界
	{
		for (int patchID = 0; patchID < fineMesh->GetBoundarySize(); ++patchID)
		{
            const int boundaryFaceSize = fineMesh->GetBoundaryFaceSize(patchID);
			for (int j = 0; j < boundaryFaceSize; ++j)
			{
				const int &faceID = fineMesh->GetBoundaryFaceID(patchID, j);
				return fineMesh->GetFace(faceID).GetOwnerID();
			}
		}		
	}
	else 
	{
		FatalError("AgglomerateSeed::seed: any boundary is none!");
	}

	return -1;
}

void AgglomerateSeed::InitFront(std::vector<int> &front)
{
    const int nCV = bPrior.size();
    front.resize(nCV);
    int iLayer = 1, iCV = 0;
    while (iCV < nCV)
    {
        bool findFlag = false;
        for (int i = 0; i < nCV; ++i)
        {
            if (bPrior[i] == iLayer)
            {
                front[iCV++] = i;
                if (!findFlag) findFlag = true;
            }
        }

        if (!findFlag)
            FatalError( "AgglomerateSeed::InitFront: iLayer = "
                       + ToString(iLayer) + " is not in bPrior!");

        iLayer++;
    }
}

AgglomerateSeed::ControlVolume AgglomerateSeed::AgglomCV(const int &fineID)
{	
	//计算当地粗化率
	int cRatioX = CalculateCoarseRatio(fineID);
	
	//开始聚合	
	const Scalar &tol = 1.1;
	Scalar preBest = 1.0E9;
	
	ControlVolume CVout = this->CreateCV(fineID); //首个待聚合单元
	std::vector<int> agglomID; //其他待聚合单元号
	std::vector<std::pair<int, int>> agglomInfo; //其他待聚合单元与本控制体的邻接单元号及面号
	std::vector<Scalar> cArea; //其他待聚合单元与已经聚合单元的重合部分的面积
	while (CVout.elementIDList.size() < cRatioX)
	{
		//step1:寻找待聚合的单元号，放在agglomID中
		this->FindAdjacientElement(CVout.faceIDList, agglomID, agglomInfo, cArea);

		//step2:如在边界层内，进行处理
		const int currentSize = CVout.elementIDList.size();
		const int &lastID = CVout.elementIDList[currentSize - 1];
		bool sameLayerEnoughFlag = false; //同层网格是否已聚合完成的标志
		int coarseRationTangCurrent = coarseRationTang; //真正的切向聚合率
		if (nLayer > 0 && currentSize < coarseRationTang && bPrior[lastID] <= nLayer) //边界层内，首层优先切向聚合
		{
			int index = 0; //统计有效单元的数量
			for (int i = 0; i < agglomID.size(); ++i)
			{
				//处于同层，优先切向聚合，但是剔除法向变化太大的单元
				if (structured || bPrior[agglomID[i]] == bPrior[lastID]) //位于同层
				{
					const Vector &faceCenter = fineMesh->GetFace(agglomInfo[i].second).GetCenter();
					Vector directLeft = (fineMesh->GetElement(agglomID[i]).GetCenter() - faceCenter).GetNormal();
					Vector directRight = (faceCenter - fineMesh->GetElement(agglomInfo[i].first).GetCenter()).GetNormal();

					if ((directLeft & directRight) > 0.7) //夹角小于45度					
					{
						agglomID[index] = agglomID[i];
						cArea[index] = cArea[i];
						index++;
					}
				}
			}
			if (index == 0) //找不到同层质量较好的单元，不再同层聚合，保留原始agglomID
			{
				sameLayerEnoughFlag = true;
				coarseRationTangCurrent = currentSize;
				cRatioX = coarseRationLayer * coarseRationTangCurrent;
			}				
			else //同层聚合
			{
				agglomID.assign(agglomID.begin(), agglomID.begin() + index);
			}
		}

		if (   (nLayer > 0 && bPrior[lastID] <= nLayer && currentSize >= coarseRationTang) //首层切向已满（达最大切向聚合率）
			|| (nLayer > 0 && bPrior[lastID] <= nLayer && sameLayerEnoughFlag)) //首层切向已满（未达最大切向聚合率）
		{
			int currentLayerDiff = 0; //当前层未满，需要切向聚合
			if (currentSize % coarseRationTangCurrent == 0) //当前层已满，下一步需要法向聚合
				currentLayerDiff = 1;

			int index = 0; //统计有效单元的数量
			for (int i = 0; i < agglomID.size(); ++i)
			{ 
				if (structured || bPrior[agglomID[i]] == bPrior[lastID] + currentLayerDiff)
				{
					agglomID[index] = agglomID[i];
					cArea[index] = cArea[i];
					index++;
				}
			}
			agglomID.assign(agglomID.begin(), agglomID.begin() + index);
		}

		//step3:从待聚合的单元挑选最佳单元
		int bestID = -1; //记录待聚合单元中质量最好的单元编号
		Scalar bestf = 1.0E9; //最佳单元的质量值		

		if (CVout.boundList.size() == 0) //CVout没有包含边界单元
		{
			for (int i = 0; i < agglomID.size(); ++i)						
				this->GetBestElement(CVout, cArea, agglomID, i, bestf, bestID);
		}
		else //CVout包含边界单元
		{
			for (int i = 0; i < agglomID.size(); ++i)
			{
				const int &ID = agglomID[i];
				int nbAdd = this->CalculateBoundarySize(CVout, ID);

				if (nbAdd == CVout.boundList.size() ||
					nbAdd == fineCVBound[fineCVBoundFlag[ID]].size())							
					this->GetBestElement(CVout, cArea, agglomID, i, bestf, bestID);
			}
		}		

		//step4:判定是否提前完成聚合
		// 4.1 找不到待聚合单元（独立聚合，返回）
		if (bestID == -1) return CVout; 

		// 4.2 还差1个单元时，与上次聚合相比（仅聚合了1个），本次找到的最好单元质量criterion[0]比上次最好质量preBest还大10%时，不再聚合了，返回
		if (!structured && CVout.elementIDList.size() + 1 >= cRatioX &&
			(fabs(bestf) > tol *preBest)) return CVout;
		
		//step5:最终确定待合并的单元列表，放入listCV
		agglomFlag[bestID] = true;

		if (structured && CVout.elementIDList.size() == 3 && bestf > -1.0E8) //结构网格已聚合3个，待加入第4个
		{
			if (CalculateAgglomSizeForStruct(CVout, bestID)) //按照2个聚合
			{
				for (int i = 1; i < CVout.elementIDList.size(); ++i)//除种子外单元重置
				{
					const int &ID = CVout.elementIDList[i];
					agglomFlag[ID] = false;
				}
				agglomFlag[bestID] = false;
				cRatioX = 2;
				CVout = this->CreateCV(fineID);
				continue;
			}
		}

		//step6:进行单元合并
		CVout = this->FuseCV(CVout, bestID, cRatioX);
		preBest = fabs(bestf);
	} //end while

	return CVout;
}

void AgglomerateSeed::FindAdjacientElement(const std::vector<int> &faceIDList, std::vector<int> &agglomID, std::vector<std::pair<int, int>> &agglomInfo, std::vector<Scalar> &cArea)
{
	//查找待聚合的单元
	agglomID.clear();
	agglomInfo.clear();
	cArea.clear();
	for (int i = 0; i < faceIDList.size(); ++i)
	{
		const int &faceID = faceIDList[i];
		const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();
		const int &neighID = fineMesh->GetFace(faceID).GetNeighborID();
		if (neighID < 0)  continue;

		int adjacientIDLeft = ownerID;
		int adjacientID = neighID;
		if (agglomFlag[adjacientID])
		{
			adjacientIDLeft = neighID;
			adjacientID = ownerID;
		}

		if (!agglomFlag[adjacientID])
		{
			int j = 0;
			for (; j < agglomID.size(); ++j)
			{
				if (adjacientID == agglomID[j]) break;
				
			}
			if (j < agglomID.size()) //找到
			{
				cArea[j] += fineMesh->GetFace(faceID).GetArea();
			}
			else //未找到
			{
				agglomID.push_back(adjacientID);
				agglomInfo.push_back(std::pair<int, int>{adjacientIDLeft, faceID});
				cArea.push_back(fineMesh->GetFace(faceID).GetArea());
			}
		}
	}
}

void AgglomerateSeed::GetBestElement(const ControlVolume &CVout, const std::vector<Scalar> &cArea, const std::vector<int> &agglomID, const int &index, Scalar &bestf, int &bestID)
{	
	const Scalar e2 = 1.0 - 1.0 / (Scalar)nDim;
	
	const int &ID = agglomID[index];
	Scalar areaV = CVout.area + fineCVAera[ID] - 2.0 * cArea[index];
	Scalar volV = CVout.volume + fineMesh->GetElement(ID).GetVolume();
	Scalar f = areaV / pow(volV, e2);
	

	if (structured && CVout.elementIDList.size() == 2)
	{
		const int &seedID = CVout.elementIDList[0];
		const int &secondID = CVout.elementIDList[1];

		bool adjFlag = false;
		const Element &e = fineMesh->GetElement(ID);
		for (int j = 0; j < e.GetFaceSize(); ++j)
		{
			const int &faceID = e.GetFaceID(j);
			const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();
			const int &neighID = fineMesh->GetFace(faceID).GetNeighborID();
			if (ownerID == seedID || neighID == seedID)
			{
				adjFlag = true;
				break;
			}
		}
		if (adjFlag)
		{
			const Vector direct1 = (fineMesh->GetElement(seedID).GetCenter() - fineMesh->GetElement(secondID).GetCenter()).GetNormal();
			const Vector direct2 = (fineMesh->GetElement(seedID).GetCenter() - fineMesh->GetElement(ID).GetCenter()).GetNormal();
			if (fabs(direct1 & direct2) < 0.7) f = f - 10E5;//优先选择夹角较大的单元
		}
		else
		{
			const Vector direct1 = (fineMesh->GetElement(seedID).GetCenter() - fineMesh->GetElement(secondID).GetCenter()).GetNormal();
			const Vector direct2 = (fineMesh->GetElement(secondID).GetCenter() - fineMesh->GetElement(ID).GetCenter()).GetNormal();
			if (fabs(direct1 & direct2) < 0.7) f = f - 10E5;//优先选择夹角较大的单元
		}
	}

	if (structured && CVout.elementIDList.size() == 3 && !CalculateAgglomSizeForStruct(CVout, ID))
	{
		f = -1.0E9;
	}

	if (f < bestf)
	{
		bestf = f;
		bestID = ID;
	}
}

int AgglomerateSeed::CalculateBoundarySize(const ControlVolume &CV1, const int &fineID)
{
	const auto &bList = fineCVBound[fineCVBoundFlag[fineID]];
	
	std::vector<int> boudum(nBound, 0);
	for (int i = 0; i < CV1.boundList.size(); ++i) boudum[CV1.boundList[i]] = 1;
	for (int i = 0; i < bList.size(); ++i) boudum[bList[i]] = 1;

	int count = 0;
	for (int i = 0; i < boudum.size(); ++i)
		if (boudum[i] == 1) count++;

	return count;
}

AgglomerateSeed::ControlVolume AgglomerateSeed::FuseCV(const ControlVolume &CV1, const int &iCV, const int &maxSize)
{
    if (CV1.elementIDList.size() == 0) FatalError("AgglomerateSeed::FuseCV: CV1.elementIDList.size() = 0!");

    //step1: 合并单元编号
    ControlVolume CVout;
    CVout.elementIDList = CV1.elementIDList;
	CVout.elementIDList.push_back(iCV);	

    if (CVout.elementIDList.size() == maxSize) return CVout;

    //step2: 合并边界信息（patchID / 边界单元号）
    std::vector<bool> boundFlag(nBound, false);
    CVout.boundList = CV1.boundList;
    for (int i = 0; i < CV1.boundList.size(); ++i)
        boundFlag[CV1.boundList[i]] = true;

	const auto &bList = fineCVBound[fineCVBoundFlag[iCV]];
	for (int j = 0; j < bList.size(); ++j)
	{
		const int &patchID = bList[j];
		if (!boundFlag[patchID])
		{
			CVout.boundList.push_back(patchID);
			boundFlag[patchID] = true;
		}
	}

    //step3: 对合并后的面进行处理（内部面与外部面）
    //step3.1: faceIntTemp中相关面置零
    for (int i = 0; i < CV1.faceIDList.size(); ++i)
    {
        const int &faceID = CV1.faceIDList[i];
        faceIntTemp[faceID] = 0;
    }

	const Element &elementICV = fineMesh->GetElement(iCV);
	for (int j = 0; j < elementICV.GetFaceSize(); ++j)
	{
		const int &faceID = elementICV.GetFaceID(j);
		faceIntTemp[faceID] = 0;
	}

    //step3.2: faceIntTemp中相关面++
    for (int i = 0; i < CV1.faceIDList.size(); ++i)
    {
        const int &faceID = CV1.faceIDList[i];
        if (fineMesh->GetFace(faceID).GetNeighborID() < 0) continue;
        ++faceIntTemp[faceID];
    }

	for (int j = 0; j < elementICV.GetFaceSize(); ++j)
	{
		const int &faceID = elementICV.GetFaceID(j);
		if (fineMesh->GetFace(faceID).GetNeighborID() < 0) continue;
		++faceIntTemp[faceID];
	}

    //step3.3: 将面分类处理
    CVout.faceIDList.clear();
	CVout.area = Scalar0;
	for (int i = 0; i < CV1.faceIDList.size(); ++i)
	{
		const int &faceID = CV1.faceIDList[i];
		if (faceIntTemp[faceID] == 1) //外部面
		{
			CVout.faceIDList.push_back(faceID);
			CVout.area += fineMesh->GetFace(faceID).GetArea();
		}
	}

	for (int j = 0; j < elementICV.GetFaceSize(); ++j)
	{
		const int &faceID = elementICV.GetFaceID(j);
		if (faceIntTemp[faceID] == 1) //外部面
		{
			CVout.faceIDList.push_back(faceID);
			CVout.area += fineMesh->GetFace(faceID).GetArea();
		}
	}

    //step4: 添加体积信息
    CVout.volume = CV1.volume;
	CVout.volume += elementICV.GetVolume();

    return CVout;
}

void AgglomerateSeed::FindNextID(const std::vector<int> &front, int &frocand)
{
	if (frocand < 0) FatalError("AgglomerateSeed::aggcand: frocand < 0!");

	int ifront = frocand;
	const int nCV1 = fineMesh->GetElementNumberReal() - 1;
	
	if (ifront > nCV1)
	{
		frocand = -1;
		return;
	}

	while (ifront <= nCV1)
	{
		if (ifront == nCV1)
		{
			if (!agglomFlag[front[ifront]]) frocand = ifront; 
			else                            frocand = -1;
			return;			
		}
		else
		{
			if (!agglomFlag[front[ifront]]) {frocand = ifront; return;}
			else                            ifront++;
		}
	}
}

AgglomerateSeed::ControlVolume AgglomerateSeed::CreateCV(const int &fineID)
{
	ControlVolume cv;
	const Element &e = fineMesh->GetElement(fineID);
	
	cv.elementIDList = std::vector<int> {fineID};
	cv.area = fineCVAera[fineID];
	cv.volume = e.GetVolume();
	cv.boundList = fineCVBound[fineCVBoundFlag[fineID]];
	
	for (int j = 0; j < e.GetFaceSize(); ++j)
	{
		const int &faceID = e.GetFaceID(j);
		const int &neighID = fineMesh->GetFace(faceID).GetNeighborID();
		if (neighID < 0)  continue;
		else cv.faceIDList.push_back(faceID);		
	}

	return cv;
}

int AgglomerateSeed::CalculateCoarseRatio(const int &fineID)
{
	if (structured) return cRatio;
	
	int cRatioX = cRatio;
	if (nLayer > 0 && coarseRationLayer > 1)
	{
		if (bPrior[fineID] - 1 <= nLayer - coarseRationLayer + 1)
		{
			cRatioX = coarseRationLayer * coarseRationTang;
		}
		else
		{
			int iPlus = bPrior[fineID] - nLayer + coarseRationLayer - 2;
			int cDiff = cRatio - coarseRationLayer;
			if (cDiff > iPlus) cRatioX = coarseRationLayer + iPlus;
			else               cRatioX = cRatio;
		}
	}

	return cRatioX;
}

bool AgglomerateSeed::CalculateAgglomSizeForStruct(const ControlVolume &CVout, const int &ID)
{
	std::vector<int> IDList = CVout.elementIDList;
	IDList.push_back(ID);
	std::vector<int> adjacientCount(4, 0);

	for (int i = 0; i < 4; ++i)
	{
		const int &ID = IDList[i];
		const Element &e = fineMesh->GetElement(ID);
		for (int j = 0; j < e.GetFaceSize(); ++j)
		{
			const int &faceID = e.GetFaceID(j);
			const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();
			const int &neighID = fineMesh->GetFace(faceID).GetNeighborID();
			if (neighID < 0) continue;

			int countInnerFace = 0;
			int index1 = -1, index2 = -1;
			for (int k = 0; k < 4; ++k)
			{
				if (ownerID == IDList[k]) { countInnerFace++; index1 = k;}
				if (neighID == IDList[k]) { countInnerFace++; index2 = k;}
			}
			if (countInnerFace != 2) continue;
			adjacientCount[index1]++;
			adjacientCount[index2]++;
		}
	}

	for (int i = 0; i < 4; ++i)
		if (adjacientCount[i] != 4) return true;

	return false;
}