//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_LEX_TERMINALS_APR_20_2009_0550PM)
#define BOOST_SPIRIT_LEX_TERMINALS_APR_20_2009_0550PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/common_terminals.hpp>

namespace boost { namespace spirit { namespace lex
{
    ///////////////////////////////////////////////////////////////////////////
    //  Define a more convenient name for an omitted token attribute type
    typedef spirit::omit_type omit;
    using spirit::omit_type;
}}}

#endif
