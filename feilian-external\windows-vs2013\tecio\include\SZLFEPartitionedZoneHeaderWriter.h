 #pragma once
#include "SzlFileLoader.h"
#include "ZoneHeaderWriterAbstract.h"
namespace tecplot { namespace ___3933 { class ItemSetIterator; class SZLFEPartitionedZoneHeaderWriter : public ZoneHeaderWriterAbstract { public: SZLFEPartitionedZoneHeaderWriter( ItemSetIterator&                  varIter, ___4636                       zone, ___4636                       ___341, ___37&                       ___36, ___2240<int32_t> const& partitionFileNums, UInt64Array const&                partitionHeaderFilePositions, UInt32Array const&                partitionNumCells, UInt32Array const&                partitionNumNodes, VarZoneMinMaxArray const&         varPartitionMinMaxes); virtual ~SZLFEPartitionedZoneHeaderWriter(); virtual uint64_t sizeInFile(bool ___2002) const; virtual ___372 write(FileWriterInterface& fileWriter) const; private: ___2240<int32_t> const& m_partitionFileNums; UInt64Array const& m_partitionHeaderFilePositions; UInt32Array const& m_partitionNumCells; UInt32Array const& m_partitionNumNodes; VarZoneMinMaxArray const& m_varPartitionMinMaxes; }; }}
