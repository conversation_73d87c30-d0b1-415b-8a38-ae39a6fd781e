﻿#include "sourceFlow/timeScheme/ExactJacobian.h"
#include "meshProcess/meshSorting/MeshSorting.h"
#include <limits>

namespace Time
{
namespace Flow
{
ExactJacobian::ExactJacobian(Package::FlowPackage &flowPackage)
	:
    BackEuler(flowPackage),
	jacobian(flowPackage.GetImplicitSolver().jacobian),
	jacobianTur(flowPackage.GetImplicitSolver().jacobianTur),
	systemNew(nullptr), resVector(nullptr), solVector(nullptr),
	systemNewTur(nullptr), resVectorTur(nullptr), solVectorTur(nullptr)
{
	if (flowConfigure.GetTimeScheme().exactJacobian.solverType == Time::LinearSolverType::PRIVATE)
	{
		systemNew = new LinearSystemSolverSelf(mesh, mesh->GetMeshDimension() + 2);
		resVector = new LinearSystemVectorSelf(mesh, nVariable);
		solVector = new LinearSystemVectorSelf(mesh, nVariable);
		if (nTurbulence > 0)
		{
			systemNewTur = new LinearSystemSolverSelf(mesh, flowPackage.GetTurbulentStatus().nVariable);
			resVectorTur = new LinearSystemVectorSelf(mesh, nTurbulence);
			solVectorTur = new LinearSystemVectorSelf(mesh, nTurbulence);
		}
	}
#if defined(_EnablePETSC_)
	else
	{
		systemNew = new LinearSystemSolverPetsc(mesh, mesh->GetMeshDimension() + 2);
		resVector = new LinearSystemVectorPetsc(mesh, nVariable);
		solVector = new LinearSystemVectorPetsc(mesh, nVariable);
		if (nTurbulence > 0)
		{
			systemNewTur = new LinearSystemSolverPetsc(mesh, flowPackage.GetTurbulentStatus().nVariable);
			resVectorTur = new LinearSystemVectorPetsc(mesh, nTurbulence);
			solVectorTur = new LinearSystemVectorPetsc(mesh, nTurbulence);
		}
	}
#endif
}

ExactJacobian::~ExactJacobian()
{
	if (systemNew != nullptr) {delete systemNew; systemNew = nullptr;}
	if (resVector != nullptr) {delete resVector; resVector = nullptr;}
	if (solVector != nullptr) {delete solVector; solVector = nullptr;}
	if (systemNewTur != nullptr) {delete systemNewTur; systemNewTur = nullptr;}
	if (resVectorTur != nullptr) {delete resVectorTur; resVectorTur = nullptr;}
	if (solVectorTur != nullptr) {delete solVectorTur; solVectorTur = nullptr;}
}

void ExactJacobian::Initialize(const Initialization::Type &initialType)
{
	const auto &timeScheme = flowConfigure.GetTimeScheme().innnerLoopType;
	if (timeScheme > Time::Scheme::ExactJacobian)
	{
		jacobian->Initialize();
		resVector->Initialize();
		solVector->Initialize();
		systemNew->Initialize(timeScheme, flowConfigure.GetTimeScheme().exactJacobian, jacobian, resVector, solVector);

		if (nTurbulence > 0)
		{
			jacobianTur->Initialize();
			resVectorTur->Initialize();
			solVectorTur->Initialize();
			systemNewTur->Initialize(timeScheme, flowConfigure.GetTimeScheme().exactJacobian, jacobianTur, resVectorTur, solVectorTur);
		}
	}

	this->InitializeBase(initialType);
}

void ExactJacobian::InitializeSolver()
{
	flowPackage.SetUpdateJacobian(true);
	jacobian->SetValZero();
	if (nTurbulence > 0) jacobianTur->SetValZero();

	solVector->SetZero();
	// solVectorTur->SetZero();
}

void ExactJacobian::Solve()
{
	//建立矩阵求解系统
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index0 = 0; index0 < elementNumber; ++index0)
	{
		const int &elementID = mesh->GetElementIDInDomain(index0);

		const Scalar deltaTInv = 1.0 / deltaT->GetValue(elementID);
		jacobian->AddVal2Diag(elementID, deltaTInv);

		resVector->SetValue(elementID, 0, -residualMass->GetValue(elementID));
		resVector->SetValue(elementID, 1, -residualMomentum->GetValue(elementID).X());
		resVector->SetValue(elementID, 2, -residualMomentum->GetValue(elementID).Y());
		if (dim3) resVector->SetValue(elementID, 3, -residualMomentum->GetValue(elementID).Z());
		resVector->SetValue(elementID, nVariable - 1, -residualEnergy->GetValue(elementID));

		if (nTurbulence > 0)
		{
			jacobianTur->AddVal2Diag(elementID, deltaTInv);
			for (int iVar = 0; iVar < nTurbulence; iVar++)
				resVectorTur->SetValue(elementID, iVar, -residualTurbulence[iVar]->GetValue(elementID));
		}
	}

	//矩阵求解
	systemNew->Solve();
	
	//矩阵求解
	if (nTurbulence > 0) systemNewTur->Solve();
}

void ExactJacobian::Update()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index0 = 0; index0 < elementNumber; ++index0)
    {
        const int &elementID = mesh->GetElementIDInDomain(index0);
        
		const Vector &UTemp0 = U0->GetValue(elementID);
		const Scalar &pTemp0 = p0->GetValue(elementID);
		const Scalar rhoTemp0 = rho0->GetValue(elementID);
		const Vector rhoUTemp0 = rhoTemp0 * UTemp0;
		const Scalar rhoETemp0 = pTemp0 / gamma1 + 0.5 * rhoTemp0 * (UTemp0 & UTemp0);

		// 获取变化量
		const Scalar deltaRho = solVector->GetValue(elementID, 0);
		const Vector deltaRhoU = Vector(solVector->GetValue(elementID, 1), solVector->GetValue(elementID, 2), dim3 ? solVector->GetValue(elementID, 3) : Scalar0);
		const Scalar deltaRhoE = solVector->GetValue(elementID, nVariable - 1);

		// 计算松弛系数
		const Scalar relaxRho = ComputeUnderRelaxationFactor(rhoTemp0, deltaRho);
		const Scalar relaxRhoE = ComputeUnderRelaxationFactor(rhoETemp0, deltaRhoE);
		Scalar relax = Min(relaxRho, relaxRhoE);
		if (relax < 1e-10) relax = 0.0;
		
		// 更新守恒量
		const Scalar rhoTemp = rhoTemp0 + relax * deltaRho;
		const Vector rhoUTemp = rhoUTemp0 + relax * deltaRhoU;
		const Scalar rhoETemp = rhoETemp0 + relax * deltaRhoE;

		// 将守恒变量转化为基本变量，并实现基本变量的体场更新
		const Scalar rhoTempInv = 1.0 / rhoTemp;
		const Vector UTemp = dim3 ? rhoTempInv * rhoUTemp : Vector(rhoTempInv * rhoUTemp.X(), rhoTempInv * rhoUTemp.Y(), Scalar0);
        const Scalar pTemp = (rhoETemp - 0.5 * rhoTemp * (UTemp & UTemp)) * gamma1;
    	rho->SetValue(elementID, rhoTemp);
    	U->SetValue(elementID, UTemp);
    	p->SetValue(elementID, pTemp);

		// 湍流量更新
		if (nTurbulence > 0)
		{
			Scalar relaxTur = 1.0;
			for (int m = 0; m < nTurbulence; ++m)
			{
				const Scalar &phi0 = flowPackage.GetField0().turbulence[m]->GetValue(elementID);
				relaxTur = Min(relaxTur, ComputeUnderRelaxationFactor(rhoTemp0 * phi0, solVectorTur->GetValue(elementID, m)));
			}
			if (relaxTur < 1e-10) relaxTur = 0.0;

			for (int m = 0; m < nTurbulence; ++m)
			{
				auto &phi = *flowPackage.GetField().turbulence[m];
				const Scalar &phi0 = flowPackage.GetField0().turbulence[m]->GetValue(elementID);
				phi.SetValue(elementID, Max((rhoTemp0 * phi0 + relaxTur * solVectorTur->GetValue(elementID, m)) / rhoTemp, SMALL));
			}
		}
	}

	flowPackage.UpdateExtrasField();

	// 限制更新量
	CheckAndLimit();
}

Scalar ExactJacobian::ComputeUnderRelaxationFactor(const Scalar &valueTemp, const Scalar &solTemp)
{	
	const Scalar delta = fabs(solTemp);
	return delta > 0.3 * valueTemp ? 0.3 * valueTemp / delta : 1.0;
}

}
}