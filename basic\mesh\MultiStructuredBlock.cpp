﻿#include "basic/mesh/MultiStructuredBlock.h"

void MultiStructuredBlock::SetNodeIndex(const bool &removeConnection)
{
	//step0: 初始化-2
	for (int n = 0; n < multiBlock.size(); ++n)
	{
		Block &block = multiBlock[n];
		block.nodeIndex.resize(block.nodeI);
		for (int i = 0; i < block.nodeI; ++i)
		{
			block.nodeIndex[i].resize(block.nodeJ);
			for (int j = 0; j < block.nodeJ; ++j)
			{
                block.nodeIndex[i][j].clear();
				block.nodeIndex[i][j].resize(block.nodeK, -2);
			}
		}
	}

    //step1: 将交接面只保留一面
	if (removeConnection && !connection.empty())
	{
		const int connectionSize = connection.size();
		const int blockSize = multiBlock.size();
		//step1.1: 遍历交接面,删除左编号大的
		std::vector<int> counter(blockSize, 0);
		std::vector<bool> flag(connectionSize, false);
		for (int m = 0; m < connectionSize; ++m)
		{
			const Connection &c = connection[m];
			if (c.leftID == c.rightID)
			{
				// 将第一个交界面填入blockConnectionID
				const int &n = c.leftID;
				counter[n]++;
				if (counter[n] == 1) flag[m] = true;
				if (counter[n] > 2) // 此类面成对出现，数量大于2时报错
					FatalError("MultiStructuredBlock::SetNodeIndex: number of self-connection > 2 in block " + ToString(n));
			}
			else if (c.leftID < c.rightID) flag[m] = true;
		}

		//step1.2: connectionID排序
		const int halfSize = connectionSize / 2;
		std::vector<int> connectionIDNew(halfSize, -1);
		connectionIDNew[0] = 0;
		flag[0] = false;
		int currentSize = 1;
		int start = 0;
		int neighID;
		
		while (currentSize < halfSize)
		{
			int count = 0;
			for (int m = start; m < currentSize; m++)
			{
				for (int step = 0; step < 2; ++step)
				{
					if (step == 0) neighID = currentSize == 1 ? 0 : connection[connectionIDNew[m]].rightID;
					else           neighID = currentSize == 1 ? 0 : connection[connectionIDNew[m]].leftID;
				
					for (int n = 0; n < connectionSize; n++)
					{
						if (!flag[n]) continue;
						if (connection[n].leftID == neighID || connection[n].rightID == neighID)
						{
							connectionIDNew[currentSize] = n;
							currentSize++;
							count++;
							flag[n] = false;
						}
					}				
				}
			}
			start = currentSize - count;
		}

		//step1.4: 重新建立交界面
		std::vector<Connection> connection1(halfSize);
		for (int m = 0; m < halfSize; m++)
		{
			const int &pos = connectionIDNew[m];
			connection1[m] = connection[pos];
		}
		connection = connection1;
	}

	//step2: 先遍历交接面的点
	nodeTotalSize = 0;
	for (int m = 0; m < connection.size(); ++m)
	{
		const Connection &c = connection[m];
		Block &left = multiBlock[c.leftID];
		Block &right = multiBlock[c.rightID];

        const int deltaI = c.leftRange[3] - c.leftRange[0];
        const int deltaJ = c.leftRange[4] - c.leftRange[1];
        const int deltaK = c.leftRange[5] - c.leftRange[2];
		for (int ii = 0; ii <= abs(deltaI); ii++)
		{
			for (int jj = 0; jj <= abs(deltaJ); jj++)
			{
				for (int kk = 0; kk <= abs(deltaK); kk++)
				{
					const int i = deltaI > 0 ? ii : deltaI + ii;
					const int j = deltaJ > 0 ? jj : deltaJ + jj;
					const int k = deltaK > 0 ? kk : deltaK + kk;

                    const std::vector<int> indexLeft0 = c.ObtainLeftIndex(i, j, k);
                    const std::vector<int> indexRight0 = c.ObtainRightIndex(i, j, k);
                    const int &iL = indexLeft0[0], &jL = indexLeft0[1], &kL = indexLeft0[2];
                    const int &iR = indexRight0[0], &jR = indexRight0[1], &kR = indexRight0[2];

					if (left.nodeIndex[iL][jL][kL] < 0 && right.nodeIndex[iR][jR][kR] < 0)
					{
						left.nodeIndex[iL][jL][kL] = nodeTotalSize;
						right.nodeIndex[iR][jR][kR] = nodeTotalSize;
						nodeTotalSize++;
					}
					else if (left.nodeIndex[iL][jL][kL] < 0 && right.nodeIndex[iR][jR][kR] >= 0)
					{
						left.nodeIndex[iL][jL][kL] = right.nodeIndex[iR][jR][kR];
					}
					else if (left.nodeIndex[iL][jL][kL] >= 0 && right.nodeIndex[iR][jR][kR] < 0)
					{
						right.nodeIndex[iR][jR][kR] = left.nodeIndex[iL][jL][kL];
					}
				}
			}
		}
	}

	//step3: 遍历物理边界点
	// 注意添加判断if (left.nodeIndex[iL][jL][kL] < 0)
	for (int n = 0; n < multiBlock.size(); ++n)
	{
		Block &block = multiBlock[n];
		for (int patchID = 0; patchID < block.boundaryRange.size(); ++patchID)
		{
			const std::vector<int> &range = block.boundaryRange[patchID];
			for (int k = range[2]; k <= range[5]; ++k)
				for (int j = range[1]; j <= range[4]; ++j)
					for (int i = range[0]; i <= range[3]; ++i)
					{
						if (block.nodeIndex[i][j][k] < 0)
						{
							block.nodeIndex[i][j][k] = nodeTotalSize;
							nodeTotalSize++;
						}
					}
		}	
	}

	//step4: 遍历内部点
	for (int n = 0; n < multiBlock.size(); ++n)
	{
		Block &block = multiBlock[n];
		for (int k = 0; k <= block.nodeK - 1; ++k)
        {
            // 三维跳过k = 0和block.nodeK - 1
            if (block.nodeK > 1 && (k == 0 || k == block.nodeK - 1)) continue;

			for (int j = 1; j < block.nodeJ - 1; ++j)
			{
				for (int i = 1; i < block.nodeI - 1; ++i)
				{
					block.nodeIndex[i][j][k] = nodeTotalSize;
					nodeTotalSize++;
				}
			}
		}
	}

	//step5: 检查点的编号
	for (int n = 0; n < multiBlock.size(); ++n)
	{
		Block &block = multiBlock[n];
		for (int k = 0; k < block.nodeK; ++k)
        {
			for (int j = 0; j < block.nodeJ; ++j)
			{
				for (int i = 0; i < block.nodeI; ++i)
				{
					if (block.nodeIndex[i][j][k] < 0)
					{
						std::cout << "blockID = " << n << ", i = " << i << ", j = " << j << ", k = " << k << std::endl;
						FatalError("MultiStructuredBlock::SetNodeIndex: nodeIndex is wrong!");
					}
				}
			}
		}
	}
}
