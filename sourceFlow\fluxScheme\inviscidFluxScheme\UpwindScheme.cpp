﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/UpwindScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

UpwindScheme::UpwindScheme(Package::FlowPackage &data,
                     Limiter::Limiter *limiter,
                     Flux::Flow::Precondition::Precondition *precondition)
    :
    InviscidFluxScheme(data, limiter, precondition)
{
    const int &level = data.GetMeshStruct().level;
    shockSwitchFlag = (data.GetFlowConfigure().GetFluxScheme(level).inviscid == Flux::Flow::Inviscid::AUSMDV);
    
    shockSwitch = nullptr;
}

void UpwindScheme::AddAverageResidual()
{
    if (shockSwitchFlag)
    {
        shockSwitch = &flowPackage.GetTempElementField("shockSwitch", Scalar0);
        this->CalculateShockSwitch();
    }

	// 内部面循环（含并行边界）
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index); 
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        //采用限制器重构左右面值
        (this->*GetFaceValue)(faceID, faceValue);
        
        //采用某种格式计算面通量（此为虚函数，需要具体格式实现）
        faceFlux = FaceFluxCalculate(faceID, faceValue);
        
        //将面通量累加到残值中
        residualMass.AddValue(ownerID, faceFlux.massFlux);
        residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
        residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
        residualMass.AddValue(neighID, -faceFlux.massFlux);
        residualMomentum.AddValue(neighID, -faceFlux.momentumFlux);
        residualEnergy.AddValue(neighID, -faceFlux.energyFlux);
        
#if defined(_EnableMultiSpecies_)
        for (int k = 0; k < speciesSize; k++)
        {
            const Scalar flux = faceFlux.massFlux > 0.0 ? faceValue.massFractionLeft[k] : faceValue.massFractionRight[k];
            faceFlux.massFractionFlux[k] = faceFlux.massFlux * flux;
			residualMassFraction[k]->AddValue(ownerID,  faceFlux.massFractionFlux[k]);
			residualMassFraction[k]->AddValue(neighID, -faceFlux.massFractionFlux[k]);
        }
#endif
    }
    
    if (shockSwitchFlag) flowPackage.FreeTempField(*shockSwitch);
    
    return;
}

void UpwindScheme::CalculateFaceFluxAverage(const int &faceID, NSFaceFlux &faceFlux)
{
    // 得到面相关信息
	const Face &face = mesh->GetFace(faceID);
	const int &ownerID = face.GetOwnerID();
	const int &neighID = face.GetNeighborID();
    const Vector faceArea = face.GetArea() * face.GetNormal();

    //获取左右面心值
	const Scalar &rhoLeft = rho.GetValue(ownerID);
	const Vector &ULeft = U.GetValue(ownerID);
	const Scalar &pLeft = p.GetValue(ownerID);
	const Scalar &HLeft = H.GetValue(ownerID);
	const Scalar &rhoRight = rho.GetValue(neighID);
	const Vector &URight = U.GetValue(neighID);
	const Scalar &pRight = p.GetValue(neighID);
	const Scalar &HRight = H.GetValue(neighID);

    const Scalar UFluxLeft = ULeft & faceArea;
    const Scalar UFluxRight = URight & faceArea;
    const Scalar rhoUFluxLeft = rhoLeft * UFluxLeft;
    const Scalar rhoUFluxRight = rhoRight * UFluxRight;
	const Scalar rhoEleft = rhoLeft * HLeft - pLeft + 0.5 * rhoLeft * (ULeft & ULeft);
	const Scalar rhoERight = rhoRight * HRight - pRight + 0.5 * rhoRight * (URight & URight);

    faceFlux.massFlux = 0.5 * (rhoUFluxLeft + rhoUFluxRight);
    faceFlux.momentumFlux = 0.5 *  (rhoUFluxLeft * ULeft + rhoUFluxRight * URight + (pLeft + pRight) * faceArea);
    faceFlux.energyFlux = 0.5 * ((rhoEleft + pLeft)* UFluxLeft + (rhoERight + pRight)* UFluxRight);

    //MRF内部面对流项的通量修正
    const std::shared_ptr<MRFZONE> mrf_tem = flowPackage.GetMRF();
    if(mrf_tem)
    {
        mrf_tem->RelativeFlux_Inerternal(face,faceArea,gamma1,
                                        faceFlux.massFlux,faceFlux.momentumFlux,faceFlux.energyFlux,
                                        rhoLeft,ULeft,pLeft,rhoRight,URight,pRight);
    }
}

void UpwindScheme::CalculateShockSwitch()
{
	// 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        
        // 获取左右面基本物理量
        const Vector &ULeft = U.GetValue(ownerID);
        const Vector &URight = U.GetValue(neighID);
		const Scalar &soundLeft = A.GetValue(ownerID);
		const Scalar &soundRight = A.GetValue(neighID);

        // 计算左右面法向速度
        const Vector &faceNormal = face.GetNormal();
        const Scalar velocityNormalLeft = ULeft & faceNormal;
        const Scalar velocityNormalRight = URight & faceNormal;

        // 激波开关计算
        if ( (velocityNormalLeft > soundLeft && velocityNormalRight < soundRight)
          || (velocityNormalLeft > -soundLeft && velocityNormalRight < -soundRight))
        {
            const Scalar machDifference = velocityNormalLeft / soundLeft - velocityNormalRight / soundRight;
            const Scalar factor = machDifference * machDifference;

            if (factor > shockSwitch->GetValue(ownerID)) shockSwitch->SetValue(ownerID, factor);
            if (factor > shockSwitch->GetValue(neighID)) shockSwitch->SetValue(neighID, factor);
        }
    }
    
    // 激波开关缩放
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar factor = Min(shockSwitch->GetValue(elementID), 1.0);
        shockSwitch->SetValue(elementID, (1.0 - cos(factor * PI)) * 0.5);
    }
}

Scalar UpwindScheme::ModifyEigenvalue(Scalar eigenvalue, const Scalar &localSound)
{
    const Scalar delta = 0.05 * localSound;
    if (eigenvalue > delta) return eigenvalue;
    else                    return 0.5 * (eigenvalue * eigenvalue + delta * delta) / delta;
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux