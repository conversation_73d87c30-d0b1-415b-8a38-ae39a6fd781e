﻿#include "meshProcess/motion/Motion.h"

Motion::Motion(const std::vector<Package::FlowPackage *> flowPackageVector_)
	:flowPackageVector(flowPackageVector_),
	flowConfig(flowPackageVector_[0]->GetFlowConfigure()),
	unsteadyCurrentTime(flowPackageVector[0]->GetUnsteadyStatus().currentTime),
	unsteadyTimeStep(flowPackageVector[0]->GetUnsteadyStatus().timeStep),
	mesh(flowPackageVector[0]->GetMeshStruct().mesh)
{

}

Motion::~Motion()
{}

Vector Motion::CaculateLineVelocityAroundAxis(const Vector &point,
	const Vector &axisPoint,
	const Vector &axisDirNor,
	const Scalar &omega)
{
	//1. 计算空间点绕轴线的矢径R
	Vector V1 = point - axisPoint;  //轴点到空间点的矢量
	Scalar m = V1 & axisDirNor; //V1在轴线方向矢量上的投影长度
	Vector V2 = m * axisDirNor; //V1在轴线方向矢量上的投影矢量
	Vector R = V1 - V2; //从轴线到空间点的矢径
	
	//2. 计算旋转线速度矢量
	Vector lineVelocity = (axisDirNor ^ R) * omega; //等于轴线方向单位矢量叉乘矢径，再乘以角速度

	return lineVelocity;
}
