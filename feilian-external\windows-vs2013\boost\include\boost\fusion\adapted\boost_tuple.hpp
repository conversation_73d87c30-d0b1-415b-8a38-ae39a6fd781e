/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying 
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_FUSION_BOOST_TUPLE_09272006_0732)
#define BOOST_FUSION_BOOST_TUPLE_09272006_0732

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/adapted/boost_tuple/tag_of.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/is_view_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/is_sequence_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/category_of_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/begin_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/end_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/size_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/at_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/value_at_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/detail/convert_impl.hpp>
#include <boost/fusion/adapted/boost_tuple/mpl/clear.hpp>

#endif
