// Copyright 2008 <PERSON>
// henry <PERSON> christophe AT hotmail DOT com
// This is an extended version of the state machine available in the boost::mpl library
// Distributed under the same license as the original.
// Copyright for the original version:
// Copyright 2005 <PERSON> and <PERSON><PERSON>sey Gurtovoy. Distributed
// under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_MSM_FRONT_EUML_ALGORITHM_H
#define BOOST_MSM_FRONT_EUML_ALGORITHM_H

#include <boost/msm/front/euml/iteration.hpp>
#include <boost/msm/front/euml/querying.hpp>
#include <boost/msm/front/euml/transformation.hpp>


#endif //BOOST_MSM_FRONT_EUML_ALGORITHM_H
