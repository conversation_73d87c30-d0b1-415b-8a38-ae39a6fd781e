//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_KARMA_AUXILIARY_MAR_26_2007_1225PM)
#define BOOST_SPIRIT_KARMA_AUXILIARY_MAR_26_2007_1225PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/auxiliary/eps.hpp>
#include <boost/spirit/home/<USER>/auxiliary/eol.hpp>
#include <boost/spirit/home/<USER>/auxiliary/lazy.hpp>
#include <boost/spirit/home/<USER>/auxiliary/attr_cast.hpp>

#endif
