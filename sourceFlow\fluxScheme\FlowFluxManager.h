﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowFluxManager.h
//! <AUTHOR>
//! @brief NS方程通量管理类，用于管理无粘项、粘性项、源项和限制器的各种格式
//! @date 2021-03-29
//
//------------------------------修改日志----------------------------------------
// 2021-07-13 李艳亮、乔龙
//    说明：修改限制器相关函数。
//
// 2021-03-29 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_FlowFluxManager_
#define _sourceFlow_fluxScheme_FlowFluxManager_

#include "sourceFlow/fluxScheme/FlowFluxScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/AUSMScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/AUSMDVScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/AUSMPWPScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/CentralScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/LaxFriedrichsScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/RoeScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/RoeSchemeMod.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/VanLeerScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/HLLCScheme.h"
#include "sourceFlow/fluxScheme/viscousFluxScheme/ViscousFluxScheme.h"
#include "sourceFlow/fluxScheme/limiter/LimiterNone.h"
#include "sourceFlow/fluxScheme/limiter/LimiterMinMod.h"
#include "sourceFlow/fluxScheme/limiter/LimiterVanLeer.h"
#include "sourceFlow/fluxScheme/limiter/LimiterVenkatakrishnan.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"
#include "sourceFlow/fluxScheme/sourceFluxScheme/SourceFluxScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief NS方程通量管理类
 * 
 */
class  FlowFluxManager
{
public:
    /**
     * @brief 构造函数，创建流场通量计算管理器对象
     * 
     * @param[in, out] data 流场包
     */
    FlowFluxManager(Package::FlowPackage &data);
    
    /**
     * @brief 析构函数
     * 
     */
    ~FlowFluxManager();

    /**
     * @brief 累加对流项平均残差
     * 
     */
    void AddConvectiveAverageResidual();

    /**
     * @brief 累加对流项耗散残差
     * 
     */
    void AddConvectiveDissipationResidual();

    /**
     * @brief 累加粘性项残差
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 累加源项残差
     * 
     */
    void AddSourceResidual();

    /**
     * @brief 残值置零
     * 
     */
    void SetResidualZero();

    /**
     * @brief 计算谱半径
     * 包括对流谱半径和粘性谱半径
     * 
     */
    void CalculateSpectralRadius();

    /**
     * @brief 获取预处理指针
     * 
     * @return Flux::Flow::Precondition::Precondition* 
     */
    Flux::Flow::Precondition::Precondition *GetPrecondition();

    /**
     * @brief 获取左右面值
     * 
     * @param[in] faceID 面编号
     */
    NSFaceValue GetFaceLeftRightValue(const int &faceID);

    /**
     * @brief 获取对流项空间离散格式
     * 
     */
    Flux::Flow::Inviscid::InviscidFluxScheme *GetInviscidFluxScheme() {return inviscidFlux;}

    /**
     * @brief 获取扩散项空间离散格式
     * 
     */
    Flux::Flow::Viscous::ViscousFluxScheme *GetViscousFluxScheme() {return viscousFlux;}

protected:
    /**
     * @brief 确定无粘项的通量格式指针
     * 
     */
    void SetInviscidFluxPointer();

    /**
     * @brief 确定有粘项的通量格式指针
     * 
     */
    void SetViscousFluxPointer();

    /**
     * @brief 确定源项的通量格式指针
     * 
     */
    void SetSourceFluxPointer();

    /**
     * @brief 确定通量限制器的指针
     * 
     */
    void SetLimiterPointer();

    /**
     * @brief 设置预处理指针
     * 
     */
    void SetPreconditionPointer();

protected:
    Package::FlowPackage &flowPackage; ///< 流场包
	const bool &nodeCenter; ///< 格点标识，true为格点

    Inviscid::InviscidFluxScheme *inviscidFlux; ///< 对流通量计算对象
    Viscous::ViscousFluxScheme *viscousFlux; ///< 粘性通量计算对象
    Source::SourceFluxScheme *sourceFlux; ///< 源项通量计算对象
    Limiter::Limiter *limiter; ///< 通量限制器对象
    Flux::Flow::Precondition::Precondition *precondition; ///< 预处理对象

    Inviscid::Scheme inviscidFluxScheme; ///< 对流通量计算方法
    Viscous::Scheme viscousFluxScheme; ///< 粘性通量计算方法
    Source::Scheme sourceFluxScheme; ///< 源项通量计算方法
    Limiter::Scheme limiterScheme; ///< 通量限制器方法
    FieldManipulation::GradientScheme gradientScheme; ///< 梯度计算方法
    Flux::ReconstructionOrder spatialOrder; ///< 空间精度
    
    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度

    ElementField<Vector> *gradientRho; ///< 密度梯度
    ElementField<Tensor> *gradientU; ///< 速度梯度
    ElementField<Vector> *gradientP; ///< 压强梯度
    ElementField<Vector> *gradientT; ///< 温度梯度
};
} // namespace Flow
} // namespace Flux

#endif 