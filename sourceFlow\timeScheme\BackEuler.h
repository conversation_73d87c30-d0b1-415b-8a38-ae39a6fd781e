﻿# ifndef _sourceFlow_timeScheme_BackEuler_
# define _sourceFlow_timeScheme_BackEuler_

#include "basic/CFD/linearSystemSolver/LinearSystemSolver.h"
#include "basic/common/Matrix.h"
#include "sourceFlow/timeScheme/FlowTime.h"
#include <iostream>
using namespace std;

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场GMRES时间推进类
 * 由基础时间类进行派生
 * 
 */
class BackEuler : public FlowTime
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    BackEuler(Package::FlowPackage &flowPackage_);

	/**
	* @brief 析构函数
	*/
	~BackEuler();

    /**
     * @brief GMRES求解内迭代求解一次
     * 
     * @param[in] recalculateResiduals 本层网格重新计算残值标识
     * @param[in] calculateForcingFunction 本层网格计算力源项标识
     */
	void Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction);
	
    /**
     * @brief 初始化
     * 
     * @param[in] initialType 流场初始化类型
     */
    virtual void Initialize(const Initialization::Type &initialType) = 0;    

protected:

	/**
	* @brief 初始化求解器
	*
	*/
    virtual void InitializeSolver() = 0;

    /**
     * @brief GMRES推进
     * 
     */
    virtual void Solve() = 0;    
	
    /**
     * @brief 流动变量更新
     * 
     */
    virtual void Update() = 0;  

    /**
    * @brief 网格重排
    *
    */
    void ReNumber();

protected:
    std::vector<std::vector<int>> OwnerIDCell; ///< 下三角阵的单元编号集合
    std::vector<std::vector<int>> NeighborIDCell; ///< 上三角阵的单元编号集合
    std::vector<std::vector<int>> OwnerIDFace; ///< 下三角阵的面编号集合
    std::vector<std::vector<int>> NeighborIDFace; ///< 上三角阵的面编号集合
    std::vector<int> elementIDSortMap; ///< 单元编号重排表
    
    bool LUSGSFlag; // LUSGS标识
};

} // namespace Flow
} // namespace Time

# endif