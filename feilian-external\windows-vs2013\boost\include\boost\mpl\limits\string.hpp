
#ifndef BOOST_MPL_LIMITS_STRING_HPP_INCLUDED
#define BOOST_MPL_LIMITS_STRING_HPP_INCLUDED

// Copyright <PERSON> 2009
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id: string.hpp 49239 2009-04-01 09:10:26Z eric_niebler $
// $Date: 2009-04-01 02:10:26 -0700 (Wed, 1 Apr 2009) $
// $Revision: 49239 $

#if !defined(BOOST_MPL_LIMIT_STRING_SIZE)
#   define BOOST_MPL_LIMIT_STRING_SIZE 32
#endif

#endif // BOOST_MPL_LIMITS_STRING_HPP_INCLUDED
