/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
    http://spirit.sourceforge.net/

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#ifndef BOOST_SPIRIT_INCLUDE_QI_CORE
#define BOOST_SPIRIT_INCLUDE_QI_CORE

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/parser.hpp>
#include <boost/spirit/home/<USER>/parse.hpp>
#include <boost/spirit/home/<USER>/what.hpp>
#include <boost/spirit/home/<USER>/action.hpp>
#include <boost/spirit/home/<USER>/char.hpp>
#include <boost/spirit/home/<USER>/directive.hpp>
#include <boost/spirit/home/<USER>/nonterminal.hpp>
#include <boost/spirit/home/<USER>/numeric.hpp>
#include <boost/spirit/home/<USER>/operator.hpp>
#include <boost/spirit/home/<USER>/string.hpp>

#endif
