//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_ITERATOR_HPP
#define BOOST_COMPUTE_ITERATOR_HPP

/// \file
///
/// Meta-header to include all Boost.Compute iterator headers.

#include <boost/compute/iterator/buffer_iterator.hpp>
#include <boost/compute/iterator/constant_iterator.hpp>
#include <boost/compute/iterator/constant_buffer_iterator.hpp>
#include <boost/compute/iterator/counting_iterator.hpp>
#include <boost/compute/iterator/discard_iterator.hpp>
#include <boost/compute/iterator/function_input_iterator.hpp>
#include <boost/compute/iterator/permutation_iterator.hpp>
#include <boost/compute/iterator/transform_iterator.hpp>
#include <boost/compute/iterator/zip_iterator.hpp>

#endif // BOOST_COMPUTE_ITERATOR_HPP
