﻿#if defined(_EnablePETSC_)

#include "basic/CFD/linearSystemSolver/BlockSparseMatrixPetsc.h"
#include "basic/field/ElementField.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

BlockSparseMatrixPetsc::BlockSparseMatrixPetsc(Mesh *mesh_, const int nVariable_)
	: BlockSparseMatrix(mesh_, nVariable_)
{
	zeroRow.resize(nVariable, Scalar0);
	one = 1.0;
}

BlockSparseMatrixPetsc::~BlockSparseMatrixPetsc()
{
	MatDestroy(&matrix);
}

void BlockSparseMatrixPetsc::Initialize()
{
	// 获取MPI信息
	PetscMPIInt rank, size;
	MPI_Comm_rank(PETSC_COMM_WORLD, &rank);
	MPI_Comm_size(PETSC_COMM_WORLD, &size);

	const int elementSize = mesh->GetElementNumberReal();
	PetscInt localSize = elementSize * nVariable; // 当前进程的总变量数

	// 获取所有子网格单元数量的和
	PetscInt globalSize;
	MPI_Allreduce(&localSize, &globalSize, 1, MPIU_INT, MPI_SUM, PETSC_COMM_WORLD);

	// 为矩阵预分配计算非零模式
	std::vector<PetscInt> realSize(elementSize, 1), paraSize(elementSize);
	
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

		if (mesh->JudgeRealElement(neighID))
		{
			realSize[ownerID]++;
			realSize[neighID]++;
		}
		else
		{
			paraSize[ownerID]++;
		}
	}

	// 创建并行块稀疏矩阵
	MatCreate(PETSC_COMM_WORLD, &matrix);

	// 设置矩阵的局部大小和全局大小
	MatSetSizes(matrix, localSize, localSize, globalSize, globalSize);
	MatSetType(matrix, MATMPIBAIJ);
	MatSetBlockSize(matrix, nVariable); // 设置每一小块的大小
	MatMPIBAIJSetPreallocation(matrix, nVariable, 0, realSize.data(), 0, paraSize.data());
	MatSetOption(matrix, MAT_NEW_NONZERO_ALLOCATION_ERR, PETSC_FALSE);

	// 创建当地单元的全局编号
	this->CreateGlobalID();

	// 边界单元数据映射关系及数据创建
	boundaryElementIndex.resize(elementSize, -1);
	for (int elemID = 0; elemID < elementSize; elemID++)
	{
		if (mesh->JudegBoundaryElemnt(elemID))
		{
			Matrix block(nVariable, nVariable);
			std::vector<std::pair<int, Matrix>> blocks;

			// 第一个为对角块
			blocks.push_back(std::make_pair(elemID, block));

			// 其余为相邻块
			const Element &elem = mesh->GetElement(elemID);
			for (int index = 0; index < elem.GetFaceSize(); ++index)
			{
				const int faceID = elem.GetFaceID(index);
				const Face &face = mesh->GetFace(faceID);
				const int &ownerID = face.GetOwnerID();
				const int &neighID = face.GetNeighborID();
				if (mesh->JudgeBoundaryFace(faceID)) continue;

				const int adjElem = ownerID == elemID ? neighID : ownerID;
				blocks.push_back(std::make_pair(adjElem, block));
			}
			boundaryBlocks.push_back(blocks);
			boundaryElementIndex[elemID] = boundaryBlocks.size() - 1;
		}
	}
}

void BlockSparseMatrixPetsc::CreateGlobalID() 
{
	// 获取MPI信息
	PetscMPIInt rank, size;
	MPI_Comm_rank(PETSC_COMM_WORLD, &rank);
	MPI_Comm_size(PETSC_COMM_WORLD, &size);

	// 局部大小
	const int elementSize = mesh->GetElementNumberReal();
	const int elementSizeAll = mesh->GetElementNumberAll();
	const int localSize = elementSize;

	// 计算起始编号
	int startGlobalID = 0;
    std::vector<int> localSizeVector(size);
    boost::mpi::all_gather(MPI::mpiWorld, localSize, localSizeVector);
	for (int i = 0; i < rank; i++) startGlobalID += localSizeVector[i];

	// 计算真实单元及并行边界单元的全局编号
	ElementField<int> fieldTemp(mesh, -1);
	for (int i = 0; i < elementSize; i++) fieldTemp.SetValue(i, startGlobalID + i);
	fieldTemp.SetGhostlValueParallel();

	// 存储单元全局编号
	globalIDs.resize(elementSizeAll, -1);
	for (int i = 0; i < elementSizeAll; i++) globalIDs[i] = fieldTemp.GetValue(i);
}

void BlockSparseMatrixPetsc::SetValZero() 
{
	MatZeroEntries(matrix);
	for (int i = 0; i < boundaryBlocks.size(); i++)
	{
		for (int j = 0; j < boundaryBlocks[i].size(); j++)
			boundaryBlocks[i][j].second.SetZero();
	}
}

void BlockSparseMatrixPetsc::AddVal2Diag(const int &row, const Scalar &val) 
{
	if (globalIDs[row] < 0) return;

	const int globalID = globalIDs[row] * nVariable;
	for (int i = 0; i < nVariable; ++i)
	{
		const int index = globalID + i;
		MatSetValues(matrix, 1, &index, 1, &index, &val, ADD_VALUES);
	}

	if (mesh->JudegBoundaryElemnt(row))
	{
		for (int i = 0; i < nVariable; ++i)
			boundaryBlocks[boundaryElementIndex[row]][0].second.AddValue(i, i, val);
	}
}

void BlockSparseMatrixPetsc::AddBlock2Diag(const int &row, const Matrix &val) 
{
	const int &globalID = globalIDs[row];
	if (globalID < 0) return;

	MatSetValuesBlocked(matrix, 1, &globalID, 1, &globalID, val.GetData(), ADD_VALUES);

	if (mesh->JudegBoundaryElemnt(row))
		boundaryBlocks[boundaryElementIndex[row]][0].second += val;
}

void BlockSparseMatrixPetsc::UpdateBlocks(const int &faceID, const int &elemI, const int &elemJ, const Matrix &valI, const Matrix &valJ)
{
	const int &globalIDI = globalIDs[elemI];
	const int &globalIDJ = globalIDs[elemJ];
	if (globalIDI < 0 || globalIDJ < 0) return;


	MatSetValuesBlocked(matrix, 1, &globalIDI, 1, &globalIDI,  valI.GetData(), ADD_VALUES);
	MatSetValuesBlocked(matrix, 1, &globalIDI, 1, &globalIDJ,  valJ.GetData(), ADD_VALUES);
	if (mesh->JudegBoundaryElemnt(elemI))
	{
		boundaryBlocks[boundaryElementIndex[elemI]][0].second += valI;
		auto &blocks = boundaryBlocks[boundaryElementIndex[elemI]];
		for (int i = 1; i < blocks.size(); i++)
		{
			if (blocks[i].first == elemJ)
			{
				blocks[i].second += valJ;
				break;
			}
		}
	}
	
	if (mesh->JudgeRealElement(elemJ))
	{
		MatSetValuesBlocked(matrix, 1, &globalIDJ, 1, &globalIDI,  (-valI).GetData(), ADD_VALUES);
		MatSetValuesBlocked(matrix, 1, &globalIDJ, 1, &globalIDJ,  (-valJ).GetData(), ADD_VALUES);
		
		if (mesh->JudegBoundaryElemnt(elemJ))
		{
			boundaryBlocks[boundaryElementIndex[elemJ]][0].second -= valJ;
			auto &blocks = boundaryBlocks[boundaryElementIndex[elemJ]];
			for (int i = 1; i < blocks.size(); i++)
			{
				if (blocks[i].first == elemI)
				{
					blocks[i].second -= valI;
					break;
				}
			}
		}
	}
}

void BlockSparseMatrixPetsc::DeleteValsRowi(const int &row, const int &index0)
{
	if (globalIDs[row] < 0) return;
	if (!mesh->JudegBoundaryElemnt(row)) return;

	const int globalIDI0 = globalIDs[row] * nVariable + index0;
	auto &blocks = boundaryBlocks[boundaryElementIndex[row]];
	for (int i = 0; i < blocks.size(); i++)
	{
		const int &elemID = blocks[i].first;
		for (int j = 0; j < nVariable; ++j)
		{
			const int globalIDJ0 = globalIDs[elemID] * nVariable + j;
			PetscScalar value1 = -blocks[i].second.GetValue(index0, j);
			if (globalIDI0 == globalIDJ0) value1 += 1.0;
			MatSetValues(matrix, 1, &globalIDI0, 1, &globalIDJ0, &value1, ADD_VALUES);
			blocks[i].second.SetValue(index0, j, Scalar0);
		}
	}
}

#endif
