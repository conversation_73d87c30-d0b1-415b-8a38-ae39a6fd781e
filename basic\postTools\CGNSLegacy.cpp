﻿#include "basic/postTools/CGNSLegacy.h"

namespace Post
{
CGNSLegacy::CGNSLegacy(Mesh *mesh_, const bool dualFlag_, const Position outputPosition_, const bool exportInteriorFlag_, const std::string caseName_)
	: BasePost(mesh_, dualFlag_, outputPosition_, exportInteriorFlag_, caseName_)
{
}

void CGNSLegacy::WriteFile()
{
	if (caseName.size() == 0) FatalError("CGNSLegacy::WriteFile: case name is missing!");

	// 确定文件名称与文件对象
	std::string fileName = caseName + ".cgns";
	std::fstream file;

	int fileIndex = 0, baseIndex = 1, zoneIndex = 1;
	int dimensions = (int)mesh->GetMeshDimension();

	// 创建CGNS文件
	if (cg_open(fileName.c_str(), CG_MODE_WRITE, &fileIndex) != CG_OK) {
		std::cerr << "无法创建CGNS文件" << std::endl;
	}

	// 创建基础节点
	if (cg_base_write(fileIndex, "Base", dimensions, dimensions, &baseIndex) != CG_OK) {
		std::cerr << "无法创建基础节点" << std::endl;
		cg_close(fileIndex);
	}

	// 创建非结构化网格区域
	nodeSize = mesh->GetNodeNumber();
	elemSize = mesh->GetElementNumberReal();
	boundaryFaceSize = 0;
	for (int i = 0; i < mesh->GetBoundarySize(); i++) boundaryFaceSize += mesh->GetBoundaryFaceSize(i);
	cgsize_t gridSize[3]; gridSize[0] = nodeSize; gridSize[1] = elemSize; gridSize[2] = boundaryFaceSize;
	if (cg_zone_write(fileIndex, baseIndex, "Zone1", gridSize, CGNS_ENUMV(Unstructured), &zoneIndex) != CG_OK)
	{
		std::cerr << "无法创建区域" << std::endl;
		cg_close(fileIndex);
	}

	// 写入网格数据
	if (!WriteGrid(fileIndex, baseIndex, zoneIndex))
	{
		cg_close(fileIndex);
	}

	// 写入流场数据
	if (!WriteVolumeData(fileIndex, baseIndex, zoneIndex))
	{
		cg_close(fileIndex);
	}

	// 写入边界条件
	for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
	{
		if (!WriteBoundaryData(fileIndex, baseIndex, zoneIndex++, patchID))
		{
			cg_close(fileIndex);
		}
	}

	// 关闭CGNS文件
	if (cg_close(fileIndex) != CG_OK)
	{
		std::cerr << "无法关闭CGNS文件" << std::endl;
	}

	std::size_t pos = caseName.rfind("_");
	if (pos == caseName.npos) Print("\nCGNS文件路径: " + caseName);
	else                      Print("\nCGNS文件路径: " + caseName.substr(0, pos));

	return;
}

// 写入网格数据
bool CGNSLegacy::WriteGrid(const int &fileIndex, const int &baseIndex, const int &zoneIndex)
{
	int coordIndex;
	const char* coordNames[3] = { "CoordinateX", "CoordinateY", "CoordinateZ" };
	std::vector<double> coords[3];

	// 准备坐标数组
	coords[0].resize(nodeSize);
	coords[1].resize(nodeSize);
	coords[2].resize(nodeSize);
	for (size_t j = 0; j < nodeSize; ++j)
	{
		coords[0][j] = mesh->GetNode(j).X();
		coords[1][j] = mesh->GetNode(j).Y();
		coords[2][j] = mesh->GetNode(j).Z();
	}

	// 写入网格坐标
	for (int i = 0; i < 3; ++i)
	{
		if (cg_coord_write(fileIndex, baseIndex, zoneIndex, CGNS_ENUMV(RealDouble),
			coordNames[i], coords[i].data(), &coordIndex) != CG_OK)
		{
			std::cerr << "无法写入" << coordNames[i] << "坐标数据" << std::endl;
			return false;
		}
	}

	std::vector<cgsize_t> elements;
	for (int i = 0; i < elemSize; i++)
	{
		const auto &elem = mesh->GetElement(i);
		for (int j = 0; j < elem.GetNodeSize(); j++)
			elements.push_back(elem.GetNodeID(j));
	}
	// 写入体单元数据（修正范围表示）
	int sectionIndex = 0;
	if (cg_section_write(fileIndex, baseIndex, zoneIndex, "VolumeElements",
		CGNS_ENUMV(HEXA_8), 1, elemSize, 0, elements.data(), &sectionIndex) != CG_OK)
	{
		std::cerr << "无法写入体单元数据" << std::endl;
		return false;
	}

	// 写入边界面数据（修正起始和结束编号）
	for (int i = 0; i < mesh->GetBoundarySize(); i++)
	{
		sectionIndex++;
		std::vector<cgsize_t> boundaryFaces;
		boundaryFaceSize = 0;
		const int faceSize = mesh->GetBoundaryFaceSize(i);
		for (int j = 0; j < faceSize; j++)
		{
			const int faceID = mesh->GetBoundaryFaceID(i, j);
			const auto &face = mesh->GetFace(faceID);
			for (int k = 0; k < face.GetNodeSize(); k++)
				boundaryFaces.push_back(face.GetNodeID(k));
		}
		int startElement = elemSize + 1;
		int endElement = startElement + boundaryFaceSize - 1;
		if (cg_section_write(fileIndex, baseIndex, zoneIndex, "BoundaryFaces",
			CGNS_ENUMV(QUAD_4), startElement, endElement, 0, boundaryFaces.data(), &sectionIndex) != CG_OK)
		{
			std::cerr << "无法写入边界面数据" << std::endl;
			return false;
		}
	}

	return true;
}

// 写入流场数据
bool CGNSLegacy::WriteVolumeData(const int &fileIndex, const int &baseIndex, const int &zoneIndex)
{
	int solIndex = 0, fieldIndex = 0;

	// 准备流场数据数组
	const std::vector<std::string> variableNames = GetVariableNames();
	const int variableSize = variableNames.size();

	std::vector<std::vector<Scalar>> fieldValues(variableSize);
	for (int i = 0; i < variableSize; ++i)
	{
		fieldValues[i].resize(elemSize);
	}

	for (int j = 0; j < scalarField.size(); j++)
	{
		for (int i = 0; i < elemSize; i++) fieldValues[fieldIndex][i] = GetScalarFieldValue(scalarField[j], i);
		fieldIndex++;
	}

	for (int j = 0; j < vectorField.size(); j++)
	{
		for (int i = 0; i < elemSize; i++) fieldValues[fieldIndex][i] = GetVectorFieldValue(vectorField[j], i).X();
		fieldIndex++;

		for (int i = 0; i < elemSize; i++) fieldValues[fieldIndex][i] = GetVectorFieldValue(vectorField[j], i).Y();
		fieldIndex++;

		if (!dim2)
		{
			for (int i = 0; i < elemSize; i++) fieldValues[fieldIndex][i] = GetVectorFieldValue(vectorField[j], i).Z();
			fieldIndex++;
		}
	}

	// 创建流场解决方案节点
	if (cg_sol_write(fileIndex, baseIndex, zoneIndex, "Solution1", CGNS_ENUMV(Vertex), &solIndex) != CG_OK)
	{
		std::cerr << "无法创建解决方案节点" << std::endl;
		return false;
	}

	// 写入流场数据
	for (int i = 0; i < variableSize; ++i)
	{
		if (cg_field_write(fileIndex, baseIndex, zoneIndex, solIndex,
			CGNS_ENUMV(RealDouble), variableNames[i].c_str(),
			fieldValues[i].data(), &fieldIndex) != CG_OK)
		{
			std::cerr << "无法写入" << variableNames[i].c_str() << "场数据" << std::endl;
			return false;
		}
	}

	return true;
}

// 写入边界条件
bool CGNSLegacy::WriteBoundaryData(const int &fileIndex, const int &baseIndex, const int &zoneIndex, const int &patchID)
{
	const int &faceNumber = mesh->GetBoundaryFaceSize(patchID);
	for (size_t i = 0; i < faceNumber; ++i)
	{
		// const auto& faceID = mesh->GetBoundaryFaceID(patchID, i);
		// const auto& face = mesh->GetFace(faceID);
		// const auto& value = boundaryData.values[i];
		// 
		// std::vector<Node>
		// // 写入边界条件
		// if (cg_boco_write(fileIndex, baseIndex, zoneIndex, face.name,
		// 	CGNS_ENUMV(BCWall), CGNS_ENUMV(PointList),
		// 	4, face.nodes, &bocIndex) != CG_OK)
		// {
		// 	std::cerr << "无法写入" << face.name << "边界条件" << std::endl;
		// 	return false;
		// }
		// 
		// // 准备法向数据
		// std::vector<double> normalListData = {
		// 	value.x,  // 压力
		// 	value.y,  // x方向速度
		// 	value.z   // y方向速度
		// };
		// 
		// // 创建NormalIndex数组（使用nullptr表示不需要索引）
		// if (cg_boco_normal_write(fileIndex, baseIndex, zoneIndex, bocIndex,
		// 	nullptr,  // NormalIndex设为nullptr
		// 	1,        // NormalListFlag设为1
		// 	CGNS_ENUMV(RealDouble),
		// 	normalListData.data()) != CG_OK) {
		// 	std::cerr << "无法写入" << face.name << "边界物理量" << std::endl;
		// 	return false;
		// }
	}

	return true;
}

std::vector<std::string> CGNSLegacy::GetVariableNames()
{
	std::vector<std::string> variableNames;

	if (fileType != FileType::SOLUTION)
	{
		variableNames.push_back("x [m]");
		variableNames.push_back("y [m]");
		if (!dim2) variableNames.push_back("z [m]");
	}

	if (fileType != FileType::GRID)
	{
		for (int i = 0; i < scalarField.size(); i++)
			variableNames.push_back(GetScalarFieldName(scalarField[i]));

		for (int i = 0; i < vectorField.size(); i++)
		{
			std::string vectorName = GetScalarFieldName(vectorField[i]);
			variableNames.push_back(vectorName + ".x");
			variableNames.push_back(vectorName + ".y");
			if (!dim2) variableNames.push_back(vectorName + ".z");
		}
	}

	return variableNames;
}

template Scalar CGNSLegacy::GetFieldValue(const ElementField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector CGNSLegacy::GetFieldValue(const ElementField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type CGNSLegacy::GetFieldValue(const ElementField<Type> &phi, const int &ID, const int boundaryID)
{
	if (outputAtCenter)
	{
		return GetCenterValue(phi, ID, boundaryID);
	}
	else
	{
		const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
		return GetNodeValue(phi, nodeID, boundaryID);
	}
}

template Scalar CGNSLegacy::GetFieldValue(const NodeField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector CGNSLegacy::GetFieldValue(const NodeField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type CGNSLegacy::GetFieldValue(const NodeField<Type> &phi, const int &ID, const int boundaryID)
{
	if (outputAtCenter)
	{
		return GetCenterValue(phi, ID, boundaryID);
	}
	else
	{
		const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
		return GetNodeValue(phi, nodeID, boundaryID);
	}
}

}// namespace Post