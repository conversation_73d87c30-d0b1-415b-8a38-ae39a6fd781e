! -*- Mode: F90; -*-
! Copyright(c) Microsoft Corporation.All rights reserved.
! Licensed under the MIT License.
!
!      (C) 2004 by Argonne National Laboratory.
!      (C) 2015 by Microsoft Corporation
!
!                                  MPICH COPYRIGHT
!
!   The following is a notice of limited availability of the code, and disclaimer
!   which must be included in the prologue of the code and in all source listings
!   of the code.
!
!   Copyright Notice
!    + 2002 University of Chicago
!
!   Permission is hereby granted to use, reproduce, prepare derivative works, and
!   to redistribute to others.  This software was authored by:
!
!   Mathematics and Computer Science Division
!   Argonne National Laboratory, Argonne IL 60439
!
!   (and)
!
!   Department of Computer Science
!   University of Illinois at Urbana-Champaign
!
!
!                                 GOVERNMENT LICENSE
!
!   Portions of this material resulted from work developed under a U.S.
!   Government Contract and are subject to the following license: the Government
!   is granted for itself and others acting on its behalf a paid-up, nonexclusive,
!   irrevocable worldwide license in this computer software to reproduce, prepare
!   derivative works, and perform publicly and display publicly.
!
!                                     DISCLAIMER
!
!   This computer code material was prepared, in part, as an account of work
!   sponsored by an agency of the United States Government.  Neither the United
!   States, nor the University of Chicago, nor any of their employees, makes any
!   warranty express or implied, or assumes any legal liability or responsibility
!   for the accuracy, completeness, or usefulness of any information, apparatus,
!   product, or process disclosed, or represents that its use would not infringe
!   privately owned rights.
!
!
       MODULE MPI_CONSTANTS
       IMPLICIT NONE

       INTEGER MPI_SOURCE, MPI_TAG, MPI_ERROR
       PARAMETER (MPI_SOURCE=3,MPI_TAG=4,MPI_ERROR=5)
       INTEGER MPI_STATUS_SIZE
       PARAMETER (MPI_STATUS_SIZE=5)
       INTEGER MPI_STATUS_IGNORE(MPI_STATUS_SIZE)
       INTEGER MPI_STATUSES_IGNORE(MPI_STATUS_SIZE,1)
       INTEGER MPI_ERRCODES_IGNORE(1)
       CHARACTER*1 MPI_ARGVS_NULL(1,1)
       CHARACTER*1 MPI_ARGV_NULL(1)
       INTEGER MPI_SUCCESS
       PARAMETER (MPI_SUCCESS=0)
       INTEGER MPI_ERR_OTHER
       PARAMETER (MPI_ERR_OTHER=15)
       INTEGER MPI_ERR_WIN
       PARAMETER (MPI_ERR_WIN=45)
       INTEGER MPI_ERR_FILE
       PARAMETER (MPI_ERR_FILE=27)
       INTEGER MPI_ERR_COUNT
       PARAMETER (MPI_ERR_COUNT=2)
       INTEGER MPI_ERR_SPAWN
       PARAMETER (MPI_ERR_SPAWN=42)
       INTEGER MPI_ERR_BASE
       PARAMETER (MPI_ERR_BASE=46)
       INTEGER MPI_ERR_RMA_CONFLICT
       PARAMETER (MPI_ERR_RMA_CONFLICT=49)
       INTEGER MPI_ERR_IN_STATUS
       PARAMETER (MPI_ERR_IN_STATUS=17)
       INTEGER MPI_ERR_INFO_KEY
       PARAMETER (MPI_ERR_INFO_KEY=29)
       INTEGER MPI_ERR_LOCKTYPE
       PARAMETER (MPI_ERR_LOCKTYPE=47)
       INTEGER MPI_ERR_OP
       PARAMETER (MPI_ERR_OP=9)
       INTEGER MPI_ERR_ARG
       PARAMETER (MPI_ERR_ARG=12)
       INTEGER MPI_ERR_READ_ONLY
       PARAMETER (MPI_ERR_READ_ONLY=40)
       INTEGER MPI_ERR_SIZE
       PARAMETER (MPI_ERR_SIZE=51)
       INTEGER MPI_ERR_BUFFER
       PARAMETER (MPI_ERR_BUFFER=1)
       INTEGER MPI_ERR_DUP_DATAREP
       PARAMETER (MPI_ERR_DUP_DATAREP=24)
       INTEGER MPI_ERR_UNSUPPORTED_DATAREP
       PARAMETER (MPI_ERR_UNSUPPORTED_DATAREP=43)
       INTEGER MPI_ERR_LASTCODE
       PARAMETER (MPI_ERR_LASTCODE=1073741823)
       INTEGER MPI_ERR_TRUNCATE
       PARAMETER (MPI_ERR_TRUNCATE=14)
       INTEGER MPI_ERR_DISP
       PARAMETER (MPI_ERR_DISP=52)
       INTEGER MPI_ERR_PORT
       PARAMETER (MPI_ERR_PORT=38)
       INTEGER MPI_ERR_INFO_NOKEY
       PARAMETER (MPI_ERR_INFO_NOKEY=31)
       INTEGER MPI_ERR_ASSERT
       PARAMETER (MPI_ERR_ASSERT=53)
       INTEGER MPI_ERR_FILE_EXISTS
       PARAMETER (MPI_ERR_FILE_EXISTS=25)
       INTEGER MPI_ERR_PENDING
       PARAMETER (MPI_ERR_PENDING=18)
       INTEGER MPI_ERR_COMM
       PARAMETER (MPI_ERR_COMM=5)
       INTEGER MPI_ERR_KEYVAL
       PARAMETER (MPI_ERR_KEYVAL=48)
       INTEGER MPI_ERR_NAME
       PARAMETER (MPI_ERR_NAME=33)
       INTEGER MPI_ERR_REQUEST
       PARAMETER (MPI_ERR_REQUEST=19)
       INTEGER MPI_ERR_GROUP
       PARAMETER (MPI_ERR_GROUP=8)
       INTEGER MPI_ERR_TOPOLOGY
       PARAMETER (MPI_ERR_TOPOLOGY=10)
       INTEGER MPI_ERR_TYPE
       PARAMETER (MPI_ERR_TYPE=3)
       INTEGER MPI_ERR_TAG
       PARAMETER (MPI_ERR_TAG=4)
       INTEGER MPI_ERR_INFO_VALUE
       PARAMETER (MPI_ERR_INFO_VALUE=30)
       INTEGER MPI_ERR_NOT_SAME
       PARAMETER (MPI_ERR_NOT_SAME=35)
       INTEGER MPI_ERR_RMA_SYNC
       PARAMETER (MPI_ERR_RMA_SYNC=50)
       INTEGER MPI_ERR_INFO
       PARAMETER (MPI_ERR_INFO=28)
       INTEGER MPI_ERR_NO_MEM
       PARAMETER (MPI_ERR_NO_MEM=34)
       INTEGER MPI_ERR_BAD_FILE
       PARAMETER (MPI_ERR_BAD_FILE=22)
       INTEGER MPI_ERR_FILE_IN_USE
       PARAMETER (MPI_ERR_FILE_IN_USE=26)
       INTEGER MPI_ERR_UNKNOWN
       PARAMETER (MPI_ERR_UNKNOWN=13)
       INTEGER MPI_ERR_UNSUPPORTED_OPERATION
       PARAMETER (MPI_ERR_UNSUPPORTED_OPERATION=44)
       INTEGER MPI_ERR_QUOTA
       PARAMETER (MPI_ERR_QUOTA=39)
       INTEGER MPI_ERR_AMODE
       PARAMETER (MPI_ERR_AMODE=21)
       INTEGER MPI_ERR_ROOT
       PARAMETER (MPI_ERR_ROOT=7)
       INTEGER MPI_ERR_RANK
       PARAMETER (MPI_ERR_RANK=6)
       INTEGER MPI_ERR_DIMS
       PARAMETER (MPI_ERR_DIMS=11)
       INTEGER MPI_ERR_NO_SUCH_FILE
       PARAMETER (MPI_ERR_NO_SUCH_FILE=37)
       INTEGER MPI_ERR_SERVICE
       PARAMETER (MPI_ERR_SERVICE=41)
       INTEGER MPI_ERR_INTERN
       PARAMETER (MPI_ERR_INTERN=16)
       INTEGER MPI_ERR_IO
       PARAMETER (MPI_ERR_IO=32)
       INTEGER MPI_ERR_ACCESS
       PARAMETER (MPI_ERR_ACCESS=20)
       INTEGER MPI_ERR_NO_SPACE
       PARAMETER (MPI_ERR_NO_SPACE=36)
       INTEGER MPI_ERR_CONVERSION
       PARAMETER (MPI_ERR_CONVERSION=23)
       INTEGER MPI_ERRORS_ARE_FATAL
       PARAMETER (MPI_ERRORS_ARE_FATAL=1409286144)
       INTEGER MPI_ERRORS_RETURN
       PARAMETER (MPI_ERRORS_RETURN=1409286145)
       INTEGER MPI_IDENT
       PARAMETER (MPI_IDENT=0)
       INTEGER MPI_CONGRUENT
       PARAMETER (MPI_CONGRUENT=1)
       INTEGER MPI_SIMILAR
       PARAMETER (MPI_SIMILAR=2)
       INTEGER MPI_UNEQUAL
       PARAMETER (MPI_UNEQUAL=3)
       INTEGER MPI_MAX
       PARAMETER (MPI_MAX=1476395009)
       INTEGER MPI_MIN
       PARAMETER (MPI_MIN=1476395010)
       INTEGER MPI_SUM
       PARAMETER (MPI_SUM=1476395011)
       INTEGER MPI_PROD
       PARAMETER (MPI_PROD=1476395012)
       INTEGER MPI_LAND
       PARAMETER (MPI_LAND=1476395013)
       INTEGER MPI_BAND
       PARAMETER (MPI_BAND=1476395014)
       INTEGER MPI_LOR
       PARAMETER (MPI_LOR=1476395015)
       INTEGER MPI_BOR
       PARAMETER (MPI_BOR=1476395016)
       INTEGER MPI_LXOR
       PARAMETER (MPI_LXOR=1476395017)
       INTEGER MPI_BXOR
       PARAMETER (MPI_BXOR=1476395018)
       INTEGER MPI_MINLOC
       PARAMETER (MPI_MINLOC=1476395019)
       INTEGER MPI_MAXLOC
       PARAMETER (MPI_MAXLOC=1476395020)
       INTEGER MPI_REPLACE
       PARAMETER (MPI_REPLACE=1476395021)
       INTEGER MPI_NO_OP
       PARAMETER (MPI_NO_OP=1476395022)
       INTEGER MPI_COMM_WORLD
       PARAMETER (MPI_COMM_WORLD=1140850688)
       INTEGER MPI_COMM_SELF
       PARAMETER (MPI_COMM_SELF=1140850689)
       INTEGER MPI_COMM_TYPE_SHARED
       PARAMETER (MPI_COMM_TYPE_SHARED=1)
       INTEGER MPI_GROUP_EMPTY
       PARAMETER (MPI_GROUP_EMPTY=1207959552)
       INTEGER MPI_COMM_NULL
       PARAMETER (MPI_COMM_NULL=67108864)
       INTEGER MPI_WIN_NULL
       PARAMETER (MPI_WIN_NULL=536870912)
       INTEGER MPI_FILE_NULL
       PARAMETER (MPI_FILE_NULL=0)
       INTEGER MPI_GROUP_NULL
       PARAMETER (MPI_GROUP_NULL=134217728)
       INTEGER MPI_OP_NULL
       PARAMETER (MPI_OP_NULL=402653184)
       INTEGER MPI_DATATYPE_NULL
       PARAMETER (MPI_DATATYPE_NULL=z'0c000000')
       INTEGER MPI_REQUEST_NULL
       PARAMETER (MPI_REQUEST_NULL=738197504)
       INTEGER MPI_ERRHANDLER_NULL
       PARAMETER (MPI_ERRHANDLER_NULL=335544320)
       INTEGER MPI_INFO_NULL
       PARAMETER (MPI_INFO_NULL=469762048)
       INTEGER MPI_MESSAGE_NULL
       PARAMETER (MPI_MESSAGE_NULL=805306368)
       INTEGER MPI_MESSAGE_NO_PROC
       PARAMETER (MPI_MESSAGE_NO_PROC=1879048192)
       INTEGER MPI_TAG_UB
       PARAMETER (MPI_TAG_UB=1681915906)
       INTEGER MPI_HOST
       PARAMETER (MPI_HOST=1681915908)
       INTEGER MPI_IO
       PARAMETER (MPI_IO=1681915910)
       INTEGER MPI_WTIME_IS_GLOBAL
       PARAMETER (MPI_WTIME_IS_GLOBAL=1681915912)
       INTEGER MPI_UNIVERSE_SIZE
       PARAMETER (MPI_UNIVERSE_SIZE=1681915914)
       INTEGER MPI_LASTUSEDCODE
       PARAMETER (MPI_LASTUSEDCODE=1681915916)
       INTEGER MPI_APPNUM
       PARAMETER (MPI_APPNUM=1681915918)
       INTEGER MPI_WIN_BASE
       PARAMETER (MPI_WIN_BASE=1711276034)
       INTEGER MPI_WIN_SIZE
       PARAMETER (MPI_WIN_SIZE=1711276036)
       INTEGER MPI_WIN_DISP_UNIT
       PARAMETER (MPI_WIN_DISP_UNIT=1711276038)
       INTEGER MPI_MAX_ERROR_STRING
       PARAMETER (MPI_MAX_ERROR_STRING=511)
       INTEGER MPI_MAX_PORT_NAME
       PARAMETER (MPI_MAX_PORT_NAME=255)
       INTEGER MPI_MAX_OBJECT_NAME
       PARAMETER (MPI_MAX_OBJECT_NAME=127)
       INTEGER MPI_MAX_INFO_KEY
       PARAMETER (MPI_MAX_INFO_KEY=254)
       INTEGER MPI_MAX_INFO_VAL
       PARAMETER (MPI_MAX_INFO_VAL=1023)
       INTEGER MPI_MAX_PROCESSOR_NAME
       PARAMETER (MPI_MAX_PROCESSOR_NAME=128-1)
       INTEGER MPI_MAX_DATAREP_STRING
       PARAMETER (MPI_MAX_DATAREP_STRING=127)
       INTEGER MPI_MAX_LIBRARY_VERSION_STRING
       PARAMETER (MPI_MAX_LIBRARY_VERSION_STRING=64-1)
       INTEGER MPI_UNDEFINED
       PARAMETER (MPI_UNDEFINED=(-32766))
       INTEGER MPI_KEYVAL_INVALID
       PARAMETER (MPI_KEYVAL_INVALID=603979776)
       INTEGER MPI_BSEND_OVERHEAD
       PARAMETER (MPI_BSEND_OVERHEAD=(95))
       INTEGER MPI_PROC_NULL
       PARAMETER (MPI_PROC_NULL=-1)
       INTEGER MPI_ANY_SOURCE
       PARAMETER (MPI_ANY_SOURCE=-2)
       INTEGER MPI_ANY_TAG
       PARAMETER (MPI_ANY_TAG=-1)
       INTEGER MPI_ROOT
       PARAMETER (MPI_ROOT=-3)
       INTEGER MPI_GRAPH
       PARAMETER (MPI_GRAPH=1)
       INTEGER MPI_CART
       PARAMETER (MPI_CART=2)
       INTEGER MPI_DIST_GRAPH
       PARAMETER (MPI_DIST_GRAPH=3)
       INTEGER MPI_VERSION
       PARAMETER (MPI_VERSION=2)
       INTEGER MPI_SUBVERSION
       PARAMETER (MPI_SUBVERSION=0)
       INTEGER MPI_LOCK_EXCLUSIVE
       PARAMETER (MPI_LOCK_EXCLUSIVE=234)
       INTEGER MPI_LOCK_SHARED
       PARAMETER (MPI_LOCK_SHARED=235)
       INTEGER MPI_CHAR
       PARAMETER (MPI_CHAR=z'4c000101')
       INTEGER MPI_UNSIGNED_CHAR
       PARAMETER (MPI_UNSIGNED_CHAR=z'4c000102')
       INTEGER MPI_SHORT
       PARAMETER (MPI_SHORT=z'4c000203')
       INTEGER MPI_UNSIGNED_SHORT
       PARAMETER (MPI_UNSIGNED_SHORT=z'4c000204')
       INTEGER MPI_INT
       PARAMETER (MPI_INT=z'4c000405')
       INTEGER MPI_UNSIGNED
       PARAMETER (MPI_UNSIGNED=z'4c000406')
       INTEGER MPI_LONG
       PARAMETER (MPI_LONG=z'4c000407')
       INTEGER MPI_UNSIGNED_LONG
       PARAMETER (MPI_UNSIGNED_LONG=z'4c000408')
       INTEGER MPI_LONG_LONG
       PARAMETER (MPI_LONG_LONG=z'4c000809')
       INTEGER MPI_LONG_LONG_INT
       PARAMETER (MPI_LONG_LONG_INT=z'4c000809')
       INTEGER MPI_FLOAT
       PARAMETER (MPI_FLOAT=z'4c00040a')
       INTEGER MPI_DOUBLE
       PARAMETER (MPI_DOUBLE=z'4c00080b')
       INTEGER MPI_LONG_DOUBLE
       PARAMETER (MPI_LONG_DOUBLE=z'4c00080c')
       INTEGER MPI_BYTE
       PARAMETER (MPI_BYTE=z'4c00010d')
       INTEGER MPI_WCHAR
       PARAMETER (MPI_WCHAR=z'4c00020e')
       INTEGER MPI_PACKED
       PARAMETER (MPI_PACKED=z'4c00010f')
       INTEGER MPI_LB
       PARAMETER (MPI_LB=z'4c000010')
       INTEGER MPI_UB
       PARAMETER (MPI_UB=z'4c000011')
       INTEGER MPI_2INT
       PARAMETER (MPI_2INT=z'4c000816')
       INTEGER MPI_SIGNED_CHAR
       PARAMETER (MPI_SIGNED_CHAR=z'4c000118')
       INTEGER MPI_UNSIGNED_LONG_LONG
       PARAMETER (MPI_UNSIGNED_LONG_LONG=z'4c000819')
       INTEGER MPI_CHARACTER
       PARAMETER (MPI_CHARACTER=z'4c00011a')
       INTEGER MPI_INTEGER
       PARAMETER (MPI_INTEGER=z'4c00041b')
       INTEGER MPI_REAL
       PARAMETER (MPI_REAL=z'4c00041c')
       INTEGER MPI_LOGICAL
       PARAMETER (MPI_LOGICAL=z'4c00041d')
       INTEGER MPI_COMPLEX
       PARAMETER (MPI_COMPLEX=z'4c00081e')
       INTEGER MPI_DOUBLE_PRECISION
       PARAMETER (MPI_DOUBLE_PRECISION=z'4c00081f')
       INTEGER MPI_2INTEGER
       PARAMETER (MPI_2INTEGER=z'4c000820')
       INTEGER MPI_2REAL
       PARAMETER (MPI_2REAL=z'4c000821')
       INTEGER MPI_DOUBLE_COMPLEX
       PARAMETER (MPI_DOUBLE_COMPLEX=z'4c001022')
       INTEGER MPI_2DOUBLE_PRECISION
       PARAMETER (MPI_2DOUBLE_PRECISION=z'4c001023')
       INTEGER MPI_2COMPLEX
       PARAMETER (MPI_2COMPLEX=z'4c001024')
       INTEGER MPI_2DOUBLE_COMPLEX
       PARAMETER (MPI_2DOUBLE_COMPLEX=z'4c002025')
       INTEGER MPI_REAL2
       PARAMETER (MPI_REAL2=z'0c000000')
       INTEGER MPI_REAL4
       PARAMETER (MPI_REAL4=z'4c000427')
       INTEGER MPI_COMPLEX8
       PARAMETER (MPI_COMPLEX8=z'4c000828')
       INTEGER MPI_REAL8
       PARAMETER (MPI_REAL8=z'4c000829')
       INTEGER MPI_COMPLEX16
       PARAMETER (MPI_COMPLEX16=z'4c00102a')
       INTEGER MPI_REAL16
       PARAMETER (MPI_REAL16=z'0c000000')
       INTEGER MPI_COMPLEX32
       PARAMETER (MPI_COMPLEX32=z'0c000000')
       INTEGER MPI_INTEGER1
       PARAMETER (MPI_INTEGER1=z'4c00012d')
       INTEGER MPI_COMPLEX4
       PARAMETER (MPI_COMPLEX4=z'0c000000')
       INTEGER MPI_INTEGER2
       PARAMETER (MPI_INTEGER2=z'4c00022f')
       INTEGER MPI_INTEGER4
       PARAMETER (MPI_INTEGER4=z'4c000430')
       INTEGER MPI_INTEGER8
       PARAMETER (MPI_INTEGER8=z'4c000831')
       INTEGER MPI_INTEGER16
       PARAMETER (MPI_INTEGER16=z'0c000000')

       INCLUDE 'mpifptr.h'

       INTEGER MPI_OFFSET
       PARAMETER (MPI_OFFSET=z'4c00083c')
       INTEGER MPI_COUNT
       PARAMETER (MPI_COUNT=z'4c00083d')
       INTEGER MPI_FLOAT_INT
       PARAMETER (MPI_FLOAT_INT=z'8c000000')
       INTEGER MPI_DOUBLE_INT
       PARAMETER (MPI_DOUBLE_INT=z'8c000001')
       INTEGER MPI_LONG_INT
       PARAMETER (MPI_LONG_INT=z'8c000002')
       INTEGER MPI_SHORT_INT
       PARAMETER (MPI_SHORT_INT=z'8c000003')
       INTEGER MPI_LONG_DOUBLE_INT
       PARAMETER (MPI_LONG_DOUBLE_INT=z'8c000004')
       INTEGER MPI_INTEGER_KIND
       PARAMETER (MPI_INTEGER_KIND=4)
       INTEGER MPI_OFFSET_KIND
       PARAMETER (MPI_OFFSET_KIND=8)
       INTEGER MPI_COUNT_KIND
       PARAMETER (MPI_COUNT_KIND=8)
       INTEGER MPI_COMBINER_NAMED
       PARAMETER (MPI_COMBINER_NAMED=1)
       INTEGER MPI_COMBINER_DUP
       PARAMETER (MPI_COMBINER_DUP=2)
       INTEGER MPI_COMBINER_CONTIGUOUS
       PARAMETER (MPI_COMBINER_CONTIGUOUS=3)
       INTEGER MPI_COMBINER_VECTOR
       PARAMETER (MPI_COMBINER_VECTOR=4)
       INTEGER MPI_COMBINER_HVECTOR_INTEGER
       PARAMETER (MPI_COMBINER_HVECTOR_INTEGER=5)
       INTEGER MPI_COMBINER_HVECTOR
       PARAMETER (MPI_COMBINER_HVECTOR=6)
       INTEGER MPI_COMBINER_INDEXED
       PARAMETER (MPI_COMBINER_INDEXED=7)
       INTEGER MPI_COMBINER_HINDEXED_INTEGER
       PARAMETER (MPI_COMBINER_HINDEXED_INTEGER=8)
       INTEGER MPI_COMBINER_HINDEXED
       PARAMETER (MPI_COMBINER_HINDEXED=9)
       INTEGER MPI_COMBINER_INDEXED_BLOCK
       PARAMETER (MPI_COMBINER_INDEXED_BLOCK=10)
       INTEGER MPI_COMBINER_STRUCT_INTEGER
       PARAMETER (MPI_COMBINER_STRUCT_INTEGER=11)
       INTEGER MPI_COMBINER_STRUCT
       PARAMETER (MPI_COMBINER_STRUCT=12)
       INTEGER MPI_COMBINER_SUBARRAY
       PARAMETER (MPI_COMBINER_SUBARRAY=13)
       INTEGER MPI_COMBINER_DARRAY
       PARAMETER (MPI_COMBINER_DARRAY=14)
       INTEGER MPI_COMBINER_F90_REAL
       PARAMETER (MPI_COMBINER_F90_REAL=15)
       INTEGER MPI_COMBINER_F90_COMPLEX
       PARAMETER (MPI_COMBINER_F90_COMPLEX=16)
       INTEGER MPI_COMBINER_F90_INTEGER
       PARAMETER (MPI_COMBINER_F90_INTEGER=17)
       INTEGER MPI_COMBINER_RESIZED
       PARAMETER (MPI_COMBINER_RESIZED=18)
       INTEGER MPI_COMBINER_HINDEXED_BLOCK
       PARAMETER (MPI_COMBINER_HINDEXED_BLOCK=19)
       INTEGER MPI_MODE_NOCHECK
       PARAMETER (MPI_MODE_NOCHECK=1024)
       INTEGER MPI_MODE_NOSTORE
       PARAMETER (MPI_MODE_NOSTORE=2048)
       INTEGER MPI_MODE_NOPUT
       PARAMETER (MPI_MODE_NOPUT=4096)
       INTEGER MPI_MODE_NOPRECEDE
       PARAMETER (MPI_MODE_NOPRECEDE=8192)
       INTEGER MPI_MODE_NOSUCCEED
       PARAMETER (MPI_MODE_NOSUCCEED=16384)
       INTEGER MPI_THREAD_SINGLE
       PARAMETER (MPI_THREAD_SINGLE=0)
       INTEGER MPI_THREAD_FUNNELED
       PARAMETER (MPI_THREAD_FUNNELED=1)
       INTEGER MPI_THREAD_SERIALIZED
       PARAMETER (MPI_THREAD_SERIALIZED=2)
       INTEGER MPI_THREAD_MULTIPLE
       PARAMETER (MPI_THREAD_MULTIPLE=3)
       INTEGER MPI_MODE_RDONLY
       PARAMETER (MPI_MODE_RDONLY=2)
       INTEGER MPI_MODE_RDWR
       PARAMETER (MPI_MODE_RDWR=8)
       INTEGER MPI_MODE_WRONLY
       PARAMETER (MPI_MODE_WRONLY=4)
       INTEGER MPI_MODE_DELETE_ON_CLOSE
       PARAMETER (MPI_MODE_DELETE_ON_CLOSE=16)
       INTEGER MPI_MODE_UNIQUE_OPEN
       PARAMETER (MPI_MODE_UNIQUE_OPEN=32)
       INTEGER MPI_MODE_CREATE
       PARAMETER (MPI_MODE_CREATE=1)
       INTEGER MPI_MODE_EXCL
       PARAMETER (MPI_MODE_EXCL=64)
       INTEGER MPI_MODE_APPEND
       PARAMETER (MPI_MODE_APPEND=128)
       INTEGER MPI_MODE_SEQUENTIAL
       PARAMETER (MPI_MODE_SEQUENTIAL=256)
       INTEGER MPI_SEEK_SET
       PARAMETER (MPI_SEEK_SET=600)
       INTEGER MPI_SEEK_CUR
       PARAMETER (MPI_SEEK_CUR=602)
       INTEGER MPI_SEEK_END
       PARAMETER (MPI_SEEK_END=604)
       INTEGER MPI_ORDER_C
       PARAMETER (MPI_ORDER_C=56)
       INTEGER MPI_ORDER_FORTRAN
       PARAMETER (MPI_ORDER_FORTRAN=57)
       INTEGER MPI_DISTRIBUTE_BLOCK
       PARAMETER (MPI_DISTRIBUTE_BLOCK=121)
       INTEGER MPI_DISTRIBUTE_CYCLIC
       PARAMETER (MPI_DISTRIBUTE_CYCLIC=122)
       INTEGER MPI_DISTRIBUTE_NONE
       PARAMETER (MPI_DISTRIBUTE_NONE=123)
       INTEGER MPI_DISTRIBUTE_DFLT_DARG
       PARAMETER (MPI_DISTRIBUTE_DFLT_DARG=-49767)
       INTEGER (KIND=8) MPI_DISPLACEMENT_CURRENT
       PARAMETER (MPI_DISPLACEMENT_CURRENT=-54278278)
       INTEGER MPI_BOTTOM, MPI_IN_PLACE
       INTEGER MPI_UNWEIGHTED, MPI_WEIGHTS_EMPTY

       COMMON /MPIPRIV1/ MPI_BOTTOM, MPI_IN_PLACE, MPI_STATUS_IGNORE

       COMMON /MPIPRIV2/ MPI_STATUSES_IGNORE, MPI_ERRCODES_IGNORE
!DEC$ ATTRIBUTES DLLIMPORT :: /MPIPRIV1/, /MPIPRIV2/

       COMMON /MPIFCMB5/ MPI_UNWEIGHTED
       COMMON /MPIFCMB9/ MPI_WEIGHTS_EMPTY
!DEC$ ATTRIBUTES DLLIMPORT :: /MPIFCMB5/, /MPIFCMB9/

       COMMON /MPIPRIVC/ MPI_ARGVS_NULL, MPI_ARGV_NULL
!DEC$ ATTRIBUTES DLLIMPORT :: /MPIPRIVC/

       END MODULE MPI_CONSTANTS

       MODULE MPI_BASE
       IMPLICIT NONE
       INTERFACE
       SUBROUTINE MPI_TYPE_CREATE_DARRAY(v0,v1,v2,v3,v4,v5,v6,v7,v8,v9,ierror)
       INTEGER v0, v1, v2, v3(*), v4(*), v5(*), v6(*), v7, v8, v9
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_DARRAY

       SUBROUTINE MPI_COMM_FREE_KEYVAL(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_COMM_FREE_KEYVAL

       SUBROUTINE MPI_TYPE_EXTENT(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0
       INTEGER(KIND=MPI_ADDRESS_KIND) v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_EXTENT

       SUBROUTINE MPI_TYPE_GET_NAME(v0,v1,v2,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_NAME

       SUBROUTINE MPI_GROUP_INTERSECTION(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_INTERSECTION

       SUBROUTINE MPI_WIN_LOCK(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2, v3
       INTEGER ierror
       END SUBROUTINE MPI_WIN_LOCK

       SUBROUTINE MPI_CARTDIM_GET(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_CARTDIM_GET

       SUBROUTINE MPI_WIN_GET_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_GET_ERRHANDLER

       SUBROUTINE MPI_COMM_SPLIT(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2, v3
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SPLIT

       SUBROUTINE MPI_COMM_SPLIT_TYPE(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2, v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SPLIT_TYPE

       SUBROUTINE MPI_CANCEL(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_CANCEL

       SUBROUTINE MPI_WIN_POST(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_WIN_POST

       SUBROUTINE MPI_WIN_COMPLETE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_WIN_COMPLETE

       SUBROUTINE MPI_TEST_CANCELLED(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0(MPI_STATUS_SIZE)
       LOGICAL v1
       INTEGER ierror
       END SUBROUTINE MPI_TEST_CANCELLED

       SUBROUTINE MPI_GROUP_SIZE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_SIZE

       SUBROUTINE MPI_ADD_ERROR_STRING(v0,v1,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER ierror
       END SUBROUTINE MPI_ADD_ERROR_STRING

       SUBROUTINE MPI_PACK_SIZE(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2, v3
       INTEGER ierror
       END SUBROUTINE MPI_PACK_SIZE

       SUBROUTINE MPI_GET_ELEMENTS(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0(MPI_STATUS_SIZE), v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GET_ELEMENTS

       SUBROUTINE MPI_GET_ELEMENTS_X(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE,MPI_COUNT_KIND
       INTEGER v0(MPI_STATUS_SIZE), v1
       INTEGER(KIND=MPI_COUNT_KIND) v2
       INTEGER ierror
       END SUBROUTINE MPI_GET_ELEMENTS_X

       SUBROUTINE MPI_ERRHANDLER_GET(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_ERRHANDLER_GET

       SUBROUTINE MPI_FILE_GET_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_FILE_GET_ERRHANDLER

       SUBROUTINE MPI_TYPE_LB(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0
       INTEGER(KIND=MPI_ADDRESS_KIND) v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_LB

       SUBROUTINE MPI_REQUEST_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_REQUEST_FREE

       SUBROUTINE MPI_GROUP_RANGE_INCL(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2(3,*), v3
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_RANGE_INCL

       SUBROUTINE MPI_TYPE_GET_TRUE_EXTENT(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0
       INTEGER(KIND=MPI_ADDRESS_KIND) v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_TRUE_EXTENT

       SUBROUTINE MPI_TYPE_GET_TRUE_EXTENT_X(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_COUNT_KIND
       INTEGER v0
       INTEGER(KIND=MPI_COUNT_KIND) v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_TRUE_EXTENT_X

       SUBROUTINE MPI_BARRIER(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_BARRIER

       SUBROUTINE MPI_IS_THREAD_MAIN(v0,ierror)
       LOGICAL v0
       INTEGER ierror
       END SUBROUTINE MPI_IS_THREAD_MAIN

       SUBROUTINE MPI_WIN_FREE_KEYVAL(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_WIN_FREE_KEYVAL

       SUBROUTINE MPI_TYPE_COMMIT(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_COMMIT

       SUBROUTINE MPI_GROUP_RANGE_EXCL(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2(3,*), v3
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_RANGE_EXCL

       SUBROUTINE MPI_REQUEST_GET_STATUS(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0
       LOGICAL v1
       INTEGER v2(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_REQUEST_GET_STATUS

       SUBROUTINE MPI_QUERY_THREAD(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_QUERY_THREAD

       SUBROUTINE MPI_ERRHANDLER_CREATE(v0,v1,ierror)
       INTERFACE
       SUBROUTINE v0(vv0,vv1)
       INTEGER vv0,vv1
       END SUBROUTINE
       END INTERFACE
       INTEGER v1
       INTEGER ierror
       END SUBROUTINE MPI_ERRHANDLER_CREATE

       SUBROUTINE MPI_COMM_SPAWN_MULTIPLE(v0,v1,v2,v3,v4,v5,v6,v7,v8,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1(*), v2(v0,*)
       INTEGER v3(*), v4(*), v5, v6, v7, v8(*)
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SPAWN_MULTIPLE

       SUBROUTINE MPI_COMM_REMOTE_GROUP(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_REMOTE_GROUP

       SUBROUTINE MPI_TYPE_GET_EXTENT(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0
       INTEGER(KIND=MPI_ADDRESS_KIND) v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_EXTENT

       SUBROUTINE MPI_TYPE_GET_EXTENT_X(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_COUNT_KIND
       INTEGER v0
       INTEGER(KIND=MPI_COUNT_KIND) v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_EXTENT_X

       SUBROUTINE MPI_COMM_COMPARE(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_COMM_COMPARE

       SUBROUTINE MPI_INFO_GET_VALUELEN(v0,v1,v2,v3,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER v2
       LOGICAL v3
       INTEGER ierror
       END SUBROUTINE MPI_INFO_GET_VALUELEN

       SUBROUTINE MPI_INFO_GET(v0,v1,v2,v3,v4,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER v2
       CHARACTER (LEN=*) v3
       LOGICAL v4
       INTEGER ierror
       END SUBROUTINE MPI_INFO_GET

       SUBROUTINE MPI_OP_COMMUTATIVE(v0,v1,ierror)
       INTEGER v0
       LOGICAL v1
       INTEGER ierror
       END SUBROUTINE MPI_OP_COMMUTATIVE

       SUBROUTINE MPI_OP_CREATE(v0,v1,v2,ierror)
       EXTERNAL v0
       LOGICAL v1
       INTEGER v2
       INTEGER ierror
       END SUBROUTINE MPI_OP_CREATE

       SUBROUTINE MPI_TYPE_CREATE_STRUCT(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0, v1(*)
       INTEGER(KIND=MPI_ADDRESS_KIND) v2(*)
       INTEGER v3(*), v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_STRUCT

       SUBROUTINE MPI_TYPE_VECTOR(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2, v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_VECTOR

       SUBROUTINE MPI_WIN_GET_GROUP(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_GET_GROUP

       SUBROUTINE MPI_GROUP_COMPARE(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_COMPARE

       SUBROUTINE MPI_CART_SHIFT(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2, v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_CART_SHIFT

       SUBROUTINE MPI_WIN_SET_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_SET_ERRHANDLER

       SUBROUTINE MPI_COMM_SPAWN(v0,v1,v2,v3,v4,v5,v6,v7,ierror)
       CHARACTER (LEN=*) v0, v1(*)
       INTEGER v2, v3, v4, v5, v6, v7(*)
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SPAWN

       SUBROUTINE MPI_COMM_GROUP(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_GROUP

       SUBROUTINE MPI_WIN_CALL_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_CALL_ERRHANDLER

       SUBROUTINE MPI_LOOKUP_NAME(v0,v1,v2,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1
       CHARACTER (LEN=*) v2
       INTEGER ierror
       END SUBROUTINE MPI_LOOKUP_NAME

       SUBROUTINE MPI_INFO_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_INFO_FREE

       SUBROUTINE MPI_COMM_SET_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SET_ERRHANDLER

       SUBROUTINE MPI_GRAPH_GET(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2, v3(*), v4(*)
       INTEGER ierror
       END SUBROUTINE MPI_GRAPH_GET

       SUBROUTINE MPI_GROUP_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_FREE

       SUBROUTINE MPI_STATUS_SET_ELEMENTS(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0(MPI_STATUS_SIZE), v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_STATUS_SET_ELEMENTS

       SUBROUTINE MPI_STATUS_SET_ELEMENTS_X(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE,MPI_COUNT_KIND
       INTEGER v0(MPI_STATUS_SIZE), v1
       INTEGER(KIND=MPI_COUNT_KIND) v2
       INTEGER ierror
       END SUBROUTINE MPI_STATUS_SET_ELEMENTS_X

       SUBROUTINE MPI_WIN_TEST(v0,v1,ierror)
       INTEGER v0
       LOGICAL v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_TEST

       SUBROUTINE MPI_WIN_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_WIN_FREE

       SUBROUTINE MPI_GRAPH_MAP(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2(*), v3(*), v4
       INTEGER ierror
       END SUBROUTINE MPI_GRAPH_MAP

       SUBROUTINE MPI_DIST_GRAPH_NEIGHBORS_COUNT(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2
       LOGICAL v3
       INTEGER ierror
       END SUBROUTINE MPI_DIST_GRAPH_NEIGHBORS_COUNT

       SUBROUTINE MPI_PACK_EXTERNAL_SIZE(v0,v1,v2,v3,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       CHARACTER (LEN=*) v0
       INTEGER v1, v2
       INTEGER(KIND=MPI_ADDRESS_KIND) v3
       INTEGER ierror
       END SUBROUTINE MPI_PACK_EXTERNAL_SIZE

       SUBROUTINE MPI_PUBLISH_NAME(v0,v1,v2,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1
       CHARACTER (LEN=*) v2
       INTEGER ierror
       END SUBROUTINE MPI_PUBLISH_NAME

       SUBROUTINE MPI_TYPE_CREATE_F90_REAL(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_F90_REAL

       SUBROUTINE MPI_OPEN_PORT(v0,v1,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER ierror
       END SUBROUTINE MPI_OPEN_PORT

       SUBROUTINE MPI_GROUP_UNION(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_UNION

       SUBROUTINE MPI_COMM_ACCEPT(v0,v1,v2,v3,v4,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1, v2, v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_COMM_ACCEPT

       SUBROUTINE MPI_FILE_CREATE_ERRHANDLER(v0,v1,ierror)
       INTERFACE
       SUBROUTINE v0(vv0,vv1)
       INTEGER vv0,vv1
       END SUBROUTINE
       END INTERFACE
       INTEGER v1
       INTEGER ierror
       END SUBROUTINE MPI_FILE_CREATE_ERRHANDLER

       SUBROUTINE MPI_WIN_GET_NAME(v0,v1,v2,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER v2
       INTEGER ierror
       END SUBROUTINE MPI_WIN_GET_NAME

       SUBROUTINE MPI_INFO_CREATE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_INFO_CREATE

       SUBROUTINE MPI_TYPE_CREATE_F90_INTEGER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_F90_INTEGER

       SUBROUTINE MPI_TYPE_SET_NAME(v0,v1,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_SET_NAME

       SUBROUTINE MPI_ATTR_DELETE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_ATTR_DELETE

       SUBROUTINE MPI_GROUP_INCL(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2(*), v3
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_INCL

       SUBROUTINE MPI_COMM_CREATE_ERRHANDLER(v0,v1,ierror)
       INTERFACE
       SUBROUTINE v0(vv0,vv1)
       INTEGER vv0,vv1
       END SUBROUTINE
       END INTERFACE
       INTEGER v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_CREATE_ERRHANDLER

       SUBROUTINE MPI_COMM_CONNECT(v0,v1,v2,v3,v4,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1, v2, v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_COMM_CONNECT

       SUBROUTINE MPI_ERROR_STRING(v0,v1,v2,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER v2
       INTEGER ierror
       END SUBROUTINE MPI_ERROR_STRING

       SUBROUTINE MPI_TYPE_GET_CONTENTS(v0,v1,v2,v3,v4,v5,v6,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0, v1, v2, v3, v4(*)
       INTEGER(KIND=MPI_ADDRESS_KIND) v5(*)
       INTEGER v6(*)
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_CONTENTS

       SUBROUTINE MPI_TYPE_STRUCT(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1(*), v2(*), v3(*), v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_STRUCT

       SUBROUTINE MPI_TYPE_CREATE_INDEXED_BLOCK(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2(*), v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_INDEXED_BLOCK

       SUBROUTINE MPI_TYPE_CREATE_HVECTOR(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0, v1
       INTEGER(KIND=MPI_ADDRESS_KIND) v2
       INTEGER v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_HVECTOR

       SUBROUTINE MPI_TYPE_FREE_KEYVAL(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_FREE_KEYVAL

       SUBROUTINE MPI_START(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_START

       SUBROUTINE MPI_ABORT(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_ABORT

       SUBROUTINE MPI_INTERCOMM_CREATE(v0,v1,v2,v3,v4,v5,ierror)
       INTEGER v0, v1, v2, v3, v4, v5
       INTEGER ierror
       END SUBROUTINE MPI_INTERCOMM_CREATE

       SUBROUTINE MPI_COMM_RANK(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_RANK

       SUBROUTINE MPI_COMM_GET_PARENT(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_COMM_GET_PARENT

       SUBROUTINE MPI_FINALIZED(v0,ierror)
       LOGICAL v0
       INTEGER ierror
       END SUBROUTINE MPI_FINALIZED

       SUBROUTINE MPI_INTERCOMM_MERGE(v0,v1,v2,ierror)
       INTEGER v0
       LOGICAL v1
       INTEGER v2
       INTEGER ierror
       END SUBROUTINE MPI_INTERCOMM_MERGE

       SUBROUTINE MPI_INFO_GET_NTHKEY(v0,v1,v2,ierror)
       INTEGER v0, v1
       CHARACTER (LEN=*) v2
       INTEGER ierror
       END SUBROUTINE MPI_INFO_GET_NTHKEY

       SUBROUTINE MPI_TYPE_MATCH_SIZE(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_MATCH_SIZE

       SUBROUTINE MPI_STATUS_SET_CANCELLED(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0(MPI_STATUS_SIZE), v1
       INTEGER ierror
       END SUBROUTINE MPI_STATUS_SET_CANCELLED

       SUBROUTINE MPI_FILE_SET_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_FILE_SET_ERRHANDLER

       SUBROUTINE MPI_INFO_DELETE(v0,v1,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER ierror
       END SUBROUTINE MPI_INFO_DELETE

       SUBROUTINE MPI_UNPUBLISH_NAME(v0,v1,v2,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1
       CHARACTER (LEN=*) v2
       INTEGER ierror
       END SUBROUTINE MPI_UNPUBLISH_NAME

       SUBROUTINE MPI_TYPE_CONTIGUOUS(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CONTIGUOUS

       SUBROUTINE MPI_INITIALIZED(v0,ierror)
       LOGICAL v0
       INTEGER ierror
       END SUBROUTINE MPI_INITIALIZED

       SUBROUTINE MPI_TYPE_CREATE_RESIZED(v0,v1,v2,v3,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0
       INTEGER(KIND=MPI_ADDRESS_KIND) v1, v2
       INTEGER v3
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_RESIZED

       SUBROUTINE MPI_TYPE_UB(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0
       INTEGER(KIND=MPI_ADDRESS_KIND) v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_UB

       SUBROUTINE MPI_INFO_DUP(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_INFO_DUP

       SUBROUTINE MPI_TYPE_DUP(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_DUP

       SUBROUTINE MPI_ERRHANDLER_SET(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_ERRHANDLER_SET

       SUBROUTINE MPI_WIN_DELETE_ATTR(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_DELETE_ATTR

       SUBROUTINE MPI_INFO_GET_NKEYS(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_INFO_GET_NKEYS

       SUBROUTINE MPI_GROUP_EXCL(v0,v1,v2,v3,ierror)
       INTEGER v0, v1, v2(*), v3
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_EXCL

       SUBROUTINE MPI_INFO_SET(v0,v1,v2,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_INFO_SET

       SUBROUTINE MPI_WAIT(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0, v1(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_WAIT

       SUBROUTINE MPI_COMM_DELETE_ATTR(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_DELETE_ATTR

       SUBROUTINE MPI_COMM_GET_NAME(v0,v1,v2,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER v2
       INTEGER ierror
       END SUBROUTINE MPI_COMM_GET_NAME

       SUBROUTINE MPI_TEST(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0
       LOGICAL v1
       INTEGER v2(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_TEST

       SUBROUTINE MPI_GET_COUNT(v0,v1,v2,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0(MPI_STATUS_SIZE), v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GET_COUNT

       SUBROUTINE MPI_ADD_ERROR_CLASS(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_ADD_ERROR_CLASS

       SUBROUTINE MPI_COMM_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_COMM_FREE

       SUBROUTINE MPI_COMM_SET_NAME(v0,v1,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SET_NAME

       SUBROUTINE MPI_COMM_DISCONNECT(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_COMM_DISCONNECT

       SUBROUTINE MPI_IPROBE(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0, v1, v2
       LOGICAL v3
       INTEGER v4(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_IPROBE

       SUBROUTINE MPI_IMPROBE(v0,v1,v2,v3,v4,v5,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0, v1, v2, v3, v4
       INTEGER v5(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_IMPROBE

       SUBROUTINE MPI_MPROBE(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0, v1, v2, v3
       INTEGER v4(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_MPROBE

       SUBROUTINE MPI_ADD_ERROR_CODE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_ADD_ERROR_CODE

       SUBROUTINE MPI_COMM_GET_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_GET_ERRHANDLER

       SUBROUTINE MPI_COMM_CREATE(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_COMM_CREATE

       SUBROUTINE MPI_OP_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_OP_FREE

       SUBROUTINE MPI_TOPO_TEST(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_TOPO_TEST

       SUBROUTINE MPI_GET_PROCESSOR_NAME(v0,v1,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1
       INTEGER ierror
       END SUBROUTINE MPI_GET_PROCESSOR_NAME

       SUBROUTINE MPI_COMM_SIZE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_SIZE

       SUBROUTINE MPI_WIN_UNLOCK(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_UNLOCK

       SUBROUTINE MPI_WIN_FLUSH(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_FLUSH

       SUBROUTINE MPI_WIN_FLUSH_LOCAL(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_FLUSH_LOCAL

       SUBROUTINE MPI_ERRHANDLER_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_ERRHANDLER_FREE

       SUBROUTINE MPI_COMM_REMOTE_SIZE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_REMOTE_SIZE

       SUBROUTINE MPI_PROBE(v0,v1,v2,v3,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_STATUS_SIZE
       INTEGER v0, v1, v2, v3(MPI_STATUS_SIZE)
       INTEGER ierror
       END SUBROUTINE MPI_PROBE

       SUBROUTINE MPI_TYPE_HINDEXED(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1(*), v2(*), v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_HINDEXED

       SUBROUTINE MPI_WIN_WAIT(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_WIN_WAIT

       SUBROUTINE MPI_WIN_SET_NAME(v0,v1,ierror)
       INTEGER v0
       CHARACTER (LEN=*) v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_SET_NAME

       SUBROUTINE MPI_TYPE_SIZE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_SIZE

       SUBROUTINE MPI_TYPE_SIZE_X(v0,v1,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_COUNT_KIND
       INTEGER v0
       INTEGER(KIND=MPI_COUNT_KIND) v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_SIZE_X

       SUBROUTINE MPI_TYPE_CREATE_SUBARRAY(v0,v1,v2,v3,v4,v5,v6,ierror)
       INTEGER v0, v1(*), v2(*), v3(*), v4, v5, v6
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_SUBARRAY

       SUBROUTINE MPI_WIN_CREATE_ERRHANDLER(v0,v1,ierror)
       INTERFACE
       SUBROUTINE v0(vv0,vv1)
       INTEGER vv0,vv1
       END SUBROUTINE
       END INTERFACE
       INTEGER v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_CREATE_ERRHANDLER

       SUBROUTINE MPI_WIN_START(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_WIN_START

       SUBROUTINE MPI_TYPE_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_FREE

       SUBROUTINE MPI_WIN_FENCE(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_WIN_FENCE

       SUBROUTINE MPI_GRAPHDIMS_GET(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GRAPHDIMS_GET

       SUBROUTINE MPI_FILE_CALL_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_FILE_CALL_ERRHANDLER

       SUBROUTINE MPI_TYPE_GET_ENVELOPE(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1, v2, v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_GET_ENVELOPE

       SUBROUTINE MPI_TYPE_DELETE_ATTR(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_DELETE_ATTR

       SUBROUTINE MPI_TYPE_CREATE_HINDEXED(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0, v1(*)
       INTEGER(KIND=MPI_ADDRESS_KIND) v2(*)
       INTEGER v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_HINDEXED

       SUBROUTINE MPI_TYPE_CREATE_HINDEXED_BLOCK(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0, v1
       INTEGER(KIND=MPI_ADDRESS_KIND) v2(*)
       INTEGER v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_HINDEXED_BLOCK

       SUBROUTINE MPI_TYPE_INDEXED(v0,v1,v2,v3,v4,ierror)
       INTEGER v0, v1(*), v2(*), v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_INDEXED

       SUBROUTINE MPI_GREQUEST_COMPLETE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_GREQUEST_COMPLETE

       SUBROUTINE MPI_GRAPH_NEIGHBORS_COUNT(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GRAPH_NEIGHBORS_COUNT

       SUBROUTINE MPI_GET_VERSION(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_GET_VERSION

       SUBROUTINE MPI_GET_LIBRARY_VERSION(v0,v1,ierror)
       CHARACTER (LEN=*) v0
       INTEGER v1
       INTEGER ierror
       END SUBROUTINE MPI_GET_LIBRARY_VERSION

       SUBROUTINE MSMPI_GET_BSEND_OVERHEAD(size)
       INTEGER size
       END SUBROUTINE MSMPI_GET_BSEND_OVERHEAD

       SUBROUTINE MSMPI_GET_VERSION(version)
       INTEGER version
       END SUBROUTINE MSMPI_GET_VERSION

       SUBROUTINE MPI_TYPE_HVECTOR(v0,v1,v2,v3,v4,ierror)
       USE MPI_CONSTANTS,ONLY:MPI_ADDRESS_KIND
       INTEGER v0, v1
       INTEGER(KIND=MPI_ADDRESS_KIND) v2
       INTEGER v3, v4
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_HVECTOR

       SUBROUTINE MPI_KEYVAL_FREE(v0,ierror)
       INTEGER v0
       INTEGER ierror
       END SUBROUTINE MPI_KEYVAL_FREE

       SUBROUTINE MPI_COMM_CALL_ERRHANDLER(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_CALL_ERRHANDLER

       SUBROUTINE MPI_COMM_JOIN(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_JOIN

       SUBROUTINE MPI_COMM_TEST_INTER(v0,v1,ierror)
       INTEGER v0
       LOGICAL v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_TEST_INTER

       SUBROUTINE MPI_CLOSE_PORT(v0,ierror)
       CHARACTER (LEN=*) v0
       INTEGER ierror
       END SUBROUTINE MPI_CLOSE_PORT

       SUBROUTINE MPI_TYPE_CREATE_F90_COMPLEX(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_TYPE_CREATE_F90_COMPLEX

       SUBROUTINE MPI_GROUP_DIFFERENCE(v0,v1,v2,ierror)
       INTEGER v0, v1, v2
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_DIFFERENCE

       SUBROUTINE MPI_COMM_DUP(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_COMM_DUP

       SUBROUTINE MPI_ERROR_CLASS(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_ERROR_CLASS

       SUBROUTINE MPI_GROUP_RANK(v0,v1,ierror)
       INTEGER v0, v1
       INTEGER ierror
       END SUBROUTINE MPI_GROUP_RANK


        SUBROUTINE MPI_INIT(ierror)
        INTEGER ierror
        END SUBROUTINE MPI_INIT

        SUBROUTINE MPI_INIT_THREAD(v0,v1,ierror)
        INTEGER v0, v1, ierror
        END SUBROUTINE MPI_INIT_THREAD

        FUNCTION MPI_WTIME()
            DOUBLE PRECISION MPI_WTIME
        END FUNCTION MPI_WTIME

        FUNCTION MPI_WTICK()
            DOUBLE PRECISION MPI_WTICK
        END FUNCTION MPI_WTICK

        FUNCTION PMPI_WTIME()
            DOUBLE PRECISION PMPI_WTIME
        END FUNCTION PMPI_WTIME

        FUNCTION PMPI_WTICK()
            DOUBLE PRECISION PMPI_WTICK
        END FUNCTION PMPI_WTICK

        SUBROUTINE MPI_NULL_DELETE_FN(a,b,c,d,e)
          INTEGER a,b,c,d,e
        END SUBROUTINE MPI_NULL_DELETE_FN

        SUBROUTINE MPI_DUP_FN(a,b,c,d,e,f,g)
          INTEGER a,b,c,d,e,g
          LOGICAL f
        END SUBROUTINE MPI_DUP_FN

        SUBROUTINE MPI_NULL_COPY_FN(a,b,c,d,e,f,g)
          INTEGER a,b,c,d,e,g
          LOGICAL f
        END SUBROUTINE MPI_NULL_COPY_FN

        SUBROUTINE MPI_COMM_NULL_DELETE_FN(a,b,c,d,e)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,e
          INTEGER (KIND=MPI_ADDRESS_KIND) c, d
        END SUBROUTINE MPI_COMM_NULL_DELETE_FN

        SUBROUTINE MPI_COMM_DUP_FN(a,b,c,d,e,f,g)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,g
          INTEGER (KIND=MPI_ADDRESS_KIND) c,d,e
          LOGICAL f
        END SUBROUTINE MPI_COMM_DUP_FN

        SUBROUTINE MPI_COMM_NULL_COPY_FN(a,b,c,d,e,f,g)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,g
          INTEGER (KIND=MPI_ADDRESS_KIND) c,d,e
          LOGICAL f
        END SUBROUTINE MPI_COMM_NULL_COPY_FN

        SUBROUTINE MPI_TYPE_NULL_DELETE_FN(a,b,c,d,e)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,e
          INTEGER (KIND=MPI_ADDRESS_KIND) c, d
        END SUBROUTINE MPI_TYPE_NULL_DELETE_FN

        SUBROUTINE MPI_TYPE_DUP_FN(a,b,c,d,e,f,g)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,g
          INTEGER (KIND=MPI_ADDRESS_KIND) c,d,e
          LOGICAL f
        END SUBROUTINE MPI_TYPE_DUP_FN

        SUBROUTINE MPI_TYPE_NULL_COPY_FN(a,b,c,d,e,f,g)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,g
          INTEGER (KIND=MPI_ADDRESS_KIND) c,d,e
          LOGICAL f
        END SUBROUTINE MPI_TYPE_NULL_COPY_FN

        SUBROUTINE MPI_WIN_NULL_DELETE_FN(a,b,c,d,e)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,e
          INTEGER (KIND=MPI_ADDRESS_KIND) c, d
        END SUBROUTINE MPI_WIN_NULL_DELETE_FN

        SUBROUTINE MPI_WIN_DUP_FN(a,b,c,d,e,f,g)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,g
          INTEGER (KIND=MPI_ADDRESS_KIND) c,d,e
          LOGICAL f
        END SUBROUTINE MPI_WIN_DUP_FN

        SUBROUTINE MPI_WIN_NULL_COPY_FN(a,b,c,d,e,f,g)
          USE MPI_CONSTANTS,ONLY: MPI_ADDRESS_KIND
          INTEGER a,b,g
          INTEGER (KIND=MPI_ADDRESS_KIND) c,d,e
          LOGICAL f
        END SUBROUTINE MPI_WIN_NULL_COPY_FN

       END INTERFACE
       END MODULE MPI_BASE

       MODULE MPI_SIZEOFS
!      This module contains the definitions for MPI_SIZEOF for the
!      predefined, named types in Fortran 90.  This is provided
!      as a separate module to allow MPI_SIZEOF to supply the
!      basic size information even when we do not provide the
!      arbitrary choice types
       IMPLICIT NONE

       PUBLIC :: MPI_SIZEOF
       INTERFACE MPI_SIZEOF
           MODULE PROCEDURE MPI_SIZEOF_I, MPI_SIZEOF_R,                &
     &                      MPI_SIZEOF_L, MPI_SIZEOF_CH, MPI_SIZEOF_CX,&
     &           MPI_SIZEOF_IV, MPI_SIZEOF_RV,                         &
     &           MPI_SIZEOF_LV, MPI_SIZEOF_CHV, MPI_SIZEOF_CXV
            MODULE PROCEDURE MPI_SIZEOF_D, MPI_SIZEOF_DV
       END INTERFACE ! MPI_SIZEOF

       CONTAINS

       SUBROUTINE MPI_SIZEOF_I( X, SIZE, IERROR )
       INTEGER X
       INTEGER SIZE, IERROR
       SIZE = 4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_I

       SUBROUTINE MPI_SIZEOF_R( X, SIZE, IERROR )
       REAL X
       INTEGER SIZE, IERROR
       SIZE = 4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_R

! If reals and doubles have been forced to the same size (e.g., with
! -i8 -r8 to compilers like g95), then the compiler may refuse to
! allow interfaces that use real and double precision (failing to
! determine which one is intended)
        SUBROUTINE MPI_SIZEOF_D( X, SIZE, IERROR )
        DOUBLE PRECISION X
        INTEGER SIZE, IERROR
        SIZE = 8
        IERROR = 0
        END SUBROUTINE MPI_SIZEOF_D

       SUBROUTINE MPI_SIZEOF_L( X, SIZE, IERROR )
       LOGICAL X
       INTEGER SIZE, IERROR
       SIZE = 4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_L

       SUBROUTINE MPI_SIZEOF_CH( X, SIZE, IERROR )
       CHARACTER X
       INTEGER SIZE, IERROR
       SIZE = 1
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_CH

       SUBROUTINE MPI_SIZEOF_CX( X, SIZE, IERROR )
       COMPLEX X
       INTEGER SIZE, IERROR
       SIZE = 2*4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_CX

       SUBROUTINE MPI_SIZEOF_IV( X, SIZE, IERROR )
       INTEGER X(*)
       INTEGER SIZE, IERROR
       SIZE = 4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_IV

       SUBROUTINE MPI_SIZEOF_RV( X, SIZE, IERROR )
       REAL X(*)
       INTEGER SIZE, IERROR
       SIZE = 4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_RV

! If reals and doubles have been forced to the same size (e.g., with
! -i8 -r8 to compilers like g95), then the compiler may refuse to
! allow interfaces that use real and double precision (failing to
! determine which one is intended)
        SUBROUTINE MPI_SIZEOF_DV( X, SIZE, IERROR )
        DOUBLE PRECISION X(*)
        INTEGER SIZE, IERROR
        SIZE = 8
        IERROR = 0
        END SUBROUTINE MPI_SIZEOF_DV

       SUBROUTINE MPI_SIZEOF_LV( X, SIZE, IERROR )
       LOGICAL X(*)
       INTEGER SIZE, IERROR
       SIZE = 4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_LV

       SUBROUTINE MPI_SIZEOF_CHV( X, SIZE, IERROR )
       CHARACTER X(*)
       INTEGER SIZE, IERROR
       SIZE = 1
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_CHV

       SUBROUTINE MPI_SIZEOF_CXV( X, SIZE, IERROR )
       COMPLEX X(*)
       INTEGER SIZE, IERROR
       SIZE = 2*4
       IERROR = 0
       END SUBROUTINE MPI_SIZEOF_CXV

! We don't include double complex.  If we did, we'd need to include the
! same hack as for real and double above if the compiler has been forced
! to make them the same size.
       END MODULE MPI_SIZEOFS

       MODULE MPI
       USE MPI_CONSTANTS
       USE MPI_SIZEOFS
       USE MPI_BASE
       END MODULE MPI
