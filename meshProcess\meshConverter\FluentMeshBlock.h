﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FluentMeshBlock.h
//! <AUTHOR> 凌空（数峰科技/西交大）
//! @brief 读取Fluent网格文件.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-11-23 李艳亮 乔龙
//     说明：调整简化（去除不必要的数据结构）
//
// 2020-07-22 张帅 凌空（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshConverter_FluentMeshBlock_
#define _meshProcess_meshConverter_FluentMeshBlock_

#include "meshProcess/meshConverter/MeshConverter.h"

/**
 * @brief 面区域类（内部面区域/边界面区域）
 * 
 */
class MshFaceZone
{
public:
    std::string st_name;
    int n_zoneID;
    int n_faceNum;
    int n_begID, n_endID;
    int n_BCType;
    int n_faceShape;

    MshFaceZone() : n_zoneID(0), n_begID(0), n_endID(0){}
};

/**
 * @brief Fluent-msh格式网格转换类
 * 
 */
class FluentMeshBlock :public MeshConverter
{
public:
    /**
     * @brief 构造函数
      * 
      * @param[in] meshFileName 网格文件名称
      * @param[in] meshDimension_ 网格维度
      * @param[in, out] mesh_ 形成的网格指针
      */
    FluentMeshBlock(const std::string& meshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_);

    ~FluentMeshBlock() {}   

    int ReadMesh(const bool &fullRead = true);

    int BuildTopology();    

    /// 建立边界拓扑结构
    int BuildBCTopology();

	/**
	* @brief 建立网格空间拓扑结构
	*
	*/
    int BuildVolumeTopology(){ return 0; }

    /// 获取边界名称
    std::vector<std::string> GetBoundaryName();
private:
    /**
     * @brief Get Index
     * 
     * @param F_FilePointer file pointer 
     * @return int mesh file index
     */
    int GetIndex(FILE *F_FilePointer);
    /**
     * @brief Get Data 
     * 
     * @param F_FilePointer file pointer
     * @param s_InputData start of the input string
     * @return int element or face id
     */
    int GetData(FILE *F_FilePointer, std::string &s_InputData);

    /**
     * @brief Get Data
     * 
     * @param F_FilePointer file pointer
     * @param s_InputData start of the input string
     * @param n_DataType data type
     * @return Scalar coordinate
     */
    Scalar GetData(FILE *F_FilePointer, std::string &s_InputData, int n_DataType);

    /**
     * @brief read a new mesh
     * 
     * @param F_FilePointer file pointer
     * @param n_Index index in mesh file
     */
    void ReadNew(FILE *F_FilePointer, int n_Index);
    /**
     * @brief read comments in msh file
     * 
     * @param inSection read fluent mesh file section
     */
    /// void ReadComment(FluentMeshFileSection& inSection);
    
    /**
     * @brief read comments in msh file
     * 
     * @param F_FilePointer mesh file pointer
     */
    void ReadComment(FILE *F_FilePointer);
    /**
     * @brief read dimension of the mesh
     * 
     * @param inSection input section
     */
    /// void ReadDimensionNumber(FluentMeshFileSection& inSection);
    /**
     * @brief read dimension of the mesh
     * 
     * @param F_FilePointer mesh file pointer
     */
    void ReadDimensionNumber(FILE *F_FilePointer);
    /**
     * @brief read node
     * 
     * @param inSection input section 
     */
    /// void ReadNode(FluentMeshFileSection& inSection);
    /**
     * @brief read node
     * 
     * @param F_FilePointer mesh pointer
     */
    void ReadNode(FILE *F_FilePointer);
    /**
     * @brief read face
     * 
     * @param inSection input section 
     */
    /// void ReadFace(FluentMeshFileSection& inSection);
    /**
     * @brief read face
     * 
     * @param F_FilePointer mesh pointer
     */
    void ReadFace(FILE *F_FilePointer, const bool &fullRead);
    /**
     * @brief read element
     * 
     * @param inSection input section
     */
    /// void ReadElement(FluentMeshFileSection& inSection);

    /**
     * @brief read element
     * 
     * @param F_FilePointer mesh file pointer
     */
    void ReadElement(FILE *F_FilePointer, const bool &fullRead);
    /**
     * @brief read zone
     * 
     * @param inSection input section 
     */
    /// void ReadZone(FluentMeshFileSection& inSection);
    /**
     * @brief read zone
     * 
     * @param F_FilePointer mesh file opinter
     */
    void ReadZone(FILE *F_FilePointer);

    
    /**
     * @brief Build up element and face zones in standard forms according to the Element- and Face- Zone Infos.
     * 
     */
    void EstablishBoundary();

    void PopulateCellFaces();

private:
    /// 面区域
    std::vector<MshFaceZone> v_FaceZoneInfo;

    /// 用于数据读取
    std::vector<int> v_parameters;

    /// 编号偏移量（常值1）
    const int offset;

    /// 内部面边界类型号，常值2
    const int internalBCID;
};


#endif
