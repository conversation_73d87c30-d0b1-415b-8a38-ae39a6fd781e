/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_LAZY_NOVEMBER_04_2008_1157AM)
#define BOOST_SPIRIT_LAZY_NOVEMBER_04_2008_1157AM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/include/phoenix_core.hpp>
#include <boost/proto/proto.hpp>
#include <boost/spirit/home/<USER>/modify.hpp>
#include <boost/spirit/home/<USER>/detail/is_spirit_tag.hpp>

namespace boost { namespace spirit
{
    template <typename Eval>
    typename proto::terminal<phoenix::actor<Eval> >::type
    lazy(phoenix::actor<Eval> const& f)
    {
        return proto::terminal<phoenix::actor<Eval> >::type::make(f);
    }

    namespace tag
    {
        struct lazy_eval 
        {
            BOOST_SPIRIT_IS_TAG()
        };
    }

    template <typename Domain>
    struct is_modifier_directive<Domain, tag::lazy_eval>
      : mpl::true_ {};
}}

#endif
