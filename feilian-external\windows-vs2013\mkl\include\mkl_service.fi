!===============================================================================
! Copyright 1999-2018 Intel Corporation.
!
! This software and the related documents are Intel copyrighted  materials,  and
! your use of  them is  governed by the  express license  under which  they were
! provided to you (License).  Unless the License provides otherwise, you may not
! use, modify, copy, publish, distribute,  disclose or transmit this software or
! the related documents without Intel's prior written permission.
!
! This software and the related documents  are provided as  is,  with no express
! or implied  warranties,  other  than those  that are  expressly stated  in the
! License.
!===============================================================================

!  Content:
!      Intel(R) Math Kernel Library (Intel(R) MKL) FORTRAN interface
!      for service routines
!*******************************************************************************

      INTEGER*4 MKL_DOMAIN_ALL
      INTEGER*4 MKL_DOMAIN_BLAS
      INTEGER*4 MKL_DOMAIN_FFT
      INTEGER*4 MKL_DOMAIN_VML
      INTEGER*4 MKL_DOMAIN_PARDISO

      INTEGER*4 MKL_DYNAMIC_TRUE
      INTEGER*4 MKL_DYNAMIC_FALSE
      INTEGER*4 MKL_ENABLE_SSE4_2
      INTEGER*4 MKL_ENABLE_AVX
      INTEGER*4 MKL_ENABLE_AVX2
      INTEGER*4 MKL_ENABLE_AVX512
      INTEGER*4 MKL_ENABLE_AVX512_MIC
      INTEGER*4 MKL_ENABLE_AVX512_MIC_E1
      INTEGER*4 MKL_ENABLE_AVX512_E1
      INTEGER*4 MKL_INTERFACE_LP64
      INTEGER*4 MKL_INTERFACE_ILP64
      INTEGER*4 MKL_INTERFACE_GNU
      INTEGER*4 MKL_THREADING_INTEL
      INTEGER*4 MKL_THREADING_SEQUENTIAL
      INTEGER*4 MKL_THREADING_PGI
      INTEGER*4 MKL_THREADING_GNU
      INTEGER*4 MKL_THREADING_TBB
      INTEGER*4 MKL_CBWR_BRANCH
      INTEGER*4 MKL_CBWR_ALL
      INTEGER*4 MKL_CBWR_OFF
      INTEGER*4 MKL_CBWR_UNSET_ALL
      INTEGER*4 MKL_CBWR_BRANCH_OFF
      INTEGER*4 MKL_CBWR_AUTO
      INTEGER*4 MKL_CBWR_COMPATIBLE
      INTEGER*4 MKL_CBWR_SSE2
      INTEGER*4 MKL_CBWR_SSSE3
      INTEGER*4 MKL_CBWR_SSE4_1
      INTEGER*4 MKL_CBWR_SSE4_2
      INTEGER*4 MKL_CBWR_AVX
      INTEGER*4 MKL_CBWR_AVX2
      INTEGER*4 MKL_CBWR_AVX512_MIC
      INTEGER*4 MKL_CBWR_AVX512
      INTEGER*4 MKL_CBWR_AVX512_MIC_E1
      INTEGER*4 MKL_CBWR_AVX512_E1
      INTEGER*4 MKL_CBWR_SUCCESS
      INTEGER*4 MKL_CBWR_ERR_INVALID_SETTINGS
      INTEGER*4 MKL_CBWR_ERR_INVALID_INPUT
      INTEGER*4 MKL_CBWR_ERR_UNSUPPORTED_BRANCH
      INTEGER*4 MKL_CBWR_ERR_UNKNOWN_BRANCH
      INTEGER*4 MKL_CBWR_ERR_MODE_CHANGE_FAILURE
      INTEGER*4 MKL_PEAK_MEM_ENABLE
      INTEGER*4 MKL_PEAK_MEM_RESET
      INTEGER*4 MKL_PEAK_MEM
      INTEGER*4 MKL_PEAK_MEM_DISABLE
      INTEGER*4 MKL_MEM_MCDRAM

      PARAMETER (MKL_DOMAIN_ALL      = 0)
      PARAMETER (MKL_DOMAIN_BLAS     = 1)
      PARAMETER (MKL_DOMAIN_FFT      = 2)
      PARAMETER (MKL_DOMAIN_VML      = 3)
      PARAMETER (MKL_DOMAIN_PARDISO  = 4)
      PARAMETER (MKL_DYNAMIC_TRUE    = 1)
      PARAMETER (MKL_DYNAMIC_FALSE   = 0)
      PARAMETER (MKL_ENABLE_SSE4_2        = 0)
      PARAMETER (MKL_ENABLE_AVX           = 1)
      PARAMETER (MKL_ENABLE_AVX2          = 2)
      PARAMETER (MKL_ENABLE_AVX512_MIC    = 3)
      PARAMETER (MKL_ENABLE_AVX512        = 4)
      PARAMETER (MKL_ENABLE_AVX512_MIC_E1 = 5)
      PARAMETER (MKL_ENABLE_AVX512_E1     = 6)
      PARAMETER (MKL_INTERFACE_LP64  = Z"00000000")
      PARAMETER (MKL_INTERFACE_ILP64 = Z"00000001")
      PARAMETER (MKL_INTERFACE_GNU   = Z"00000002")
      PARAMETER (MKL_THREADING_INTEL = 0)
      PARAMETER (MKL_THREADING_SEQUENTIAL = 1)
      PARAMETER (MKL_THREADING_PGI = 2)
      PARAMETER (MKL_THREADING_GNU = 3)
      PARAMETER (MKL_THREADING_TBB = 4)

      PARAMETER (MKL_CBWR_BRANCH      = 1)
      PARAMETER (MKL_CBWR_ALL         = -1)

      PARAMETER (MKL_CBWR_OFF         = 0)
      PARAMETER (MKL_CBWR_UNSET_ALL   = 0)

      PARAMETER (MKL_CBWR_BRANCH_OFF     = 1)
      PARAMETER (MKL_CBWR_AUTO           = 2)
      PARAMETER (MKL_CBWR_COMPATIBLE     = 3)
      PARAMETER (MKL_CBWR_SSE2           = 4)
      PARAMETER (MKL_CBWR_SSSE3          = 6)
      PARAMETER (MKL_CBWR_SSE4_1         = 7)
      PARAMETER (MKL_CBWR_SSE4_2         = 8)
      PARAMETER (MKL_CBWR_AVX            = 9)
      PARAMETER (MKL_CBWR_AVX2           = 10)
      PARAMETER (MKL_CBWR_AVX512_MIC     = 11)
      PARAMETER (MKL_CBWR_AVX512         = 12)
      PARAMETER (MKL_CBWR_AVX512_MIC_E1  = 13)
      PARAMETER (MKL_CBWR_AVX512_E1      = 14)

      PARAMETER (MKL_CBWR_SUCCESS                 =  0)
      PARAMETER (MKL_CBWR_ERR_INVALID_SETTINGS    = -1)
      PARAMETER (MKL_CBWR_ERR_INVALID_INPUT       = -2)
      PARAMETER (MKL_CBWR_ERR_UNSUPPORTED_BRANCH  = -3)
      PARAMETER (MKL_CBWR_ERR_UNKNOWN_BRANCH      = -4)
      PARAMETER (MKL_CBWR_ERR_MODE_CHANGE_FAILURE = -8)

      PARAMETER (MKL_PEAK_MEM_DISABLE =  0)
      PARAMETER (MKL_PEAK_MEM_ENABLE  =  1)
      PARAMETER (MKL_PEAK_MEM_RESET   = -1)
      PARAMETER (MKL_PEAK_MEM         =  2)
      PARAMETER (MKL_MEM_MCDRAM       =  1)

      INTERFACE
      SUBROUTINE MKL_GET_VERSION_STRING(BUF)
      CHARACTER*(*) BUF
      END SUBROUTINE
      END INTERFACE

      INTERFACE
      DOUBLE PRECISION FUNCTION MKL_GET_CPU_FREQUENCY()
      END FUNCTION
      END INTERFACE

      INTERFACE
      DOUBLE PRECISION FUNCTION MKL_GET_MAX_CPU_FREQUENCY()
      END FUNCTION
      END INTERFACE

      INTERFACE
      DOUBLE PRECISION FUNCTION MKL_GET_CLOCKS_FREQUENCY()
      END FUNCTION
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_GET_CPU_CLOCKS(CPU_CLOCKS)
      INTEGER*8  CPU_CLOCKS
      END SUBROUTINE
      END INTERFACE

! Threading control functions

      INTERFACE
      INTEGER*4 FUNCTION MKL_GET_MAX_THREADS()
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_GET_NUM_STRIPES()
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_DOMAIN_GET_MAX_THREADS(DOMAIN)
      INTEGER*4 DOMAIN
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_SET_NUM_THREADS_LOCAL(NTHRS)
      INTEGER*4  NTHRS
      END FUNCTION
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_SET_NUM_THREADS(NTHRS)
      INTEGER*4  NTHRS
      END SUBROUTINE
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_SET_NUM_STRIPES(NSTRP)
      INTEGER*4  NSTRP
      END SUBROUTINE
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_DOMAIN_SET_NUM_THREADS(NTHRS,DOMAIN)
      INTEGER*4 NTHRS
      INTEGER*4 DOMAIN
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_GET_DYNAMIC()
      END FUNCTION
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_SET_DYNAMIC(MKL_DYNAMIC)
      INTEGER*4 MKL_DYNAMIC
      END SUBROUTINE
      END INTERFACE

! Memory functions

      INTERFACE
      FUNCTION MKL_MALLOC(SIZE,ALIGN)
      USE ISO_C_BINDING
      INTEGER(KIND=C_INTPTR_T) MKL_MALLOC
      INTEGER(KIND=C_SIZE_T)   SIZE
      INTEGER*4 ALIGN
      END FUNCTION MKL_MALLOC
      END INTERFACE

      INTERFACE
      FUNCTION MKL_CALLOC(NUM,SIZE,ALIGN)
      USE ISO_C_BINDING
      INTEGER(KIND=C_INTPTR_T) MKL_CALLOC
      INTEGER(KIND=C_SIZE_T)   NUM,SIZE
      INTEGER*4 ALIGN
      END FUNCTION MKL_CALLOC
      END INTERFACE

      INTERFACE
      FUNCTION MKL_REALLOC(PTR,SIZE)
      USE ISO_C_BINDING
      INTEGER(KIND=C_INTPTR_T) MKL_REALLOC,PTR
      INTEGER(KIND=C_SIZE_T)   SIZE
      END FUNCTION MKL_REALLOC
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_FREE(PTR)
      USE ISO_C_BINDING
      INTEGER(KIND=C_INTPTR_T) PTR
      END SUBROUTINE MKL_FREE
      END INTERFACE

      INTERFACE
      INTEGER*8 FUNCTION MKL_MEM_STAT(N_BUFF)
      INTEGER*4 N_BUFF
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*8 FUNCTION MKL_PEAK_MEM_USAGE(RESET)
      INTEGER*4 RESET
      END FUNCTION
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_FREE_BUFFERS()
      END SUBROUTINE
      END INTERFACE

      INTERFACE
      SUBROUTINE MKL_THREAD_FREE_BUFFERS()
      END SUBROUTINE
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_DISABLE_FAST_MM()
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_SET_MEMORY_LIMIT(MEM_TYPE,LIMIT)
      USE ISO_C_BINDING
      INTEGER*4 MEM_TYPE
      INTEGER(KIND=C_SIZE_T) LIMIT
      END FUNCTION
      END INTERFACE

! Intel(R) MKL Progress routine

      INTERFACE
      FUNCTION MKL_PROGRESS( THREAD, STEP, STAGE )
      INTEGER*4          THREAD,STEP
      CHARACTER*(*)      STAGE
      INTEGER            MKL_PROGRESS
      END FUNCTION
      END INTERFACE


      INTERFACE
      INTEGER*4 FUNCTION MKL_ENABLE_INSTRUCTIONS(TYPE)
      INTEGER*4 TYPE
      END FUNCTION
      END INTERFACE

! Intel(R) MKL dynamic interface

      INTERFACE
      INTEGER*4 FUNCTION MKL_SET_INTERFACE_LAYER(MKL_INTERFACE)
      INTEGER*4 MKL_INTERFACE
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_SET_THREADING_LAYER(MKL_THREADING)
      INTEGER*4 MKL_THREADING
      END FUNCTION
      END INTERFACE

! Intel(R) MKL CBWR functions

      INTERFACE
      INTEGER*4 FUNCTION MKL_CBWR_GET(MKL_CBWR)
      INTEGER*4 MKL_CBWR
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_CBWR_SET(MKL_CBWR)
      INTEGER*4 MKL_CBWR
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_CBWR_GET_AUTO_BRANCH()
      END FUNCTION
      END INTERFACE

! Intel(R) MKL MPI routines

      INTEGER*4 MKL_BLACS_CUSTOM
      INTEGER*4 MKL_BLACS_MSMPI
      INTEGER*4 MKL_BLACS_INTELMPI
      INTEGER*4 MKL_BLACS_MPICH2

      PARAMETER (MKL_BLACS_CUSTOM = 0)
      PARAMETER (MKL_BLACS_MSMPI = 1)
      PARAMETER (MKL_BLACS_INTELMPI = 2)
      PARAMETER (MKL_BLACS_MPICH2 = 3)

      INTERFACE
      INTEGER*4 FUNCTION MKL_SET_MPI(VERBOSE, CUSTOM_LIBRARY_NAME)
      INTEGER*4 VERBOSE
      CHARACTER*(*) CUSTOM_LIBRARY_NAME
      END FUNCTION
      END INTERFACE

! Intel(R) MKL verbose function

      INTERFACE
      INTEGER*4 FUNCTION MKL_VERBOSE(ENABLE)
      INTEGER*4 ENABLE
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_VERBOSE_OUTPUT_FILE(FILE_PATH)
      CHARACTER*(*) FILE_PATH
      END FUNCTION
      END INTERFACE

      INTERFACE
      INTEGER*4 FUNCTION MKL_SET_ENV_MODE(MODE)
      INTEGER*4 MODE
      END FUNCTION
      END INTERFACE

! Obsolete names

      INTEGER*4 MKL_CBWR_SSE3
      PARAMETER (MKL_CBWR_SSE3        = 5)

!*******************************************************************************
