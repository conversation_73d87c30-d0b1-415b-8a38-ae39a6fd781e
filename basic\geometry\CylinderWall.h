﻿#ifndef _basic_geometry_CylinderWall_
#define _basic_geometry_CylinderWall_

#include "basic/geometry/PlaneWall.h"
#include "basic/geometry/Line.h"

/**
 * @brief 几何命名空间
 * 
 */
namespace Geometry
{

/**
 * @brief 柱面
 * 
 */
class CylinderWall
{
public:
    /**
     * @brief 柱形壳构造函数
     * 
     */
    CylinderWall();

    /**
     * @brief 柱形壳构造函数
     * 
     * @param[in] radius1_ 下端面半径
     * @param[in] radius2_ 上端面半径
     * @param[in] axis_ 对称轴
     * @param[in] wallNumber_ 分割平面数量
     * @param[in] propType_ 属性
     * @param[in] both_ 是否为双面
     * 
     */
    CylinderWall(const Scalar &radius1_, const Scalar &radius2_, const Line &axis_,
                 const int &wallNumber_, const int &propType_ = 0, const bool &both_ = false);
    
    /**
     * @brief 柱形壳构造函数
     * 
     * @param[in] radius1_ 下端面半径
     * @param[in] radius2_ 上端面半径
     * @param[in] axis_ 对称轴
     * @param[in] wallNumber_ 分割平面数量
     * @param[in] propType_ 属性
     * @param[in] transVelocity_ 平移速度
     * @param[in] rotationVelocity_ 旋转速度
     * @param[in] rotationLine_ 旋转轴
     * @param[in] both_ 是否为双面
     * 
     */
    CylinderWall(const Scalar &radius1_, const Scalar &radius2_, const Line &axis_,
                 const int &wallNumber_, const int &propType_,
                 const Vector &transVelocity_, const Scalar &rotationVelocity_, const Line &rotationLine_, const bool both_ = false);
    
    /**
     * @brief 获取柱形壳所包含平面数量
     * 
     * @return int 平面数量
     * 
     */
    inline const int &GetWallNumber()const {return this->wallNumber;}

    /**
     * @brief 获取柱形壳其中某一个平面
     * 
     * @param[in] n 平面编号
     * @return PlaneWall 平面
     * 
     */
    inline const PlaneWall &GetWall(const int &n)const {return this->walls[n];}

private:
    /**
     * @brief 生成柱形壳
     * 
     */
    void CreateCylinder();

    Vector RotateToZ();
    Vector RotateBackFromZ(const Vector& p);
    void TransformationMatrices();

private:
    int propType; ///< 属性编号
    int wallNumber; ///< 平面数量
    std::vector<PlaneWall> walls; ///< 平面列表
    
    Line axis; ///< 几何旋转轴
    Scalar radius1; ///< 底端旋转半径
    Scalar radius2; ///< 顶端旋转半径
    bool bothSide; ///< 双侧面标识

    bool moving; ///< 运动标识
    bool rotating; ///< 旋转标识 
    Line rotationLine; ///< 旋转运动轴线
    Scalar rotationVelocity; ///< 旋转速度（rad/s）
    Vector translationVelocity; ///< 平移速度 
    
    Line zAxis; ///< 旋转后的轴线
    Matrix TransMatrix; ///< 从轴线转换到z轴的转换矩阵
    Matrix ITransMatrix; ///< 从z轴转换到轴线的转换矩阵

#if defined(_BaseParallelMPI_)
public:
	template<class Archive>
	void serialize(Archive& ar, const unsigned int version)
	{
		//当前类成员序列化
		ar& propType;
		ar& wallNumber;
		ar& walls;
		ar& axis;
		ar& radius1;
		ar& radius2;
		ar& bothSide;
		ar& moving;
		ar& rotating;
		ar& rotationLine;
		ar& rotationVelocity;
		ar& translationVelocity;
		ar& zAxis;
		ar& TransMatrix;
		ar& ITransMatrix;
	}
#endif

};

} // namespace Geometry

#endif