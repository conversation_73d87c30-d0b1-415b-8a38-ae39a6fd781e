﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DecomposeBase.h
//! <AUTHOR>
//! @brief 用于网格分区的基类
//! @date 2022-07-26
//
//------------------------------修改日志----------------------------------------
// 2022-07-26 李艳亮、乔龙
//    说明：建立
//
//
//------------------------------修改日志----------------------------------------

#ifndef _meshProcess_decompose_DecomposeBase_
#define _meshProcess_decompose_DecomposeBase_

#include "basic/mesh/Mesh.h"

/**
* @brief 网格分区的基类
*
*/
class DecomposeBase
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in] pmesh 网格指针
	* @param[in] nPros 分区数量
	*/
    DecomposeBase(Mesh* pmesh, const int &nPros);

	/**
	* @brief 虚析构函数
	*
	*/
    virtual ~DecomposeBase() {}

	/**
	* @brief 分区函数（虚函数）
	*
	* @param[in] cellWeights_ 单元权重
	* @param[in, out] decomposeIDList 每个网格单元所属分区的容器
	*/
    virtual void Decompose(const std::vector<int> &cellWeights_, std::vector<int> &decomposeIDList) = 0;

protected:
	int n_pros; ///< 分区数量
	Mesh* p_mesh; ///< 待分区的网格
};

#endif
