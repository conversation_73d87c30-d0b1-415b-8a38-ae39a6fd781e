﻿#ifndef _basic_CFD_gradient_LeastSquare_
#define _basic_CFD_gradient_LeastSquare_

#include "basic/field/ElementField.h"
#include "basic/configure/Configure.h"
#include "basic/configure/ConfigureMacro.h"
#include "basic/CFD/gradient/GradientBase.h"

/**
 * @brief 梯度计算命名空间
 * 
 */
namespace Gradient
{

/**
 * @brief 节点最小二乘梯度计算类
 * 
 */
class LeastSquare : public GradientBase
{

enum { I_11 = 0, I_12, I_13, I_22, I_23, I_33 };

public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] LSVertexFlag_ 节点最小二乘标识
     * @param[in] nodeCenter_ 格点标识
     */
    LeastSquare(Mesh *mesh_, const bool &LSVertexFlag_, const bool nodeCenter_ = false);

    /**
     * @brief 析构函数
     * 
     */
    ~LeastSquare();
    
    /**
     * @brief 标量场梯度计算函数
     * 
     * @param[in] phi 标量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    void CalculateScalar(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);

    /**
     * @brief 矢量场梯度计算函数
     * 
     * @param[in] phi 矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    void CalculateVector(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);

    /**
     * @brief 梯度计算函数
     * 
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template<class Type, class TypeGradient>
    void Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

private:
    /**
     * @brief 搜索点相邻单元
     *
     */
    void SearchFaceAdjacentElements();
    
    /**
     * @brief 搜索点相邻单元
     *
     */
    void SearchNodeAdjacentElements();
    
    /**
     * @brief 计算最小二乘系数
     *
     */
    void CalculateLSWeight();
    
    /**
     * @brief 计算最小二乘矩阵
     *
     * @param[out] mat 最小二乘矩阵
     */
    void CalculateRMatrix(std::vector<std::vector<Scalar>> &mat);

private:
    /// 相邻单元连接信息，两个数标识相邻两个单元编号，其中first小于second
    std::vector<std::pair<int, int>> adjacentConnect;

    /// 最小二乘系数，与单元相邻信息一致，其中first单元编号小于second单元编号
    std::vector<std::pair<Vector, Vector>> LSWeight;

    /// 采用顶点最小二乘标识
    bool LSVertexFlag;
};

}
#endif