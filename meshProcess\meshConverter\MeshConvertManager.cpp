﻿#include "meshProcess/meshConverter/MeshConvertManager.h"
#include "basic/common/GeometryTools.h"

MeshConvertManager::MeshConvertManager(const std::string &meshFileName_,
                                       const Preprocessor::MeshType &meshType_,
									   const Mesh::MeshDim &meshDimension_,
                                       Mesh *mesh_,
                                       const Configure::MeshTransformStruct &meshTransform_)
    :
    meshFileName(meshFileName_), meshType(meshType_), meshDimension(meshDimension_), mesh(mesh_),
    meshTransform(meshTransform_), meshConverter(nullptr)
{
    this->SetMeshConverterPointer();
}

MeshConvertManager::~MeshConvertManager()
{
    if (meshConverter != nullptr) { delete meshConverter; meshConverter = nullptr; }
}

void MeshConvertManager::SetMeshConverterPointer()
{
    const std::string typeString = Configure::meshTypeReverseMap.find(meshType)->second;
    
    Print("网格类型: " + typeString);
    Print("网格文件名称: " + meshFileName.substr(meshFileName.rfind("/") + 1, -1));

    std::fstream file;
    file.open(meshFileName);
    if (!file.is_open()) FatalError("网格文件" + meshFileName + "不存在\n");
    if (file.is_open()) file.close();

    switch (meshType)
    {
    case Preprocessor::MeshType::CGNS:
    {
        meshConverter = new CgnsMesh(meshFileName, meshDimension, mesh);
        if (((CgnsMesh *)meshConverter)->ObtainZoneType() == 2)
        {
            delete ((CgnsMesh *)meshConverter);
            meshConverter = new CgnsMeshStructured(meshFileName, meshDimension, mesh);
        }

        break;
    }
    case Preprocessor::MeshType::FLUENT:
        meshConverter = new FluentMeshBlock(meshFileName, meshDimension, mesh);
        break;
    case Preprocessor::MeshType::ADLG:
        meshConverter = new DlgMesh(meshFileName, meshDimension, mesh, false);
        break;
    case Preprocessor::MeshType::BDLG:
        meshConverter = new DlgMesh(meshFileName, meshDimension, mesh, true);
        break;
    default:
        FatalError("MeshConvertManager::ReadMesh: meshType is unknown ");
    }
}

int MeshConvertManager::ReadMesh(const bool &fullRead)
{
    if (meshConverter->ReadMesh(fullRead)) return 1;
    this->TransformMesh();

    return 0;
}

int MeshConvertManager::BuildTopology()
{
    return meshConverter->BuildTopology();
}

void MeshConvertManager::BuildBoundaryTopology()
{
    meshConverter->BuildBCTopology();
}

void MeshConvertManager::BuildVolumeTopology()
{
    meshConverter->BuildVolumeTopology();
}

std::vector<std::string> MeshConvertManager::GetBoundaryName()
{
    return meshConverter->GetBoundaryName();
}

void MeshConvertManager::TransformMesh()
{
    std::ostringstream stringStream;

    // 网格缩放
    if (meshTransform.scaleFlag)
    {
        const Vector &scale = meshTransform.scale;
        for (int i = 0; i < mesh->v_node.size(); i++)
        {
            Vector &value = mesh->v_node[i];
            value.SetX(value.X() *  scale.X());
            value.SetY(value.Y() *  scale.Y());
            value.SetZ(value.Z() *  scale.Z());
        }
    }
    
    // 网格平移
    if (meshTransform.transferFlag)
    {
        Vector transfer = meshTransform.transfer;
        for (int i = 0; i < mesh->v_node.size(); i++) mesh->v_node[i] += transfer;
    }
    
    // 网格旋转
    if (meshTransform.rotateFlag)
    {
        Vector rotate = meshTransform.rotate;
        for (int i = 0; i < mesh->v_node.size(); i++)
        {
            mesh->v_node[i] = RotatePoint(mesh->v_node[i], Vector0, Vector(1.0, 0.0, 0.0), rotate.X());
            mesh->v_node[i] = RotatePoint(mesh->v_node[i], Vector0, Vector(0.0, 1.0, 0.0), rotate.Y());
            mesh->v_node[i] = RotatePoint(mesh->v_node[i], Vector0, Vector(0.0, 0.0, 1.0), rotate.Z());
        }
    }

    return;
}