/*=============================================================================
    Copyright (c) 2001-2007 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying 
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0
      , typename ClassA
      , typename A0
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            1
          , RT
          , RT(ClassT::*)(T0)
        >
      , ClassA
      , A0
    >::type const
    bind(
        RT(ClassT::*f)(T0)
      , ClassA const & obj
      , A0 const& a0
    )
    {
        typedef detail::member_function_ptr<
            1
          , RT
          , RT(ClassT::*)(T0)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0
            >::make(
                fp_type(f)
              , obj
              , a0
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0
      , typename ClassA
      , typename A0
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            1
          , RT
          , RT(ClassT::*)(T0) const
        >
      , ClassA
      , A0
    >::type const
    bind(
        RT(ClassT::*f)(T0) const
      , ClassA const & obj
      , A0 const& a0
    )
    {
        typedef detail::member_function_ptr<
            1
          , RT
          , RT(ClassT::*)(T0) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0
            >::make(
                fp_type(f)
              , obj
              , a0
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0
      , typename A0
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            1
          , RT
          , RT(ClassT::*)(T0)
        >
      , ClassT
      , A0
    >::type const
    bind(
        RT(ClassT::*f)(T0)
      , ClassT & obj
      , A0 const& a0
    )
    {
        typedef detail::member_function_ptr<
            1
          , RT
          , RT(ClassT::*)(T0)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0
            >::make(
                fp_type(f)
              , obj
              , a0
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1
      , typename ClassA
      , typename A0 , typename A1
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            2
          , RT
          , RT(ClassT::*)(T0 , T1)
        >
      , ClassA
      , A0 , A1
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1
    )
    {
        typedef detail::member_function_ptr<
            2
          , RT
          , RT(ClassT::*)(T0 , T1)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1
            >::make(
                fp_type(f)
              , obj
              , a0 , a1
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1
      , typename ClassA
      , typename A0 , typename A1
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            2
          , RT
          , RT(ClassT::*)(T0 , T1) const
        >
      , ClassA
      , A0 , A1
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1
    )
    {
        typedef detail::member_function_ptr<
            2
          , RT
          , RT(ClassT::*)(T0 , T1) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1
            >::make(
                fp_type(f)
              , obj
              , a0 , a1
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1
      , typename A0 , typename A1
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            2
          , RT
          , RT(ClassT::*)(T0 , T1)
        >
      , ClassT
      , A0 , A1
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1
    )
    {
        typedef detail::member_function_ptr<
            2
          , RT
          , RT(ClassT::*)(T0 , T1)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1
            >::make(
                fp_type(f)
              , obj
              , a0 , a1
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2
      , typename ClassA
      , typename A0 , typename A1 , typename A2
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            3
          , RT
          , RT(ClassT::*)(T0 , T1 , T2)
        >
      , ClassA
      , A0 , A1 , A2
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2
    )
    {
        typedef detail::member_function_ptr<
            3
          , RT
          , RT(ClassT::*)(T0 , T1 , T2)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2
      , typename ClassA
      , typename A0 , typename A1 , typename A2
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            3
          , RT
          , RT(ClassT::*)(T0 , T1 , T2) const
        >
      , ClassA
      , A0 , A1 , A2
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2
    )
    {
        typedef detail::member_function_ptr<
            3
          , RT
          , RT(ClassT::*)(T0 , T1 , T2) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2
      , typename A0 , typename A1 , typename A2
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            3
          , RT
          , RT(ClassT::*)(T0 , T1 , T2)
        >
      , ClassT
      , A0 , A1 , A2
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2
    )
    {
        typedef detail::member_function_ptr<
            3
          , RT
          , RT(ClassT::*)(T0 , T1 , T2)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            4
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3)
        >
      , ClassA
      , A0 , A1 , A2 , A3
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3
    )
    {
        typedef detail::member_function_ptr<
            4
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            4
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3) const
        >
      , ClassA
      , A0 , A1 , A2 , A3
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3
    )
    {
        typedef detail::member_function_ptr<
            4
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3
      , typename A0 , typename A1 , typename A2 , typename A3
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            4
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3)
        >
      , ClassT
      , A0 , A1 , A2 , A3
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3
    )
    {
        typedef detail::member_function_ptr<
            4
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2 , A3
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            5
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4)
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4
    )
    {
        typedef detail::member_function_ptr<
            5
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            5
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4) const
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4
    )
    {
        typedef detail::member_function_ptr<
            5
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            5
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4)
        >
      , ClassT
      , A0 , A1 , A2 , A3 , A4
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4
    )
    {
        typedef detail::member_function_ptr<
            5
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2 , A3 , A4
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            6
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5)
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5
    )
    {
        typedef detail::member_function_ptr<
            6
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            6
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5) const
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5
    )
    {
        typedef detail::member_function_ptr<
            6
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            6
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5)
        >
      , ClassT
      , A0 , A1 , A2 , A3 , A4 , A5
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5
    )
    {
        typedef detail::member_function_ptr<
            6
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2 , A3 , A4 , A5
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            7
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6)
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5 , A6
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6
    )
    {
        typedef detail::member_function_ptr<
            7
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5 , A6
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            7
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6) const
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5 , A6
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6
    )
    {
        typedef detail::member_function_ptr<
            7
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5 , A6
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            7
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6)
        >
      , ClassT
      , A0 , A1 , A2 , A3 , A4 , A5 , A6
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6
    )
    {
        typedef detail::member_function_ptr<
            7
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2 , A3 , A4 , A5 , A6
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            8
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7)
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6 , A7 const& a7
    )
    {
        typedef detail::member_function_ptr<
            8
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6 , a7
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            8
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7) const
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6 , A7 const& a7
    )
    {
        typedef detail::member_function_ptr<
            8
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6 , a7
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            8
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7)
        >
      , ClassT
      , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6 , A7 const& a7
    )
    {
        typedef detail::member_function_ptr<
            8
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6 , a7
            );
    }
    
    
    
    
    
    
    
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            9
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8)
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8)
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6 , A7 const& a7 , A8 const& a8
    )
    {
        typedef detail::member_function_ptr<
            9
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6 , a7 , a8
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8
      , typename ClassA
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            9
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8) const
        >
      , ClassA
      , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8) const
      , ClassA const & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6 , A7 const& a7 , A8 const& a8
    )
    {
        typedef detail::member_function_ptr<
            9
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8) const
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassA
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6 , a7 , a8
            );
    }
    template <
        typename RT
      , typename ClassT
      , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8
      , typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8
    >
    inline
    typename detail::expression::function_eval<
        detail::member_function_ptr<
            9
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8)
        >
      , ClassT
      , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
    >::type const
    bind(
        RT(ClassT::*f)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8)
      , ClassT & obj
      , A0 const& a0 , A1 const& a1 , A2 const& a2 , A3 const& a3 , A4 const& a4 , A5 const& a5 , A6 const& a6 , A7 const& a7 , A8 const& a8
    )
    {
        typedef detail::member_function_ptr<
            9
          , RT
          , RT(ClassT::*)(T0 , T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8)
        > fp_type;
        return
            detail::expression::function_eval<
                fp_type
              , ClassT
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
            >::make(
                fp_type(f)
              , obj
              , a0 , a1 , a2 , a3 , a4 , a5 , a6 , a7 , a8
            );
    }
