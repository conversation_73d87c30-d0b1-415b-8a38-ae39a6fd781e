﻿////////////////////////////////////////////////////////////////////////////////////
////---------------------------------------ARI-CFD-----------------------------/////
////------------------------中国航空工业空气动力研究院-------------------------/////
///////////////////////////////////////////////////////////////////////////////////
//! @file      FlowTime.h
//! @brief     CFD流场求解的时间基类
//! <AUTHOR>
//! @date      2021-08-10
//
//------------------------------修改日志----------------------------------------
//
//  2021-08-10 李艳亮、乔龙
//    说明：建立并进行规范化。
//------------------------------------------------------------------------------

# ifndef _sourceFlow_timeScheme_FlowTime_
# define _sourceFlow_timeScheme_FlowTime_

#include "basic/CFD/gradient/GradientManager.h"
#include "basic/CFD/smoother/Smoother.h"
#include "sourceFlow/boundaryCondition/FlowBoundaryManager.h"
#include "sourceFlow/fluxScheme/FlowFluxManager.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"
#include "sourceFlow/turbulence/TurbulenceManager.h"
#include "sourceFlow/timeScheme/CFLNumberManager.h"

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场时间基类
 * 用于派生具体时间格式
 * 
 */
class FlowTime
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    FlowTime(Package::FlowPackage &flowPackage_);

    /**
     * @brief 析构函数
     * 
     */
    virtual ~FlowTime();
    
    /**
     * @brief 初始化
     * 
     * @param[in] initialType 流场初始化类型
     */
    virtual void Initialize(const Initialization::Type &initialType) = 0;    

    /**
     * @brief 内迭代求解函数
     * 纯虚函数，在具体时间格式中实现
     * 
     * @param[in] recalculateResiduals 本层网格重新计算残值标识
     * @param[in] calculateForcingFunction 本层网格计算力源项标识
     */
    virtual void Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction) = 0;
    
    /**
     * @brief 边界条件更新
     * 
     */
    void InitializeBoundaryCondition();

    /**
     * @brief 边界条件更新
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 设置细网格层级
     * 全多重计算时，不同循环序列细网格层级不同
     * 
     * @param[in] fineMeshLevel_ 细网格层级
     */
    void SetFineGridLevel(const int &fineMeshLevel_);
    
    /**
     * @brief 保存上一步物理场
     * 
     */
    void SaveOld();

    /**
    * @brief 更新梯度场和muT
    *
    */
    void UpdateGradientAndMuT();

    /**
    * @brief 计算本层网格流场残值
    *
    */
    void CalculateResidual();

protected:
    /**
     * @brief 基类初始化
     * 
     * @param[in] initialType 流场初始化类型
     */
    void InitializeBase(const Initialization::Type &initialType);    

    /**
    * @brief 边界残值更新
    * 仅在格点计算中实际调用
    *
    */
    void UpdateBoundaryResidual();

    /**
     * @brief 计算当地时间步长
     * 
     */
    void CalculateDeltaT();

    /**
     * @brief 计算本层网格流场残值
     * 
     * @param[in] currentStage 当前计算步数
     * @param[in] dissipativeResidualFlag 残差重新计算标志
     * @param[in] multiGridForceFlag 多重网格循环向下路径标识
     */
    void CalculateFlowResidual(const int &currentStage,
                               const bool dissipativeResidualFlag = true,
                               const bool multiGridForceFlag = false);

    /**
     * @brief 残值光顺
     * 
     */
    void SmoothResiduals();

    /**
    * @brief 从细网格层中将残值赋给Force项
    *
    */
    void AcceptFlowForceFromFine();

    /**
    * @brief 根据细网格传入残差和粗网格残差计算粗网格Force项
    *
    */
    void CalculateFlowForceFromFine();

    /**
    * @brief 从细网格层中添加Force项
    *
    */
    void AddFlowForceFromFine();

    /**
     * @brief 保存耗散残差
     * 混合格式计算第一步，保存残值中的耗散项部分
     * 
     */
    void SaveFlowDissipativeResidual();
    
    /**
     * @brief 添加耗散残差
     * 
     */
    void AddFlowDissipativeResidual();
    
    /**
     * @brief 双时间步计算添加非定常残值源项
     * 
     */
    void AddUnsteadyResidual();
    
    /**
     * @brief 对更新后变量进行限制
     * 
     */
    void CheckAndLimit();

    /**
     * @brief 对残值进行低速预处理
     * 
     */
    void CalculatePrecondition();
    
    /**
     * @brief 对一个控制体的流场变量进行检查并打印详细信息
     * 
     * @param[in] elementID 单元编号
     */
    void CheckAndOutput(const int &elementID);

    /**
     * @brief 输出一个控制体的几何信息
     * 
     * @param[in] elementID 单元编号
     */
    void OutputCell(const int &elementID);
    
    /**
     * @brief 监测一个控制体的流场变量、残值及面的左右值
     * 
     * @param[in] elementID 单元编号
     */
    void MonitorCellValue(const int &elementID);

protected:
	const Configure::Flow::FlowConfigure &flowConfigure; ///< 流场参数
    Package::FlowPackage &flowPackage; ///< 流场包
    Mesh *mesh; ///< 当前网格
    const Material::Flow::Materials &material; ///< 材料对象
    const bool &implicitFlag; ///<隐式计算标识

    int numberLevel; ///< 多重网格求解总层级
    int currentLevel; ///< 当前网格层数
    int fineMeshLevel; ///< 细网格层级
	const bool nodeCenter; ///< 格点标识，true为格点

    ElementField<Scalar> *rho; ///< 密度
    ElementField<Vector> *U; ///< 速度
    ElementField<Scalar> *p; ///< 压强
    ElementField<Scalar> *T; ///< 温度
	ElementField<Scalar> *A; ///< 音速
	ElementField<Scalar> *H; ///< 总焓
    std::vector<ElementField<Scalar> *> turbulence; ///< 湍流量

    ElementField<Scalar> *residualMass; ///< 质量残值
    ElementField<Vector> *residualMomentum; ///< 动量残值
    ElementField<Scalar> *residualEnergy; ///< 能量残值
    std::vector<ElementField<Scalar> *> residualTurbulence; ///< 湍流量残值

    ElementField<Scalar> *rho0; ///< 上一步密度
    ElementField<Vector> *U0; ///< 上一步速度
    ElementField<Scalar> *p0; ///< 上一步压强
    std::vector<ElementField<Scalar> *> turbulence0; ///< 上一步湍流量

    ElementField<Scalar> *lambdaConvective; ///< 对流谱半径
    ElementField<Scalar> *lambdaViscous; ///< 粘性谱半径
    ElementField<Scalar> *deltaT; ///< 时间步长
    std::vector<ElementField<Scalar>*> jacobianTurbulence; ///< 点隐式湍流jacobian
	
    Flux::Flow::FlowFluxManager fluxNS; ///< NS方程的通量
    Boundary::Flow::FlowBoundaryManager boundaryConditionNS; ///< NS方程的边界条件
    Turbulence::TurbulenceManager *turbulenceNS; ///< 流体的粘性处理类型（如无粘、层流、一方程SA、二方程k-w等）
    Flux::Flow::Precondition::Precondition *precondition; ///< 低速预处理指针及相关变量
    Smoother::FieldSmoother *smoother; ///< 残值光顺对象
    
    Gradient::GradientManager *gradient; ///< 梯度计算对象
    FieldManipulation::GradientScheme gradientScheme; ///< 梯度计算方法

    CFLNumberManager CFLNumber; ///< CFL数管理器

    // 流动状态的相关参数
    const bool dim3;         ///< 三维标识
    const int nVariable;     ///< 流场变量数量
    const bool &viscousFlag; ///< 粘性标识，true为粘性
    const int &nTurbulence;  ///< 湍流量的变量数量

#if defined(_EnableMultiSpecies_)
	const int &speciesSize; ///< 组分数量
	std::vector<ElementField<Scalar> *> massFraction; ///< 湍流量
	std::vector<ElementField<Scalar> *> massFraction0; ///< 湍流量
	std::vector<ElementField<Scalar> *> residualMassFraction; ///< 湍流量残值
#endif

    Time::Scheme timeScheme; ///< 时间格式类型        

    // 应用多重网格技术时细网格对粗网格的方程残值限制项（Force项）
    ElementField<Scalar> residualForceMass; ///< 质量残值限制项
    ElementField<Vector> residualForceMomentum; ///< 动量残值限制项
    ElementField<Scalar> residualForceEnergy; ///< 能量残值限制项
    std::vector<ElementField<Scalar> *> residualForceTurbulence; ///< 湍流残值限制项

    // 非定常相关参数
    bool unsteadyFlag; ///< 非定常标识：true为非定常
    bool dualTime; ///< 双时间步标识,true为双时间步，false为定常
    Scalar physicalDeltaTime; ///< 物理时间步长
    Time::UnsteadyOrder unsteadyOrder; ///< 非定常阶数
    Scalar beta; ///< 非定常计算稳定性修正常数

	const Scalar &gamma1; ///< gamma - 1.0
    const Scalar gamma1Inv; ///< 1.0 / (\gamma - 1.0); 

    int numberStage; ///< 时间推进格式中的迭代步数
    bool hybridFlag; ///< 混合格式标识, true为混合
    bool updateTurbOnly; ///< 只更新湍流方程，用于机器学习训练集生成
    std::vector<bool> calculatedFlag; ///< 混合格式计算中每步耗散项计算标识容器

    // 混合格式计算时的耗散残值（对流项耗散项和粘性项残值之和）
    ElementField<Scalar> dissipativeResidualMass; ///< 质量耗散残值
    ElementField<Vector> dissipativeResidualMomentum; ///< 动量耗散残值
    ElementField<Scalar> dissipativeResidualEnergy; ///< 能量耗散残值
    std::vector<ElementField<Scalar> *> dissipativeResidualTurbulence; ///< 湍流对流项采用一阶迎风，无对流耗散
    
private:
	const std::vector<FlowMacro::Scalar> &turbulenceResidualMacro; ///< 湍流量残值枚举名称的容器
};

} // namespace Flow
} // namespace Time

#endif
