// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CTIME_HEADER
#define __CTIME_HEADER

#include <time.h>

namespace std {
  using ::size_t;
  using ::clock_t;
  using ::time_t;
  using ::tm;
  using ::asctime;
  using ::clock;
  using ::difftime;
  using ::localtime;
  using ::strftime;
  using ::ctime;
  using ::gmtime;
  using ::mktime;
  using ::time;
}

#endif // CTIME_HEADER
