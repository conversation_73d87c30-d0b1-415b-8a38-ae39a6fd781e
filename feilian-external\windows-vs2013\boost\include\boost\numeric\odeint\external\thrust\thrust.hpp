/*
  [auto_generated]
  boost/numeric/odeint/external/thrust/thrust.hpp

  [begin_description]
  includes all headers required for using odeint with thrust
  [end_description]

  Copyright 2013 <PERSON><PERSON>
  Copyright 2013 <PERSON>

  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or
  copy at http://www.boost.org/LICENSE_1_0.txt)
*/


#ifndef BOOST_NUMERIC_ODEINT_EXTERNAL_THRUST_HPP_DEFINED
#define BOOST_NUMERIC_ODEINT_EXTERNAL_THRUST_HPP_DEFINED

#include <boost/numeric/odeint/external/thrust/thrust_algebra.hpp>
#include <boost/numeric/odeint/external/thrust/thrust_operations.hpp>
#include <boost/numeric/odeint/external/thrust/thrust_algebra_dispatcher.hpp>
#include <boost/numeric/odeint/external/thrust/thrust_operations_dispatcher.hpp>
#include <boost/numeric/odeint/external/thrust/thrust_resize.hpp>

#endif // BOOST_NUMERIC_ODEINT_EXTERNAL_THRUST_HPP_DEFINED
