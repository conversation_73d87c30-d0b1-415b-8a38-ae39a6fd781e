//  synchronizaion.hpp  --------------------------------------------------------------//

//  Copyright 2010 <PERSON> J. Botet Escriba
//  Copyright 2015 <PERSON><PERSON>

//  Distributed under the Boost Software License, Version 1.0.
//  See http://www.boost.org/LICENSE_1_0.txt


#ifndef BOOST_DETAIL_WINAPI_SYNCHRONIZATION_HPP
#define BOOST_DETAIL_WINAPI_SYNCHRONIZATION_HPP

#include <boost/detail/winapi/basic_types.hpp>
#include <boost/detail/winapi/critical_section.hpp>
#include <boost/detail/winapi/wait.hpp>
#include <boost/detail/winapi/event.hpp>
#include <boost/detail/winapi/mutex.hpp>
#include <boost/detail/winapi/semaphore.hpp>
#include <boost/detail/winapi/init_once.hpp>
#include <boost/detail/winapi/srw_lock.hpp>
#include <boost/detail/winapi/condition_variable.hpp>
#include <boost/detail/winapi/apc.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_DETAIL_WINAPI_SYNCHRONIZATION_HPP
