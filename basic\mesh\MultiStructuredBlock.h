﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MultiStructuredBlock.h
//! <AUTHOR>
//! @brief 多块结构网格的辅助类
//! @date 2024-06-05
//
//------------------------------修改日志----------------------------------------
// 2024-06-05 李艳亮、乔龙（气动院）
//    说明：建立并规范化。
//
//------------------------------------------------------------------------------
#ifndef _basic_mesh_MultiStructuredBlock_
#define _basic_mesh_MultiStructuredBlock_

#include "basic/mesh/MeshSupport.h"

/**
 * @brief 单块结构网格数据结构
 * @details 表示一个结构化的网格块，包含网格点、单元信息及边界范围等
 */
struct Block
{
public:
    /**
     * @brief 默认构造函数
     */
    Block() : nodeI(0), nodeJ(0), nodeK(0), nodeIJ(0),
              cellI(0), cellJ(0), cellK(0), cellIJ(0), cellstart(0) {};

    /**
     * @brief 带参数构造函数
     * @param idim I方向网格点数
     * @param jdim J方向网格点数 
     * @param kdim K方向网格点数
     * @param cellstart 当前块单元的起始编号
     * @param boundaryRange_ 边界范围容器
     */
    Block(const int& idim, const int& jdim, const int& kdim, 
          const int& cellstart, const std::vector<std::vector<int>>& boundaryRange_)
        : nodeI(idim), nodeJ(jdim), nodeK(kdim), nodeIJ(idim * jdim),
          cellI(idim - 1), cellJ(jdim - 1), cellK(kdim - 1),
          cellIJ((idim - 1) * (jdim - 1)), cellstart(cellstart),
          boundaryRange(boundaryRange_) {};

    /**
     * @brief 获取网格点全局索引
     * @param i I方向局部索引
     * @param j J方向局部索引
     * @param k K方向局部索引
     * @return 全局点索引
     */
    const int& GetNodeIndex(const int& i, const int& j, const int& k) const
    { 
        return nodeIndex[i][j][k]; 
    }

    /**
     * @brief 获取网格单元全局索引
     * @param i I方向局部索引
     * @param j J方向局部索引 
     * @param k K方向局部索引
     * @return 全局单元索引
     */
    const int GetElementIndex(const int& i, const int& j, const int& k) const
    { 
        return cellstart + k * cellIJ + j * cellI + i; 
    }

    /**
     * @brief 根据全局索引获取局部IJK索引
     * @param index 全局单元索引
     * @param[out] i I方向局部索引
     * @param[out] j J方向局部索引
     * @param[out] k K方向局部索引
     */
    void GetElementIJK(const int& index, int& i, int& j, int& k) const
    {
        int cellIJK = index - cellstart;
        k = cellIJK / cellIJ;
        int totalIJ = cellIJK % cellIJ;
        j = totalIJ / cellI;
        i = totalIJ % cellI;
    }

public:
    int nodeI;  ///< I方向网格点数
    int nodeJ;  ///< J方向网格点数
    int nodeK;  ///< K方向网格点数
    int nodeIJ; ///< K平面内网格点数(nodeI*nodeJ)
    
    /// 局部点与全局点的索引映射关系 [i][j][k] -> globalIndex
    std::vector<std::vector<std::vector<int>>> nodeIndex;  

    int cellI;     ///< I方向网格单元数(nodeI-1)
    int cellJ;     ///< J方向网格单元数(nodeJ-1) 
    int cellK;     ///< K方向网格单元数(nodeK-1)
    int cellIJ;    ///< K平面内网格单元数(cellI*cellJ)
    int cellstart; ///< 当前块单元的起始全局编号

    std::vector<std::vector<int>> boundaryRange; ///< 物理边界范围 [边界ID][minI,maxI,minJ,maxJ,minK,maxK]
};

/**
 * @brief 多块网格间的邻接关系(一个交接面)
 * @details 描述两个网格块之间的连接关系，包括ID映射和索引变换
 */
struct Connection
{
    int leftID;  ///< 左侧块ID
    int rightID; ///< 右侧块ID
    
    std::vector<int> leftRange;  ///< 左侧块索引范围 [minI,maxI,minJ,maxJ,minK,maxK]
    std::vector<int> rightRange; ///< 右侧块索引范围 [minI,maxI,minJ,maxJ,minK,maxK]
    std::vector<int> transform;  ///< 索引变换规则 [i变换,j变换,k变换]

    /**
     * @brief 默认构造函数
     */
    Connection() = default;

    /**
     * @brief 拷贝构造函数
     * @param c 源连接对象
     */
    Connection(const Connection& c) = default;

    /**
     * @brief 带参数构造函数
     * @param leftID_ 左侧块ID
     * @param rightID_ 右侧块ID
     * @param leftRange_ 左侧块索引范围
     * @param rightRange_ 右侧块索引范围
     * @param transform_ 索引变换规则
     */
    Connection(const int& leftID_, const int& rightID_,
              const std::vector<int>& leftRange_,
              const std::vector<int>& rightRange_,
              const std::vector<int>& transform_)
        : leftID(leftID_), rightID(rightID_),
          leftRange(leftRange_), rightRange(rightRange_),
          transform(transform_) {}

    /**
     * @brief 获取左侧块索引
     * @param i I方向局部索引
     * @param j J方向局部索引
     * @param k K方向局部索引
     * @return 左侧块索引数组 [i,j,k]
     */
    std::vector<int> ObtainLeftIndex(const int& i, const int& j, const int& k) const {
        return {leftRange[0] + i, leftRange[1] + j, leftRange[2] + k};
    }

    /**
     * @brief 获取右侧块索引
     * @param i I方向局部索引
     * @param j J方向局部索引
     * @param k K方向局部索引
     * @return 右侧块索引数组 [i,j,k]
     */
    std::vector<int> ObtainRightIndex(const int& i, const int& j, const int& k) const {
        std::vector<int> indexRight(3, 0);
        
        // 根据变换规则计算右侧索引
        auto applyTransform = [&](int axis, int value) {
            switch (transform[axis]) {
                case  1: indexRight[0] = rightRange[0] + value; break;
                case -1: indexRight[0] = rightRange[0] - value; break;
                case  2: indexRight[1] = rightRange[1] + value; break;
                case -2: indexRight[1] = rightRange[1] - value; break;
                case  3: indexRight[2] = rightRange[2] + value; break;
                case -3: indexRight[2] = rightRange[2] - value; break;
            }
        };

        applyTransform(0, i);
        applyTransform(1, j);
        applyTransform(2, k);

        return indexRight;
    }
};

/**
 * @brief 多块结构网格管理类
 * @details 管理多个结构网格块及其连接关系，提供全局索引管理功能
 */
class MultiStructuredBlock
{
public:
    MultiStructuredBlock() = default; ///< 默认构造函数
    ~MultiStructuredBlock() = default; ///< 默认析构函数

    /**
     * @brief 获取网格块数量
     * @return 网格块总数
     */
    int GetBlockSize() const { return multiBlock.size(); }

    /**
     * @brief 获取连接关系数量
     * @return 连接关系总数
     */
    int GetConnectionSize() const { return connection.size(); }

    /**
     * @brief 获取总网格点数
     * @return 全局网格点总数
     */
    int GetNodeTotalSize() const { return nodeTotalSize; }

    /**
     * @brief 获取指定网格块
     * @param blockID 块ID
     * @return 网格块对象引用
     */
    const Block& GetBlock(const int& blockID) const { return multiBlock[blockID]; }

    /**
     * @brief 获取指定连接关系
     * @param connectionID 连接ID
     * @return 连接关系对象引用
     */
    const Connection& GetConnection(const int& connectionID) const { 
        return connection[connectionID]; 
    }

    /**
     * @brief 添加网格块
     * @param block_ 网格块对象
     */
    void AddBlock(const Block& block_) { multiBlock.push_back(block_); }

    /**
     * @brief 添加连接关系
     * @param connection_ 连接关系对象
     */
    void AddConnection(const Connection& connection_) {
        connection.push_back(connection_);
    }

    /**
     * @brief 设置全局点索引
     * @param removeConnection 是否移除连接关系
     */
    void SetNodeIndex(const bool& removeConnection);

    /**
     * @brief 清空所有网格块和连接关系
     */
    void Clear() { 
        multiBlock.clear();  
        connection.clear(); 
        nodeTotalSize = 0; 
    }

private:
    std::vector<Block> multiBlock; ///< 网格块容器
    std::vector<Connection> connection; ///< 连接关系容器
    int nodeTotalSize = 0; ///< 全局网格点总数
};

#endif // _basic_mesh_MultiStructuredBlock_
