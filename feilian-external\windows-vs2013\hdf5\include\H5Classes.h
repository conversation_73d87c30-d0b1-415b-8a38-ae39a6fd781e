// C++ informative line for the emacs editor: -*- C++ -*-
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * Copyright by the Board of Trustees of the University of Illinois.         *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://support.hdfgroup.org/ftp/HDF5/releases.  *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

#ifndef __H5Classes_H
#define __H5Classes_H

namespace H5 {
        class Exception;
        class IdComponent;
        class H5Location;
        class H5Object;
        class PropList;
        class FileCreatPropList;
        class FileAccPropList;
        class DSetCreatPropList;
        class DSetMemXferPropList;
        class DTypePropList;
        class DataType;
        class DataSpace;
        class AtomType;
        class PredType;
        class EnumType;
        class IntType;
        class FloatType;
        class StrType;
        class CompType;
        class AbstractDs;
        class DataSet;
        class Group;
        class H5File;
        class Attribute;
        class H5Library;
}
#endif // __H5Classes_H
