//
//  (C) Copyright 2010-2011 <PERSON> Escriba
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).
//

#ifndef BOOST_CHRONO_CLOCK_STRING_HPP
#define BOOST_CHRONO_CLOCK_STRING_HPP

#include <string>

namespace boost
{
  namespace chrono
  {

    template<class Clock, class CharT>
    struct clock_string;

  } // chrono

} // boost

#endif  // BOOST_CHRONO_CLOCK_STRING_HPP
