 #pragma once
#include "AltTecUtil.h"
#include "basicTypes.h"
#include "IJK.h"
namespace tecplot { namespace ___3933 { inline bool supportedOrderedVolumeZoneType(ZoneType_e ___4692, tecplot::___3933::___1844 const& ___4632) { return (___4692 == ___4704 && ___4632.i() > 1 && ___4632.___2105() > 1 && ___4632.___2134() > 1); } inline bool ___3894(ZoneType_e ___4692) { return ___4692==___4701 || ___4692==___4695; } inline bool ___3895(ZoneType_e ___4692, ___1844 const& ___4632) { return supportedOrderedVolumeZoneType(___4692, ___4632) || ___3894(___4692); } inline bool supportedZoneType(ZoneType_e ___4692) { return ___4692 == ___4704 || ___4692 == ___4702 || ___4692 == ___4700 || ___4692 == ___4701 || ___4692 == ___4695 || ___4692 == ___4696; } inline bool linearZoneType(ZoneType_e ___4692, ___1844 const& ___4632) { bool ___3358 = false; if (___4692 == ___4696) { ___3358 = true; } else if (___4692 == ___4704) { int ___1089 = 0; if (___4632.i() > 1) ++___1089; if (___4632.___2105() > 1) ++___1089; if (___4632.___2134() > 1) ++___1089; ___3358 = ___1089 <= 1; } return ___3358; } inline bool ___3158(ZoneType_e ___4692) { return ___4692 == ___4698 || ___4692 == ___4699; } inline ___372 ___4641(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); ZoneType_e ___4692 = ___36.___4620(zone+1); return ___4692==___4704; } inline ___372 ___4645(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); ZoneType_e ___4692 = ___36.___4620(zone+1); return ___4692==___4701; } inline ___372 ___4637(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); ZoneType_e ___4692 = ___36.___4620(zone+1); return ___4692==___4695; } inline ___372 ___4639(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); ZoneType_e ___4692 = ___36.___4620(zone+1); return ___4692==___4701 || ___4692==___4695; } inline ___372 ___4646(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); if (___4639(___36, zone)) { return ___4226; } else if (___4641(___36, zone)) { ___1844 ___1843; ___36.___4615(zone + 1, ___1843); if (___1843.i() > 1 && ___1843.___2105() > 1 && ___1843.___2134() > 1) return ___4226; } return ___1305; } inline ___372 ___4643(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); return (___36.___4620(zone + 1) == ___4698 || ___36.___4620(zone + 1) == ___4699); } inline ___372 zoneIsPartitioned(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); return ___36.zoneIsPartitioned(zone + 1); } inline ___372 ___4644(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); return ___3894(___36.___4620(zone + 1)); } inline ___372 ___4642(___37& ___36, ___4636 zone) { REQUIRE(zone >= 0); return ___4646(___36, zone) && ___4641(___36, zone); } }}
