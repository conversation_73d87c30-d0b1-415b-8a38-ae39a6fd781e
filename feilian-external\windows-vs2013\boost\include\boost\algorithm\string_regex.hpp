//  Boost string_algo library string_regex.hpp header file  ---------------------------//

//  Copyright Pavol Droba 2002-2004.
//
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/ for updates, documentation, and revision history.

#ifndef BOOST_STRING_ALGO_REGEX_HPP
#define BOOST_STRING_ALGO_REGEX_HPP

/*! \file
    Cumulative include for string_algo library.
    In addition to string.hpp contains also regex-related stuff.
*/

#include <boost/regex.hpp>
#include <boost/algorithm/string.hpp>
#include <boost/algorithm/string/regex.hpp>

#endif  // BOOST_STRING_ALGO_REGEX_HPP
