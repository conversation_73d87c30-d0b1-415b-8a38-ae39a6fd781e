﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Wall.h
//! <AUTHOR> 乔龙
//! @brief 物面边界的基类
//! @date  2022-06-24
//
//------------------------------修改日志----------------------------------------
//
// 2022-06-24 李艳亮 乔龙 
// 说明：建立
// 
//------------------------------------------------------------------------------
#ifndef _sourceFlow_boundaryCondition_Wall_
#define _sourceFlow_boundaryCondition_Wall_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 壁面边界条件基类
 * 派生自流场边界条件基类
 * 
 */
class Wall : public FlowBoundary
{
public:
    /**
     * @brief 构造函数，初始化壁面边界条件
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     */
    Wall(const int &boundaryPatchID, Package::FlowPackage &data);

    /**
     * @brief 纯虚函数，初始化边界值
     * 
     */
    virtual void Initialize() = 0;

    /**
     * @brief 纯虚函数，更新边界条件
     * 
     */
    virtual void UpdateBoundaryCondition() = 0;

    /**
     * @brief 计算边界粘性残值
     * 
     */
    virtual void AddDiffusiveResidual() = 0;
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    virtual void AddConvectiveResidual() = 0;

    /**
     * @brief 更新边界残值(用于对偶网格)
     * 
     */
    virtual void UpdateBoundaryResidual() = 0;

protected:
    /**
     * @brief 根据流动类型确定壁面速度、层流和湍流粘性系数
     * 
     */
    void SetVelocityAndMu();
    
    /**
     * @brief 更新滑移壁面边界残值(用于对偶网格)
     * 
     */
    void UpdateBoundaryResidualSlipping();

    /**
     * @brief 更新静止壁面边界残值(用于对偶网格)
     * 
     */
    void UpdateBoundaryResidualStatic();

};

} // namespace Flow
} // namespace Boundary


#endif
