// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CMATH_HEADER
#define __CMATH_HEADER

#include <math.h>

namespace std {
  using ::acos;
  using ::cos;
  using ::fmod;
  using ::modf;
  using ::tan;
  using ::asin;
  using ::cosh;
  using ::frexp;
  using ::pow;
  using ::tanh;
  using ::atan;
  using ::exp;
  using ::ldexp;
  using ::sin;
  using ::atan2;
  using ::fabs;
  using ::log;
  using ::sinh;
  using ::ceil;
  using ::floor;
  using ::log10;
  using ::sqrt;
}

#endif // CMATH_HEADER
