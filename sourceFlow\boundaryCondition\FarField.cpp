﻿#include "sourceFlow/boundaryCondition/FarField.h"

namespace Boundary
{
namespace Flow
{

FarField::FarField(const int &boundaryPatchID,
             Package::FlowPackage &data,
             const Scalar &density,
             const Vector &velocity,
             const Scalar &pressure,
             Flux::Flow::Precondition::Precondition *precondition_)
    :
    ExternalBoundary(boundaryPatchID, data),
    precondition(precondition_),
    gamma(data.GetMaterialNumber().gamma),
    gamma1(data.GetMaterialNumber().gamma1)
{
    this->rho_inf = density;
    this->velocity_inf = velocity;
    this->Pressure_inf = pressure;
    this->soundSpeed_inf = material.GetSoundSpeed(Pressure_inf, rho_inf);
    turbulentViscosityRatio =  data.GetFlowConfigure().GetFlowReference().turbulentViscosityRatio;
}

void FarField::Initialize()
{
    this->UpdateBoundaryCondition();
}

void FarField::UpdateBoundaryCondition()
{
    if (precondition != nullptr) this->UpdateBoundaryConditionPrecondition();
    else                         this->UpdateBoundaryConditionNonPrecondition();
}

void FarField::UpdateBoundaryConditionNonPrecondition0()
{
    // 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);        
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();

        // 取得特定面心相邻体心物理量
        Scalar rhoOwner; Vector UOwner; Scalar pOwner;
        if(nodeCenter)
        {
            const int &adjacentID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, j);
            rhoOwner = rho.GetValue(adjacentID);
            UOwner = U.GetValue(adjacentID);
            pOwner = p.GetValue(adjacentID);
        }
        else
        {
            rhoOwner = rho.GetValue(ownerID);
            UOwner = U.GetValue(ownerID);
            pOwner = p.GetValue(ownerID);
        }
        
        const Scalar aOwner = material.GetSoundSpeed(pOwner, rhoOwner);
        const Scalar UNormal = UOwner & faceNormal;

        const Scalar UModified = fabs(UNormal);
        const Scalar cModified = aOwner;

        // 参考 JIRI BLAZEK 《COMPUTATIONAL FLUID DYNAMICS Principles and Applications》3rd:pp 262-264
        const Scalar ROwner = UNormal + 2 * aOwner / gamma1;
        const Scalar R_inf = (velocity_inf & faceNormal) - 2 * soundSpeed_inf / gamma1;

        const Vector Vb_norm = faceNormal * (R_inf + ROwner) / 2.0;
        const Scalar ab = gamma1 / 4.0 * (ROwner - R_inf);
        
        // 远场边界某特定面心的物理量
        Vector Ub;
        Scalar pb;
        Scalar rhob;
        
        if ((R_inf + ROwner) > 0) /* Out let */ // 出流边界条件
        {
            // 超音速出流边界上各面心的密度、速度矢量、压强直接取相邻体心的对应值
            if (UModified / cModified > 1.0)
            {
                Ub = UOwner;
                pb = pOwner;
                rhob = rhoOwner;
            }
            else  // 亚音速出流边界上面心的密度、速度矢量、压强采用Riemann 无反射边界条件
            {
                Vector Vi_tang = UOwner;
                Vi_tang.Projection(faceNormal);
                Ub = Vi_tang + Vb_norm;
                rhob = pow(pow(rhoOwner, gamma) * pow(ab, 2.0) / pOwner / gamma, 1.0 / gamma1);
                pb = rhob * pow(ab, 2.0) / gamma;
            }
        }
        else // 入流边界条件
        {
            // 超音速入流边界上各面心的密度、速度矢量、压强直接取自由来流的对应值
            if (UModified / cModified > 1.0)
            {
                Ub = velocity_inf;
                pb = Pressure_inf;
                rhob = rho_inf;
            }
            else // 亚音速入流边界上各面心的密度、速度矢量、压强采用Riemann 无反射边界条件.
            {
                Vector Vinf_tang = velocity_inf;
                Vinf_tang.Projection(faceNormal);
                Ub = Vinf_tang + Vb_norm;
                rhob = pow(pow(rho_inf, gamma) * pow(ab, 2.0) / Pressure_inf / gamma, 1.0 / gamma1);
                pb = rhob * pow(ab, 2.0) / gamma;
            }
        }
        
        const Scalar Tb = material.GetTemperature(pb, rhob);

        if(nodeCenter)
        {
            rho.SetValue(ownerID, rhob);
            U.SetValue(ownerID, Ub);
            p.SetValue(ownerID, pb);
            T.SetValue(ownerID, Tb);
        }

        rho.SetValue(neighID, rhob);
        U.SetValue(neighID, Ub);
        p.SetValue(neighID, pb);
        T.SetValue(neighID, Tb);
    }

    return;
}

void FarField::UpdateBoundaryConditionNonPrecondition()
{
    // 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();

        // 取得特定面心相邻体心物理量
        const Scalar &rhoOwner = rho.GetValue(ownerID);
        const Vector &UOwner = U.GetValue(ownerID);
        const Scalar &pOwner = p.GetValue(ownerID);
        const Scalar TOwner = material.GetTemperature(pOwner, rhoOwner);
        const Scalar muOwner = material.Mu(TOwner);
        const Scalar aOwner = material.GetSoundSpeed(pOwner, rhoOwner);

        // 计算法向速度
        const Scalar UNormal = UOwner & faceNormal;
        const Scalar UInfNormal = velocity_inf & faceNormal;

        // 计算特征值
        const Scalar eigenValue1 = UNormal;
        const Scalar eigenValue2 = UNormal + aOwner;
        const Scalar eigenValue3 = UInfNormal - soundSpeed_inf;

        const Scalar ROC = this->rho_inf*this->soundSpeed_inf;
        const Scalar C2 = this->soundSpeed_inf*this->soundSpeed_inf;

        // 计算特征量
        Vector WW1; Scalar WW4, WW5;
        if (eigenValue1 > 0.0) WW1 = (C2 * rhoOwner - pOwner) * faceNormal + ROC * (UOwner ^ faceNormal);
        else                   WW1 = (C2 * rho_inf - Pressure_inf) * faceNormal + ROC* (velocity_inf ^ faceNormal);
        if (eigenValue2 > 0.0) WW4 = 0.5 * ROC * UNormal + 0.5 * pOwner;
        else                   WW4 = 0.5 * ROC * UInfNormal + 0.5 * Pressure_inf;
        if (eigenValue3 > 0.0) WW5 = -0.5 * ROC * UNormal + 0.5 * pOwner;
        else                   WW5 = -0.5 * ROC * UInfNormal + 0.5 * Pressure_inf;

        // 计算基本量
        const Scalar rhob = ((WW1 & faceNormal) + WW4 + WW5) / C2;
        const Vector Ub = ((faceNormal ^ WW1) + faceNormal *(WW4 - WW5)) / ROC;
        const Scalar pb = WW4 + WW5;
        const Scalar Tb = material.GetTemperature(pb, rhob);

        rho.SetValue(neighID, 2.0 * rhob - rhoOwner);
        U.SetValue(neighID, 2.0 * Ub - UOwner);
        p.SetValue(neighID, 2.0 * pb - pOwner);
        T.SetValue(neighID, 2.0 * Tb - TOwner);
    }

    return;
}

void FarField::UpdateBoundaryConditionPrecondition()
{
	const Scalar K2 = 4.0;

	// 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceID(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		//const Vector faceNormal = -1.0 * mesh->GetFace(faceID).GetNormal(); 
		// 代码处理成流入为正向
		const Vector faceNormal = -1.0 * mesh->GetFace(faceID).GetNormal();

		// 2.1 设置自由来流值法向速度和面单元的局部量
		Scalar U2Inf, C2Inf, Ma2Inf, UnormalInf, betaInf;
		C2Inf = soundSpeed_inf * soundSpeed_inf;
		U2Inf = velocity_inf & velocity_inf;
		UnormalInf = velocity_inf & faceNormal;
		Ma2Inf = (velocity_inf & velocity_inf) / C2Inf;

		betaInf = Min(Max(U2Inf, K2*U2Inf), C2Inf);

		Scalar rhoFace, TFace, U2Face, C2Face, Ma2Face, pFace, betaFace, UnormalFace;
		Vector UFace;
		rhoFace = rho.GetValue(ownerID);
		UFace = U.GetValue(ownerID);
		pFace = p.GetValue(ownerID);
		TFace = T.GetValue(ownerID);
		UnormalFace = UFace & faceNormal;
		U2Face = UFace & UFace;
		C2Face = material.GetSoundSpeed(TFace) * material.GetSoundSpeed(TFace);
		Ma2Face = U2Face / C2Face;
		betaFace = Min(Max(U2Face, K2*U2Inf), C2Face);

		// 2.2 计算黎曼条件中相关的分量
		Scalar zInf, z1Inf, fInf, zFace, fFace;
		zInf = 1 + betaInf / C2Inf;
		z1Inf = 1 - betaInf / C2Inf;
		fInf = sqrt(abs(zInf*zInf*UnormalInf*UnormalInf + 4 * (1 - Ma2Inf)*betaInf));
		zFace = 1 + betaFace / C2Face;
		// Scalar z1Face = 1 - betaFace / C2Face;
		fFace = sqrt(abs(zFace*zFace*UnormalFace*UnormalFace + 4 * (1 - Ma2Face)*betaFace));

		// 2.3 前向（入流）特征值方向的判断和处理
		Scalar eigenvalue4, fact4Inf;
		eigenvalue4 = 0.5 * (zInf*UnormalInf + fInf);
		fact4Inf = 0.5 * (z1Inf*UnormalInf - fInf) / (rho_inf*betaInf);
		// Scalar fact4Face = 0.5 * (z1Face*UnormalFace - fFace) / (rhoFace*betaFace);
		Scalar R4;
		// 超音速出流, R4来源于内部流场
		if (eigenvalue4 < 0.0)
		{
			R4 = UnormalFace - pFace*fact4Inf;
		}
		// 其余情况，R4来源于边界远场值
		else
		{
			R4 = UnormalInf - Pressure_inf*fact4Inf;
		}

		// 2.4 后向（出流）特征值方向的判断和处理
		Scalar eigenvalue5, fact5Inf;
		eigenvalue5 = 0.5 * (UnormalFace*zFace - fFace);
		fact5Inf = 0.5 * (UnormalInf*z1Inf + fInf) / (rho_inf*betaInf);
		// Scalar fact5Face = 0.5 * (UnormalFace*z1Face + fFace) / (rhoFace*betaFace);
		Scalar R5;
		// 超音速入流, R5来源于边界远场值
		if (eigenvalue5 > 0.0)
		{
			R5 = UnormalInf - Pressure_inf*fact5Inf;
		}
		// 其余情况，R5来源于内部流场
		else
		{
			R5 = UnormalFace - pFace*fact5Inf;
		}

		// 2.5 应用平均化处理法向速度和音速
		Scalar pSpec, UnornalTemp;
		UnornalTemp = (fact5Inf*R4 - fact4Inf*R5) / (fact5Inf - fact4Inf);
		pSpec = (R4 - R5) / (fact5Inf - fact4Inf);

		// 2.6 确定切向速度
		Vector UTang;
		Scalar SSpec;
		if (UnornalTemp > 0.0)
			// 入流边界，远场自由流的切向速度、熵
		{
			UTang = velocity_inf + (UnornalTemp - UnormalInf)*faceNormal;
			SSpec = Pressure_inf / pow(rho_inf, gamma);
		}
		// 出流边界，切向速度和熵外插
		else
		{
			UTang = UFace + (UnornalTemp - UnormalFace) * faceNormal;
			SSpec = pFace / pow(rhoFace, gamma);
		}

		// 2.7 确定密度和能量
		Scalar rhoSpec;
		rhoSpec = pow(pSpec / SSpec, 1.0 / gamma);
		Scalar Tspec = material.GetTemperature(pSpec, rhoSpec);

		/*
		// 2.8 计算得到远场边界处的流动变量值后，进行虚单元的变量更新
		rho.SetValue(neighID, 2 * rhoSpec - rhoFace);
		U.SetValue(neighID, 2 * UTang - UFace);
		p.SetValue(neighID, 2 * pSpec - pFace);
		T.SetValue(neighID, 2 * Tspec - material.GetTemperature(pFace, rhoFace));
		*/

		/*
		// 2.8 计算得到远场边界处的流动变量值后，进行虚单元的变量更新
		if (nodeCenter)
		{
		rho.SetValue(ownerID, rhoSpec);
		U.SetValue(ownerID, UTang);
		p.SetValue(ownerID, pSpec);
		T.SetValue(ownerID, Tspec);
		}

		rho.SetValue(neighID, rhoSpec);
		U.SetValue(neighID, UTang);
		p.SetValue(neighID, pSpec);
		T.SetValue(neighID, Tspec);
		*/



		// 2.8 计算得到远场边界处的流动变量值后，进行虚单元的变量更新  
		if (nodeCenter)
		{
			rho.SetValue(ownerID, rhoSpec);
			rho.SetValue(neighID, rhoSpec);
			U.SetValue(ownerID, UTang);
			U.SetValue(neighID, UTang);
			p.SetValue(ownerID, pSpec);
			p.SetValue(neighID, pSpec);
			T.SetValue(ownerID, Tspec);
			T.SetValue(neighID, Tspec);
		}
		else
		{
			rho.SetValue(neighID, 2 * rhoSpec - rhoFace);
			U.SetValue(neighID, 2 * UTang - UFace);
			p.SetValue(neighID, 2 * pSpec - pFace);
			T.SetValue(neighID, 2 * Tspec - material.GetTemperature(pFace, rhoFace));
        }
	}

	return;
}

}// namespace Flow
}// namespace Boundary