﻿#! /usr/bin/python3
# FeiLian_jobrun.py
# 2025-02-08

import os
import sys
import subprocess
import shutil
import platform


def run(XML_FILE, HOST_FILE, NP_CORE):
    # 判断系统
    SYSTEM = platform.system()
    if SYSTEM == "Windows":
        print("This is Windows system.")
    elif SYSTEM == "Linux":
        print("This is Linux system.")
    else:
        print(f"Undefined system : {SYSTEM}")
        sys.exit()

    # ---------------------------------------------------------------
    # 请仔细检查以下配置是否正确
    if SYSTEM == "Linux":
        # 成飞环境
        # FEILIAN_HOME = "/gpfs/software/FeiLian_1.1.7.18"
        # NASTRAN_HOME = "/gpfs/software/nastran/nastran2014"
        # NASTRAN_EXE = os.path.join(NASTRAN_HOME, "bin/nast20141")
        # MPI_HOME = os.path.join(FEILIAN_HOME, "mpich")
        
        # 气动院环境
        # FEILIAN_HOME = "/home/<USER>/guochengpeng/2025-1-26/feilian"
        NASTRAN_HOME = "/home/<USER>/MSC/MSC_Nastran/20121"
        NASTRAN_EXE = os.path.join(NASTRAN_HOME, "bin/nast20121")
        MPI_HOME = "/home/<USER>/intel/oneapi/mpi/2021.1.1"
        
        MPI_RUN = os.path.join(MPI_HOME, "bin/mpiexec")
        pass

        # 设置环境变量
        # os.environ['AUTHINFO'] = '27500@mgt01'

        # 设置栈大小限制
        # import resource
        # soft, hard = resource.getrlimit(resource.RLIMIT_STACK)
        # print(f"当前栈大小限制: 软限制 = {soft}, 硬限制 = {hard}")
        # resource.setrlimit(resource.RLIMIT_AS, (899617304, resource.RLIM_INFINITY))  # 设置最大内存使用
        # 下面设置栈大小会影响到subprocess.call()、subprocess.run, os.system()等的运行
        # resource.setrlimit(resource.RLIMIT_STACK, (8192, resource.RLIM_INFINITY))    # 设置栈大小
        # resource.setrlimit(resource.RLIMIT_CORE, (0, 0))                             # 禁用核心转储
        #soft, hard = resource.getrlimit(resource.RLIMIT_STACK)
        # print(f"设置后栈大小限制: 软限制 = {soft}, 硬限制 = {hard}")

    # windows 
    if SYSTEM == "Windows":
        # 个人计算机环境，每个人可能都不同
        # FEILIAN_HOME = r"D:\Apps\FeiLian 1.1.8.3"
        NASTRAN_HOME = r"C:\MSC.Software\MSC_Nastran\20170"
        # MPI_HOME = r"D:\Apps\IntelXE2018u3\parallel_studio_xe_2018\compilers_and_libraries_2018\windows\mpi\intel64"
        # MPI_RUN = os.path.join(MPI_HOME, "bin/mpiexec.exe")
        # NASTRAN_EXE = os.path.join(NASTRAN_HOME, "bin/nastran.exe")
        pass

    # # 设置环境变量
    # os.environ['LD_LIBRARY_PATH'] = ":".join([
    #     os.path.join(FEILIAN_HOME, "bin"),
    #     os.path.join(FEILIAN_HOME, "lib"),
    #     os.path.join(FEILIAN_HOME, "hdf5/lib"),
    #     os.path.join(FEILIAN_HOME, "mpich/lib"),
    #     os.environ.get('LD_LIBRARY_PATH', '')    
    # ])
    # 设置路径
    os.environ['PATH'] = os.pathsep.join([
            os.path.join(NASTRAN_HOME, "bin"),
            os.environ.get('PATH', '')
    ])

    # 定义文件名
    WORK_DIR = os.getcwd()
    BASE_FILE = os.path.basename(WORK_DIR)

    if SYSTEM == "Windows":
        if not os.path.exists(HOST_FILE):
            with open(HOST_FILE, 'w') as hostfile:
                hostfile.write("localhost")


    # 检查可执行文件是否都存在
    def check_command(command):
        result = shutil.which(command)
        if result is None:
            print(f"{command} not found! exit")
            sys.exit()
        else:
            print(f"{command} found. {result}")

    check_command("mainAeroStatic")
    check_command("flowPreprocessor")
    check_command("flowForceNondimensionalize")
    check_command("aflr3")
    check_command("aflr3_to_cgns")

    NASTRAN_EXE = None
    nastran_list = ['nastran.exe', 'nast20121', 'nast20140', 'nast20141', 'nast20170', 'nast20171']
    for name in nastran_list:
        tmp = shutil.which(name)
        if tmp:
            NASTRAN_EXE = tmp
            print(f"Found NASTRAN: {NASTRAN_EXE}")
            break
    if NASTRAN_EXE is None:
        print("nastran exe file not found in your enviroment! exit")
        sys.exit(1)
    with open(XML_FILE, 'r') as file:
        data = file.readlines()
    with open(XML_FILE, 'w') as file:
        for line in data:
            if "<NastranPath>" in line:
                line = line.replace(line.split('<NastranPath>')[1].split('</NastranPath>')[0], NASTRAN_EXE)
            file.write(line)

    WALL_FILE = "wall.dat"
    BOUND_FILE = "boundary.xml"

    MAXDEFORM_FILE = "maxDeform.dat"
    SOLVER_FILE = "AriCFD_Solver.info"
    OUT_FILE = "screen.out"
    # 请仔细检查以上配置是否正确
    # ---------------------------------------------------------------

    def get_xml_value(xml_file, path):
        """
        从 XML 文件中提取指定路径的值。
        :param xml_file: XML 文件路径
        :param path: 以 '/' 分隔的路径字符串，例如 'mesh/meshFile'
        :return: 找到的值，如果未找到则返回 None
        """
        with open(xml_file, 'r', encoding='utf-8') as file:
            xml_content = file.read()

        # 将路径分割为层级
        elements = path.split('/')
        
        # 遍历每个层级
        for element in elements:
            # 构建当前元素的开始和结束标签
            start_tag = f"<{element}>"
            end_tag = f"</{element}>"
            
            # 查找当前元素的开始和结束位置
            start_index = xml_content.find(start_tag)
            if start_index == -1:
                print(f"{element} is not exist.")
                sys.exit()  # 如果找不到元素，打印消息并退出
            
            # 更新内容为当前元素的内容
            end_index = xml_content.find(end_tag, start_index)
            if end_index == -1:
                print(f"{element} end tag is not exist.")
                sys.exit()  # 如果找不到结束标签，打印消息并退出
            
            # 提取当前元素的内容
            xml_content = xml_content[start_index + len(start_tag):end_index]
            if xml_content.strip() == '':
                print(f"{element} is empty.")
                sys.exit()  # 如果内容为空，打印消息并退出
        xml_content = xml_content.strip()
        if xml_content == 'true':
            xml_content = '1'
        elif xml_content == 'false':
            xml_content = '0'

        return xml_content.strip()  # 返回最终提取的值，去除多余空白

    def delete_folder_recursive(folder_path):
        try:
            # 遍历文件夹中的所有内容
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if item != 'nul':
                    # 如果是文件，直接删除
                    if os.path.isfile(item_path):
                        try:
                            os.remove(item_path)
                        except Exception as e:
                            print(f"Failed to delete file {item}: {e}")
                    
                    # 如果是文件夹，递归删除
                    elif os.path.isdir(item_path):
                        delete_folder_recursive(item_path)
            
            # 删除空文件夹
            try:
                os.rmdir(folder_path)
            except Exception as e:
                print(f"Failed to delete folder {folder_path}: {e}")
        
        except Exception as e:
            print(f"Error accessing {folder_path}: {e}")


    def count_bMesh_files(directory_path):
        # 初始化计数器为0
        count = 0
        
        # 检查路径是否存在
        if not os.path.exists(directory_path):
            print("指定的文件夹路径不存在。")
            return 0
        
        # 遍历目录中的所有条目
        for filename in os.listdir(directory_path):
            # 获取完整的文件路径
            file_path = os.path.join(directory_path, filename)
            
            # 检查是否是文件，并且后缀是.bMesh
            if os.path.isfile(file_path) and filename.endswith('.bMesh'):
                count += 1
        
        return count



    # 读取 XML 文件并提取 Delta_Deform 的收敛标准
    Delta_Deform_threshold = get_xml_value(XML_FILE, 'Delta_Deform')
    print("Delta_Deform is: {}".format(Delta_Deform_threshold))

    # 获取初始计算网格名及弹性变形后的网格名
    CGNS_FILE = get_xml_value(XML_FILE, 'mesh/meshFile')
    print("CGNS_FILE is: {}".format(CGNS_FILE))
    # 
    flag = 0
    if "/" in CGNS_FILE:
        with open(XML_FILE, 'r') as file:
            data = file.readlines()
        with open(XML_FILE, 'w') as file:
            for line in data:
                if "<meshFile>" in line and flag == 0:
                    line = line.replace(line.split('<meshFile>')[1].split('</meshFile>')[0], CGNS_FILE)
                    CGNS_FILE = CGNS_FILE.split("/")[-1]
                    flag = 1
                file.write(line)

    # 获取弹性变形后的网格名
    DEFORM_MESH = get_xml_value(XML_FILE, 'AeroStatic/MESH_DEFORM/DeformMeshFile')
    print("DEFORM_MESH is: {}".format(DEFORM_MESH))
    CASE_NAME = DEFORM_MESH[:-5]

    # 是否重新生成网格
    ReMesh_Flag = int(get_xml_value(XML_FILE, 'ReMesh_Flag'))
    print("ReMesh_Flag is: {}".format(ReMesh_Flag))

    # 定义气弹计算最大循环次数
    FSI_ITMAX = get_xml_value(XML_FILE, 'FSI_ITMAX')
    print("FSI_ITMAX is: {}".format(FSI_ITMAX))

    # 获取AeroStatic/FEM/MeshFile
    bdfFile = get_xml_value(XML_FILE, 'AeroStatic/FEM/MeshFile')
    print("bdfFile is: {}".format(bdfFile))
    bdf_prefix = bdfFile[:-4]

    preMeshpath = get_xml_value(XML_FILE, 'preprocess/outputPath')
    print("preMeshpath is: {}".format(preMeshpath))

    # 生成aflr3必要的参数
    if ReMesh_Flag == 1:
        if not os.path.isfile("{}.mapbc".format(CASE_NAME)):
            print('{}.mapbc file is not exist.'.format(CASE_NAME))
            sys.exit()
        else:
            with open("{}.mapbc".format(CASE_NAME), 'r') as file:
                nbc = int(file.readline().strip())  # Assuming the first line contains the number of boundary conditions
                list1 = []
                list2 = []
                for i in range(2, nbc + 2):
                    line = file.readline().strip()
                    bcname = line.split()[2]  # Assuming the third column contains the boundary name
                    if "far" in bcname.lower():
                        bc = 1
                    elif "sym" in bcname.lower():
                        bc = 2
                    else:
                        bc = -1
                    list1.append(i - 1)
                    list2.append(bc)

    #####################################################################################################################
    # 开始第一次前处理以及计算
    try:
        nbMesh = count_bMesh_files(preMeshpath)
        if nbMesh != int(NP_CORE) + 1:
            os.system(f"flowPreprocessor {XML_FILE}")
            print("flowPreprocessor done.")
        else:
            print("preMesh is already finished! skip")
    except:
        print("Error! Execute flowPreprocessor!")
        sys.exit()

    # 开始第一次静气动弹性计算
    try:
        os.remove(os.path.join(WORK_DIR, f"bdf/{bdf_prefix}.log"))
        print(f"nastran analysis log file delete")
    except FileNotFoundError:
        print("first time nastran analysis!")
    except PermissionError:
        print("delete nastran analysis log file failed. No permission!")
        
    if SYSTEM == "Linux":
        cmdlist = ["mpiexec -machinefile ", os.path.join(WORK_DIR, HOST_FILE), " -n ", NP_CORE, "mainAeroStatic", XML_FILE]
    elif SYSTEM == "Windows":
        cmdlist = ["mpiexec -localonly -n", NP_CORE, "mainAeroStatic", XML_FILE]
    mpi_run_mainAeroStatic = " ".join(cmdlist)
    print(mpi_run_mainAeroStatic)

    try:
        os.system(mpi_run_mainAeroStatic)
        print("mainAeroStatic done.")
    except:
        print("Error! Parallel run mainAeroStatic error! exit")
        sys.exit()

    # 气动力无量纲化
    cmdlist = ["flowForceNondimensionalize", XML_FILE]
    forcenondim_command = " ".join(cmdlist)
    try:
        os.system(forcenondim_command)
        print("first time flowForceNondimensionalize done.")
    except:
        print("Error! flowForceNondimensionalize run failed! exit")
        sys.exit()


    # 判断计算结果文件是否正常
    if not os.path.isfile(SOLVER_FILE):
        print("{} is not exist. exit.".format(SOLVER_FILE))
        sys.exit()

    # 读取maxDeform.dat文件
    with open(MAXDEFORM_FILE, 'r') as file:
        maxdeform_num1 = file.readlines()[1].strip()  # 读取第二行并去除空白
    #将这个值写入maxDeform_history.dat文件
    with open("maxDeform_history.dat", 'w') as file:
        file.write(maxdeform_num1 + '\n')


    def remesh_aflr3():
        # 由面网格生成体网格
        cmdlist = ["aflr3 -i", "{}.surf".format(CASE_NAME), "-blc", "-blds", "0.001", "-blrm", "1.2", "-BC_IDs", ",".join(map(str, list1)), "-Grid_BC_Flag", ",".join(map(str, list2))]
        aflr3_command = " ".join(cmdlist)
        try:
            os.system(aflr3_command)
        except:
            print("Error! run aflr3 failed! exit")
            sys.exit()

        if not os.path.isfile("{}.b8.ugrid".format(CASE_NAME)):
            print("aflr3 failed. {} is not generated. exit.".format("{}.b8.ugrid".format(CASE_NAME)))
            sys.exit()
        else:
            print("aflr3 done.")

        # 网格转换 ugrid 到 cgns
        cmdlist = ["aflr3_to_cgns", "-b8", "{}.b8.ugrid".format(CASE_NAME), "{}.cgns".format(CASE_NAME)]
        aflr3_to_cgns_command = " ".join(cmdlist)
        try:
            os.system(aflr3_to_cgns_command)
            print("aflr3_to_cgns finished!")
        except:
            print("Error! aflr3_to_cgns run failed! exit")
            sys.exit()
        if not os.path.isfile("{}.cgns".format(CASE_NAME)):
            print("aflr3_to_cgns failed. {} is not generated. exit.".format("{}.cgns".format(CASE_NAME)))
            sys.exit()
        else:
            print("aflr3_to_cgns done.")

    # 开始第一次气动弹性计算后，由面网格生成体网格
    if ReMesh_Flag == 1:
        remesh_aflr3()


    # 开始气弹循环计算
    # WORK_DIR为初始工作目录
    # PREV_DIR为上一轮计算目录
    # BASE_DIR为初始计算目录
    PREV_DIR = WORK_DIR
    BASE_DIR = WORK_DIR
    for i in range(1, int(FSI_ITMAX) + 1):
        folder_name = "{}_{}".format(BASE_FILE, i)
        if os.path.exists(os.path.join(WORK_DIR, folder_name)):
            delete_folder_recursive(os.path.join(WORK_DIR, folder_name))

        if not os.path.exists(os.path.join(WORK_DIR, folder_name)):
            os.makedirs(folder_name)
        
        # 将初始文件拷贝到新一轮计算目录
        shutil.copy(XML_FILE, os.path.join(folder_name, XML_FILE))
        shutil.copy(BOUND_FILE, os.path.join(folder_name, BOUND_FILE))
        shutil.copy(WALL_FILE, os.path.join(folder_name, WALL_FILE))
        # shutil.copytree(os.path.join(BASE_DIR, "bdf"), os.path.join(folder_name, "bdf"))
        print(os.path.join(BASE_DIR, "bdf/" + bdfFile))
        if not os.path.exists(folder_name + '/bdf'):
            os.makedirs(folder_name + "/bdf")
        os.link(os.path.join(BASE_DIR, "bdf/" + bdfFile), os.path.join(folder_name, "bdf/" + bdfFile))
        shutil.copy(os.path.join(BASE_DIR, "bdf/FOR.DAT"), os.path.join(folder_name, "bdf/FOR.DAT"))
        os.link(os.path.join(BASE_DIR, "bdf/xyzfp_id.plt"), os.path.join(folder_name, "bdf/xyzfp_id.plt"))

        os.chdir(folder_name)
        # 链接最原始的计算网格，这个网格仅仅用于提取原始的气动几何形状
        # 如果CGNS_FILE存在先删除，再链接，否则直接链接
        if os.path.exists(CGNS_FILE):
            os.remove(CGNS_FILE)
        os.link(os.path.join(BASE_DIR, CGNS_FILE), CGNS_FILE)

        if os.path.exists("WallTecplot_prev.plt"):  
            os.remove("WallTecplot_prev.plt")
        os.link("{}/WallTecplot.plt".format(PREV_DIR), "WallTecplot_prev.plt")

        # 获取弹性变形后的计算网格名
        new_deformmesh = "new_{}".format(DEFORM_MESH)

        # 将上一轮完成的变形网格链接，并重命名后作为输入计算网格
        if not os.path.isfile(os.path.join(PREV_DIR, DEFORM_MESH)):
            print("{} is not exist.".format(os.path.join(PREV_DIR, DEFORM_MESH)))
            sys.exit()
        if os.path.exists(new_deformmesh):
            os.remove(new_deformmesh)
        os.link(os.path.join(PREV_DIR, DEFORM_MESH), new_deformmesh)

        # 在xml文件中修改计算网格名
        # 如果ReMesh_Flag为0，上一步计算已经对网格进行了转换，则需要修改缩放比例为1 1 1、平移量为0.0 0.0 0.0和旋转量为0.0 0.0 0.0
        # 如果ReMesh_Flag为1，上一步计算输出了surf文件在原始坐标系和比例之下，则需要保持初始的缩放比例、平移量和旋转量
        with open(XML_FILE, 'r') as file:
            data = file.readlines()
        init_flag = -1
        with open(XML_FILE, 'w') as file:
            for line in data:
                if "<meshFile>" in line:
                    line = line.replace(line.split('<meshFile>')[1].split('</meshFile>')[0], new_deformmesh)
                if ReMesh_Flag == 0:
                    if "<scale>" in line:
                        line = line.replace(line.split('<scale>')[1].split('</scale>')[0], "1 1 1")
                    if "<transfer>" in line:
                        line = line.replace(line.split('<transfer>')[1].split('</transfer>')[0], "0.0 0.0 0.0")
                    if "<rotate>" in line:
                        line = line.replace(line.split('<rotate>')[1].split('</rotate>')[0], "0.0 0.0 0.0")
                if "<initialization>" in line:
                    init_flag = 1
                if init_flag == 1 and "<type>" in line:
                    line = line.replace(line.split('<type>')[1].split('</type>')[0], 'REFERENCE')
                file.write(line)

        # 流场前处理
        try:
            os.system(f"flowPreprocessor {XML_FILE}")
            print("flowPreprocessor done.")
        except:
            print("Error! Execute flowPreprocessor!")
            sys.exit()

        # 如果是重新生成网格，则需要拷贝surf等文件，这些文件不是用于计算的，静气动弹性计算读取他并修改surf文件
        if ReMesh_Flag == 1:
            shutil.copy(os.path.join(PREV_DIR, "{}.surf".format(CASE_NAME)), ".")
            shutil.copy(os.path.join(PREV_DIR, "{}.mapbc".format(CASE_NAME)), ".")

        # 静气动弹性计算
        print(os.getcwd())
        try:
            os.system(mpi_run_mainAeroStatic)
            print("mainAeroStatic done.")
        except:
            print("Error! Parallel run mainAeroStatic error! exit")
            sys.exit()

        # 检查maxDeform文件是否存在
        if not os.path.isfile(MAXDEFORM_FILE):
            print("maxDeform.dat is not exist.")
            sys.exit()

        # 气动力无量纲化
        cmdlist = ["flowForceNondimensionalize", XML_FILE]
        forcenondim_command = " ".join(cmdlist)
        try:
            os.system(forcenondim_command)
            print("first time flowForceNondimensionalize done.")
        except:
            print("Error! flowForceNondimensionalize run failed! exit")
            sys.exit()

        # 由面网格生成体网格，并转换为cgns格式
        if ReMesh_Flag == 1:
            remesh_aflr3()
                
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

        # 检查max_deform值，如果小于XX，则收敛并结束循环
        if not os.path.isfile(MAXDEFORM_FILE):
            print("maxDeform.dat is not exist.")
            sys.exit()
        else:
            # 读取上一次计算的max_deform值
            with open(os.path.join(PREV_DIR, MAXDEFORM_FILE), 'r') as file:
                max_deform_num1 = file.readlines()[1].strip()  # 读取第二行并去除空白
            # 读取当前计算的max_deform值
            with open(MAXDEFORM_FILE, 'r') as file:
                max_deform_num2 = file.readlines()[1].strip()  # 读取第二行并去除空白
                # 将当前计算的max_deform值写入maxDeform_history.dat文件
                with open(os.path.join(BASE_DIR,"maxDeform_history.dat"), 'a') as file:
                    file.write(max_deform_num2 + '\n')
            # 计算两次计算的max_deform值的差值
            delta_deform = abs(float(max_deform_num1) - float(max_deform_num2))
            # 如果差值小于收敛标准，则收敛并结束循环
            if delta_deform < float(Delta_Deform_threshold):
                print(f"Result Deformation converged. difference: {delta_deform}")
                sys.exit()

        # 将当前路径保存为上一轮计算路径
        PREV_DIR = os.getcwd()
        # 切换到初始工作路径，准备下一次循环
        os.chdir(BASE_DIR)
        
        print(f"\n{PREV_DIR} calculation is finished!\n\n\n")

    # 达到最大循环次数，则结束循环
    print(f"达到最大循环次数{FSI_ITMAX}，结束循环")

if __name__ == '__main__':
    # 判断输入参数
    print("使用本脚本注意事项：\n \
    \t1、确保使用python3.2以上版本\n \
    \t2、确保设置了mpiexec、mainAeroStatic、aflr3、nastran的环境变量\n \
    \t3、确保Linux设置了必要的动态链接库的环境变量LD_LIBRARY_PATH\n \
    \t\t动态链接库包括mkl、hdf5\n \
    \t4、可以使用shell脚本设置PATH和LD_LIBRARY_PATH然后再调用本脚本")

    if len(sys.argv) != 7:
        print("Usage: python3 FeiLian_jobrun.py \
    --xmlfile <case.xml> \
    --machinefile <nodefile> \
    --np <20>")
        print(" --xmlfile <case.xml> 参数文件")
        print(" --machinefile <nodefile> 节点信息")
        print(" --np <20> 并行核数")
        sys.exit()

    XML_FILE = sys.argv[2]
    HOST_FILE = sys.argv[4]
    NP_CORE = sys.argv[6]

    # XML_FILE = "case.xml"
    # HOST_FILE = "nodefile"
    # NP_CORE = "6"
    # SYSTEM = "Linux"

    run(XML_FILE, HOST_FILE, NP_CORE)