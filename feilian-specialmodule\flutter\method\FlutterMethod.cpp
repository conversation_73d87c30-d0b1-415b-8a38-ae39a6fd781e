#include "FlutterMethod.h"
#include <set>

FlutterMethod::FlutterMethod(const Package::FlowPackage *data)
    : data(data), mesh(data->GetMeshStruct().mesh)
{
    // 读取基本参数
    physicalTimeStep = data->GetFlowConfigure().GetControl().outerLoop.timeStep;

    // 读取模态数据和振型场
    ReadModalDataAndShapeFields();

    // 初始化结构动力学历史状态
    InitializeHistoryStates();

    // 初始化流固耦合界面信息
    InitializeFluidStructureInterface();

    // 初始化颤振分析
    Initialize();
}

// 初始化颤振分析
void FlutterMethod::Initialize()
{
    if (GetMPIRank() == 0) Print("初始化颤振分析系统...");

    // 初始化状态变量
    const int stateVectorSize = 2 * numberOfModes;
    currentModalStates.resize(stateVectorSize, 0.0);
    previousModalStates.resize(stateVectorSize, 0.0);
    currentGeneralizedForces.resize(stateVectorSize, 0.0);
    previousGeneralizedForces.resize(stateVectorSize, 0.0);
    correctedGeneralizedForces.resize(stateVectorSize, 0.0);

    // 初始化状态转移矩阵
    stateTransitionMatrix.resize(stateVectorSize, std::vector<Scalar>(stateVectorSize, 0.0));
    integratedStateMatrix.resize(stateVectorSize, std::vector<Scalar>(stateVectorSize, 0.0));
    generalizedMassMatrix.resize(stateVectorSize, std::vector<Scalar>(stateVectorSize, 0.0));

    // 设置初始模态状态
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        int displacementIndex = 2 * modeIndex;     // 位移索引
        int velocityIndex = 2 * modeIndex + 1;     // 速度索引

        // 初始化模态状态
        previousModalStates[displacementIndex] = 0.0;  // 初始位移
        previousModalStates[velocityIndex] = 0.0;      // 初始速度
        currentModalStates[displacementIndex] = 0.0;
        currentModalStates[velocityIndex] = 0.0;

        // 初始化广义力
        currentGeneralizedForces[displacementIndex] = 0.0;
        currentGeneralizedForces[velocityIndex] = 0.0;
        previousGeneralizedForces[displacementIndex] = 0.0;
        previousGeneralizedForces[velocityIndex] = 0.0;
        correctedGeneralizedForces[displacementIndex] = 0.0;
        correctedGeneralizedForces[velocityIndex] = 0.0;
    }

    // 初始化状态转移矩阵
    InitializeStateTransitionMatrices();

    if (GetMPIRank() == 0) Print("颤振分析系统初始化完成");
}

// 预测步：基于历史状态和上一时刻气动力预测当前时刻模态响应
void FlutterMethod::PredictModalResponse()
{
    if (GetMPIRank() == 0) Print("执行模态响应预测步...");

    // 更新历史状态（将当前状态移动到历史）
    UpdateHistoryStates();

    // 预测步使用外推的气动力（基于历史气动力）
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        // 线性外推气动力：F_pred = 2*F_n - F_{n-1}
        if (generalizedForces_n1.size() > modeIndex && generalizedForces_n2.size() > modeIndex)
        {
            generalizedForces_n[modeIndex] = 2.0 * generalizedForces_n1[modeIndex] - generalizedForces_n2[modeIndex];
        }
        else
        {
            // 如果历史数据不足，使用上一时刻的值
            generalizedForces_n[modeIndex] = generalizedForces_n1[modeIndex];
        }
    }

    // 使用预测的气动力进行结构动力学时间积分
    NewmarkBetaIntegration();

    // 同步到兼容性接口
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        currentModalStates[2 * modeIndex] = modalDisplacements_n[modeIndex];
        currentModalStates[2 * modeIndex + 1] = modalVelocities_n[modeIndex];
    }

    if (GetMPIRank() == 0)
    {
        Print("模态响应预测步完成");
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            Print("模态 " + ToString(modeIndex+1) + " 预测位移: " + ToString(modalDisplacements_n[modeIndex]) +
                  ", 预测速度: " + ToString(modalVelocities_n[modeIndex]) +
                  ", 预测气动力: " + ToString(generalizedForces_n[modeIndex]));
        }
    }
}

// 校正步：基于预测变形计算的新气动力校正模态响应
void FlutterMethod::CorrectModalResponse()
{
    if (GetMPIRank() == 0) Print("执行模态响应校正步...");

    // 基于预测的结构变形计算当前时刻的真实气动力
    ComputeGeneralizedAerodynamicForces();

    // 更新广义力为校正后的值
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        // 从校正后的广义力中提取（速度索引存储广义力）
        Scalar correctedForce = correctedGeneralizedForces[2 * modeIndex + 1];

        // 校正步使用平均气动力：F_corr = 0.5 * (F_pred + F_computed)
        generalizedForces_n[modeIndex] = 0.5 * (generalizedForces_n[modeIndex] + correctedForce);
    }

    // 使用校正后的气动力重新进行结构动力学时间积分
    NewmarkBetaIntegration();

    // 同步到兼容性接口
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        currentModalStates[2 * modeIndex] = modalDisplacements_n[modeIndex];
        currentModalStates[2 * modeIndex + 1] = modalVelocities_n[modeIndex];
    }

    // 更新时间历史数据（兼容性）
    const int stateVectorSize = 2 * numberOfModes;
    for (int stateIndex = 0; stateIndex < stateVectorSize; stateIndex++)
    {
        previousGeneralizedForces[stateIndex] = currentGeneralizedForces[stateIndex];
        currentGeneralizedForces[stateIndex] = correctedGeneralizedForces[stateIndex];
        previousModalStates[stateIndex] = currentModalStates[stateIndex];
    }

    if (GetMPIRank() == 0)
    {
        Print("模态响应校正步完成");
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            Print("模态 " + ToString(modeIndex+1) +
                  " 校正位移: " + ToString(modalDisplacements_n[modeIndex]) +
                  ", 校正速度: " + ToString(modalVelocities_n[modeIndex]) +
                  ", 校正加速度: " + ToString(modalAccelerations_n[modeIndex]) +
                  ", 广义力: " + ToString(generalizedForces_n[modeIndex]));
        }
    }
}

// 初始化状态转移矩阵
void FlutterMethod::InitializeStateTransitionMatrices()
{
    if (GetMPIRank() == 0) Print("计算状态转移矩阵...");

    // 设置广义质量矩阵（对角矩阵，直接给定）
    SetupGeneralizedMassMatrix();

    // 计算状态转移矩阵
    ComputeStateTransitionMatrix();

    // 计算积分状态转移矩阵
    ComputeIntegratedStateMatrix();

    // 将积分状态矩阵与广义质量矩阵相乘：integratedStateMatrix = integratedStateMatrix * generalizedMassMatrix
    const int stateVectorSize = 2 * numberOfModes;
    std::vector<std::vector<Scalar>> tempMatrix(stateVectorSize, std::vector<Scalar>(stateVectorSize, 0.0));

    for (int rowIndex = 0; rowIndex < stateVectorSize; rowIndex++)
    {
        for (int colIndex = 0; colIndex < stateVectorSize; colIndex++)
        {
            tempMatrix[rowIndex][colIndex] = 0.0;
            for (int sumIndex = 0; sumIndex < stateVectorSize; sumIndex++)
            {
                tempMatrix[rowIndex][colIndex] +=
                    integratedStateMatrix[rowIndex][sumIndex] * generalizedMassMatrix[sumIndex][colIndex];
            }
        }
    }

    // 将结果复制回积分状态矩阵
    for (int rowIndex = 0; rowIndex < stateVectorSize; rowIndex++)
    {
        for (int colIndex = 0; colIndex < stateVectorSize; colIndex++)
        {
            integratedStateMatrix[rowIndex][colIndex] = tempMatrix[rowIndex][colIndex];
        }
    }

    if (GetMPIRank() == 0) Print("状态转移矩阵计算完成");
}

// 计算状态转移矩阵
void FlutterMethod::ComputeStateTransitionMatrix()
{
    const Scalar machNumber = 0.8; // 马赫数，应该从配置中读取
    soundSpeed = freeStreamVelocity / machNumber;

    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        const int displacementIndex = 2 * modeIndex;     // 位移索引
        const int velocityIndex = 2 * modeIndex + 1;     // 速度索引

        const Scalar frequency = modalFrequencies[modeIndex];
        const Scalar dampingRatio = modalDampingRatios[modeIndex];

        // 检查阻尼比是否合理
        if (dampingRatio > 1.0)
        {
            FatalError("模态 " + ToString(modeIndex+1) + " 阻尼比 " + ToString(dampingRatio) + " > 1.0");
        }

        const Scalar dampingCoeff = -frequency * dampingRatio;
        const Scalar dampedFrequency = frequency * sqrt(1.0 - dampingRatio * dampingRatio);
        const Scalar scaledTimeStep = physicalTimeStep * referenceLength / soundSpeed;
        const Scalar exponentialCos = exp(dampingCoeff * scaledTimeStep) * cos(dampedFrequency * scaledTimeStep);
        const Scalar exponentialSin = exp(dampingCoeff * scaledTimeStep) * sin(dampedFrequency * scaledTimeStep);

        // 状态转移矩阵元素
        stateTransitionMatrix[displacementIndex][displacementIndex] =
            exponentialCos - dampingCoeff * exponentialSin / dampedFrequency;
        stateTransitionMatrix[displacementIndex][velocityIndex] =
            exponentialSin / dampedFrequency;
        stateTransitionMatrix[velocityIndex][displacementIndex] =
            -(dampingCoeff * dampingCoeff + dampedFrequency * dampedFrequency) * exponentialSin / dampedFrequency;
        stateTransitionMatrix[velocityIndex][velocityIndex] =
            exponentialCos + dampingCoeff * exponentialSin / dampedFrequency;
    }
}

// 计算积分状态转移矩阵
void FlutterMethod::ComputeIntegratedStateMatrix()
{
    const Scalar machNumber = 0.8; // 马赫数，应该从配置中读取
    soundSpeed = freeStreamVelocity / machNumber;

    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        const int displacementIndex = 2 * modeIndex;     // 位移索引
        const int velocityIndex = 2 * modeIndex + 1;     // 速度索引

        const Scalar frequency = modalFrequencies[modeIndex];
        const Scalar dampingRatio = modalDampingRatios[modeIndex];
        const Scalar dampingCoeff = -frequency * dampingRatio;
        const Scalar dampedFrequency = frequency * sqrt(1.0 - dampingRatio * dampingRatio);
        const Scalar scaledTimeStep = physicalTimeStep * referenceLength / soundSpeed;
        const Scalar exponentialCos = exp(dampingCoeff * scaledTimeStep) * cos(dampedFrequency * scaledTimeStep);
        const Scalar exponentialSin = exp(dampingCoeff * scaledTimeStep) * sin(dampedFrequency * scaledTimeStep);

        const Scalar frequencySquaredSum = dampingCoeff * dampingCoeff + dampedFrequency * dampedFrequency;

        // 积分状态转移矩阵元素
        integratedStateMatrix[displacementIndex][displacementIndex] =
            (2.0 * dampingCoeff * (exponentialCos - 1.0) +
             (dampedFrequency - dampingCoeff * dampingCoeff / dampedFrequency) * exponentialSin) / frequencySquaredSum;
        integratedStateMatrix[displacementIndex][velocityIndex] =
            (dampingCoeff * exponentialSin / dampedFrequency - exponentialCos + 1.0) / frequencySquaredSum;
        integratedStateMatrix[velocityIndex][displacementIndex] =
            -dampingCoeff * exponentialSin / dampedFrequency + exponentialCos - 1.0;
        integratedStateMatrix[velocityIndex][velocityIndex] =
            exponentialSin / dampedFrequency;
    }
}

// 设置广义质量矩阵（对角矩阵，直接给定）
void FlutterMethod::SetupGeneralizedMassMatrix()
{
    // 广义质量矩阵是对角矩阵，对角元素为各模态的广义质量的倒数
    // 在状态空间表示中，只有速度对应的索引需要设置质量矩阵元素
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        const int velocityIndex = 2 * modeIndex + 1; // 速度索引
        generalizedMassMatrix[velocityIndex][velocityIndex] = 1.0 / modalMasses[modeIndex];
    }

    if (GetMPIRank() == 0)
    {
        Print("广义质量矩阵设置完成");
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            const int velocityIndex = 2 * modeIndex + 1;
            Print("模态 " + ToString(modeIndex+1) + " 广义质量倒数: " +
                  ToString(generalizedMassMatrix[velocityIndex][velocityIndex]));
        }
    }
}

// 计算广义气动力（基于摄动场方法）
void FlutterMethod::ComputeGeneralizedAerodynamicForces()
{
    if (GetMPIRank() == 0) Print("基于摄动场计算广义气动力...");

    // 清零校正后的广义力
    const int stateVectorSize = 2 * numberOfModes;
    for (int stateIndex = 0; stateIndex < stateVectorSize; stateIndex++)
    {
        correctedGeneralizedForces[stateIndex] = 0.0;
    }

    // 获取压力场
    const ElementField<Scalar> &pressure = *data->GetField().pressure;

    // 遍历所有流固耦合界面的面
    for (int faceIndex = 0; faceIndex < fluidStructureInterfaceFaceIDs.size(); faceIndex++)
    {
        const int &faceID = fluidStructureInterfaceFaceIDs[faceIndex];
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();

        // 获取面上的压力（取相邻体心值）
        const Scalar facePressure = pressure.GetValue(ownerID);

        // 获取面几何信息
        const Vector &faceNormal = face.GetNormal();
        const Scalar &faceArea = face.GetArea();

        // 对每个模态计算广义力贡献
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            // 直接从摄动场获取该面对应的模态振型位移
            Vector modalShapeDisplacement = GetFaceModalShapeDisplacement(faceID, modeIndex);

            // 计算广义力: Qᵢ = ∫ p * (φᵢ · n) dS
            // 直接使用压力，摄动场已经包含了正确的量纲
            Scalar forceContribution = facePressure * Dot(modalShapeDisplacement, faceNormal) * faceArea;

            // 广义力存储在速度对应的索引中（遵循Fortran代码的约定）
            int velocityIndex = 2 * modeIndex + 1;
            correctedGeneralizedForces[velocityIndex] += forceContribution;
        }
    }

    if (GetMPIRank() == 0)
    {
        Print("广义气动力计算完成，处理面数: " + ToString(fluidStructureInterfaceFaceIDs.size()));
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            int velocityIndex = 2 * modeIndex + 1;
            Print("模态 " + ToString(modeIndex+1) + " 广义气动力: " +
                  ToString(correctedGeneralizedForces[velocityIndex]));
        }
    }
}

// 更新整个气动网格坐标
void FlutterMethod::UpdateAerodynamicMeshCoordinates()
{
    if (GetMPIRank() == 0) Print("更新气动网格坐标...");

    // 保存上一时刻位移
    previousNodalDisplacements = currentNodalDisplacements;

    // 基于模态振型场计算所有节点的位移
    ComputeNodalDisplacementsFromModalShapes();

    // 更新所有节点坐标
    const int nodeNumber = mesh->GetNodeNumber();
    int updatedNodeCount = 0;

    // 注意：这里需要实际的网格变形实现
    // 当前版本仅作为占位符
    if (GetMPIRank() == 0) Print("警告：网格坐标更新功能需要完整实现");

    updatedNodeCount = nodeNumber;

    if (GetMPIRank() == 0)
    {
        Print("气动网格坐标更新完成，更新节点数: " + ToString(updatedNodeCount));

        // 输出统计信息
        Scalar maxDisplacement = 0.0;
        if (currentNodalDisplacements.size() > 0) {
            for (int nodeID = 0; nodeID < (int)currentNodalDisplacements.size(); nodeID++)
            {
                Scalar displacement = currentNodalDisplacements[nodeID].Mag();
                maxDisplacement = Max(maxDisplacement, displacement);
            }
        }
        Print("最大节点位移: " + ToString(maxDisplacement));
    }
}

// 基于模态振型场计算所有节点的位移
void FlutterMethod::ComputeNodalDisplacementsFromModalShapes()
{
    const int nodeNumber = mesh->GetNodeNumber();

    // 初始化位移场
    currentNodalDisplacements.resize(nodeNumber, Vector0);

    // 对每个节点进行模态叠加
    for (int nodeID = 0; nodeID < nodeNumber; nodeID++)
    {
        currentNodalDisplacements[nodeID] = Vector0;

        // 模态叠加: u = Σ φᵢ * qᵢ
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            Scalar modalAmplitude = currentModalStates[2 * modeIndex]; // 位移分量（偶数索引）

            // 检查模态振型场是否存在该节点的数据
            if (nodeID < modalShapeFields[modeIndex].size())
            {
                currentNodalDisplacements[nodeID] += modalShapeFields[modeIndex][nodeID] * modalAmplitude;
            }
        }
    }

    if (GetMPIRank() == 0)
    {
        // 输出模态响应信息
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            Print("模态 " + ToString(modeIndex+1) + " 响应: " + ToString(currentModalStates[2*modeIndex]) +
                  " (位移), " + ToString(currentModalStates[2*modeIndex+1]) + " (速度)");
        }
    }
}

// 析构函数
FlutterMethod::~FlutterMethod()
{
    // 清理动态分配的内存（如果有的话）
}

// 读取模态数据和振型场
void FlutterMethod::ReadModalDataAndShapeFields()
{
    if (GetMPIRank() == 0) Print("读取模态数据和振型场...");

    // 从配置文件读取模态数量
    numberOfModes = data->GetFlowConfigure().GetFlutter().numModes;

    // 读取基本参数
    referenceLength = 1.0; // 参考长度，应该从配置中读取
    freeStreamVelocity = 100.0; // 来流速度，应该从配置中读取
    dynamicPressure = 0.5 * 1.225 * freeStreamVelocity * freeStreamVelocity; // 动压，应该从配置中读取

    // 初始化模态参数数组
    modalMasses.resize(numberOfModes);
    modalFrequencies.resize(numberOfModes);
    modalDampingRatios.resize(numberOfModes);

    // 读取模态参数（从文件或配置中读取）
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        modalMasses[modeIndex] = data->GetFlowConfigure().GetFlutter().modalMasses[modeIndex];
        modalFrequencies[modeIndex] = data->GetFlowConfigure().GetFlutter().modalFrequencies[modeIndex];
        modalDampingRatios[modeIndex] = data->GetFlowConfigure().GetFlutter().modalDamping[modeIndex];
    }

    // 从网格分区文件中读取模态振型场数据（已在前处理中写入）
    const int nodeNumber = mesh->GetNodeNumber();
    modalShapeFields.resize(numberOfModes);

    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        modalShapeFields[modeIndex].resize(nodeNumber, Vector0);

        // 从网格对象中获取模态振型场数据（已在网格读取时加载）
        // 注意：这里需要实际的模态振型场数据读取实现
        if (GetMPIRank() == 0) Print("警告：模态振型场数据读取功能需要完整实现");

        // 占位符：初始化为零向量
        for (int nodeID = 0; nodeID < nodeNumber; nodeID++)
        {
            modalShapeFields[modeIndex][nodeID] = Vector0;
        }
    }

    if (GetMPIRank() == 0)
    {
        Print("模态数据和振型场读取完成");
        Print("模态数量: " + ToString(numberOfModes));
        Print("参考长度: " + ToString(referenceLength));
        Print("来流速度: " + ToString(freeStreamVelocity));
        Print("动压: " + ToString(dynamicPressure));
        for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
        {
            Print("模态 " + ToString(modeIndex+1) + " - 质量: " + ToString(modalMasses[modeIndex]) +
                  ", 频率: " + ToString(modalFrequencies[modeIndex]) +
                  ", 阻尼比: " + ToString(modalDampingRatios[modeIndex]));
        }
    }
}

// 初始化流固耦合界面信息
void FlutterMethod::InitializeFluidStructureInterface()
{
    if (GetMPIRank() == 0) Print("初始化流固耦合界面信息...");

    // 从配置中获取流固耦合界面边界条件编号
    const auto &couplingBoundaryIDs = data->GetFlowConfigure().GetFlutter().couplingBoundaryIDs;

    // 收集所有流固耦合界面的面
    for (int boundaryID : couplingBoundaryIDs)
    {
        // 获取指定边界的所有面
        const int boundaryFaceSize = mesh->GetBoundaryFaceSize(boundaryID);

        for (int index = 0; index < boundaryFaceSize; index++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(boundaryID, index);
            fluidStructureInterfaceFaceIDs.push_back(faceID);

            const Face &face = mesh->GetFace(faceID);
            interfaceFaceNormals.push_back(face.GetNormal());
            interfaceFaceAreas.push_back(face.GetArea());
        }
    }

    if (GetMPIRank() == 0)
    {
        Print("流固耦合界面面数量: " + ToString(fluidStructureInterfaceFaceIDs.size()));
        Print("流固耦合界面信息初始化完成");
    }
}

// 获取面的模态振型位移
Vector FlutterMethod::GetFaceModalShapeDisplacement(int faceID, int modeIndex)
{
    const Face &face = mesh->GetFace(faceID);
    const int nodeSize = face.GetNodeSize();

    // 计算面心模态振型位移（节点模态振型位移的平均）
    Vector faceModalDisplacement = Vector0;
    for (int i = 0; i < nodeSize; i++)
    {
        const int &nodeID = face.GetNodeID(i);
        // 检查节点是否在模态振型场范围内
        if (nodeID < modalShapeFields[modeIndex].size())
        {
            faceModalDisplacement += modalShapeFields[modeIndex][nodeID];
        }
    }
    if (nodeSize > 0) faceModalDisplacement /= nodeSize;

    return faceModalDisplacement;
}

// 更新网格几何属性（体积、面积、法矢等）
void FlutterMethod::UpdateMeshGeometricProperties()
{
    if (GetMPIRank() == 0) Print("更新网格几何属性...");

    // 保存旧的体积用于几何守恒律检查
    std::vector<Scalar> oldVolumes;
    const int elementNumber = mesh->GetElementNumberReal();
    oldVolumes.reserve(elementNumber);

    for (int elemID = 0; elemID < elementNumber; elemID++)
    {
        oldVolumes.push_back(mesh->GetElement(elemID).GetVolume());
    }

    // 重新计算所有面的中心和面积，以及所有单元的中心和体积
    // 由于网格坐标已经更新，需要重新计算所有几何属性
    mesh->CalculateCenterAndVolume(false); // 计算所有面和单元

    // 计算网格速度（几何守恒律需要）
    ComputeMeshVelocities();

    // 几何守恒律检查和修正
    if (GetMPIRank() == 0) Print("检查几何守恒律...");

    Scalar maxVolumeChange = 0.0;
    Scalar totalVolumeChange = 0.0;

    for (int elemID = 0; elemID < elementNumber; elemID++)
    {
        const Scalar newVolume = mesh->GetElement(elemID).GetVolume();
        const Scalar volumeChange = fabs(newVolume - oldVolumes[elemID]);
        const Scalar relativeChange = volumeChange / (oldVolumes[elemID] + SMALL);

        maxVolumeChange = Max(maxVolumeChange, relativeChange);
        totalVolumeChange += volumeChange;
    }

    if (GetMPIRank() == 0)
    {
        Print("网格几何属性更新完成");
        Print("最大相对体积变化: " + ToString(maxVolumeChange));
        Print("总体积变化: " + ToString(totalVolumeChange));

        // 几何守恒律警告
        if (maxVolumeChange > 0.01) // 1%阈值
        {
            Print("警告：检测到较大的体积变化，可能违反几何守恒律");
            Print("建议检查网格变形算法或使用更精确的几何更新方法");
        }
    }
}

// 计算网格速度（用于几何守恒律）
void FlutterMethod::ComputeMeshVelocities()
{
    if (GetMPIRank() == 0) Print("计算网格速度...");

    const int nodeNumber = mesh->GetNodeNumber();
    const Scalar timeStep = data->GetFlowConfigure().GetControl().outerLoop.timeStep;

    // 计算节点速度
    std::vector<Vector> nodeVelocities(nodeNumber, Vector0);

    if (timeStep > SMALL && previousNodalDisplacements.size() == currentNodalDisplacements.size())
    {
        for (int nodeID = 0; nodeID < nodeNumber; nodeID++)
        {
            if (nodeID < (int)currentNodalDisplacements.size() &&
                nodeID < (int)previousNodalDisplacements.size())
            {
                // 网格速度 = (当前位移 - 上一时刻位移) / 时间步长
                Vector displacementChange = currentNodalDisplacements[nodeID] - previousNodalDisplacements[nodeID];
                nodeVelocities[nodeID] = displacementChange / timeStep;
            }
        }
    }

    // 计算面中心的网格速度（用于通量计算中的GCL项）
    const int faceNumber = mesh->GetFaceNumber();
    for (int faceID = 0; faceID < faceNumber; faceID++)
    {
        const Face &face = mesh->GetFace(faceID);
        const int nodeSize = face.GetNodeSize();

        Vector faceVelocity = Vector0;
        for (int i = 0; i < nodeSize; i++)
        {
            const int &nodeID = face.GetNodeID(i);
            if (nodeID < nodeVelocities.size())
            {
                faceVelocity += nodeVelocities[nodeID];
            }
        }

        if (nodeSize > 0)
        {
            faceVelocity /= nodeSize;
            // 这里应该将面速度存储到适当的数据结构中
            // 在实际的CFD求解器中，这些速度会用于计算几何守恒律项
        }
    }

    if (GetMPIRank() == 0) Print("网格速度计算完成");
}

// 初始化历史状态
void FlutterMethod::InitializeHistoryStates()
{
    if (GetMPIRank() == 0) Print("初始化结构动力学历史状态...");

    // 初始化模态坐标历史
    modalDisplacements_n.resize(numberOfModes, 0.0);
    modalDisplacements_n1.resize(numberOfModes, 0.0);
    modalDisplacements_n2.resize(numberOfModes, 0.0);

    modalVelocities_n.resize(numberOfModes, 0.0);
    modalVelocities_n1.resize(numberOfModes, 0.0);
    modalVelocities_n2.resize(numberOfModes, 0.0);

    modalAccelerations_n.resize(numberOfModes, 0.0);
    modalAccelerations_n1.resize(numberOfModes, 0.0);

    // 初始化广义力历史
    generalizedForces_n.resize(numberOfModes, 0.0);
    generalizedForces_n1.resize(numberOfModes, 0.0);
    generalizedForces_n2.resize(numberOfModes, 0.0);

    // 设置初始条件（可以从配置文件读取）
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        // 初始位移（通常为0）
        modalDisplacements_n[modeIndex] = 0.0;
        modalDisplacements_n1[modeIndex] = 0.0;

        // 初始速度（通常为0）
        modalVelocities_n[modeIndex] = 0.0;
        modalVelocities_n1[modeIndex] = 0.0;

        // 初始加速度（通常为0）
        modalAccelerations_n[modeIndex] = 0.0;
        modalAccelerations_n1[modeIndex] = 0.0;
    }

    if (GetMPIRank() == 0) Print("结构动力学历史状态初始化完成");
}

// 更新历史状态
void FlutterMethod::UpdateHistoryStates()
{
    // 更新模态坐标历史：n-2 <- n-1 <- n <- new
    modalDisplacements_n2 = modalDisplacements_n1;
    modalDisplacements_n1 = modalDisplacements_n;

    modalVelocities_n2 = modalVelocities_n1;
    modalVelocities_n1 = modalVelocities_n;

    modalAccelerations_n1 = modalAccelerations_n;

    // 更新广义力历史
    generalizedForces_n2 = generalizedForces_n1;
    generalizedForces_n1 = generalizedForces_n;

    // 同步到兼容性接口
    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        currentModalStates[2 * modeIndex] = modalDisplacements_n[modeIndex];
        currentModalStates[2 * modeIndex + 1] = modalVelocities_n[modeIndex];

        previousModalStates[2 * modeIndex] = modalDisplacements_n1[modeIndex];
        previousModalStates[2 * modeIndex + 1] = modalVelocities_n1[modeIndex];
    }
}

// Newmark-β时间积分方法
void FlutterMethod::NewmarkBetaIntegration()
{
    if (GetMPIRank() == 0) Print("使用Newmark-β方法进行结构动力学时间积分...");

    const Scalar dt = data->GetFlowConfigure().GetControl().outerLoop.timeStep;

    // Newmark-β参数（平均加速度方法）
    const Scalar beta = 0.25;   // 无条件稳定
    const Scalar gamma = 0.5;   // 无数值阻尼

    // Newmark-β系数
    const Scalar a0 = 1.0 / (beta * dt * dt);
    const Scalar a1 = gamma / (beta * dt);
    const Scalar a2 = 1.0 / (beta * dt);
    const Scalar a3 = 1.0 / (2.0 * beta) - 1.0;
    const Scalar a4 = gamma / beta - 1.0;
    const Scalar a5 = dt * (gamma / (2.0 * beta) - 1.0);
    const Scalar a6 = dt * (1.0 - gamma);
    const Scalar a7 = gamma * dt;

    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        // 模态参数
        const Scalar omega = modalFrequencies[modeIndex];
        const Scalar zeta = modalDampingRatios[modeIndex];
        const Scalar mass = modalMasses[modeIndex];

        // 模态刚度和阻尼
        const Scalar stiffness = mass * omega * omega;
        const Scalar damping = 2.0 * zeta * omega * mass;

        // 当前时刻的广义力
        const Scalar force_n1 = generalizedForces_n[modeIndex]; // 新时刻的力

        // 有效刚度矩阵
        const Scalar K_eff = stiffness + a0 * mass + a1 * damping;

        // 有效载荷
        const Scalar F_eff = force_n1 + mass * (
            a0 * modalDisplacements_n[modeIndex] +
            a2 * modalVelocities_n[modeIndex] +
            a3 * modalAccelerations_n[modeIndex]
        ) + damping * (
            a1 * modalDisplacements_n[modeIndex] +
            a4 * modalVelocities_n[modeIndex] +
            a5 * modalAccelerations_n[modeIndex]
        );

        // 求解新时刻的位移
        const Scalar q_n1 = F_eff / K_eff;

        // 更新速度和加速度
        const Scalar q_dot_n1 = a1 * (q_n1 - modalDisplacements_n[modeIndex]) -
                                a4 * modalVelocities_n[modeIndex] -
                                a5 * modalAccelerations_n[modeIndex];

        const Scalar q_ddot_n1 = a0 * (q_n1 - modalDisplacements_n[modeIndex]) -
                                 a2 * modalVelocities_n[modeIndex] -
                                 a3 * modalAccelerations_n[modeIndex];

        // 存储新时刻的状态
        modalDisplacements_n[modeIndex] = q_n1;
        modalVelocities_n[modeIndex] = q_dot_n1;
        modalAccelerations_n[modeIndex] = q_ddot_n1;
    }

    if (GetMPIRank() == 0) Print("Newmark-β时间积分完成");
}

// 中心差分时间积分方法
void FlutterMethod::CentralDifferenceIntegration()
{
    if (GetMPIRank() == 0) Print("使用中心差分方法进行结构动力学时间积分...");

    const Scalar dt = data->GetFlowConfigure().GetControl().outerLoop.timeStep;
    const Scalar dt2 = dt * dt;

    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        // 模态参数
        const Scalar omega = modalFrequencies[modeIndex];
        const Scalar zeta = modalDampingRatios[modeIndex];
        const Scalar mass = modalMasses[modeIndex];

        // 模态刚度和阻尼
        const Scalar stiffness = mass * omega * omega;
        const Scalar damping = 2.0 * zeta * omega * mass;

        // 当前时刻的广义力
        const Scalar force_n = generalizedForces_n[modeIndex];

        // 有效质量（包含阻尼项）
        const Scalar M_eff = mass + 0.5 * dt * damping;

        // 有效载荷
        const Scalar F_eff = force_n - stiffness * modalDisplacements_n[modeIndex] -
                            damping * (modalDisplacements_n[modeIndex] - modalDisplacements_n1[modeIndex]) / dt +
                            mass * (2.0 * modalDisplacements_n[modeIndex] - modalDisplacements_n1[modeIndex]) / dt2;

        // 求解新时刻的位移
        const Scalar q_n1 = F_eff * dt2 / M_eff;

        // 更新速度（中心差分）
        const Scalar q_dot_n = (q_n1 - modalDisplacements_n1[modeIndex]) / (2.0 * dt);

        // 更新加速度
        const Scalar q_ddot_n = (q_n1 - 2.0 * modalDisplacements_n[modeIndex] + modalDisplacements_n1[modeIndex]) / dt2;

        // 存储新时刻的状态
        modalDisplacements_n[modeIndex] = q_n1;
        modalVelocities_n[modeIndex] = q_dot_n;
        modalAccelerations_n[modeIndex] = q_ddot_n;
    }

    if (GetMPIRank() == 0) Print("中心差分时间积分完成");
}

// Runge-Kutta时间积分方法（4阶）
void FlutterMethod::RungeKuttaIntegration()
{
    if (GetMPIRank() == 0) Print("使用4阶Runge-Kutta方法进行结构动力学时间积分...");

    const Scalar dt = data->GetFlowConfigure().GetControl().outerLoop.timeStep;

    for (int modeIndex = 0; modeIndex < numberOfModes; modeIndex++)
    {
        // 模态参数
        const Scalar omega = modalFrequencies[modeIndex];
        const Scalar zeta = modalDampingRatios[modeIndex];
        const Scalar mass = modalMasses[modeIndex];

        // 当前状态
        const Scalar q_n = modalDisplacements_n[modeIndex];
        const Scalar q_dot_n = modalVelocities_n[modeIndex];
        const Scalar force_n = generalizedForces_n[modeIndex];

        // RK4系数计算
        auto computeAcceleration = [&](Scalar q, Scalar q_dot, Scalar force) -> Scalar {
            const Scalar stiffness_force = mass * omega * omega * q;
            const Scalar damping_force = 2.0 * zeta * omega * mass * q_dot;
            return (force - stiffness_force - damping_force) / mass;
        };

        // k1
        const Scalar k1_q = q_dot_n;
        const Scalar k1_q_dot = computeAcceleration(q_n, q_dot_n, force_n);

        // k2
        const Scalar k2_q = q_dot_n + 0.5 * dt * k1_q_dot;
        const Scalar k2_q_dot = computeAcceleration(q_n + 0.5 * dt * k1_q, k2_q, force_n);

        // k3
        const Scalar k3_q = q_dot_n + 0.5 * dt * k2_q_dot;
        const Scalar k3_q_dot = computeAcceleration(q_n + 0.5 * dt * k2_q, k3_q, force_n);

        // k4
        const Scalar k4_q = q_dot_n + dt * k3_q_dot;
        const Scalar k4_q_dot = computeAcceleration(q_n + dt * k3_q, k4_q, force_n);

        // 更新状态
        const Scalar q_n1 = q_n + dt * (k1_q + 2.0 * k2_q + 2.0 * k3_q + k4_q) / 6.0;
        const Scalar q_dot_n1 = q_dot_n + dt * (k1_q_dot + 2.0 * k2_q_dot + 2.0 * k3_q_dot + k4_q_dot) / 6.0;
        const Scalar q_ddot_n1 = computeAcceleration(q_n1, q_dot_n1, force_n);

        // 存储新时刻的状态
        modalDisplacements_n[modeIndex] = q_n1;
        modalVelocities_n[modeIndex] = q_dot_n1;
        modalAccelerations_n[modeIndex] = q_ddot_n1;
    }

    if (GetMPIRank() == 0) Print("4阶Runge-Kutta时间积分完成");
}