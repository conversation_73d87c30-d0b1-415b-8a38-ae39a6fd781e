 #pragma once
 #if defined EXTERN
 #undef EXTERN
 #endif
 #if defined ___857
 #define EXTERN
 #else
 #define EXTERN extern
 #endif
#include "ThirdPartyHeadersBegin.h"
#   include <vector>
#include "ThirdPartyHeadersEnd.h"
EXTERN ___372 ___2876(struct ___1405 **___1401, char          *___1439, ___1398   ___3686, int32_t       *___2104); EXTERN ___372 ___3279(___1405    *___1401, short            ___2104, ___372        ___3568, ___1172      *___2847, ___1172      *NumVars, int32_t     *___2792, char           **___903, ___4118         **___340, ___1632         **___336, ___3839  **___791, ___3839   *___4285, ___264      *___886, ___90       ___263, ___3501         **___2075, ___372       *___1821, ___372       *___1820, ___134    *___4682, ___3839   *___4366, ___134    *___4338, ___3501          *___2053, ___2227      **___1441, DataFileType_e  *___1408);
