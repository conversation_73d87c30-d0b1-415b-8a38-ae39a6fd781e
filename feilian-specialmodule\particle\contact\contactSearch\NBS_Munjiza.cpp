﻿#include "feilian-specialmodule/particle/contact/contactSearch/NBS_Munjiza.h"

namespace Particle
{
namespace Contact
{

NBS_Munjiza::NBS_Munjiza(const Vector &minDomain, const Vector &maxDomain,
                         const <PERSON>alar &minDiameter, const Scalar &maxDiameter, 
						 const int &numParticlesMax, const int &numParticles,
		        		 std::vector<Particle *> &particles_,
						 ElementField<Scalar> *volumeFraction_, const Scalar &ratio)
						 : SearchCell(minDomain, maxDomain, minDiameter, maxDiameter, numParticlesMax, numParticles, particles_, volumeFraction_, true, ratio)
{
	indexPrtcl.reserve(numParticlesMax);
}

void NBS_Munjiza::Initialize()
{
	this->SearchCell::Initialize();

	const IndexVector &numCell = this->GetCellNumber();

	this->sortIDY.resize(numCell.y);

	this->sortIDX_Y0.resize(numCell.x);
	this->sortIDX_Y1.resize(numCell.x);

	this->sortIDZ_Y0_X.resize(3);
	this->sortIDZ_Y0_X[0].resize(numCell.z);
	this->sortIDZ_Y0_X[1].resize(numCell.z);
	this->sortIDZ_Y0_X[2].resize(numCell.z);

	this->sortIDZ_Y1_X.resize(2);
	this->sortIDZ_Y1_X[0].resize(numCell.z);
	this->sortIDZ_Y1_X[1].resize(numCell.z);
}

void NBS_Munjiza::SearchParticleParticle()
{
	this->SetContactNumber(0);

	this->BoxIndex();

	this->BroadSearch();
}

void NBS_Munjiza::BroadSearch()
{
	// 计算域无颗粒，跳过搜索
	if (this->GetParticleNumber() == 0) return;

	if (!this->IsContactSearch()) return;

	const IndexVector &numCell = this->GetCellNumber();

	// 将所有颗粒按照y坐标进行分类，形成y坐标切片
	this->sortIDY.clear();
	this->YList();

	// y坐标切片内，相同x的所有搜索单元容器
	this->sortIDX_Y0.clear();

	// 遍历所有y坐标切片
	for (int iy = 0; iy < numCell.y; ++iy)
	{
		// y坐标切片内存在颗粒
		if (this->sortIDY[iy].empty())
		{
			this->sortIDX_Y0.clear();
		}
		else
		{
			// 遍历y坐标切片内的所有颗粒，按照颗粒位置x坐标相应的整数坐标分类，形成xy坐标切片
			this->sortIDX_Y1.clear();
			this->XList(iy);

			this->sortIDZ_Y1_X[0].clear();
			this->sortIDZ_Y0_X[0].clear();
			
			// sortIDZ_Y0_X[1]更新
			this->ZList0(0, 1);
			
			for (int ix = 0; ix < numCell.x; ++ix)
			{
				this->sortIDZ_Y1_X[1].clear();
				this->sortIDZ_Y0_X[2].clear();
				
				if (!this->sortIDX_Y1[ix].empty())
				{
					// sortIDZ_Y1_X[1]和sortIDZ_Y0_X[2]更新
					this->ZList1(ix, 1);
					this->ZList0(ix, 2);

					// 遍历当前单元临近单元临近单元（向前搜索）
					for (int iz = 0; iz < numCell.z; ++iz) this->LoopNBSMask(iy, ix, iz);
				}
				
				this->sortIDZ_Y1_X[0] = this->sortIDZ_Y1_X[1];
				this->sortIDZ_Y0_X[0] = this->sortIDZ_Y0_X[1];
				this->sortIDZ_Y0_X[1] = this->sortIDZ_Y0_X[2];
			}

			this->sortIDX_Y0 = this->sortIDX_Y1;
		}
	}
}

void NBS_Munjiza::LoopNBSMask(const int &iy, const int &ix, const int &iz)
{
	const auto &sortID_Z11 = this->sortIDZ_Y1_X[1];
	for (int i = 0; i < sortID_Z11[iz].size(); ++i)
	{
		const int &n = sortID_Z11[iz][i];

		// 在相同搜索单元内
		// (ix, iy, iz)
		for (int j = i + 1; j < sortID_Z11[iz].size(); ++j)
		{
			const int &m = sortID_Z11[iz][j];

			// 精细搜索
			this->FineSearch(n, m);
			this->numConservativeContacts++;
		}

		// xy切片内当前搜索单元的z方向前一个单元
		// (ix, iy, iz - 1)
		if (iz >= 1)
		{
			const int index = iz - 1;
			if (index < 0 || index >= sortID_Z11.size()) continue;

			for (int j = 0; j < sortID_Z11[index].size(); ++j)
			{
				const int &m = sortID_Z11[index][j];

				// 精细搜索
				this->FineSearch(n, m);
				this->numConservativeContacts++;
			}
		}

		// y切片内当前x方向前一层xy切片
		// (ix - 1, iy, iz - 1), (ix - 1, iy, iz), (ix - 1, iy, iz + 1)
		const auto &sortID_Z10 = this->sortIDZ_Y1_X[0];
		for (int k = -1; k <= 1; ++k)
		{
			const int index = iz + k;
			if (index < 0 || index >= sortID_Z10.size()) continue;

			for (int j = 0; j < sortID_Z10[index].size(); ++j)
			{
				const int &m = sortID_Z10[index][j];

				// 精细搜索
				this->FineSearch(n, m);
				this->numConservativeContacts++;
			}
		}

		// (iy - 1) 层级切片内所有单元
		// (ix - 1, iy - 1, iz - 1), (ix - 1, iy - 1, iz), (ix - 1, iy - 1, iz + 1)
		// (ix    , iy - 1, iz - 1), (ix    , iy - 1, iz), (ix    , iy - 1, iz + 1)
		// (ix + 1, iy - 1, iz - 1), (ix + 1, iy - 1, iz), (ix + 1, iy - 1, iz + 1)
		for (int k = 0; k <= 2; ++k)
		{
			const auto &sortID_Z0k = this->sortIDZ_Y0_X[k];
			for (int m = -1; m <= 1; ++m)
			{
				const int index = iz + m;
				if (index < 0 || index >= sortID_Z0k.size()) continue;

				for (int j = 0; j < sortID_Z0k[index].size(); ++j)
				{
					const int &m = sortID_Z0k[index][j];

					// 精细搜索
					this->FineSearch(n, m);
					this->numConservativeContacts++;
				}
			}
		}
	}
}

void NBS_Munjiza::YList()
{
	const IndexVector &numCell = this->GetCellNumber();

	// 遍历所有颗粒，按照颗粒位置y坐标相应的整数坐标分类
	const int &nPrtcl = this->GetParticleNumber();
	for (int n = 0; n < nPrtcl; ++n)
	{
		if (this->particles[n] != nullptr)
		{
			const IndexVector &ind = this->GetIndex(n);
			if (ind.y >= 0) this->sortIDY[ind.y].push_back(n);
		}
	}
}

void NBS_Munjiza::XList(const int &iy, const bool &lcheck)
{
	const IndexVector &numCell = this->GetCellNumber();

	// 遍历y坐标切片内的所有颗粒，按照颗粒位置x坐标相应的整数坐标分类
	const int elementSize = sortIDY[iy].size();
	for (int i = 0; i < elementSize; ++i)
	{
		const int &n = sortIDY[iy][i];
		const int &ix = this->GetIndex(n).x;
		if (ix >= 0) sortIDX_Y1[ix].push_back(n);
	}
}

void NBS_Munjiza::ZList1(const int &ix, const int &k, const bool &lcheck)
{
	const IndexVector &numCell = this->GetCellNumber();

	// k: 0, 1, 2 --> index: ix-1, ix , ix+1
	const int &index = ix + k - 1;
	if (index < 0 || index >= sortIDX_Y1.size()) return;

	// 遍历xy坐标切片内的所有颗粒，按照颗粒位置z坐标相应的整数坐标分类
	for (int i = 0; i < sortIDX_Y1[index].size(); ++i)
	{
		const int &n = sortIDX_Y1[index][i];
		const int &iz = this->GetIndex(n).z;
		if (iz >= 0) sortIDZ_Y1_X[k][iz].push_back(n);
	}
}

void NBS_Munjiza::ZList0(const int &ix, const int &k, const bool &lcheck)
{
	const IndexVector &numCell = this->GetCellNumber();

	// k: 0, 1, 2 --> index: ix-1, ix , ix+1
	const int &index = ix + k - 1;
	if (index < 0 || index >= sortIDX_Y0.size()) return;

	// 遍历xy坐标切片内的所有颗粒，按照颗粒位置z坐标相应的整数坐标分类，即形成xyz坐标切片
	for (int i = 0; i < sortIDX_Y0[index].size(); ++i)
	{
		const int &n = sortIDX_Y0[index][i];
		const int &iz = this->GetIndex(n).z;
		if (iz >= 0) sortIDZ_Y0_X[k][iz].push_back(n);
	}
}

} // namespace Contact

} // namespace Particle
