﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/AUSMPWPScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

AUSMPWPScheme::AUSMPWPScheme(Package::FlowPackage &data,                             
                             Limiter::Limiter *limiter,
                             Flux::Flow::Precondition::Precondition *precondition)
    :
    UpwindScheme(data, limiter, precondition)
{
}

NSFaceFlux AUSMPWPScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    //获取左右面值
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;

    //得到面法向和面积大小
    const Face &face = mesh->GetFace(faceID);
    const Vector &faceNormal = face.GetNormal();
    const Scalar &faceArea = face.GetArea();            

    //计算左右面的温度、声速、焓和法向速度
    const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
    const Scalar enthalpyLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);
    const Scalar TRight = material.GetTemperature(pRight, rhoRight);
    const Scalar enthalpyRight = Cp * TRight + 0.5 * (URight & URight);
    const Scalar velocityNormLeft = (ULeft & faceNormal);
    const Scalar velocityNormRight = (URight & faceNormal);

    //计算声速面心值
    const Vector UTildaLeft = ULeft - (ULeft & faceNormal) * faceNormal;
    const Vector UTildaRight = URight - (URight & faceNormal) * faceNormal;
    const Scalar enthalpyShared = 0.5 * (enthalpyLeft - 0.5 * (UTildaLeft & UTildaLeft) + enthalpyRight - 0.5 * (UTildaRight & UTildaRight));
    const Scalar soundShared2 = 2.0 * (gamma - 1) * enthalpyShared / (gamma + 1);
    const Scalar soundSharedTmp = sqrt(soundShared2);

    //计算左右面的马赫数
    const Scalar soundShared = soundShared2 / Max(Max(soundSharedTmp, velocityNormLeft), Max(soundSharedTmp, -velocityNormRight));
    const Scalar machLeft = velocityNormLeft / soundShared;
    const Scalar machRight = velocityNormRight / soundShared;

    const Scalar machTmpSqr = 0.5 * ((ULeft & ULeft) + (URight & URight)) / soundShared / soundShared;

    const Scalar MACH_REF = 0.05;
    const Scalar mo = sqrt(Min(1.0, Max(machTmpSqr, MACH_REF * MACH_REF)));
    const Scalar fa = Min(1.0, Max((2.0 - mo) * mo, 0.0));

    const Scalar BETA = 0.125;
    const Scalar alpha = Min(0.1875, Max(-0.75, 0.1875 * (5 * fa - 4.0)));

    //计算Ma+,Ma-,p+,p-
    Scalar machLeftPositive;
    Scalar machRightNegative;
    Scalar pLeftPositive;
    Scalar pRightNegative;
    if (fabs(machLeft) < 1.0)
    {
        machLeftPositive = 0.25 * (machLeft + 1.0) * (machLeft + 1.0) + BETA * (machLeft * machLeft - 1.0) * (machLeft * machLeft - 1.0);
        pLeftPositive = 0.25 * (machLeft + 1.0) * (machLeft + 1.0) * (2.0 - machLeft) + alpha * machLeft * (machLeft * machLeft - 1.0) * (machLeft * machLeft - 1.0);
    }
    else
    {
        machLeftPositive = ((machLeft >= 1) ? machLeft : 0.0);
        pLeftPositive = ((machLeft >= 1) ? 1.0 : 0.0);
    }

    if (fabs(machRight) < 1.0)
    {
        machRightNegative = -0.25 * (machRight - 1.0) * (machRight - 1.0) - BETA * (machRight * machRight - 1.0) * (machRight * machRight - 1.0);
        pRightNegative = 0.25 * (machRight - 1.0) * (machRight - 1.0) * (2.0 + machRight) - alpha * machRight * (machRight * machRight - 1.0) * (machRight * machRight - 1.0);
    }
    else
    {
        machRightNegative = ((machRight >= 1) ? 0.0 : machRight);
        pRightNegative = ((machRight >= 1) ? 0.0 : 1.0);
    }

    //计算马赫数和压强的面心值
    const Scalar machShared = machLeftPositive + machRightNegative;
    const Scalar pShared = pLeftPositive * pLeft + pRightNegative * pRight;

    const Scalar machHatShared = machShared - (pRight - pLeft) / (pRight + pLeft) * Min(0.25 / fa, 1.0) * Max(1.0 - machTmpSqr, 0.0);
    const Scalar machHatLeftPositive = machHatShared > 0 ? machHatShared : 0.0;
    const Scalar machHatRightNegative = machHatShared > 0 ? 0.0 : machHatShared;
    const Scalar pHatShared = pShared - 0.75 * fa * soundShared * pLeftPositive * pRightNegative * (rhoLeft + rhoRight) * (velocityNormRight - velocityNormLeft);

    //计算面通量
    Scalar left = machHatLeftPositive * soundShared * rhoLeft * faceArea;
    Scalar right = machHatRightNegative * soundShared * rhoRight * faceArea;
    faceFlux.massFlux = left + right;
    faceFlux.momentumFlux = left * ULeft + right * URight + pHatShared * faceNormal * faceArea;
    faceFlux.energyFlux = left * enthalpyLeft + right * enthalpyRight;

    return faceFlux;
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux