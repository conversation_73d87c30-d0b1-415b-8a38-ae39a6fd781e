//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_EXCEPTION_HPP
#define BOOST_COMPUTE_EXCEPTION_HPP

/// \file
///
/// Meta-header to include all Boost.Compute exception headers.

#include <boost/compute/exception/context_error.hpp>
#include <boost/compute/exception/no_device_found.hpp>
#include <boost/compute/exception/opencl_error.hpp>
#include <boost/compute/exception/unsupported_extension_error.hpp>

#endif // BOOST_COMPUTE_EXCEPTION_HPP
