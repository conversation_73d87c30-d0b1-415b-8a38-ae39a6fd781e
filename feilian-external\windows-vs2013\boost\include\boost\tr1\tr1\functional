//  (C) Copyright <PERSON> 2005.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_TR1_FUNCTIONAL_INCLUDED)
#  define BOOST_TR1_FUNCTIONAL_INCLUDED
#  ifndef BOOST_TR1_NO_RECURSION
#     define BOOST_TR1_NO_RECURSION
#     define BOOST_TR1_NO_FUNCTIONAL_RECURSION
#  endif
#  include <boost/tr1/detail/config_all.hpp>
#  if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#     include_next <functional>
#  else
#     include BOOST_TR1_STD_HEADER(functional)
#  endif
#  ifdef BOOST_TR1_NO_FUNCTIONAL_RECURSION
#     undef BOOST_TR1_NO_FUNCTIONAL_RECURSION
#     undef BOOST_TR1_NO_RECURSION
#  endif
#endif

#if !defined(BOOST_TR1_FULL_FUNCTIONAL_INCLUDED) && !defined(BOOST_TR1_NO_RECURSION)
#  define BOOST_TR1_FULL_FUNCTIONAL_INCLUDED
#  define BOOST_TR1_NO_RECURSION
#  include <boost/tr1/functional.hpp>
#  undef BOOST_TR1_NO_RECURSION
#endif

