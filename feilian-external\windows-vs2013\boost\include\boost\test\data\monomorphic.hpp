//  (C) Copyright Gennadiy Rozental 2001.
//  Distributed under the Boost Software License, Version 1.0.
//  (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/test for the library home page.
//
//!@file
//!@brief Monomorphic dataset interfaces
// ***************************************************************************

#ifndef BOOST_TEST_DATA_MONOMORPHIC_HPP_102211GER
#define BOOST_TEST_DATA_MONOMORPHIC_HPP_102211GER

// Boost.Test
#include <boost/test/data/monomorphic/array.hpp>
#include <boost/test/data/monomorphic/collection.hpp>
#include <boost/test/data/monomorphic/initializer_list.hpp>
#include <boost/test/data/monomorphic/generate.hpp>
#include <boost/test/data/monomorphic/generators.hpp>
#include <boost/test/data/monomorphic/grid.hpp>
#include <boost/test/data/monomorphic/join.hpp>
#include <boost/test/data/monomorphic/singleton.hpp>
#include <boost/test/data/monomorphic/zip.hpp>

#endif // BOOST_TEST_DATA_MONOMORPHIC_HPP_102211GER

