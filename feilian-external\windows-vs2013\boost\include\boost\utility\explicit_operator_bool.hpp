/*
 * Copyright (c) 2014 <PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0. (See
 * accompanying file LICENSE_1_0.txt or copy at
 * http://www.boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_UTILITY_EXPLICIT_OPERATOR_BOOL_HPP
#define BOOST_UTILITY_EXPLICIT_OPERATOR_BOOL_HPP

// The header file at this path is deprecated;
// use boost/core/explicit_operator_bool.hpp instead.

#include <boost/core/explicit_operator_bool.hpp>

#endif
