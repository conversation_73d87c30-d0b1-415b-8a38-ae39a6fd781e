﻿#ifndef _particle_contact_contactSearch_ContactSearch_
#define _particle_contact_contactSearch_ContactSearch_

#include "basic/geometry/Geometry.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"
#include "feilian-specialmodule/particle/contact/ContactList.h"
#include "feilian-specialmodule/particle/contact/contactSearch/NBS_Munjiza.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{
    
/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class SearchParticleParticle
{
public:
    const int &numParticlesMax;
    int numParticles;
    const std::vector<Particle *> &particles;
    const Configure::Particle::ContactSearchMethod &contactSearchMethod;

    ContactList *contactListPP;
    NBS_Munjiza *m_NBS_Munjiza;

public:
    SearchParticleParticle(const Configure::Particle::ParticleConfigure &DEMConfigure_, const int &max_nPrtcl_, int &numParticles_,
                  std::vector<Particle *> &particels_, ContactList *contactListPP_, ElementField<Scalar> *volumeFraction);
    
    void Initialize();

    /**
     * @brief 更新颗粒数量
     * 
     * @param numParticleNew 颗粒数量
     */
    void SetParticleNumber(const int &numParticleNew);

    /**
     * @brief 搜索接触情况
     * 
     */
    void FindContacts();
    
    /**
     * @brief 获得接触数量
     * 
     * @return std::vector<int> 
     */
    std::vector<int> GetContactNumber();
};

} // namespace Contact

} // namespace Particle

#endif