﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ParallelBoundary.h
//! <AUTHOR> 尹强
//! @brief ParallelBoundary为并行计算时不同分割网格的交界面边界条件类,具体类
//! @date  2021-3-30
//
//------------------------------修改日志----------------------------------------
//
// 2021-03-30 尹强 
// 说明：添加注释并规范化
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_ParallelBoundary_
#define _sourceFlow_boundaryCondition_ParallelBoundary_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 并行计算交界面边界条件类
 * 
 */
class ParallelBoundary
{
public:
    /**
     * @brief 构造函数，创建并行边界条件对象
     * 
     * @param[in, out] data 包含各类场的数据包
     */
    ParallelBoundary(Package::FlowPackage & data);

    /**
     * @brief 并行计算不同分割网格的交界面边界条件
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 更新更新边界虚单元残值
     * 
     */
    void UpdateBoundaryResidual();

private:
    Mesh *mesh; ///< 网格指针
    const Material::Flow::Materials &material; ///< 材料对象
    Package::FlowPackage &flowPackage; ///< 流场包

    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度
    ElementField<Scalar> &A; ///< 音速
    ElementField<Scalar> &H; ///< 总焓
    ElementField<Scalar> *muLaminar; ///< 层流粘性系数
#if defined(_EnableMultiSpecies_)
	const std::vector<ElementField<Scalar> *> &massFraction; ///< 组分指针容器
#endif
    
    ElementField<Scalar> &residualMass; ///< 质量残值
    ElementField<Vector> &residualMomentum; ///< 动量残值
    ElementField<Scalar> &residualEnergy; ///< 能量残值

    const bool &nodeCenter; ///< 边界数据存储在节点标识
};

} // namespace Flow
} // namespace Boundary

#endif
