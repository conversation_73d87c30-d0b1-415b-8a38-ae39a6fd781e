﻿#include "sourceFlow/boundaryCondition/WallMoving.h"
// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{
// 绝热壁面边界条件
WallMoving::WallMoving(const int &boundaryPatchID, Package::FlowPackage &data, const Vector &UWall_)
    :
    Wall(boundaryPatchID, data), UWall(UWall_)
{
    if (muLaminar == nullptr)
        FatalError("WallMoving::WallMoving： 该边界仅支持粘性流动...");
}

void WallMoving::Initialize()
{
    this->UpdateBoundaryCondition();    
}

void WallMoving::UpdateBoundaryCondition()
{
    this->BoundFromElement(rho);
    this->BoundFromElement(p);
    this->BoundFromElement(T);

    // 更新速度
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        if (nodeCenter)
        {
            U.SetValue(ownerID, UWall);
            U.SetValue(neighID, UWall);
        }
        else
        {
            U.SetValue(neighID, 2.0 * UWall - U.GetValue(ownerID));
        }
    }

    return;
}

void WallMoving::AddConvectiveResidual()
{
    this->AddMRFConvectiveResidualAverage();
}

void WallMoving::AddDiffusiveResidual()
{
    if(!nodeCenter) this->AddDiffusiveResidualCellCenter();

    return;
}

void WallMoving::UpdateBoundaryResidual()
{
    if (!nodeCenter) return;

    if (muLaminar != nullptr) this->UpdateBoundaryResidualStatic();
    else                      this->UpdateBoundaryResidualSlipping();
}

}// namespace Flow
}// namespace Boundary
