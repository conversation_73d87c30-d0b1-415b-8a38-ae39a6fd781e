/*
 [auto_generated]
 /boost/numeric/odeint/external/mtl4/mtl4.hpp

 [begin_description]
 includes all headers required for using mtl4 with odeint
 [end_description]

 Copyright 2013 <PERSON><PERSON>t
 Copyright 2013 <PERSON>

 Distributed under the Boost Software License, Version 1.0.
 (See accompanying file LICENSE_1_0.txt or
 copy at http://www.boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_NUMERIC_ODEINT_EXTERNAL_MTL4_MTL4_HPP_INCLUDED
#define BOOST_NUMERIC_ODEINT_EXTERNAL_MTL4_MTL4_HPP_INCLUDED

#include <boost/numeric/odeint/external/mtl4/mtl4_algebra_dispatcher.hpp>
#include <boost/numeric/odeint/external/mtl4/mtl4_resize.hpp>

#endif // BOOST_NUMERIC_ODEINT_EXTERNAL_MTL4_MTL4_INCLUDED
