/*
 *          Copyright <PERSON><PERSON> 2007 - 2015.
 * Distributed under the Boost Software License, Version 1.0.
 *    (See accompanying file LICENSE_1_0.txt or copy at
 *          http://www.boost.org/LICENSE_1_0.txt)
 */
/*!
 * \file   functional.hpp
 * \author <PERSON><PERSON>
 * \date   30.03.2008
 *
 * This header includes all functional helpers.
 */

#ifndef BOOST_LOG_UTILITY_FUNCTIONAL_HPP_INCLUDED_
#define BOOST_LOG_UTILITY_FUNCTIONAL_HPP_INCLUDED_

#include <boost/log/detail/config.hpp>

#include <boost/log/utility/functional/logical.hpp>
#include <boost/log/utility/functional/in_range.hpp>
#include <boost/log/utility/functional/begins_with.hpp>
#include <boost/log/utility/functional/ends_with.hpp>
#include <boost/log/utility/functional/contains.hpp>
#include <boost/log/utility/functional/matches.hpp>

#include <boost/log/utility/functional/nop.hpp>
#include <boost/log/utility/functional/bind_assign.hpp>
#include <boost/log/utility/functional/bind_output.hpp>
#include <boost/log/utility/functional/bind_to_log.hpp>
#include <boost/log/utility/functional/bind.hpp>
#include <boost/log/utility/functional/fun_ref.hpp>
#include <boost/log/utility/functional/as_action.hpp>
#include <boost/log/utility/functional/save_result.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_LOG_UTILITY_FUNCTIONAL_HPP_INCLUDED_
