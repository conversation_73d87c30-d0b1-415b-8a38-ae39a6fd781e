﻿#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/postprocessor/Postprocessor.h"

int main(int argc, char **argv)
{
	if (argc <= 2)
	{
		FatalError("命令行的参数错误");
		return -1;
	}

    // 输出软件相关信息
    SetInfoFile("AriCFD_Postprocessor.info");
    PrintTitleInfo(" ARI-Postprocessor ");
    PrintSystemTime();

    // 定义参数文件对象
    Configure::Flow::FlowConfigure flowConfigure;

    // 读入参数文件
    flowConfigure.ReadCaseXml(argv[1]);

    // 打印计算参数
    flowConfigure.PrintInformation();

    // 设置后处理的线程数
    flowConfigure.SetOpenMPThread();

	Post::Type type = Post::Type::AUTO;
	std::string resultsPath;
	for (int i = 2; i < argc; i++)
	{
		std::string argvT = argv[i];
		if (argvT == "-t" && i + 1 < argc)
		{
			std::string typeString = argv[i + 1];
			typeString = TransformToCapital(typeString);
			auto pos = Configure::resultTypeMap.find(typeString);
			if (pos == Configure::resultTypeMap.end()) FatalError("Result type isnot supported!");
			else                                       type = pos->second;
		}

		if (argvT == "-d" && i + 1 < argc)
		{
			resultsPath = argv[i + 1];
		}
	}

	Postprocessor postprocess(flowConfigure, type);
	if (!resultsPath.empty()) postprocess.SetProcessResultsPath(resultsPath);
    CheckStatus(3000);

	for (int i = 2; i < argc; i++)
	{
		std::string argvT = argv[i];
		if (argvT == "-t") break;
		if (argvT == "-d") break;

		postprocess.Process(argv[i]);
	}

    return 0;
}