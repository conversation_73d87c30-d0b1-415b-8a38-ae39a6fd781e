/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010-2011 <PERSON>

    Di<PERSON>ributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
        struct assign
            : proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 43> , proto::call< proto::_child_c< 43>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 43> , proto::call< proto::_child_c< 43>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 44> , proto::call< proto::_child_c< 44>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 43> , proto::call< proto::_child_c< 43>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 44> , proto::call< proto::_child_c< 44>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 45> , proto::call< proto::_child_c< 45>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 43> , proto::call< proto::_child_c< 43>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 44> , proto::call< proto::_child_c< 44>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 45> , proto::call< proto::_child_c< 45>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 46> , proto::call< proto::_child_c< 46>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 43> , proto::call< proto::_child_c< 43>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 44> , proto::call< proto::_child_c< 44>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 45> , proto::call< proto::_child_c< 45>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 46> , proto::call< proto::_child_c< 46>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 47> , proto::call< proto::_child_c< 47>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > , proto::or_< proto::when< proto::nary_expr<proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ , proto::_ > , proto::and_< assign( proto::_child_c< 0> , proto::call< proto::_child_c< 0>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 1> , proto::call< proto::_child_c< 1>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 2> , proto::call< proto::_child_c< 2>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 3> , proto::call< proto::_child_c< 3>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 4> , proto::call< proto::_child_c< 4>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 5> , proto::call< proto::_child_c< 5>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 6> , proto::call< proto::_child_c< 6>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 7> , proto::call< proto::_child_c< 7>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 8> , proto::call< proto::_child_c< 8>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 9> , proto::call< proto::_child_c< 9>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 10> , proto::call< proto::_child_c< 10>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 11> , proto::call< proto::_child_c< 11>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 12> , proto::call< proto::_child_c< 12>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 13> , proto::call< proto::_child_c< 13>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 14> , proto::call< proto::_child_c< 14>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 15> , proto::call< proto::_child_c< 15>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 16> , proto::call< proto::_child_c< 16>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 17> , proto::call< proto::_child_c< 17>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 18> , proto::call< proto::_child_c< 18>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 19> , proto::call< proto::_child_c< 19>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 20> , proto::call< proto::_child_c< 20>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 21> , proto::call< proto::_child_c< 21>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 22> , proto::call< proto::_child_c< 22>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 23> , proto::call< proto::_child_c< 23>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 24> , proto::call< proto::_child_c< 24>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 25> , proto::call< proto::_child_c< 25>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 26> , proto::call< proto::_child_c< 26>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 27> , proto::call< proto::_child_c< 27>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 28> , proto::call< proto::_child_c< 28>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 29> , proto::call< proto::_child_c< 29>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 30> , proto::call< proto::_child_c< 30>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 31> , proto::call< proto::_child_c< 31>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 32> , proto::call< proto::_child_c< 32>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 33> , proto::call< proto::_child_c< 33>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 34> , proto::call< proto::_child_c< 34>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 35> , proto::call< proto::_child_c< 35>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 36> , proto::call< proto::_child_c< 36>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 37> , proto::call< proto::_child_c< 37>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 38> , proto::call< proto::_child_c< 38>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 39> , proto::call< proto::_child_c< 39>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 40> , proto::call< proto::_child_c< 40>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 41> , proto::call< proto::_child_c< 41>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 42> , proto::call< proto::_child_c< 42>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 43> , proto::call< proto::_child_c< 43>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 44> , proto::call< proto::_child_c< 44>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 45> , proto::call< proto::_child_c< 45>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 46> , proto::call< proto::_child_c< 46>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 47> , proto::call< proto::_child_c< 47>(proto::_state) > ) , proto::and_< assign( proto::_child_c< 48> , proto::call< proto::_child_c< 48>(proto::_state) > ) > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > >
              , proto::when<
                    proto::terminal<proto::_>
                  , do_assign(proto::_, proto::_state)
                >
              > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > > >
        {};
