﻿#include "meshProcess/agglomeration/AgglomerateStructuredMesh.h"

void AgglomerateStructuredMesh::Agglomerate(Mesh *fineMesh_, Mesh *coarseMesh_, const int &coarseLevel, std::vector<int> &finalDecomp, int& nCoarseCells)
{
	//step0: 检查网格数量是否符合要求
	fineMesh = fineMesh_;
	coarseMesh = coarseMesh_;
	
	//step1: 开始聚合
	int cRatio = 4; //粗化率
	if (fineMesh->GetMeshDimension() == Mesh::MeshDim::md3D) cRatio = 8;
	
	this->BuildMultiStructuredBlock(nCoarseCells, cRatio);//建立粗网格的MultiStructuredBlock

	//step2: 建立单元对应关系
	MultiStructuredBlock &multiBlockFine = fineMesh->multiStructuredBlock;
	MultiStructuredBlock &multiBlockCoarse = coarseMesh->multiStructuredBlock;

    finalDecomp.clear();
	finalDecomp.resize(fineMesh->GetElementNumberReal(), -1);
	for (int n = 0; n < multiBlockFine.GetBlockSize(); ++n)
	{
		const Block &blockFine = multiBlockFine.GetBlock(n);
		const Block &blockCoarse = multiBlockCoarse.GetBlock(n);
		for (int k = 0; k < Max(blockFine.cellK, 1); ++k)
			for (int j = 0; j < blockFine.cellJ; ++j)
				for (int i = 0; i < blockFine.cellI; ++i)
				{
					int fineID = blockFine.GetElementIndex(i, j, k);
					int iCoarse = i / 2;
					int jCoarse = j / 2;
					int kCoarse = k / 2;
					int coarseID = blockCoarse.GetElementIndex(iCoarse, jCoarse, kCoarse);					
					finalDecomp[fineID] = coarseID;
				}
	}
}

void AgglomerateStructuredMesh::BuildMultiStructuredBlock(int &nCoarseCells, const int &cRatio)
{
	MultiStructuredBlock &multiBlockFine = fineMesh->multiStructuredBlock;
	MultiStructuredBlock &multiBlockCoarse = coarseMesh->multiStructuredBlock;
	
	for (int n = 0; n < multiBlockFine.GetBlockSize(); ++n)
	{
		const Block &blockFine = multiBlockFine.GetBlock(n);

		int nodeICoarse = blockFine.cellI / 2 + 1;
		int nodeJCoarse = blockFine.cellJ / 2 + 1;
		int nodeKCoarse = Max(blockFine.cellK / 2 + 1, 1);

		//建立粗网格块对象
		Block blockCoarse(nodeICoarse, nodeJCoarse, nodeKCoarse, nCoarseCells, multiBlockFine.GetBlock(n).boundaryRange);
		nCoarseCells += blockFine.cellI * blockFine.cellJ * Max(blockFine.cellK, 1) / cRatio;

		//处理物理边界
		for (int patchID = 0; patchID < blockFine.boundaryRange.size(); ++patchID)
		{
			const std::vector<int> &rangeFine = blockFine.boundaryRange[patchID];
			std::vector<int> &rangeCoarse = blockCoarse.boundaryRange[patchID];

			for (int m = 0; m < 6; ++m)
				rangeCoarse[m] = rangeFine[m] / 2;			
		}

		//添加粗网格块对象
		multiBlockCoarse.AddBlock(blockCoarse);
	}

	// 处理连接关系
	for (int n = 0; n < multiBlockFine.GetConnectionSize(); ++n)
	{		
		Connection cCoarse = multiBlockFine.GetConnection(n);
		
		for (int m = 0; m < 6; ++m)
		{
			cCoarse.leftRange[m] /= 2;
			cCoarse.rightRange[m] /= 2;
		}

		multiBlockCoarse.AddConnection(cCoarse);
	}

	multiBlockCoarse.SetNodeIndex(false);
}