/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    
    
    
    
    
    
        template <typename A0>
        struct make_locals<A0>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0>
            > type;
            static type const make(A0 a0)
            {
                return
                    type(
                        proto::child_c<1>(a0)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1>
        struct make_locals<A0 , A1>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1>
            > type;
            static type const make(A0 a0 , A1 a1)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2>
        struct make_locals<A0 , A1 , A2>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3>
        struct make_locals<A0 , A1 , A2 , A3>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4>
        struct make_locals<A0 , A1 , A2 , A3 , A4>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33 , typename A34>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33 , A34>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33; typedef typename proto::result_of::value< typename proto::result_of::child_c< A34 , 0 >::type >::type tag_type34; typedef typename proto::result_of::child_c< A34 , 1 >::type var_type34;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33> , fusion::pair<tag_type34, var_type34>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33 , A34 a34)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33) , proto::child_c<1>(a34)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33 , typename A34 , typename A35>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33 , A34 , A35>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33; typedef typename proto::result_of::value< typename proto::result_of::child_c< A34 , 0 >::type >::type tag_type34; typedef typename proto::result_of::child_c< A34 , 1 >::type var_type34; typedef typename proto::result_of::value< typename proto::result_of::child_c< A35 , 0 >::type >::type tag_type35; typedef typename proto::result_of::child_c< A35 , 1 >::type var_type35;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33> , fusion::pair<tag_type34, var_type34> , fusion::pair<tag_type35, var_type35>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33 , A34 a34 , A35 a35)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33) , proto::child_c<1>(a34) , proto::child_c<1>(a35)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33 , typename A34 , typename A35 , typename A36>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33 , A34 , A35 , A36>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33; typedef typename proto::result_of::value< typename proto::result_of::child_c< A34 , 0 >::type >::type tag_type34; typedef typename proto::result_of::child_c< A34 , 1 >::type var_type34; typedef typename proto::result_of::value< typename proto::result_of::child_c< A35 , 0 >::type >::type tag_type35; typedef typename proto::result_of::child_c< A35 , 1 >::type var_type35; typedef typename proto::result_of::value< typename proto::result_of::child_c< A36 , 0 >::type >::type tag_type36; typedef typename proto::result_of::child_c< A36 , 1 >::type var_type36;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33> , fusion::pair<tag_type34, var_type34> , fusion::pair<tag_type35, var_type35> , fusion::pair<tag_type36, var_type36>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33 , A34 a34 , A35 a35 , A36 a36)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33) , proto::child_c<1>(a34) , proto::child_c<1>(a35) , proto::child_c<1>(a36)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33 , typename A34 , typename A35 , typename A36 , typename A37>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33 , A34 , A35 , A36 , A37>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33; typedef typename proto::result_of::value< typename proto::result_of::child_c< A34 , 0 >::type >::type tag_type34; typedef typename proto::result_of::child_c< A34 , 1 >::type var_type34; typedef typename proto::result_of::value< typename proto::result_of::child_c< A35 , 0 >::type >::type tag_type35; typedef typename proto::result_of::child_c< A35 , 1 >::type var_type35; typedef typename proto::result_of::value< typename proto::result_of::child_c< A36 , 0 >::type >::type tag_type36; typedef typename proto::result_of::child_c< A36 , 1 >::type var_type36; typedef typename proto::result_of::value< typename proto::result_of::child_c< A37 , 0 >::type >::type tag_type37; typedef typename proto::result_of::child_c< A37 , 1 >::type var_type37;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33> , fusion::pair<tag_type34, var_type34> , fusion::pair<tag_type35, var_type35> , fusion::pair<tag_type36, var_type36> , fusion::pair<tag_type37, var_type37>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33 , A34 a34 , A35 a35 , A36 a36 , A37 a37)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33) , proto::child_c<1>(a34) , proto::child_c<1>(a35) , proto::child_c<1>(a36) , proto::child_c<1>(a37)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33 , typename A34 , typename A35 , typename A36 , typename A37 , typename A38>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33 , A34 , A35 , A36 , A37 , A38>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33; typedef typename proto::result_of::value< typename proto::result_of::child_c< A34 , 0 >::type >::type tag_type34; typedef typename proto::result_of::child_c< A34 , 1 >::type var_type34; typedef typename proto::result_of::value< typename proto::result_of::child_c< A35 , 0 >::type >::type tag_type35; typedef typename proto::result_of::child_c< A35 , 1 >::type var_type35; typedef typename proto::result_of::value< typename proto::result_of::child_c< A36 , 0 >::type >::type tag_type36; typedef typename proto::result_of::child_c< A36 , 1 >::type var_type36; typedef typename proto::result_of::value< typename proto::result_of::child_c< A37 , 0 >::type >::type tag_type37; typedef typename proto::result_of::child_c< A37 , 1 >::type var_type37; typedef typename proto::result_of::value< typename proto::result_of::child_c< A38 , 0 >::type >::type tag_type38; typedef typename proto::result_of::child_c< A38 , 1 >::type var_type38;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33> , fusion::pair<tag_type34, var_type34> , fusion::pair<tag_type35, var_type35> , fusion::pair<tag_type36, var_type36> , fusion::pair<tag_type37, var_type37> , fusion::pair<tag_type38, var_type38>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33 , A34 a34 , A35 a35 , A36 a36 , A37 a37 , A38 a38)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33) , proto::child_c<1>(a34) , proto::child_c<1>(a35) , proto::child_c<1>(a36) , proto::child_c<1>(a37) , proto::child_c<1>(a38)
                    );
            }
        };
    
    
    
    
    
    
    
        template <typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29 , typename A30 , typename A31 , typename A32 , typename A33 , typename A34 , typename A35 , typename A36 , typename A37 , typename A38 , typename A39>
        struct make_locals<A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29 , A30 , A31 , A32 , A33 , A34 , A35 , A36 , A37 , A38 , A39>
        {
            typedef typename proto::result_of::value< typename proto::result_of::child_c< A0 , 0 >::type >::type tag_type0; typedef typename proto::result_of::child_c< A0 , 1 >::type var_type0; typedef typename proto::result_of::value< typename proto::result_of::child_c< A1 , 0 >::type >::type tag_type1; typedef typename proto::result_of::child_c< A1 , 1 >::type var_type1; typedef typename proto::result_of::value< typename proto::result_of::child_c< A2 , 0 >::type >::type tag_type2; typedef typename proto::result_of::child_c< A2 , 1 >::type var_type2; typedef typename proto::result_of::value< typename proto::result_of::child_c< A3 , 0 >::type >::type tag_type3; typedef typename proto::result_of::child_c< A3 , 1 >::type var_type3; typedef typename proto::result_of::value< typename proto::result_of::child_c< A4 , 0 >::type >::type tag_type4; typedef typename proto::result_of::child_c< A4 , 1 >::type var_type4; typedef typename proto::result_of::value< typename proto::result_of::child_c< A5 , 0 >::type >::type tag_type5; typedef typename proto::result_of::child_c< A5 , 1 >::type var_type5; typedef typename proto::result_of::value< typename proto::result_of::child_c< A6 , 0 >::type >::type tag_type6; typedef typename proto::result_of::child_c< A6 , 1 >::type var_type6; typedef typename proto::result_of::value< typename proto::result_of::child_c< A7 , 0 >::type >::type tag_type7; typedef typename proto::result_of::child_c< A7 , 1 >::type var_type7; typedef typename proto::result_of::value< typename proto::result_of::child_c< A8 , 0 >::type >::type tag_type8; typedef typename proto::result_of::child_c< A8 , 1 >::type var_type8; typedef typename proto::result_of::value< typename proto::result_of::child_c< A9 , 0 >::type >::type tag_type9; typedef typename proto::result_of::child_c< A9 , 1 >::type var_type9; typedef typename proto::result_of::value< typename proto::result_of::child_c< A10 , 0 >::type >::type tag_type10; typedef typename proto::result_of::child_c< A10 , 1 >::type var_type10; typedef typename proto::result_of::value< typename proto::result_of::child_c< A11 , 0 >::type >::type tag_type11; typedef typename proto::result_of::child_c< A11 , 1 >::type var_type11; typedef typename proto::result_of::value< typename proto::result_of::child_c< A12 , 0 >::type >::type tag_type12; typedef typename proto::result_of::child_c< A12 , 1 >::type var_type12; typedef typename proto::result_of::value< typename proto::result_of::child_c< A13 , 0 >::type >::type tag_type13; typedef typename proto::result_of::child_c< A13 , 1 >::type var_type13; typedef typename proto::result_of::value< typename proto::result_of::child_c< A14 , 0 >::type >::type tag_type14; typedef typename proto::result_of::child_c< A14 , 1 >::type var_type14; typedef typename proto::result_of::value< typename proto::result_of::child_c< A15 , 0 >::type >::type tag_type15; typedef typename proto::result_of::child_c< A15 , 1 >::type var_type15; typedef typename proto::result_of::value< typename proto::result_of::child_c< A16 , 0 >::type >::type tag_type16; typedef typename proto::result_of::child_c< A16 , 1 >::type var_type16; typedef typename proto::result_of::value< typename proto::result_of::child_c< A17 , 0 >::type >::type tag_type17; typedef typename proto::result_of::child_c< A17 , 1 >::type var_type17; typedef typename proto::result_of::value< typename proto::result_of::child_c< A18 , 0 >::type >::type tag_type18; typedef typename proto::result_of::child_c< A18 , 1 >::type var_type18; typedef typename proto::result_of::value< typename proto::result_of::child_c< A19 , 0 >::type >::type tag_type19; typedef typename proto::result_of::child_c< A19 , 1 >::type var_type19; typedef typename proto::result_of::value< typename proto::result_of::child_c< A20 , 0 >::type >::type tag_type20; typedef typename proto::result_of::child_c< A20 , 1 >::type var_type20; typedef typename proto::result_of::value< typename proto::result_of::child_c< A21 , 0 >::type >::type tag_type21; typedef typename proto::result_of::child_c< A21 , 1 >::type var_type21; typedef typename proto::result_of::value< typename proto::result_of::child_c< A22 , 0 >::type >::type tag_type22; typedef typename proto::result_of::child_c< A22 , 1 >::type var_type22; typedef typename proto::result_of::value< typename proto::result_of::child_c< A23 , 0 >::type >::type tag_type23; typedef typename proto::result_of::child_c< A23 , 1 >::type var_type23; typedef typename proto::result_of::value< typename proto::result_of::child_c< A24 , 0 >::type >::type tag_type24; typedef typename proto::result_of::child_c< A24 , 1 >::type var_type24; typedef typename proto::result_of::value< typename proto::result_of::child_c< A25 , 0 >::type >::type tag_type25; typedef typename proto::result_of::child_c< A25 , 1 >::type var_type25; typedef typename proto::result_of::value< typename proto::result_of::child_c< A26 , 0 >::type >::type tag_type26; typedef typename proto::result_of::child_c< A26 , 1 >::type var_type26; typedef typename proto::result_of::value< typename proto::result_of::child_c< A27 , 0 >::type >::type tag_type27; typedef typename proto::result_of::child_c< A27 , 1 >::type var_type27; typedef typename proto::result_of::value< typename proto::result_of::child_c< A28 , 0 >::type >::type tag_type28; typedef typename proto::result_of::child_c< A28 , 1 >::type var_type28; typedef typename proto::result_of::value< typename proto::result_of::child_c< A29 , 0 >::type >::type tag_type29; typedef typename proto::result_of::child_c< A29 , 1 >::type var_type29; typedef typename proto::result_of::value< typename proto::result_of::child_c< A30 , 0 >::type >::type tag_type30; typedef typename proto::result_of::child_c< A30 , 1 >::type var_type30; typedef typename proto::result_of::value< typename proto::result_of::child_c< A31 , 0 >::type >::type tag_type31; typedef typename proto::result_of::child_c< A31 , 1 >::type var_type31; typedef typename proto::result_of::value< typename proto::result_of::child_c< A32 , 0 >::type >::type tag_type32; typedef typename proto::result_of::child_c< A32 , 1 >::type var_type32; typedef typename proto::result_of::value< typename proto::result_of::child_c< A33 , 0 >::type >::type tag_type33; typedef typename proto::result_of::child_c< A33 , 1 >::type var_type33; typedef typename proto::result_of::value< typename proto::result_of::child_c< A34 , 0 >::type >::type tag_type34; typedef typename proto::result_of::child_c< A34 , 1 >::type var_type34; typedef typename proto::result_of::value< typename proto::result_of::child_c< A35 , 0 >::type >::type tag_type35; typedef typename proto::result_of::child_c< A35 , 1 >::type var_type35; typedef typename proto::result_of::value< typename proto::result_of::child_c< A36 , 0 >::type >::type tag_type36; typedef typename proto::result_of::child_c< A36 , 1 >::type var_type36; typedef typename proto::result_of::value< typename proto::result_of::child_c< A37 , 0 >::type >::type tag_type37; typedef typename proto::result_of::child_c< A37 , 1 >::type var_type37; typedef typename proto::result_of::value< typename proto::result_of::child_c< A38 , 0 >::type >::type tag_type38; typedef typename proto::result_of::child_c< A38 , 1 >::type var_type38; typedef typename proto::result_of::value< typename proto::result_of::child_c< A39 , 0 >::type >::type tag_type39; typedef typename proto::result_of::child_c< A39 , 1 >::type var_type39;
            typedef fusion::map<
                fusion::pair<tag_type0, var_type0> , fusion::pair<tag_type1, var_type1> , fusion::pair<tag_type2, var_type2> , fusion::pair<tag_type3, var_type3> , fusion::pair<tag_type4, var_type4> , fusion::pair<tag_type5, var_type5> , fusion::pair<tag_type6, var_type6> , fusion::pair<tag_type7, var_type7> , fusion::pair<tag_type8, var_type8> , fusion::pair<tag_type9, var_type9> , fusion::pair<tag_type10, var_type10> , fusion::pair<tag_type11, var_type11> , fusion::pair<tag_type12, var_type12> , fusion::pair<tag_type13, var_type13> , fusion::pair<tag_type14, var_type14> , fusion::pair<tag_type15, var_type15> , fusion::pair<tag_type16, var_type16> , fusion::pair<tag_type17, var_type17> , fusion::pair<tag_type18, var_type18> , fusion::pair<tag_type19, var_type19> , fusion::pair<tag_type20, var_type20> , fusion::pair<tag_type21, var_type21> , fusion::pair<tag_type22, var_type22> , fusion::pair<tag_type23, var_type23> , fusion::pair<tag_type24, var_type24> , fusion::pair<tag_type25, var_type25> , fusion::pair<tag_type26, var_type26> , fusion::pair<tag_type27, var_type27> , fusion::pair<tag_type28, var_type28> , fusion::pair<tag_type29, var_type29> , fusion::pair<tag_type30, var_type30> , fusion::pair<tag_type31, var_type31> , fusion::pair<tag_type32, var_type32> , fusion::pair<tag_type33, var_type33> , fusion::pair<tag_type34, var_type34> , fusion::pair<tag_type35, var_type35> , fusion::pair<tag_type36, var_type36> , fusion::pair<tag_type37, var_type37> , fusion::pair<tag_type38, var_type38> , fusion::pair<tag_type39, var_type39>
            > type;
            static type const make(A0 a0 , A1 a1 , A2 a2 , A3 a3 , A4 a4 , A5 a5 , A6 a6 , A7 a7 , A8 a8 , A9 a9 , A10 a10 , A11 a11 , A12 a12 , A13 a13 , A14 a14 , A15 a15 , A16 a16 , A17 a17 , A18 a18 , A19 a19 , A20 a20 , A21 a21 , A22 a22 , A23 a23 , A24 a24 , A25 a25 , A26 a26 , A27 a27 , A28 a28 , A29 a29 , A30 a30 , A31 a31 , A32 a32 , A33 a33 , A34 a34 , A35 a35 , A36 a36 , A37 a37 , A38 a38 , A39 a39)
            {
                return
                    type(
                        proto::child_c<1>(a0) , proto::child_c<1>(a1) , proto::child_c<1>(a2) , proto::child_c<1>(a3) , proto::child_c<1>(a4) , proto::child_c<1>(a5) , proto::child_c<1>(a6) , proto::child_c<1>(a7) , proto::child_c<1>(a8) , proto::child_c<1>(a9) , proto::child_c<1>(a10) , proto::child_c<1>(a11) , proto::child_c<1>(a12) , proto::child_c<1>(a13) , proto::child_c<1>(a14) , proto::child_c<1>(a15) , proto::child_c<1>(a16) , proto::child_c<1>(a17) , proto::child_c<1>(a18) , proto::child_c<1>(a19) , proto::child_c<1>(a20) , proto::child_c<1>(a21) , proto::child_c<1>(a22) , proto::child_c<1>(a23) , proto::child_c<1>(a24) , proto::child_c<1>(a25) , proto::child_c<1>(a26) , proto::child_c<1>(a27) , proto::child_c<1>(a28) , proto::child_c<1>(a29) , proto::child_c<1>(a30) , proto::child_c<1>(a31) , proto::child_c<1>(a32) , proto::child_c<1>(a33) , proto::child_c<1>(a34) , proto::child_c<1>(a35) , proto::child_c<1>(a36) , proto::child_c<1>(a37) , proto::child_c<1>(a38) , proto::child_c<1>(a39)
                    );
            }
        };
