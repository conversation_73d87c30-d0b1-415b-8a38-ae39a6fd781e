﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Postprocessor.h
//! <AUTHOR>
//! @brief 后处理合并网格和流场解的类
//! @date 2024-02-23
//
//------------------------------修改日志----------------------------------------
// 2021-08-20 李艳亮
//    说明：建立并规范化。
//
// 2024-02-23 乔龙
//    说明：根据流场计算需求进行调整。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_postprocessor_Postprocessor_
#define _sourceFlow_postprocessor_Postprocessor_

#include "basic/field/DataPackage.h"
#include "basic/field/FieldManipulation.h"
#include "basic/postTools/BasePost.h"
#include "basic/postTools/Ensight.h"
#include "basic/postTools/Tecplot.h"
#include "basic/postTools/VTKLegacy.h"
#include "basic/postTools/CGNSLegacy.h"
#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/material/Materials.h"

/**
 * @brief 流场后处理类
 * 主要用于流场合并及衍生物理场计算
 * 
 */
class  Postprocessor
{
public:
    /**
     * @brief 构造函数，创建流场后处理对象
     * 
     * @param[in] flowConfigure_ 流场参数
     * @param[in] postType_ 输出文件类型
     * @param[in] globalMesh_ 全局网格
     * @param[in] boundaryMeshFlag_ 边界网格标识
     */
    Postprocessor(Configure::Flow::FlowConfigure &flowConfigure_, const Post::Type &postType_ = Post::Type::AUTO,
                  Mesh *globalMesh_ = nullptr, const bool &boundaryMeshFlag_ = false);

    /**
     * @brief 析构函数
     * 
     */
    ~Postprocessor();

    /**
     * @brief 后处理操作函数
     * 
     * @param[in] stepString 后处理目标步数
     */
    void Process(const std::string &stepString);

    /**
    * @brief 设置后处理目标步数
    *
    * @param[in] stepString 后处理目标步数
    */
    void SetProcessStep(const std::string &stepString);

    /**
    * @brief 设置后处理结果输出路径
    *
    * @param[in] pathString 后处理目标步数
    */
    void SetProcessResultsPath(const std::string &pathString);

    /**
    * @brief 设置后处理结果按网格节点输出
    *
    * @param[in] flag 网格节点输出标识
    */
    void SetOutputCellVertice(const bool flag = true);

    /**
    * @brief 获取单元场
    *
    * @param[out] stepString 目标步数
    */
	void GetScalarFields(std::vector<ElementField<Scalar> *> &scalarFieldVector);

    /**
    * @brief 获取单元场
    *
    * @param[out] stepString 目标步数
    */
	void GetVectorFields(std::vector<ElementField<Vector> *> &vectorFieldVector);

    /**
    * @brief 创建全局网格和当地网格容器
    *
    */
    void CreateGlobalMesh();

    /**
     * @brief 创建合并后的所有物理量对象
     * 
     */
    void InitializeFields();
    
    /**
    * @brief 输入合并后的全局物理场
    *
    */
    void InputBasicFields();

    /**
    * @brief 其他衍生物理场计算
    *
    */
    void CalculateOtherFields();

    /**
    * @brief 输出后处理结果
    * 根据参数文件后处理类型，选择Tecplot或Ensight格式输出
    *
    */
    void WritePostResults();
    
	/**
	* @brief 读取物理场
	*
	* @param[in] name 物理场名称
	* @param[in] value 物理量类型标识
	*/
	template<class Type>
	ElementField<Type> *ReadField(std::string name, const Type &value);

    /**
	* @brief 读取并更新运动后的坐标点
	*/
	void ReadAndUpdateNodes();

    /**
     * @brief 动网格计算时，更新全局网格信息
     * 
     */
    void UpdateGlobalMesh();

    /**
	* @brief 读取全局网格和当地网格容器
	*
	*/
	void ReadMesh();

    /**
     * @brief 初始化后处理对象指针
     * 
     */
    void InitializePostPointer();

	/**
	* @brief 清空指针
	*
	*/
	void DeletePostPointers();

	/**
	* @brief 输出机器学习模型输入特征，用于建立训练集
	*
	*/
	void WriteFeatures();

	/**
	* @brief 输出湍流黏性系数，用于建立训练集
	*
	*/
	void WriteMut();

private:
    Configure::Flow::FlowConfigure &flowConfigure; ///< 用户各种设置参数
    Material::Flow::Materials *material; ///< 材料参数

    int nPart; ///< 并行分区的数量
    int nZones; ///< 网格子域数量
	int zoneID; ///< 当前后处理的子域编号
    std::string currentStep; ///< 合并哪一步的物理场
    bool binaryMesh; ///< 网格是否是二进制存储标志，true表示为二进制
    bool dualMesh; ///< 对偶网格标识，真为对偶网格
    Post::Type postType; ///< 输出类型
	Post::Position postPosition; ///< 输出物理场位置
	bool exportInteriorFlag; ///< 后处理结果输出数据编号，空白为全部输出，-1为内部区域，其他非负编号为边界全局编号
	std::vector<std::string> exportValueName; ///< 输出变量名称，空白为全部输出
	bool surfaceVelocityFlag; ///< 表面速度投影标识

    std::vector<FlowMacro::Scalar> turbulentVariable; ///< 湍流量及其残值所对应宏的容器

    SubMesh *subMesh; ///< 全局网格
    Mesh *globalMesh; ///< 全局网格
    Mesh *globalMesh0; ///< 全局网格
    std::vector<SubMesh *>localMeshVector; ///< 所有子网格
    const bool boundaryMeshFlag;

	DataPackage *fieldPackage; ///< 流场包

	ElementField<Scalar> *rho; ///< 全局密度场指针
	ElementField<Vector> *U; ///< 全局速度场指针
	ElementField<Scalar> *p; ///< 全局压强场指针
	ElementField<Scalar> *MuT; ///< 全局湍流粘性系数指针

	ElementField<Scalar> *meanMuTurbulent; ///< 时均湍流黏性系数场
	ElementField<Scalar> *meanMu; ///< 时均主流黏性系数场
	ElementField<Vector> *meanVelocity; ///< 时均速度场
	ElementField<Scalar> *meanPressure; ///< 时均压力场
	ElementField<Scalar> *meanDensity; ///< 时均密度场

    std::vector<std::vector<int>> vv_boundaryNodeID; ///< 边界点编号
    std::vector<std::vector<int>> vv_boundaryInnerNodeID; ///< 边界点相邻内点编号
    std::vector<std::vector<Vector>> vv_boundaryNodeNormal; ///< 边界点处边界法向矢量

    int fieldSize; ///< 场元素数量

    bool viscousFlag; ///< 粘性标识
    bool turbulenceFlag; ///< 湍流标识
	bool desFlag;      ///< DES标识
	bool unsteadyFlag;
	bool ddesFlag;

	bool oversetFlag; ///< 重叠标识
	bool motionFlag; ///< 运动标识
    
    Post::BasePost *postPointer; ///< 后处理指针

    std::string resultOutputPath; ///< 后处理结果输出路径
    std::string fieldPath; ///< 物理场路径
    std::string resultPath; ///< 结果路径

    bool exportFeaturesFlag;
    bool exportMutFlag;

};

#endif 
