﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LinearSystemSolver.h
//! <AUTHOR>
//! @brief 大型线性方程组的求解类.
//! @date 2024-12-12
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-12 李艳亮、孔名驰
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _basic_CFD_linearSystemSolver_LinearSystemSolver_
#define _basic_CFD_linearSystemSolver_LinearSystemSolver_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrix.h"
#include "basic/CFD/linearSystemSolver/LinearSystemVector.h"
#include "basic/mesh/Mesh.h"
#include "sourceFlow/configure/FlowConfigure.h"

/**
 * @brief 大型线性方程组的求解类
 * 
 */
class LinearSystemSolver
{
public:
    /**
     * @brief 线性方程组求解的构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] nVariable_ 待求解物理量个数
     */
	LinearSystemSolver(Mesh *mesh_, const int nVariable_) : mesh(mesh_), nVariable(nVariable_) {}

	/**
	 * @brief 构造函数
	 *
	 */
	virtual ~LinearSystemSolver() {}

    /**
     * @brief 线性方程组求解的初始化
     * 
     * @param[in] scheme 求解方法
     * @param[in] paramters 求解方法对应参数
     * @param[in] CSRMatrix_ 矩阵
     * @param[in] resVector_ 右端列向量
	 * @param[in, out] solVector_ 解向量
     */
	virtual void Initialize(const Time::Scheme &scheme,
                            const Configure::Flow::ExactJacobianStruct &paramters,
					        BlockSparseMatrix *CSRMatrix_,
					        LinearSystemVector *resVector_,
					        LinearSystemVector *solVector_) = 0;

    /**
     * @brief 线性方程组求解
     * 
     */
	virtual void Solve() = 0;
	
protected:
    Mesh *mesh;
    const int nVariable;
};
#endif
