//  (C) Copyright <PERSON> 2005.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)


#ifndef BOOST_TR1_COMPLEX_INCLUDED
#  define BOOST_TR1_COMPLEX_INCLUDED
#  ifndef BOOST_TR1_NO_RECURSION
#     define BOOST_TR1_NO_RECURSION
#     define BOOST_TR1_NO_COMPLEX_RECURSION
#  endif
#  include <boost/tr1/detail/config_all.hpp>
#  if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#     include_next <complex>
#  else
#     include BOOST_TR1_STD_HEADER(complex)
#  endif
#  ifdef BOOST_TR1_NO_COMPLEX_RECURSION
#     undef BOOST_TR1_NO_COMPLEX_RECURSION
#     undef BOOST_TR1_NO_RECURSION
#  endif
#endif

#if !defined(BOOST_TR1_FULL_COMPLEX_INCLUDED) && !defined(BOOST_TR1_NO_RECURSION)
#  define BOOST_TR1_FULL_COMPLEX_INCLUDED
#  define BOOST_TR1_NO_RECURSION
#  include <boost/tr1/complex.hpp>
#  undef BOOST_TR1_NO_RECURSION
#endif

