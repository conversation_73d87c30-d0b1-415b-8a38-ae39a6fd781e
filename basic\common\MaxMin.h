﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MaxMin.h
//! <AUTHOR>
//! @brief 最大、最小和符号函数
//! @date 2022-04-09
//
//------------------------------修改日志----------------------------------------
// 2022-08-03 乔龙
//    说明：修改。
//
// 2020-07-22 张帅
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_common_MaxMin_
#define _basic_common_MaxMin_

#include "basic/common/Vector.h"

/**
 * @brief 返回两个无符号整型数中的较小值
 * @param FirstVal 第一个无符号整型数
 * @param SecondVal 第二个无符号整型数
 * @return 两个数中的较小值
 */
inline size_t Min(const size_t& FirstVal, const size_t& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个整型数中的较小值
 * @param FirstVal 第一个整型数
 * @param SecondVal 第二个整型数
 * @return 两个数中的较小值
 */
inline int Min(const int& FirstVal, const int& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个浮点数中的较小值
 * @param FirstVal 第一个浮点数
 * @param SecondVal 第二个浮点数
 * @return 两个数中的较小值
 */
inline float Min(const float& FirstVal, const float& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个标量中的较小值
 * @param FirstVal 第一个标量
 * @param SecondVal 第二个标量
 * @return 两个标量中的较小值
 */
inline Scalar Min(const Scalar& FirstVal, const Scalar& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个向量中各分量中的较小值组成的新向量
 * @param FirstVal 第一个向量
 * @param SecondVal 第二个向量
 * @return 新向量，其各分量为两个输入向量对应分量的较小值
 */
inline Vector Min(const Vector& FirstVal, const Vector& SecondVal)
{
    return Vector( FirstVal.X() < SecondVal.X() ? FirstVal.X() : SecondVal.X(),
                   FirstVal.Y() < SecondVal.Y() ? FirstVal.Y() : SecondVal.Y(),
                   FirstVal.Z() < SecondVal.Z() ? FirstVal.Z() : SecondVal.Z() );
}

/**
 * @brief 返回两个无符号整型数中的较大值
 * @param FirstVal 第一个无符号整型数
 * @param SecondVal 第二个无符号整型数
 * @return 两个数中的较大值
 */
inline size_t Max(const size_t& FirstVal, const size_t& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个整型数中的较大值
 * @param FirstVal 第一个整型数
 * @param SecondVal 第二个整型数
 * @return 两个数中的较大值
 */
inline int Max(const int& FirstVal, const int& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个浮点数中的较大值
 * @param FirstVal 第一个浮点数
 * @param SecondVal 第二个浮点数
 * @return 两个数中的较大值
 */
inline float Max(const float& FirstVal, const float& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个标量中的较大值
 * @param FirstVal 第一个标量
 * @param SecondVal 第二个标量
 * @return 两个标量中的较大值
 */
inline Scalar Max(const Scalar& FirstVal, const Scalar& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

/**
 * @brief 返回两个向量中各分量中的较大值组成的新向量
 * @param FirstVal 第一个向量
 * @param SecondVal 第二个向量
 * @return 新向量，其各分量为两个输入向量对应分量的较大值
 */
inline Vector Max(const Vector& FirstVal, const Vector& SecondVal)
{
    return Vector( FirstVal.X() > SecondVal.X() ? FirstVal.X() : SecondVal.X(),
                   FirstVal.Y() > SecondVal.Y() ? FirstVal.Y() : SecondVal.Y(),
                   FirstVal.Z() > SecondVal.Z() ? FirstVal.Z() : SecondVal.Z() );
}

/**
 * @brief 返回整型数的符号
 * @param value 输入整型数
 * @return 1表示正数，-1表示负数，0表示零
 */
inline int Sign(const int& value)
{
    return (value > 0) ? 1 : ((value < 0) ? -1 : 0);
}

/**
 * @brief 返回浮点数的符号
 * @param value 输入浮点数
 * @return 1.0f表示正数，-1.0f表示负数，0.0f表示零
 */
inline float Sign(const float& value)
{
    return (value > 0.0f) ? 1.0f : ((value < 0.0f) ? -1.0f : 0.0f);
}

/**
 * @brief 返回标量的符号
 * @param value 输入标量
 * @return 1.0表示正数，-1.0表示负数，0.0表示零
 */
inline Scalar Sign(const Scalar& value)
{
    return (value > 0.0) ? 1.0 : ((value < 0.0) ? -1.0 : 0.0);
}

#endif // _basic_common_MaxMin_