!===============================================================================
! Copyright 2014-2018 Intel Corporation.
!
! This software and the related documents are Intel copyrighted  materials,  and
! your use of  them is  governed by the  express license  under which  they were
! provided to you (License).  Unless the License provides otherwise, you may not
! use, modify, copy, publish, distribute,  disclose or transmit this software or
! the related documents without Intel's prior written permission.
!
! This software and the related documents  are provided as  is,  with no express
! or implied  warranties,  other  than those  that are  expressly stated  in the
! License.
!===============================================================================

!  Content:
!      Intel(R) Math Kernel Library (Intel(R) MKL) FORTRAN macros for MKL_DIRECT_CALL
!*******************************************************************************
#ifdef MKL_DIRECT_CALL_SEQ
#ifndef MKL_DIRECT_CALL
#define MKL_DIRECT_CALL
#endif
#endif

#ifdef MKL_DIRECT_CALL

#ifdef MKL_DIRECT_CALL_SEQ
#define MKL_DIRECT_CALL_FLAG 1
#else
#define MKL_DIRECT_CALL_FLAG 0
#endif

#define dgemm DGEMM
#define sgemm SGEMM
#define zgemm ZGEMM
#define cgemm CGEMM

#define DGEMM(ta, tb, m, n, k, alpha, A, LDA, B, LDB, beta, C, LDC) DGEMM_DIRECT(ta,tb,m,n,k,alpha,A,LDA,B,LDB,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define SGEMM(ta, tb, m, n, k, alpha, A, LDA, B, LDB, beta, C, LDC) SGEMM_DIRECT(ta,tb,m,n,k,alpha,A,LDA,B,LDB,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define CGEMM(ta, tb, m, n, k, alpha, A, LDA, B, LDB, beta, C, LDC) CGEMM_DIRECT(ta,tb,m,n,k,alpha,A,LDA,B,LDB,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define ZGEMM(ta, tb, m, n, k, alpha, A, LDA, B, LDB, beta, C, LDC) ZGEMM_DIRECT(ta,tb,m,n,k,alpha,A,LDA,B,LDB,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))

#define zgemm3m ZGEMM3M
#define cgemm3m CGEMM3M

#define CGEMM3M(ta, tb, m, n, k, alpha, A, LDA, B, LDB, beta, C, LDC) CGEMM3M_DIRECT(ta,tb,m,n,k,alpha,A,LDA,B,LDB,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define ZGEMM3M(ta, tb, m, n, k, alpha, A, LDA, B, LDB, beta, C, LDC) ZGEMM3M_DIRECT(ta,tb,m,n,k,alpha,A,LDA,B,LDB,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))

#define dtrsm DTRSM
#define strsm STRSM
#define ztrsm ZTRSM
#define ctrsm CTRSM

#define DTRSM(sa, su, st, sd, m, n, alpha, a, lda, b, ldb) DTRSM_DIRECT(sa,su,st,sd,m,n,alpha,a,lda,b,ldb,INT(MKL_DIRECT_CALL_FLAG))
#define STRSM(sa, su, st, sd, m, n, alpha, a, lda, b, ldb) STRSM_DIRECT(sa,su,st,sd,m,n,alpha,a,lda,b,ldb,INT(MKL_DIRECT_CALL_FLAG))
#define CTRSM(sa, su, st, sd, m, n, alpha, a, lda, b, ldb) CTRSM_DIRECT(sa,su,st,sd,m,n,alpha,a,lda,b,ldb,INT(MKL_DIRECT_CALL_FLAG))
#define ZTRSM(sa, su, st, sd, m, n, alpha, a, lda, b, ldb) ZTRSM_DIRECT(sa,su,st,sd,m,n,alpha,a,lda,b,ldb,INT(MKL_DIRECT_CALL_FLAG))

#define dsyrk DSYRK
#define ssyrk SSYRK
#define zsyrk ZSYRK
#define csyrk CSYRK

#define DSYRK(ul, tr, n, k, alpha, A, LDA, beta, C, LDC) DSYRK_DIRECT(ul,tr,n,k,alpha,A,LDA,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define SSYRK(ul, tr, n, k, alpha, A, LDA, beta, C, LDC) SSYRK_DIRECT(ul,tr,n,k,alpha,A,LDA,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define CSYRK(ul, tr, n, k, alpha, A, LDA, beta, C, LDC) CSYRK_DIRECT(ul,tr,n,k,alpha,A,LDA,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))
#define ZSYRK(ul, tr, n, k, alpha, A, LDA, beta, C, LDC) ZSYRK_DIRECT(ul,tr,n,k,alpha,A,LDA,beta,C,LDC,INT(MKL_DIRECT_CALL_FLAG))

#define daxpy DAXPY
#define saxpy SAXPY
#define caxpy CAXPY
#define zaxpy ZAXPY

#define DAXPY(n, alpha, x, incx, y, incy) DAXPY_DIRECT(n,alpha,x,incx,y,incy,INT(MKL_DIRECT_CALL_FLAG))
#define SAXPY(n, alpha, x, incx, y, incy) SAXPY_DIRECT(n,alpha,x,incx,y,incy,INT(MKL_DIRECT_CALL_FLAG))
#define CAXPY(n, alpha, x, incx, y, incy) CAXPY_DIRECT(n,alpha,x,incx,y,incy,INT(MKL_DIRECT_CALL_FLAG))
#define ZAXPY(n, alpha, x, incx, y, incy) ZAXPY_DIRECT(n,alpha,x,incx,y,incy,INT(MKL_DIRECT_CALL_FLAG))

#define ddot DDOT_DIRECT
#define sdot SDOT_DIRECT
#define DDOT DDOT_DIRECT
#define SDOT SDOT_DIRECT

#endif
