///////////////////////////////////////////////////////////////////////////////
// matchers.hpp
//
//  Copyright 2008 Eric <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_XPRESSIVE_DETAIL_CORE_MATCHERS_HPP_EAN_10_04_2005
#define BOOST_XPRESSIVE_DETAIL_CORE_MATCHERS_HPP_EAN_10_04_2005

// MS compatible compilers support #pragma once
#if defined(_MSC_VER)
# pragma once
#endif

//#include <boost/xpressive/detail/core/matcher/action_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/alternate_end_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/alternate_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/any_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/assert_bol_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/assert_bos_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/assert_eol_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/assert_eos_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/assert_word_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/attr_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/charset_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/end_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/epsilon_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/keeper_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/literal_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/logical_newline_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/lookahead_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/lookbehind_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/mark_begin_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/mark_end_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/mark_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/optional_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/posix_charset_matcher.hpp>
//#include <boost/xpressive/detail/core/matcher/predicate_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/range_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/regex_byref_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/regex_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/repeat_begin_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/repeat_end_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/set_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/simple_repeat_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/string_matcher.hpp>
#include <boost/xpressive/detail/core/matcher/true_matcher.hpp>

#endif
