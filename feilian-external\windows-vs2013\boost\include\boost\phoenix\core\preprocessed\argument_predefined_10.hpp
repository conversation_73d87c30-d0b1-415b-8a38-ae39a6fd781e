/*==============================================================================
    Copyright (c) 2001-2010 <PERSON>
    Copyright (c) 2010-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    namespace placeholders
    {
        typedef expression::argument<1>::type arg1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type arg2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type arg3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type arg4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type arg5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type arg6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type arg7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type arg8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type arg9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type arg10_type BOOST_ATTRIBUTE_UNUSED;
        typedef expression::argument<1>::type _1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type _2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type _3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type _4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type _5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type _6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type _7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type _8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type _9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type _10_type BOOST_ATTRIBUTE_UNUSED;
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED arg1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED arg2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED arg3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED arg4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED arg5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED arg6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED arg7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED arg8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED arg9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED arg10 = {{{}}};
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED _1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED _2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED _3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED _4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED _5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED _6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED _7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED _8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED _9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED _10 = {{{}}};
    }
    namespace arg_names
    {
        typedef expression::argument<1>::type arg1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type arg2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type arg3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type arg4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type arg5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type arg6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type arg7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type arg8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type arg9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type arg10_type BOOST_ATTRIBUTE_UNUSED;
        typedef expression::argument<1>::type _1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type _2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type _3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type _4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type _5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type _6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type _7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type _8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type _9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type _10_type BOOST_ATTRIBUTE_UNUSED;
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED arg1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED arg2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED arg3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED arg4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED arg5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED arg6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED arg7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED arg8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED arg9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED arg10 = {{{}}};
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED _1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED _2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED _3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED _4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED _5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED _6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED _7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED _8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED _9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED _10 = {{{}}};
    }
