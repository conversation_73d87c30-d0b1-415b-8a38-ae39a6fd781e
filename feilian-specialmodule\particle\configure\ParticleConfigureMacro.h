﻿#ifndef _particle_configure_ParticleConfigureMacro_
#define _particle_configure_ParticleConfigureMacro_

/**
 * @brief 参数命名空间
 * 
 */
namespace Configure
{

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{
/// 壁面接触类型
enum WallContactType
{
	WCT_PLANE,
	WCT_GRANULAR
};

/// 接触列类型
enum ContactForceType
{
	LINEAR_NOLIMITED, ///< 切向位移不受限制的线弹性阻尼模型
	LINEAR_LIMITED, ///< 切向位移受限制的线弹性阻尼模型
	NONLINEAR_NOLIMITED, ///< 切向位移不受限制的非线性粘弹性模型
	NONLINEAR_LIMITED ///< 切向位移受限制的非线性粘弹性模型
};

/// 接触力矩类型
enum ContactTorqueType
{
	CTT_NONE, ///< 不计算力矩
	CONSTANT, ///< 常力矩模型，与速度无关
	VARIABLE ///< 变力矩模型，与速度相关
};

/// 接触搜索方法
enum ContactSearchMethod
{
	NBS,
	NBS_Munjiza,
	NBS_Hrchl
};

/// 运动积分格式
enum MotionIntegrationScheme
{
	PIM_FE,
	PIM_ME,
	PIM_AB2,
	PIM_AB3,
	PIM_AB4,
	PIM_AB5,
	PIM_AB2AM3,
	PIM_AB3AM4,
	PIM_AB4AM5
};

enum InsertionMethod
{
    BOX,
    FILE
};

enum InsertionShape
{
    PLANE,
	ZONE
};

enum DistributionType
{
    UNIFORM,
    NORMAL,
    LOG_NORMAL
};

enum FileType
{
	VTK,
	TECPLOT
};

enum InterpolationMethod
{
	INTERPOLATION_CONSTANT,
	TAYLOR_1ST,
	IDW,
	LINEAR,
	TRILINEAR,
	LEAST_SQUARE,
	HAMONIC
};

} // namespace Particle

} // namespace Configure

#endif