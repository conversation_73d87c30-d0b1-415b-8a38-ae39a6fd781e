// Copyright 2008 <PERSON>
// henry <PERSON> christophe AT hotmail DOT com
// This is an extended version of the state machine available in the boost::mpl library
// Distributed under the same license as the original.
// Copyright for the original version:
// Copyright 2005 <PERSON> and Aleksey Gurtovoy. Distributed
// under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_MSM_FRONT_EUML_EUML_H
#define BOOST_MSM_FRONT_EUML_EUML_H

#include <boost/msm/front/euml/common.hpp>
#include <boost/msm/front/euml/operator.hpp>
#include <boost/msm/front/euml/guard_grammar.hpp>
#include <boost/msm/front/euml/state_grammar.hpp>
#include <boost/msm/front/euml/stt_grammar.hpp>
#ifdef BOOST_MSM_EUML_PHOENIX_SUPPORT
#include <boost/msm/front/euml/phoenix_placeholders.hpp>
#endif

#endif //BOOST_MSM_FRONT_EUML_EUML_H
