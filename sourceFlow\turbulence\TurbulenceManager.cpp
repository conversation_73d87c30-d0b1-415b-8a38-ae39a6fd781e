﻿#include "sourceFlow/turbulence/TurbulenceManager.h"

namespace Turbulence
{

TurbulenceManager::TurbulenceManager(Package::FlowPackage &flowPackage_)
    :
    flowPackage(flowPackage_),
    flowConfigure(flowPackage_.GetFlowConfigure()),
    turbulenceType(flowPackage_.GetTurbulentStatus().turbulenceType),
	gradientScheme(flowPackage_.GetFlowConfigure().GetFluxScheme(flowPackage_.GetMeshStruct().level).gradient),
	nodeCenter(flowPackage_.GetFlowConfigure().GetPreprocess().dualMeshFlag),
	turbulenceVector(flowPackage_.GetField().turbulence),
	turbulenceGradientVector(flowPackage_.GetGradientField().gradientTurbulence)
{    
    this->SetTurbulencePointer();
}

TurbulenceManager::~TurbulenceManager()
{
    if (turbulence != nullptr) { delete turbulence; turbulence = nullptr; }
}

void TurbulenceManager::Initialize(const Initialization::Type &initialType)
{
    if (initialType == Initialization::Type::REFERENCE)
	    turbulence->InitializeTurbulentField();

    turbulence->CalculateMuTurbulent();
    turbulence->BoundaryInitialize();
}

void TurbulenceManager::CalculateMuTurbulent()
{
    turbulence->CalculateMuTurbulent();
}

void TurbulenceManager::SetResidualZero()
{
    turbulence->SetResidualZero();
}

void TurbulenceManager::AddConvectiveAverageResidual()
{
    turbulence->AddConvectiveAverageResidual();
}

void TurbulenceManager::AddConvectiveDissipationResidual()
{
    turbulence->AddConvectiveDissipationResidual();
}

void TurbulenceManager::AddDiffusiveResidual()
{
    turbulence->PreCalculate();
    turbulence->AddDiffusiveResidual(); 
}

void TurbulenceManager::AddSourceResidual()
{
    turbulence->AddSourceResidual();
}

int TurbulenceManager::CheckAndLimit()
{
    return turbulence->CheckAndLimit();
}

void TurbulenceManager::InitializeBoundary()
{
    FatalError("TurbulenceManager::InitializeBoundary: error");
	return;

    turbulence->UpdateBoundaryCondition();
}

void TurbulenceManager::UpdateBoundaryCondition()
{
    turbulence->UpdateBoundaryCondition();
}

void TurbulenceManager::UpdateBoundaryResidual()
{
    turbulence->UpdateBoundaryResidual();
}

void TurbulenceManager::SetTurbulencePointer()
{
    switch (this->turbulenceType)
    {
    case Model::INVISCID:
    case Model::LAMINAR:
    {
        turbulence = nullptr;
        break;
    }
    case Model::SPALART_ALLMARAS:
    {
        turbulence = new SpalartAllmaras(flowPackage);
        break;
    }
    case Model::MENTER_SST:
    {
        turbulence = new MenterSST(flowPackage);
        break;
    }
    case Model::MENTER_SST_ML:
    {
        turbulence = new MenterSSTML(flowPackage);
        break;
    }
	case Model::K_EPSILON:
	{
    	turbulence = new KEpsilon(flowPackage);
		break;
	}
    case Model::K_OMEGA_V2:
    {
        turbulence = new KOmegaV2(flowPackage);
        break;
    }
	case Model::MENTER_SST_GAMMARE:
	{
		turbulence = new MenterSSTGammaRe(flowPackage);
		break;
	}
	case Model::SPALART_ALLMARAS_BC:
	{
		turbulence = new SpalartAllmarasBC(flowPackage);
		break;
	}
    case Model::SPALART_ALLMARAS_DES:
    {
		turbulence = new SpalartAllmarasDES(flowPackage);
        break;
    }
    case Model::SPALART_ALLMARAS_DDES:
    {
		turbulence = new SpalartAllmarasDDES(flowPackage);
        break;
    }
	case Model::MENTER_SST_DES:
	{
		turbulence = new MenterSSTDES(flowPackage);
		break;
	}
    case Model::MENTER_SST_DDES:
    {
    	turbulence = new MenterSSTDDES(flowPackage);
    	break;
    }
    default:
    {
        FatalError("TurbulenceManager::SetTurbulencePointer: Turbulence Model is unkown!");
		return;
    }
    }
    return;
}

}//namespace Turbulence
