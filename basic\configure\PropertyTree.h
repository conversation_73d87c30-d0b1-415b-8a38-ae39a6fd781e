﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file PropertyTree.h
//! <AUTHOR>
//! @brief PropertyTree类
//! @date 2022-07-11
//
//------------------------------修改日志----------------------------------------
// 2022-07-11 乔龙
//    说明：建立
//------------------------------------------------------------------------------

#ifndef _basic_configure_PropertyTree_
#define _basic_configure_PropertyTree_

#include "basic/common/BoostLib.h"
#include "basic/common/ConfigUtility.h"
#include "basic/configure/Parameter.h"

namespace PTree = boost::property_tree;

/**
 * @brief 基于boost::property_tree构建的属性树类
 * 
 */
class PropertyTree
{
public:
    /**
     * @brief 空构造函数，创建属性树对象
     * 
     */
    PropertyTree();

    /**
     * @brief 基于xml文件的构造函数
     * 
     * @param[in] fileName xml文件名称
     * @param[in, out] mapPosValue_ 参数映射关系
     */
    PropertyTree(const std::string &fileName, std::vector<std::pair<std::string, Parameter>> *mapPosValue_ = nullptr);

    /**
     * @brief 子节点构造函数
     * 
     * @param[in] propertyTree_ 父树对象
     * @param[in] nodeName 子节点名称
     */
    PropertyTree(PropertyTree &propertyTree_, std::string nodeName);

    /**
     * @brief 析构函数
     * 
     */
    ~PropertyTree();

    /**
    * @brief 获取子节点
    *
    * @param[in] childName 子节点名称
    * @param[in] childTree 子节点
     * @return true 已获取
     * @return false 未获取
    */
    bool GetChildTree(std::string childName, PropertyTree &childTree);

    /**
    * @brief 是否包含子节点
    *
    * @param[in] childName 子节点名称
     * @return true 已获取
     * @return false 未获取
    */
    bool HasChildTree(std::string childName);

    /**
     * @brief 读取节点下的布尔变量值
     * 
     * @param[in] paraName 变量名称
     * @param[in] defaultValue 默认值
     * @return true 
     * @return false 
     */
    bool ReadBool(std::string paraName, bool defaultValue = false);

    /**
     * @brief 读取节点下的整型变量值
     * 
     * @param[in] paraName 变量名称
     * @param[in] defaultValue 默认值
     * @return int 
     */
    int ReadInt(std::string paraName, int defaultValue = INT_MAX);

    /**
     * @brief 读取节点下的标量变量值
     * 
     * @param[in] paraName 变量名称
     * @param[in] defaultValue 默认值
     * @return Scalar 
     */
    Scalar ReadScalar(std::string paraName, Scalar defaultValue = INF);

    /**
     * @brief 读取节点下的矢量变量值
     * 
     * @param[in] paraName 变量名称
     * @param[in] defaultValue 默认值
     * @return Vector 
     */
    Vector ReadVector(std::string paraName, Vector defaultValue = Vector(INF, INF, INF));

    /**
     * @brief 读取节点下的字符串变量值
     * 
     * @param[in] paraName 变量名称
     * @param capitalFlag 大写转换标识，true为转换
     * @param[in] defaultValue 默认值
     * @return std::string 
     */
    std::string ReadString(std::string paraName, bool capitalFlag, std::string defaultValue = "");

    /**
     * @brief 读取节点下的整型数组变量值
     * 
     * @param[in] paraName 变量名称
     * @param[in] defaultValue 默认值
     * @return std::vector<int> 
     */
    std::vector<int> ReadIntList(std::string paraName, std::vector<int> defaultValue = std::vector<int>{});

    /**
     * @brief 读取节点下的标量数组变量值
     * 
     * @param[in] paraName 变量名称
     * @param[in] defaultValue 默认值
     * @return std::vector<Scalar> 
     */
    std::vector<Scalar> ReadScalarList(std::string paraName, std::vector<Scalar> defaultValue = std::vector<Scalar>{});

    /**
     * @brief 读取节点下的字符串数组变量值
     * 
     * @param[in] paraName 变量名称
     * @param capitalFlag 大写转换标识，true为转换
     * @param[in] defaultValue 默认值
     * @return std::vector<std::string> 
     */
    std::vector<std::string> ReadStringList(std::string paraName, bool capitalFlag, std::vector<std::string> defaultValue = std::vector<std::string>{});

    /**
    * @brief 设置Tree名称
    *
    * @param[in] treeName_ 变量名称
    */
    void SetTreeName(const std::string treeName_ = "") { this->treeName = treeName_; }

	/**
	* @brief 设置Tree名称
	*
	* @param[in] treeName_ 变量名称
	*/
	std::string GetTreeName() { return this->treeName; }

private:
    PTree::ptree ptree; ///< 属性树
    std::string treeName; ///< 属性树名称
    std::vector<std::pair<std::string, Parameter>> *mapPosValue;
};

/**
* @brief 获得指定树上某节点的值
*
* @tparam Type 数据类型
* @param tree_ 节点所在树
* @param nodeName 节点名称
* @param value0 节点初始值
* @param capitalFlag 字符串大小写转换标识
* @return Type 返回值
*/
template <class Type>
Type ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, Type &value0, const bool capitalFlag = true);

/**
* @brief 获得指定树上某节点的单一值
*
* @tparam enumType 数据类型
* @param tree_ 节点所在树
* @param nodeName 节点名称
* @param value0 节点初始值
* @param capitalFlag 字符串大小写转换标识
* @return enumType 返回值
*/
template <class enumType>
enumType ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, enumType &value0, const std::map<std::string, enumType> &map, const std::map<enumType, std::string> &reverseMap)
{
	std::string stringTemp = reverseMap.find(value0)->second;
	ReadNodeValue(tree_, nodeName, stringTemp);
	if (map.find(stringTemp) != map.end()) value0 = map.find(stringTemp)->second;
	else WarningContinue("ReadNodeValue: " + tree_.GetTreeName() + "." + nodeName + " is wrong!");
	return value0;
}

/**
* @brief 获得指定树上某节点的多个值
*
* @tparam enumType 数据类型
* @param tree_ 节点所在树
* @param nodeName 节点名称
* @param value0 节点初始值
* @param capitalFlag 字符串大小写转换标识
* @return std::vector<enumType *> 返回值
*/
template <class enumType>
std::vector<enumType *> ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, std::vector<enumType *> value0, const std::map<std::string, enumType> &map, const std::map<enumType, std::string> &reverseMap)
{
	std::vector<std::string> stringTemp(value0.size());
	for (int i = 0; i < value0.size(); i++) stringTemp[i] = reverseMap.find(*(value0[i]))->second;

	ReadNodeValue(tree_, nodeName, stringTemp);
	for (int k = 0; k<stringTemp.size(); k++)
	{
		const auto &pos1 = map.find(stringTemp[k]);
		if (pos1 != map.end()) (*(value0[k])) = pos1->second;
		else                   WarningContinue("ReadNodeValue: " + tree_.GetTreeName() + "." + nodeName + " is wrong!");
	}

	return value0;
}

#endif