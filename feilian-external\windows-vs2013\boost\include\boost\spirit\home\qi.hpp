/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_QI_MARCH_04_2007_0852PM)
#define BOOST_SPIRIT_QI_MARCH_04_2007_0852PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/action.hpp>
#include <boost/spirit/home/<USER>/auto.hpp>
#include <boost/spirit/home/<USER>/auxiliary.hpp>
#include <boost/spirit/home/<USER>/char.hpp>
#include <boost/spirit/home/<USER>/copy.hpp>
#include <boost/spirit/home/<USER>/binary.hpp>
#include <boost/spirit/home/<USER>/directive.hpp>
#include <boost/spirit/home/<USER>/nonterminal.hpp>
#include <boost/spirit/home/<USER>/numeric.hpp>
#include <boost/spirit/home/<USER>/operator.hpp>
#include <boost/spirit/home/<USER>/parse.hpp>
#include <boost/spirit/home/<USER>/parse_attr.hpp>
#include <boost/spirit/home/<USER>/parser.hpp>
#include <boost/spirit/home/<USER>/skip_over.hpp>
#include <boost/spirit/home/<USER>/string.hpp>
#include <boost/spirit/home/<USER>/what.hpp>
#include <boost/spirit/home/<USER>/stream.hpp>

#endif
