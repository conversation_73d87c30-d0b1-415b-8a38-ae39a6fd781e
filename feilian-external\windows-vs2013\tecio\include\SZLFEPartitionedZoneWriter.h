 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <map>
#include <vector>
#include <boost/unordered_set.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "SZLFEPartitionedZoneHeaderWriter.h"
#include "ZoneWriterAbstract.h"
#include "PartitionTecUtilDecorator.h"
namespace tecplot { namespace ___3933 { class ___1350; class ___1352; class ___2724; class NodeToElemMap; class ZoneInfoCache; class ItemSetIterator; class SZLFEPartitionedZoneWriter : public ___4709 { public: SZLFEPartitionedZoneWriter( ItemSetIterator&              varIter, ___4636                   zone, ___4636                   ___341, std::vector<___372> const& ___4564, ___372                     ___4499, ___37&                   ___36, ZoneInfoCache&                zoneInfoCache); virtual ~SZLFEPartitionedZoneWriter(); protected: struct NeighborCellSubzoneInfo { std::vector<___2718> m_nodes; std::vector<___2718> m_neighborNodes; std::vector<boost::unordered_set<___2090::SubzoneOffset_t> > m_cellSubzones; std::vector<std::vector<___2479> > m_varMinMaxes; }; typedef std::map<___2090::___2980, NeighborCellSubzoneInfo> NeighborCellSubzoneInfoMap; void gatherNeighborCellSubzoneInfo(NeighborCellSubzoneInfoMap& neighborCellSubzoneInfoMap, ___2090::___2980 ___2977, ___4352 ___2843, ___2724& ___2723, std::vector<___1352> const& fieldDatas, ___1350 const& zoneInfo); virtual uint64_t zoneConnectivityFileSize(bool ___2002); virtual uint64_t zoneDataFileSize(bool ___2002); virtual uint64_t zoneHeaderFileSize(bool ___2002); virtual ___372 writeZoneConnectivity(FileWriterInterface& szpltFile); virtual ___372 writeZoneData(FileWriterInterface& szpltFile); virtual ___372 writeZoneHeader(FileWriterInterface& szpltFile); SZLFEPartitionedZoneHeaderWriter m_headerWriter; ZoneInfoCache& ___2680; PartitionTecUtilDecorator m_partitionTecUtil; ___2240<int32_t> m_partitionFileNums; UInt64Array m_partitionHeaderFilePositions; UInt32Array m_partitionNumCells; UInt32Array m_partitionNumNodes; VarZoneMinMaxArray m_varPartitionMinMaxes; std::map<___4636, boost::shared_ptr<___4709> > m_partitionWriters; private: void exchangeGhostInfo(std::vector<boost::shared_ptr<___1350> >& partitionInfos); void createPartitionWriters(); }; }}
