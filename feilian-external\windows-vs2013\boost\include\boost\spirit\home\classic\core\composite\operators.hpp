/*=============================================================================
    Copyright (c) 1998-2003 <PERSON>
    Copyright (c) 2001 <PERSON>
    Copyright (c) 2002 <PERSON><PERSON><PERSON> Kaiser
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_OPERATORS_HPP)
#define BOOST_SPIRIT_OPERATORS_HPP

#include <boost/spirit/home/<USER>/core/composite/sequence.hpp>
#include <boost/spirit/home/<USER>/core/composite/sequential_and.hpp>
#include <boost/spirit/home/<USER>/core/composite/sequential_or.hpp>
#include <boost/spirit/home/<USER>/core/composite/alternative.hpp>
#include <boost/spirit/home/<USER>/core/composite/difference.hpp>
#include <boost/spirit/home/<USER>/core/composite/intersection.hpp>
#include <boost/spirit/home/<USER>/core/composite/exclusive_or.hpp>
#include <boost/spirit/home/<USER>/core/composite/kleene_star.hpp>
#include <boost/spirit/home/<USER>/core/composite/positive.hpp>
#include <boost/spirit/home/<USER>/core/composite/optional.hpp>
#include <boost/spirit/home/<USER>/core/composite/list.hpp>

#endif
