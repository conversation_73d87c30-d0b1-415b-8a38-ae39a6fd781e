/*==============================================================================
    Copyright (c) 2001-2010 <PERSON>
    Copyright (c) 2010-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    namespace placeholders
    {
        typedef expression::argument<1>::type arg1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type arg2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type arg3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type arg4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type arg5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type arg6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type arg7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type arg8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type arg9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type arg10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type arg11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type arg12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type arg13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type arg14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type arg15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type arg16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type arg17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type arg18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type arg19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type arg20_type BOOST_ATTRIBUTE_UNUSED;
        typedef expression::argument<1>::type _1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type _2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type _3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type _4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type _5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type _6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type _7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type _8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type _9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type _10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type _11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type _12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type _13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type _14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type _15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type _16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type _17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type _18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type _19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type _20_type BOOST_ATTRIBUTE_UNUSED;
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED arg1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED arg2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED arg3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED arg4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED arg5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED arg6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED arg7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED arg8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED arg9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED arg10 = {{{}}}; expression::argument<11>::type const BOOST_ATTRIBUTE_UNUSED arg11 = {{{}}}; expression::argument<12>::type const BOOST_ATTRIBUTE_UNUSED arg12 = {{{}}}; expression::argument<13>::type const BOOST_ATTRIBUTE_UNUSED arg13 = {{{}}}; expression::argument<14>::type const BOOST_ATTRIBUTE_UNUSED arg14 = {{{}}}; expression::argument<15>::type const BOOST_ATTRIBUTE_UNUSED arg15 = {{{}}}; expression::argument<16>::type const BOOST_ATTRIBUTE_UNUSED arg16 = {{{}}}; expression::argument<17>::type const BOOST_ATTRIBUTE_UNUSED arg17 = {{{}}}; expression::argument<18>::type const BOOST_ATTRIBUTE_UNUSED arg18 = {{{}}}; expression::argument<19>::type const BOOST_ATTRIBUTE_UNUSED arg19 = {{{}}}; expression::argument<20>::type const BOOST_ATTRIBUTE_UNUSED arg20 = {{{}}};
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED _1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED _2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED _3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED _4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED _5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED _6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED _7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED _8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED _9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED _10 = {{{}}}; expression::argument<11>::type const BOOST_ATTRIBUTE_UNUSED _11 = {{{}}}; expression::argument<12>::type const BOOST_ATTRIBUTE_UNUSED _12 = {{{}}}; expression::argument<13>::type const BOOST_ATTRIBUTE_UNUSED _13 = {{{}}}; expression::argument<14>::type const BOOST_ATTRIBUTE_UNUSED _14 = {{{}}}; expression::argument<15>::type const BOOST_ATTRIBUTE_UNUSED _15 = {{{}}}; expression::argument<16>::type const BOOST_ATTRIBUTE_UNUSED _16 = {{{}}}; expression::argument<17>::type const BOOST_ATTRIBUTE_UNUSED _17 = {{{}}}; expression::argument<18>::type const BOOST_ATTRIBUTE_UNUSED _18 = {{{}}}; expression::argument<19>::type const BOOST_ATTRIBUTE_UNUSED _19 = {{{}}}; expression::argument<20>::type const BOOST_ATTRIBUTE_UNUSED _20 = {{{}}};
    }
    namespace arg_names
    {
        typedef expression::argument<1>::type arg1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type arg2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type arg3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type arg4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type arg5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type arg6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type arg7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type arg8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type arg9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type arg10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type arg11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type arg12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type arg13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type arg14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type arg15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type arg16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type arg17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type arg18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type arg19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type arg20_type BOOST_ATTRIBUTE_UNUSED;
        typedef expression::argument<1>::type _1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type _2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type _3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type _4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type _5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type _6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type _7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type _8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type _9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type _10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type _11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type _12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type _13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type _14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type _15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type _16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type _17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type _18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type _19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type _20_type BOOST_ATTRIBUTE_UNUSED;
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED arg1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED arg2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED arg3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED arg4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED arg5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED arg6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED arg7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED arg8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED arg9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED arg10 = {{{}}}; expression::argument<11>::type const BOOST_ATTRIBUTE_UNUSED arg11 = {{{}}}; expression::argument<12>::type const BOOST_ATTRIBUTE_UNUSED arg12 = {{{}}}; expression::argument<13>::type const BOOST_ATTRIBUTE_UNUSED arg13 = {{{}}}; expression::argument<14>::type const BOOST_ATTRIBUTE_UNUSED arg14 = {{{}}}; expression::argument<15>::type const BOOST_ATTRIBUTE_UNUSED arg15 = {{{}}}; expression::argument<16>::type const BOOST_ATTRIBUTE_UNUSED arg16 = {{{}}}; expression::argument<17>::type const BOOST_ATTRIBUTE_UNUSED arg17 = {{{}}}; expression::argument<18>::type const BOOST_ATTRIBUTE_UNUSED arg18 = {{{}}}; expression::argument<19>::type const BOOST_ATTRIBUTE_UNUSED arg19 = {{{}}}; expression::argument<20>::type const BOOST_ATTRIBUTE_UNUSED arg20 = {{{}}};
        expression::argument<1>::type const BOOST_ATTRIBUTE_UNUSED _1 = {{{}}}; expression::argument<2>::type const BOOST_ATTRIBUTE_UNUSED _2 = {{{}}}; expression::argument<3>::type const BOOST_ATTRIBUTE_UNUSED _3 = {{{}}}; expression::argument<4>::type const BOOST_ATTRIBUTE_UNUSED _4 = {{{}}}; expression::argument<5>::type const BOOST_ATTRIBUTE_UNUSED _5 = {{{}}}; expression::argument<6>::type const BOOST_ATTRIBUTE_UNUSED _6 = {{{}}}; expression::argument<7>::type const BOOST_ATTRIBUTE_UNUSED _7 = {{{}}}; expression::argument<8>::type const BOOST_ATTRIBUTE_UNUSED _8 = {{{}}}; expression::argument<9>::type const BOOST_ATTRIBUTE_UNUSED _9 = {{{}}}; expression::argument<10>::type const BOOST_ATTRIBUTE_UNUSED _10 = {{{}}}; expression::argument<11>::type const BOOST_ATTRIBUTE_UNUSED _11 = {{{}}}; expression::argument<12>::type const BOOST_ATTRIBUTE_UNUSED _12 = {{{}}}; expression::argument<13>::type const BOOST_ATTRIBUTE_UNUSED _13 = {{{}}}; expression::argument<14>::type const BOOST_ATTRIBUTE_UNUSED _14 = {{{}}}; expression::argument<15>::type const BOOST_ATTRIBUTE_UNUSED _15 = {{{}}}; expression::argument<16>::type const BOOST_ATTRIBUTE_UNUSED _16 = {{{}}}; expression::argument<17>::type const BOOST_ATTRIBUTE_UNUSED _17 = {{{}}}; expression::argument<18>::type const BOOST_ATTRIBUTE_UNUSED _18 = {{{}}}; expression::argument<19>::type const BOOST_ATTRIBUTE_UNUSED _19 = {{{}}}; expression::argument<20>::type const BOOST_ATTRIBUTE_UNUSED _20 = {{{}}};
    }
