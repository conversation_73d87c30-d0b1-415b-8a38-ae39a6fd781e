# 摄动场数据存储改进方案

## 当前实现问题

### 1. 数据存储分离
- 摄动场数据保存为独立文件：`outputPath/perturbation/caseName_perturbation_mode{i}_part{j}.dat`
- 与传统分区文件（`.bMesh`）分离
- 增加了文件管理和数据一致性维护的复杂性

### 2. 分区处理不完整
- 当前只创建占位符文件
- 缺少节点映射提取逻辑
- 无法正确处理分区摄动场数据

## 改进方案：集成到分区文件

### 方案A：扩展.bMesh文件格式（推荐）

#### 文件结构扩展
```
传统.bMesh文件内容
+
摄动场数据段：
[4字节] 摄动场标识 (PERT)
[4字节] 模态数量
[4字节] 节点数量
对每个模态：
  [4字节] 模态索引
  [节点数量 * 12字节] 摄动位移数据 (dx,dy,dz)
```

#### 实现步骤

1. **修改DecomposeManager::WriteSubMeshFile**
```cpp
void DecomposeManager::WriteSubMeshFile(const std::string &outputPath, 
                                       const std::string &meshName, 
                                       const bool &binary,
                                       PerturbationFieldManager *perturbManager = nullptr)
{
    // 原有网格数据写入
    // ...
    
    // 写入摄动场数据
    if (perturbManager != nullptr)
    {
        WritePerturbationFieldData(file, partID, nodeLocalToGlobal, perturbManager, binary);
    }
}
```

2. **添加摄动场写入方法**
```cpp
void DecomposeManager::WritePerturbationFieldData(std::fstream &file,
                                                 int partID,
                                                 const std::vector<int> &nodeLocalToGlobal,
                                                 PerturbationFieldManager *perturbManager,
                                                 bool binary)
{
    // 写入摄动场标识
    std::string perturbID = "PERT";
    IO::Write(file, perturbID, binary);
    
    // 写入模态数量和节点数量
    int numModes = perturbManager->GetNumModes();
    int nodeNumber = nodeLocalToGlobal.size();
    IO::Write(file, numModes, binary);
    IO::Write(file, nodeNumber, binary);
    
    // 写入各模态数据
    for (int modeIndex = 0; modeIndex < numModes; modeIndex++)
    {
        IO::Write(file, modeIndex, binary);
        
        // 写入该分区的摄动场数据
        for (int localNodeID = 0; localNodeID < nodeNumber; localNodeID++)
        {
            int globalNodeID = nodeLocalToGlobal[localNodeID];
            const Vector &disp = perturbManager->GetNodePerturbationDisplacement(modeIndex, globalNodeID);
            
            Scalar dx = disp.X(), dy = disp.Y(), dz = disp.Z();
            IO::Write(file, dx, binary);
            IO::Write(file, dy, binary);
            IO::Write(file, dz, binary);
        }
    }
}
```

3. **修改SubMesh读取**
```cpp
// 在SubMesh::ReadMesh中添加摄动场数据读取
void SubMesh::ReadPerturbationFieldData(std::fstream &file, bool binary)
{
    std::string perturbID;
    IO::Read(file, perturbID, binary);
    
    if (perturbID == "PERT")
    {
        int numModes, nodeNumber;
        IO::Read(file, numModes, binary);
        IO::Read(file, nodeNumber, binary);
        
        // 初始化摄动场数据结构
        perturbationData.resize(numModes);
        
        for (int modeIndex = 0; modeIndex < numModes; modeIndex++)
        {
            int modeIdx;
            IO::Read(file, modeIdx, binary);
            
            perturbationData[modeIndex].resize(nodeNumber);
            for (int nodeID = 0; nodeID < nodeNumber; nodeID++)
            {
                Scalar dx, dy, dz;
                IO::Read(file, dx, binary);
                IO::Read(file, dy, binary);
                IO::Read(file, dz, binary);
                perturbationData[modeIndex][nodeID] = Vector(dx, dy, dz);
            }
        }
    }
}
```

### 方案B：独立摄动场文件（当前实现的完善）

#### 完善节点映射提取
```cpp
void MeshProcess::ProcessPerturbationFieldPartition(const DecomposeManager &decomposeManager,
                                                   const std::string &outputPath,
                                                   const std::string &meshName,
                                                   int partitionCount,
                                                   int startID)
{
    // 通过扩展DecomposeManager接口获取节点映射
    for (int partID = startID; partID < startID + partitionCount; partID++)
    {
        // 获取该分区的节点映射关系
        std::vector<int> nodeMapping = decomposeManager.GetNodeLocalToGlobal(partID);
        
        // 输出摄动场分区文件
        perturbationFieldManager->OutputPartitionedPerturbationFields(
            outputPath + "perturbation/", meshName, partID, nodeMapping);
    }
}
```

#### 需要扩展DecomposeManager接口
```cpp
class DecomposeManager
{
public:
    // 添加公共接口
    std::vector<int> GetNodeLocalToGlobal(int partID) const;
    std::vector<int> GetElementLocalToGlobal(int partID) const;
    
private:
    // 现有的LocalToGlobalInfo需要保存以供后续访问
    std::vector<LocalToGlobalInfo> partitionInfo;
};
```

## 推荐方案：方案A

### 优势
1. **数据一致性**：摄动场数据与网格数据在同一文件中
2. **文件管理简化**：减少文件数量，便于管理
3. **读取效率**：一次读取获得所有数据
4. **扩展性好**：便于后续添加其他附加数据

### 实施步骤
1. 扩展.bMesh文件格式定义
2. 修改DecomposeManager写入逻辑
3. 修改SubMesh读取逻辑
4. 更新相关接口和文档
5. 进行兼容性测试

### 兼容性考虑
- 添加版本标识，确保向后兼容
- 摄动场数据为可选段，不影响现有功能
- 提供格式转换工具

## 数据访问接口

### SubMesh中添加摄动场访问方法
```cpp
class SubMesh
{
public:
    // 摄动场数据访问
    bool HasPerturbationData() const;
    int GetPerturbationModeCount() const;
    const Vector& GetNodePerturbationDisplacement(int modeIndex, int nodeID) const;
    void ComputeModalSuperposition(const std::vector<Scalar> &modalAmplitudes, 
                                  std::vector<Vector> &nodeDisplacements) const;
    
private:
    std::vector<std::vector<Vector>> perturbationData; // [modeIndex][nodeID]
    bool hasPerturbationData;
};
```

## 总结

通过将摄动场数据集成到传统的.bMesh分区文件中，可以：
- 简化数据管理
- 提高数据一致性
- 便于后续的颤振计算使用
- 保持系统架构的统一性

这种方案更符合CFD软件的数据管理模式，建议优先实施。
