﻿#ifndef _particle_contact_contactSearch_ContactSearchPW_
#define _particle_contact_contactSearch_ContactSearchPW_

#include "basic/geometry/Geometry.h"
#include "feilian-specialmodule/particle/basic/IndexVector.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"
#include "feilian-specialmodule/particle/contact/ContactList.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class SearchParticleWall
{
private:
    const int &numParticles; ///< 当前颗粒数量
    std::vector<Particle *> &particles; ///< 颗粒容器
    const Scalar &dt; ///< 颗粒运动积分步长
    const int &iterNumber; ///< 迭代步数
    ContactList *contactListPW; ///< 壁面接触列表
    Geometry::Geometry *geometry; ///< 几何信息

    std::vector<std::vector<int>> nearWallParticleRange; ///< 近壁面颗粒范围
    std::vector<int> nearWallParticleID; ///< 近壁面颗粒编号

    int lastIterStep; ///< 迭代步

public:
    
    /**
     * @brief 近壁面颗粒搜索对象
     * 
     * @param numParticles_ 当前颗粒数量
     * @param particels_ 颗粒容器
     * @param dt_ 颗粒运动积分步长
     * @param iterNumber_ 迭代步数
     * @param contactListPW_ 壁面接触列表
     * @param geometry_ 几何
     */
    SearchParticleWall(const int &numParticles_, std::vector<Particle *> &particels_,
                     const Scalar &dt_, const int &iterNumber_, ContactList *contactListPW_,
                     Geometry::Geometry *geometry_);
    
    /**
     * @brief 初始化函数
     * 
     */
    void Initialize();
    
    /**
     * @brief 更新当前壁面的近壁面颗粒信息
     * 
     * @return true 
     * @return false 
     */
    bool FindNearWallParticle();
    
    /**
     * @brief 颗粒与壁面接触搜索
     * 
     */
    void FindContacts();
    
    /**
     * @brief 重置下一次更新近壁面颗粒信息的步数
     * 
     */
    void ResetSearch();

private:
    /**
     * @brief 更新当前壁面的近壁面颗粒信息
     * 
     * @param wallID 壁面索引
     * @param dx 近壁面颗粒距离判据
     */
    void FindNearWallParticle(const int &wallID, const Scalar &dx);
};

} // namespace Contact

} // namespace Particle

#endif