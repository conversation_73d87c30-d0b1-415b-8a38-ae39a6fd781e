﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SourceFluxScheme.h
//! <AUTHOR>
//! @brief NS方程源项通量计算类。
//! @date 2022-02-23
//
//------------------------------修改日志----------------------------------------
// 2022-02-23 乔龙
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_sourceFluxScheme_SourceFluxScheme_
#define _sourceFlow_fluxScheme_sourceFluxScheme_SourceFluxScheme_

#include "sourceFlow/fluxScheme/FlowFluxScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 源项通量命名空间
 * 
 */
namespace Source
{
/**
 * @brief 流场源项通量基类
 * 用于派生具体通量格式，不能直接实例化
 * 
 */
class  SourceFluxScheme : public FlowFluxScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     */
    SourceFluxScheme(Package::FlowPackage &data);

    /**
     * @brief 析构函数
     * 
     */
    ~SourceFluxScheme();

    /**
     * @brief 通量累加
     * 
     */
    void AddResidual();
};

} // namespace Source
} // namespace Flow
} // namespace Flux
#endif 