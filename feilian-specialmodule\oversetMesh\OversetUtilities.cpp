﻿#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

void OversetMesh::CollectGlobalBCfaces(List<List<Face>> &globalFaces, List<List<List<Vector>>> &globalFaceNodes, const Boundary::Type &bcType)
{
	// 全局重叠边界容器
	globalFaces.clear();
	globalFaceNodes.clear();
	globalFaces.resize(n_Zones);
	globalFaceNodes.resize(n_Zones);
	
	List<List<Vector>> globalFaceCenters;
	List<List<Vector>> globalFaceNormals;
	List<List<Scalar>> globalFaceArea;
	globalFaceCenters.resize(n_Zones);
	globalFaceNormals.resize(n_Zones);
	globalFaceArea.resize(n_Zones);
	
	// 将各子域边界面元中心收集至全局容器的对应子域中
	for (int i = 0; i < localMesh->GetBoundarySize(); i++)
	{
		const int &globalID = localMesh->GetBoundaryIDGlobal(i);
		if (bcType == Boundary::Type::WALL)
		{
			if (!flowConfig.JudgeWallGlobal(globalID))
			{
				continue;
			}
		}
		else if (bcType == Boundary::Type::OVERSET)
		{
			if (!flowConfig.JudgeOversetGlobal(globalID))
			{
				continue;
			}
		}

		// 获取边界所属子域
		const int &BoundaryZoneID = zoneManager->GetBoundaryZoneID(i);
		int faceNum = localMesh->GetBoundaryFaceSize(i);
		for (int j = 0; j < faceNum; j++)
		{
			const int &faceID = localMesh->GetBoundaryFaceID(i,j);
			const Face &face = localMesh->GetFace(faceID);

			globalFaceCenters[BoundaryZoneID].push_back(face.GetCenter());
			globalFaceNormals[BoundaryZoneID].push_back(face.GetNormal());
			globalFaceArea[BoundaryZoneID].push_back(face.areaMag);

			List<Vector> faceNodes;
			for (int k = 0; k < face.GetNodeSize(); k++)
			{
				faceNodes.push_back(this->localMesh->GetNode(face.GetNodeID(k)));
			}
			globalFaceNodes[BoundaryZoneID].push_back(faceNodes);
		}
	}

	if (nProcessor > 1) // 并行计算时合并各进程壁面信息
	{
		for (int zoneID = 0; zoneID < n_Zones; zoneID++)
		{
			this->AllGatherAndMergeList(globalFaceCenters[zoneID]);
			this->AllGatherAndMergeList(globalFaceNormals[zoneID]);
			this->AllGatherAndMergeList(globalFaceArea[zoneID]);
			this->AllGatherAndMergeList(globalFaceNodes[zoneID]);
		}
	}

	// 创建面并加入容器
	for (int i = 0; i < n_Zones; i++)
	{
		for (int j = 0; j < globalFaceCenters[i].size(); ++j)
		{
			Face face;
			face.center = globalFaceCenters[i][j];
			face.normal = globalFaceNormals[i][j];
			face.areaMag = globalFaceArea[i][j];
			globalFaces[i].push_back(face);
		}
	}
}

KdtTree *OversetMesh::CreateFaceKdtTree(const List<Face> &faceList)
{
	KdtTree *kdtree;

	if (faceList.size() == 0)
	{
		kdtree = NULL;
		return kdtree;
	}

	List<List<Scalar>> faceCenterCloud;
	List<int> faceIDCloud;
	List<Scalar> temp;
	for (int i = 0; i < faceList.size(); i++)
	{
		const Vector &faceCenter = faceList[i].GetCenter();
		temp = {faceCenter.X(), faceCenter.Y(), faceCenter.Z()};
		faceCenterCloud.push_back(temp);
		faceIDCloud.push_back(i);
	}

	kdtree = new KdtTree(dim, faceCenterCloud, faceIDCloud);

	faceCenterCloud.clear();
	faceIDCloud.clear();

	return kdtree;
}

bool OversetMesh::NodeInOversetBC(const Vector &node)
{
	for (int zoneID = 0; zoneID < globalOversetFaceTrees.size(); zoneID++)
	{
		if (globalOversetFaceTrees[zoneID])
		{
			List<Scalar> temp = {node.X(), node.Y(), node.Z()};
			Pair<Scalar, int> nearest = globalOversetFaceTrees[zoneID]->FindNearestNeighbour(temp);

			Face &face = globalOversetFaces[zoneID][nearest.second];

			Vector nodeToFace = face.GetCenter() - node;
			Scalar d = face.GetNormal() & nodeToFace;

			if (d > 0) // 点到面心的矢量与面心法向同向，在重叠边界内部
			{
				return true;
			}
		}
	}
	return false;
}

bool OversetMesh::NodeInWall(const int &nodeID, const int &zoneID)
{
	Face &face = globalWallFaces[zoneID][nearestWallFaceID[zoneID][nodeID]];
	Node &node = localMesh->v_node[nodeID];

	Vector nodeToFace = face.GetCenter() - node;
	Scalar d = face.GetNormal() & nodeToFace;

	if (d < 0) // 点到面心的矢量与面心法向反向，在物面内部
	{
		return true;
	}
	return false;
}

bool OversetMesh::NodeInElem(const Node &node, const Element &elem)
{
	//TODO:测试发现，面由三个以上点构成时可能会有一些扭曲，在极个别情况下会造成node实际在单元里面，但到面心的矢量略微偏向内部，从而判断不再单元里
	for (int faceI = 0; faceI < elem.GetFaceSize(); faceI++)
	{
		const int &faceID = elem.GetFaceID(faceI);
		const Face &face = localMesh->GetFace(faceID); // 引用当前face
		Vector elemCenterToFace = face.GetCenter() - elem.GetCenter();
		Vector nodeToFace = face.GetCenter() - node;
		Scalar d1 = face.GetNormal() & elemCenterToFace; // 用于判断elemCenter到面心的矢量与面法向是否同向
		Scalar d2 = face.GetNormal() & nodeToFace;		 // 用于判断node到面心的矢量与面法向是否同向
		Scalar d = d1 * d2;
		if (d < 0) // 贡献单元中心到面心的矢量 与 elemCenter到面心的矢量 相对面法矢不同向，存在不同向即可确定位于单元外部
		{
			return false;
		}
	}
	return true; // 位于所有面内部，则位于贡献单元内部
}

bool OversetMesh::NodeInFace(const Node &node, const Face &face, const List<Vector> &faceNodes)
{

}

void OversetMesh::GetNodeNeighbour(const int &elemID, Set<int> &neiIDlist)
{
	if (elemID < 0 || elemID >= localMesh->GetElementNumberReal())
	{
		FatalError("element ID out of range in OversetMesh::GetNodeNeighbour! procID: " + ToString(processorID) + ", elemID: " + ToString(elemID));
	}
	neiIDlist.clear();
	const Element &elem = localMesh->GetElement(elemID);
	const int &nodeSize = elem.GetNodeSize();
	for (int i = 0; i < nodeSize; i++)
	{
		const int &nodeID = elem.GetNodeID(i);
		List<int> &nodeNei = nodeNeiElem[nodeID];
		for (int j = 0; j < nodeNei.size(); j++)
		{
			if (nodeNei[j] != elemID)
			{
				neiIDlist.insert(nodeNei[j]);
			}
		}
	}
	// 把相邻的并行虚单元也加入列表，使得推进时能跨过并行边界
	const int &faceSize = elem.GetFaceSize();
	for (int i = 0; i < faceSize; i++)
	{
		const Face &face = localMesh->GetFace(elem.GetFaceID(i));
		const int &neiID =  face.GetNeighborID();
		if (localMesh->GetElement(neiID).GetElemType() == Element::ElemType::ghostParallel)
		{
			neiIDlist.insert(neiID);
		}
	}
	
	if (0 == neiIDlist.size()) 
	{
		std::stringstream ss;
		ss << "element " << elemID << " is found to be isolated by Mesh::SearchElementNeighbor";
		WarningContinue(ss.str());
	}
}

void OversetMesh::GetNodeNeighbourCalc(const int &elemID, List<int> &nodeNeiCalc)
{
	Set<int> nodeNeiSet;
	this->GetNodeNeighbour(elemID, nodeNeiSet);

	for (Set<int>::iterator it = nodeNeiSet.begin(); it != nodeNeiSet.end(); it++)
	{
		const int &neiType = elemTypeField->GetValue(*it);
		if (neiType == OversetElemType::CALCULATED)
		{
			nodeNeiCalc.push_back(*it);
		}
	}
}

void OversetMesh::GetNodeNeighbourHole(const int &elemID, List<int> &nodeNeiHole)
{
	nodeNeiHole.clear();
	Set<int> nodeNeiSet;
	this->GetNodeNeighbour(elemID, nodeNeiSet);
	for (Set<int>::iterator it = nodeNeiSet.begin(); it != nodeNeiSet.end(); it++)
	{
		const int &neiType = elemTypeField->GetValue(*it);
		if (neiType == OversetElemType::HOLE)
		{
			nodeNeiHole.push_back(*it);
		}
	}
}

void OversetMesh::GetNodeNeighbour(const Face &face, Set<int> &nodeNei)
{
	nodeNei.clear();
	const int &nodeSize = face.GetNodeSize();
	for (int i = 0; i < nodeSize; i++)
	{
		const int &nodeID = face.GetNodeID(i);
		List<int> &neiElem = nodeNeiElem[nodeID];
		nodeNei.insert(neiElem.begin(), neiElem.end());
	}
}

void OversetMesh::GetNodeNeighbourHole(const Face &face, Set<int> &nodeNeiHole)
{
	nodeNeiHole.clear();

	Set<int> nodeNei;
	this->GetNodeNeighbour(face, nodeNei);
	for (Set<int>::iterator it = nodeNei.begin(); it != nodeNei.end(); it++)
	{
		const int &neiType = elemTypeField->GetValue(*it);
		if (neiType == OversetElemType::HOLE)
		{
			nodeNeiHole.insert(*it);
		}
	}
}

void OversetMesh::GetNodeNeighbourCalc(const Face &face, Set<int> &nodeNeiHole)
{
	nodeNeiHole.clear();

	Set<int> nodeNei;
	this->GetNodeNeighbour(face, nodeNei);
	for (Set<int>::iterator it = nodeNei.begin(); it != nodeNei.end(); it++)
	{
		const int &neiType = elemTypeField->GetValue(*it);
		if (neiType == OversetElemType::CALCULATED)
		{
			nodeNeiHole.insert(*it);
		}
	}
}

void OversetMesh::GetNodeNeighbourCalc(const Set<Acceptor> &srcAcpt, Set<int> &nodeNeiCalc)
{
	if (srcAcpt.size() == 0)
	{
		return;
	}

	nodeNeiCalc.clear();
	for (auto it = srcAcpt.begin(); it != srcAcpt.end(); it++)
	{
		List<int> temp;
		this->GetNodeNeighbourCalc(it->GetAcceptorID(), temp);
		nodeNeiCalc.insert(temp.begin(), temp.end());
	}
}

void OversetMesh::GetNodeNeighbourHole(const Set<Acceptor> &srcAcpt, Set<int> &nodeNeiHole)
{
	if (srcAcpt.size() == 0)
	{
		return;
	}

	nodeNeiHole.clear();
	for (auto it = srcAcpt.begin(); it != srcAcpt.end(); it++)
	{
		List<int> temp;
		this->GetNodeNeighbourHole(it->GetAcceptorID(), temp);
		nodeNeiHole.insert(temp.begin(), temp.end());
	}
}

void OversetMesh::GetNodeNeighbourAcpt(const int &elemID, Set<int> &nodeNeiAcpt)
{
	nodeNeiAcpt.clear();
	Set<int> nodeNeiSet;
	this->GetNodeNeighbour(elemID, nodeNeiSet);
	for (Set<int>::iterator it = nodeNeiSet.begin(); it != nodeNeiSet.end(); it++)
	{
		const int &neiType = elemTypeField->GetValue(*it);
		if (neiType == OversetElemType::ACCEPTOR || neiType > 5)
		{
			nodeNeiAcpt.insert(*it);
		}
	}
}

void OversetMesh::GetFaceNeighbour(const int &elemID, List<int> &neiIDlist)
{
	if (elemID < 0 || elemID >= localMesh->GetElementNumberReal())
	{
		FatalError("element ID out of range in Overset::GetFaceNeighbor!");
	}
	neiIDlist.clear();
	const int &faceSize =localMesh->GetElement(elemID).GetFaceSize();
	for (int i = 0; i < faceSize; i++)
	{
		const int &faceID= localMesh->GetElement(elemID).GetFaceID(i);
		const int &ownerID = localMesh->GetFace(faceID).GetOwnerID();
		const int &neiID = localMesh->GetFace(faceID).GetNeighborID();
		if ((-1 != ownerID) && (elemID != ownerID))
		{
			neiIDlist.push_back(ownerID);
		}
		if ((-1 != neiID) && (elemID != neiID))
		{
			neiIDlist.push_back(neiID);
		}
	}
	if (0 == neiIDlist.size()) // 仅包含其自身
	{
		std::stringstream ss;
		ss << "element " << elemID << " is found to be isolated by Mesh::SearchElementNeighbor";
		WarningContinue(ss.str());
	}
}

void OversetMesh::GetFaceNeighbourCalc(const int &elemID, List<int> &calcNeiID)
{
	if (elemID >= localMesh->GetElementNumberReal())
	{
		return;
	}
	
	List<int> neiIDlist;
	this->GetFaceNeighbour(elemID, neiIDlist);

	for (int j = 0; j < neiIDlist.size(); j++)
	{
		const int &neiType = elemTypeField->GetValue(neiIDlist[j]);
		if (neiType == OversetElemType::CALCULATED)
		{
			calcNeiID.push_back(neiIDlist[j]);
		}
	}
}

void OversetMesh::GetFaceNeighbourAcpt(const int &elemID, List<int> &acptNeiID)
{
	if (elemID >= localMesh->GetElementNumberReal())
	{
		return;
	}
	
	List<int> neiIDlist;
	this->GetFaceNeighbour(elemID, neiIDlist);

	for (int j = 0; j < neiIDlist.size(); j++)
	{
		const int &neiType = elemTypeField->GetValue(neiIDlist[j]);
		if (neiType == OversetElemType::ACCEPTOR)
		{
			acptNeiID.push_back(neiIDlist[j]);
		}
	}
}

void OversetMesh::GetFaceNeighbourHole(const Acceptor &srcAcpt, List<int> &holeNeiID)
{
	const int &elemID = srcAcpt.GetAcceptorID();
	if (elemID >= localMesh->GetElementNumberReal())
	{
		return;
	}
	
	List<int> neiIDlist;
	this->GetFaceNeighbour(elemID, neiIDlist);

	for (int j = 0; j < neiIDlist.size(); j++)
	{
		const int &neiType = elemTypeField->GetValue(neiIDlist[j]);
		if (neiType == OversetElemType::HOLE)
		{
			holeNeiID.push_back(neiIDlist[j]);
		}
	}
}

void OversetMesh::GetFaceNeighbourHole(const Face &face, Set<int> &holeNeiID)
{
	holeNeiID.clear();
	const int &ownerID = face.GetOwnerID();
	const int &neiID = face.GetNeighborID();
	const int &ownerType = elemTypeField->GetValue(ownerID);
	const int &neiType = elemTypeField->GetValue(neiID);

	if (ownerType == OversetElemType::HOLE)
	{
		holeNeiID.insert(ownerID);
	}
	if (neiType == OversetElemType::HOLE)
	{
		holeNeiID.insert(neiID);
	}
}

void OversetMesh::GetFaceNeighbourHole(const Set<Acceptor> &srcAcpt, Set<int> &holeNeiID)
{
	for (auto it = srcAcpt.begin(); it != srcAcpt.end(); it++)
	{
		List<int> temp;
		this->GetFaceNeighbourHole(*it, temp);
		holeNeiID.insert(temp.begin(), temp.end());
	}
}


bool OversetMesh::JudgeOversetPatchElem(const int &elemID)
{
	auto it = oversetPatchElemID.find(elemID);
	if (it != oversetPatchElemID.end())
	{
		return true;
	}
	return false;
}

bool OversetMesh::isNodeInPolygon(const Node &node, const List<Node> &polygon)
{
	const int &nodeNum = polygon.size();
	if (nodeNum < 3)
	{
		return false;
	}

	bool inside = false;
	Scalar xIntersect;
	Node n1 = polygon[0];
	for (int i = 1; i <= nodeNum; i++)
	{
		const Node &n2 = polygon[i % nodeNum]; //闭合多边形，最后一条边连接最后一个顶点和第一个顶点
		if (node.Y() > Min(n1.Y(), n2.Y()) && node.Y() <= Max(n1.Y(), n2.Y()) && node.X() <= Max(n1.X(), n2.X())) //检查点是否在边的垂直范围内
		{
			if (n1.Y() != n2.Y()) // 计算射线与边的交点的x坐标
			{
				xIntersect = (node.Y() - n1.Y()) * (n2.X() - n1.X()) / (n2.Y() - n1.Y()) + n1.X();
				if (fabs(xIntersect - node.X()) < 1e-8) //点正好在边上
				{
					return true;
				}
				if (n1.X() == n2.X() || node.X() < xIntersect) //如果边是垂直的或点的x坐标小于交点的x坐标，翻转inside标志
				{
					inside = !inside;
				}
			}
		}
		n1 = n2; // 移到下一边
	}
	return inside;
}

bool OversetMesh::isSegmentIntersectPlane(const Node &n1, const Node &n2, const Face &f)
{
	const Vector &normal = f.GetNormal();

	// 平面方程：normal.x * x + normal.y * y + normal.z * z + d = 0
	const Node &faceNode0 = localMesh->GetNode(f.GetNodeID(0));
	Scalar d = -1 * (normal & faceNode0);
	// 计算线段两个端点到平面的距离
	Scalar d1 = (normal & n1) + d;
	Scalar d2 = (normal & n2) + d;
	// 如果线段端点在平面两侧，则与平面相交
	if (d1 * d2 < 0)
	{
		return true;
	}
	// 如果线段的一个端点在平面上，也视为相交
	if (fabs(d1) < 1e-8 || fabs(d2) < 1e-8)
	{
		return true;
	}
	return false;
}

bool OversetMesh::isSegmentIntersectPolygon(const Node &n1, const Node &n2, const Face &f)
{
	// 首先判断线段是否与f所在无限平面相交
	if (!this->isSegmentIntersectPlane(n1, n2, f))
	{
		return false;
	}

	// 计算线段与平面交点
	const Vector &normal = f.GetNormal();
	Vector lineDirection = n2 - n1;
	Scalar denominator = normal & lineDirection;
	List<Node> faceNodes;
	for (int i = 0; i < f.GetNodeSize(); ++i)
	{
		const Node &faceNode = localMesh->GetNode(f.GetNodeID(i));
		faceNodes.push_back(Node(faceNode.X(), faceNode.Y(), faceNode.Z()));
	}
	if (fabs(denominator) < 1e-8) // 线段与平面平行，检查端点是否在多边形内部
	{
		if (isNodeInPolygon(n1,faceNodes))
		{
			return true;
		}
		if (isNodeInPolygon(n2,faceNodes))
		{
			return true;
		}
		return false;
	}

	const Node &faceNode0 = localMesh->GetNode(f.GetNodeID(0));
	Scalar t = -((normal & n1) + (normal & faceNode0))/denominator;
	if (t<0 ||t>1)
	{
		return false;
	}
	Node intersectionPoint = n1+ lineDirection*t;

	// 将交点投影到多边形平面，判断是否在多边形内部
	// 为了简化计算，将多边形投影到XY平面上
	Node projectedNode(intersectionPoint.X(), intersectionPoint.Y(), 0);
	List<Node> projectedPolygon;
	for (int i = 0; i < f.GetNodeSize(); ++i)
	{
		const Node &faceNode = localMesh->GetNode(f.GetNodeID(i));
		projectedPolygon.push_back(Node(faceNode.X(), faceNode.Y(), 0));
	}

	return isNodeInPolygon(projectedNode, projectedPolygon);
}