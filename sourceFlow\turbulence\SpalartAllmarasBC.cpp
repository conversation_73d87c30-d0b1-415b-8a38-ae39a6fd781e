﻿#include "sourceFlow/turbulence/SpalartAllmarasBC.h"

namespace Turbulence
{

SpalartAllmarasBC::SpalartAllmarasBC(Package::FlowPackage &flowPackage)
    :SpalartAllmaras(flowPackage)
{
}

SpalartAllmarasBC::~SpalartAllmarasBC()
{
}

void SpalartAllmarasBC::AddSourceResidual()
{
	// 仅细网格计算湍流方程的源项
	// if (currentLevel > 0) return;

	//源项残差按单元循环
	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar &rhoI = rho.GetValue(elementID);
		const Scalar &muI = muLaminar.GetValue(elementID);
		const Scalar &mut = muTurbulent->GetValue(elementID);

		const Scalar &distance = mesh->GetNearWallDistance(elementID);
		const Scalar y = Max(distance, SMALL);
		const Scalar y2 = y * y;

		const Tensor omega = gradientU.GetValue(elementID).AntiSymm();
		const Scalar S = sqrt(2.0 * (omega && omega));

		const Scalar Tu = flowPackage.GetFlowConfigure().GetFlowReference().turbulentIntensity * 100.0;
		const Scalar Rev = rhoI * y2 / muI * S;
		const Scalar ReTheta = Rev / 2.193;
		const Scalar ReThetaC = 803.73 * pow(Tu + 0.6067, -1.027);
		const Scalar term1Temp = Max(ReTheta - ReThetaC, 0.0);
		const Scalar term1 = term1Temp / (ReThetaC * 0.002);
		const Scalar sqrtterm1 = sqrt(term1);

		const Scalar term2 = Max(mut / (0.02 * muI), 0.0);
		const Scalar sqrtterm2 = sqrt(term2);
		const Scalar gammaBC = 1.0 - exp(-sqrtterm1 - sqrtterm2);

		SpalartAllmaras::AddSourceResidual(elementID, 1.0, gammaBC);
	}
}


}//namespace Turbulence