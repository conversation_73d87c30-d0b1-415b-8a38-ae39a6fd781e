 #pragma once
#include "MASTER.h"
#include "GLOBAL.h"
#include "TECGLBL.h"
#include "IJK.h"
typedef struct GhostInfo_s const* GhostInfo_pa; namespace tecplot { namespace ___3933 { class ___37 { public: virtual ~___37() {} virtual void      ___3817(char** ___3855) const = 0; virtual void      ___3827(___3839* ___3819) const = 0; virtual ___2227 ___3832(___3839 ___3819) const = 0; virtual char*     ___3833(___3839 ___3819, ___2227 ___3853) const = 0; virtual ___264 ___235() const = 0; virtual ___264 ___274(___4636 zone) const = 0; virtual ___264 ___273(___4352 ___4336) const = 0; virtual int32_t    ___247(___264 ___265) const = 0; virtual void       ___243(___264 ___265, int32_t index, char** ___2685, ___90* ___4314, AuxDataType_e* type, ___372* ___3361) const = 0; virtual ___372   ___896(void) const = 0; virtual ___372   datasetGetTitle(char** datasetTitle) const = 0; virtual int32_t     ___889(void) const = 0; virtual ___3501      datasetGetRelevantZones(double solutionTimeMin, double solutionTimeMax, ___372 ignoreStaticZones) const = 0; virtual ___4636 ___891(void) const = 0; virtual ___4352  ___890(void) const = 0; virtual ___4352  ___4345(char ___214) const = 0; virtual ___372   ___4344(___4352 ___4368, char** ___4362) const = 0; virtual int32_t     ___4343(___4352 ___4368) const = 0; virtual ___372   ___4638(___4636 ___4658) const = 0; virtual ___372   ___4640(___4636 ___4658) const = 0; virtual ___372   ___4641(___4636 ___4658) const = 0; virtual int32_t     ___4613(___4636 ___4658) const = 0; virtual ___372   ___4614(___3501* ___1153) const = 0; virtual void        ___4615(___4636 ___4658, ___1844& ___2715) const = 0; virtual ___372   ___4616(___4636 ___4658, char** ___4652) const = 0; virtual ___4636 ___4617(___4636 ___4658) const = 0; virtual double      ___4618(___4636 ___4658) const = 0; virtual ___1172  ___4619(___4636 ___4658) const = 0; virtual ZoneType_e  ___4620(___4636 ___4658) const = 0; virtual ___372   ___4353(___4352 ___4368) const = 0; virtual ___372   varGetEnabled(___3501* enabledVars) const = 0; virtual int32_t     solutionTimeGetNumTimeSteps() const = 0; virtual double      solutionTimeGetMinTime() const = 0; virtual double      solutionTimeGetMaxTime() const = 0; virtual ___372   ___3768() const = 0; virtual GeomID_t    ___1592(void) = 0; virtual TextID_t    ___4087(void) = 0; virtual int32_t     ___797(void) = 0; virtual ___372   ___796(___3839* ___2171, int32_t ___4453) = 0; virtual void        ___3779(char const* ___3001, ___372 ___3584, ___372 ___3579) const = 0; virtual void        ___3778(char const* ___3001) const = 0; virtual ___372   ___3769(int PercentDone) const = 0; virtual void        ___3770(void) const = 0; virtual ___372   ___1983(void) const = 0; virtual void        ___858(void) = 0; virtual void        ___859(void) = 0; virtual ___4636 ___544(___3501 ___4684, ___4636 zone) const = 0; virtual ___3501      ___545(___4636 zone) const = 0; virtual ValueLocation_e ___910(___4636 ___4658, ___4352 ___4368) const = 0; virtual ValueLocation_e ___911(___1361 ___1351) const = 0; virtual ___372       ___913(___4636 zone, ___4352 ___4336, double* minVal, double* maxVal) const = 0; virtual ___372       ___912(___1361 ___1351, double* minVal, double* maxVal) const = 0; virtual FieldDataType_e ___923(___4636 ___4658, ___4352 ___4368) = 0; virtual ___1172      ___921(___4636 ___4658, ___4352 ___4368) = 0; virtual ___1361 ___918(___4636 ___4658, ___4352 ___4368) = 0; virtual ___1361 ___915(___4636 ___4658, ___4352 ___4368) = 0; virtual ___1361 ___917(___4636 ___4658, ___4352 ___4368) = 0; virtual ___1361 ___916(___4636 ___4658, ___4352 ___4368) = 0; virtual ___1361 ___924(___4636 ___4658, ___4352 ___4368) = 0; virtual double       ___909(___1361 ___1351, ___81 ___2733) = 0; virtual void         dataValueSetByRef(___1361 ___1351, ___81 ___2733, double ___4298) = 0; virtual void         ___919(___4636 ___4658, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363) = 0; virtual void         ___925(___4636 ___4658, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363) = 0;
virtual ___4636  ___914(___3501 ___4684, ___4636 zone, ___4352 ___4336) const = 0; virtual ___3501       ___922(___4636 ___4658, ___4352 ___4368) const = 0; virtual ___372    ___926(___4636 ___4658, ___4352 ___4368) const = 0; virtual ___1383 ___927(___1361 ___1309) = 0; virtual ___1384 ___928(___1361 ___1309) = 0; virtual FieldDataType_e ___920(___1361 ___1352) = 0; virtual ___2727       ___867(___4636 ___4658) = 0; virtual ___2727       ___869(___4636 ___4658) = 0; virtual ___2718      ___865(___2727 ___2723, ___465 ___468, ___682 ___683) = 0; virtual void             ___870(___2727 ___2723, ___465 ___468, ___682 ___683, ___2718 ___2716) = 0; virtual OffsetDataType_e dataNodeGetRawItemType(___2727 ___2723) = 0; virtual int32_t*         dataNodeGetRawPtrByRef(___2727 ___2723) = 0; virtual int64_t*         dataNodeGetRawPtrByRef64(___2727 ___2723) = 0; virtual ___2742 dataNodeToElemMapGetReadableRef(___4636 ___4658) const = 0; virtual ___465      dataNodeToElemMapGetNumElems(___2742 nodeToElemMap, ___2718 ___2709) const = 0; virtual ___465      dataNodeToElemMapGetElem(___2742 nodeToElemMap, ___2718 ___2709, ___465 elemOffset) const = 0; virtual FaceNeighborMode_e ___836(___4636 ___4658) const = 0; virtual void ___837( ___1292 ___1274, ___2227 ___1144, int32_t   face, int32_t   ___2692, ___2227* ___2691, ___4636* ___2695) const = 0; virtual ___372 ___835( ___1292 ___1274, ___2227 ___1144, int32_t   face, ___3501 ___4) const = 0; virtual int32_t ___838(___1292 ___1274, ___2227 ___1144, int32_t face, ___372* neighborsAreUserSpecified) const = 0; virtual ___1292 ___839(___4636 zone) const = 0; virtual ___372 setAddMember(___3501 set, ___3493 ___2401, ___372 showErr) const = 0; virtual ___3501 setAlloc(___372 showErr) const = 0; virtual void ___3484(___3501* set) const = 0; virtual ___3493 ___3491(___3501 set, ___3493 ___2401) const = 0; virtual ___3493 setGetPrevMember(___3501 set, ___3493 ___2401) const = 0; virtual ___3493 setGetMemberCount(___3501 set) const = 0; virtual ___372 ___3495(___3501 set, ___3493 ___2401) const = 0; virtual ___372 setIsEqual(___3501 ___3477, ___3501 ___3478) const = 0; virtual void setRemoveMember(___3501 set, ___3493 ___2401) const = 0; virtual void ___1557(GeomID_t ___1805, int32_t ___3157, ___2227 ___3141, double* x, double* ___4583) const = 0; virtual void ___1558(GeomID_t ___1805, ___2227 ___3141, double* x, double* ___4583) const = 0; virtual void ___1560(GeomID_t ___1805, int32_t ___3157, ___2227 ___3141, double* x, double* ___4583, double* z) const = 0; virtual void ___1561(GeomID_t ___1805, ___2227 ___3141, double* x, double* ___4583, double* z) const = 0; virtual double ___1564(GeomID_t ___1805) const = 0; virtual ArrowheadAttachment_e ___1565(GeomID_t ___1805) const = 0; virtual double ___1566(GeomID_t ___1805) const = 0; virtual ArrowheadStyle_e ___1567(GeomID_t ___1805) const = 0; virtual double ___1570(GeomID_t ___1805) const = 0; virtual int32_t ___1576(GeomID_t ___1805) const = 0; virtual void ___1577(GeomID_t ___1805, double* ___1824, double* ___4394) const = 0; virtual void ___1591(GeomID_t ___1805, double* ___4574, double* ___4591, double* ___4715) const = 0; virtual Clipping_e ___1593(GeomID_t ___1805) const = 0; virtual ___516 ___1594(GeomID_t ___1805) const = 0; virtual DrawOrder_e ___1595(GeomID_t ___1805) const = 0; virtual ___516 ___1596(GeomID_t ___1805) const = 0; virtual ___372 ___1597(GeomID_t ___1805) const = 0; virtual LinePattern_e ___1598(GeomID_t ___1805) const = 0; virtual double ___1599(GeomID_t ___1805) const = 0; virtual ___372 ___1600(GeomID_t ___1805, char** macroFunctionCmd) const = 0; virtual GeomID_t ___1601(GeomID_t ___1805) const = 0; virtual double ___1602(GeomID_t ___1805) const = 0; virtual CoordSys_e ___1603(GeomID_t ___1805) const = 0; virtual GeomID_t ___1604(GeomID_t ___1805) const = 0; virtual Scope_e ___1605(GeomID_t ___1805) const = 0; virtual GeomForm_e ___1606(GeomID_t ___1805) const = 0; virtual ___4636 ___1607(GeomID_t ___1805) const = 0; virtual ___372 ___1610(GeomID_t ___1805) const = 0; virtual ___2227 ___1619(GeomID_t ___1805, int32_t ___3157) const = 0; virtual ___2227 ___1620(GeomID_t ___1805) const = 0; virtual ___2227 ___1626(GeomID_t ___1805) const = 0;
virtual void ___1628(GeomID_t ___1805, double* ___4458, double* ___1826) const = 0; virtual double ___1648(GeomID_t ___1805) const = 0; virtual ___516 ___4064(TextID_t ___4171) const = 0; virtual ___516 ___4065(TextID_t ___4171) const = 0; virtual double ___4066(TextID_t ___4171) const = 0; virtual double ___4067(TextID_t ___4171) const = 0; virtual TextBox_e ___4068(TextID_t ___4171) const = 0; virtual TextAnchor_e ___4084(TextID_t ___4171) const = 0; virtual void ___4085(TextID_t ___4171, double* ___4574, double* ___4591, double* ___4715) const = 0; virtual double ___4086(TextID_t ___4171) const = 0; virtual Clipping_e ___4088(TextID_t ___4171) const = 0; virtual ___516 ___4089(TextID_t ___4171) const = 0; virtual double ___4090(TextID_t ___4171) const = 0; virtual double ___4091(TextID_t ___4171) const = 0; virtual ___372 ___4092(TextID_t ___4171, char** ___2330) const = 0; virtual TextID_t ___4093(TextID_t ___4171) const = 0; virtual CoordSys_e ___4094(TextID_t ___4171) const = 0; virtual TextID_t ___4095(TextID_t ___4171) const = 0; virtual Scope_e ___4096(TextID_t ___4171) const = 0; virtual Units_e ___4097(TextID_t ___4171) const = 0; virtual ___372 ___4098(TextID_t ___4171, char** ___4126) const = 0; virtual char* ___4099(TextID_t ___4171) const = 0; virtual ___372 ___4100(TextID_t ___4171) const = 0; virtual ___372 ___4101(TextID_t ___4171) const = 0; virtual ___4636 ___4102(TextID_t ___4171) const = 0; virtual ___372 ___4105(TextID_t ___4171) const = 0; virtual ___2664 ___4152() = 0; virtual void ___4153(___2664* mutex) = 0; virtual void ___4154(___2664 mutex) = 0; virtual void ___4155(___2664 mutex) = 0; virtual void ___4156(___4160 ___2118, ___90 ___2123, ___2120 ___2119) = 0; virtual int ___4157() = 0; virtual ___2120 ___4158() = 0; virtual void ___4159(___2120* ___2119) = 0; virtual void ___4161(___2120 ___2119) = 0; virtual ___372    ___4304(___1361 ___1351) const = 0; virtual ___372    ___4309(___2727 ___2723) const = 0; virtual PlotType_e   ___1513() const = 0; virtual int32_t datasetGetNumPartitionFiles() const = 0; virtual int32_t zoneGetOwnerProcess(___4636 ___4658) const = 0; virtual int32_t zonePartitionGetOwnerProcess(___4636 ___4658, ___4636 partitionNum) const = 0; virtual ___372 zoneIsPartitioned(___4636 ___4658) const = 0; virtual ___4636 zoneGetNumPartitions(___4636 ___4658) const = 0; virtual void zonePartitionGetIJK(___4636 ___4658, ___4636 partitionNum, ___1844& ___1861) const = 0; virtual void zonePartitionGetIJKOffset(___4636 ___4658, ___4636 partitionNum, ___1844& ___1862) const = 0; virtual ___372 dataValueGetMinMaxByZonePartitionVar(___4636 ___4658, ___4636 partitionNum, ___4352 ___4336, double* minVal, double* maxVal) const = 0; virtual ___1361 dataValuePartitionGetReadableNLRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const = 0; virtual ___1361 dataValuePartitionGetReadableCCRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const = 0; virtual ___1361 dataValuePartitionGetReadableNativeRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const = 0; virtual ___1361 dataValuePartitionGetReadableDerivedRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const = 0; virtual ___1361 dataValuePartitionGetWritableNativeRef(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368) const = 0; virtual void         dataValuePartitionGetReadableRawPtr(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363) const = 0; virtual void         dataValuePartitionGetWritableRawPtr(___4636 ___4658, ___4636 partitionNum, ___4352 ___4368, void** ___880, FieldDataType_e* ___1363) const = 0; virtual ___2727 dataNodePartitionGetReadableRef(___4636 ___4658, ___4636 partitionNum) const = 0; virtual ___2727 dataNodePartitionGetWritableRef(___4636 ___4658, ___4636 partitionNum) const = 0; virtual ___2742 dataNodeToElemMapPartitionGetReadableRef(___4636 ___4658, ___4636 partitionNum) const = 0; virtual GhostInfo_pa zoneGhostNodeInfoGetRef(___4636 ___4658) const = 0; virtual GhostInfo_pa zoneGhostCellInfoGetRef(___4636 ___4658) const = 0; virtual GhostInfo_pa zonePartitionGhostNodeInfoGetRef(___4636 ___4658, ___4636 partitionNum) const = 0; virtual GhostInfo_pa zonePartitionGhostCellInfoGetRef(___4636 ___4658, ___4636 partitionNum) const = 0; virtual ___81               ghostInfoGetNumItemsByRef(GhostInfo_pa ghostInfo) const = 0;
virtual ___81               ghostInfoGetItemByRef(GhostInfo_pa ghostInfo, ___81 itemNum) const = 0; virtual ___2090::___2980 ghostInfoGetNeighborByRef(GhostInfo_pa ghostInfo, ___81 itemNum) const = 0; virtual ___81               ghostInfoGetNeighborItemByRef(GhostInfo_pa ghostInfo, ___81 itemNum) const = 0; }; }}
