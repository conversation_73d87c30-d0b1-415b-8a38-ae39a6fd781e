///////////////////////////////////////////////////////////////////////////////
/// \file xpressive.hpp
/// Includes all of xpressive including support for both static and
/// dynamic regular expressions.
//
//  Copyright 2008 Eric <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_XPRESSIVE_HPP_EAN_10_04_2005
#define BOOST_XPRESSIVE_HPP_EAN_10_04_2005

// MS compatible compilers support #pragma once
#if defined(_MSC_VER)
# pragma once
#endif

#include <boost/xpressive/xpressive_static.hpp>
#include <boost/xpressive/xpressive_dynamic.hpp>

#endif
