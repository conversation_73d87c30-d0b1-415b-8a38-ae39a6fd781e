//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_REPOSITORY_SUPPORT_FLUSH_MULTI_PASS_JUL_11_2009_0823PM)
#define BOOST_SPIRIT_REPOSITORY_SUPPORT_FLUSH_MULTI_PASS_JUL_11_2009_0823PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/terminal.hpp>

namespace boost { namespace spirit { namespace repository
{
    // The flush_multi_pass extended terminal
    BOOST_SPIRIT_TERMINAL( flush_multi_pass )

}}}

#endif
