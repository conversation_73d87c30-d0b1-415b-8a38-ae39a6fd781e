/* file: mkl_df.h */
/*******************************************************************************
* Copyright 2006-2018 Intel Corporation.
*
* This software and the related documents are Intel copyrighted  materials,  and
* your use of  them is  governed by the  express license  under which  they were
* provided to you (License).  Unless the License provides otherwise, you may not
* use, modify, copy, publish, distribute,  disclose or transmit this software or
* the related documents without Intel's prior written permission.
*
* This software and the related documents  are provided as  is,  with no express
* or implied  warranties,  other  than those  that are  expressly stated  in the
* License.
*******************************************************************************/

/*
//++
//  The main DF header file.
//--
*/

#ifndef __MKL_DF_H__
#define __MKL_DF_H__

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include "mkl_df_defines.h"
#include "mkl_df_functions.h"
#include "mkl_df_types.h"

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __MKL_DF_H__ */
