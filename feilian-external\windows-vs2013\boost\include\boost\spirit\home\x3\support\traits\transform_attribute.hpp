/*=============================================================================
    Copyright (c) 2001-2014 <PERSON>
    Copyright (c) 2001-2012 <PERSON><PERSON><PERSON>
    http://spirit.sourceforge.net/

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_X3_ATTRIBUTE_TRANSFORM_JAN_8_2012_0721PM)
#define BOOST_SPIRIT_X3_ATTRIBUTE_TRANSFORM_JAN_8_2012_0721PM

#include <boost/mpl/identity.hpp>

namespace boost { namespace spirit { namespace x3 { namespace traits
{
    ///////////////////////////////////////////////////////////////////////////
    //  transform_attribute
    //
    //  Sometimes the user needs to transform the attribute types for certain
    //  attributes. This template can be used as a customization point, where
    //  the user is able specify specific transformation rules for any attribute
    //  type.
    ///////////////////////////////////////////////////////////////////////////
    template <typename Exposed, typename Transformed, typename Tag
      , typename Enable = void>
    struct transform_attribute;

    ///////////////////////////////////////////////////////////////////////////
    template <typename Tag, typename Transformed, typename Exposed>
    typename transform_attribute<Exposed, Transformed, Tag>::type
    pre_transform(Exposed& attr)
    {
        return transform_attribute<Exposed, Transformed, Tag>::pre(attr);
    }

    template <typename Tag, typename Transformed, typename Exposed>
    typename transform_attribute<Exposed, Transformed, Tag>::type
    pre_transform(Exposed const& attr)
    {
        return transform_attribute<Exposed const, Transformed, Tag>::pre(attr);
    }
}}}}

#endif
