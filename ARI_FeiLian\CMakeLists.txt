# 获取当前文件夹名称
set(EXECUTABLE_FOLDER FeiLian)

# 定义可执行文件自定义依赖库列表
### 所有依赖库之间，若存在依赖关系时，应将被依赖库排在依赖库之后
set (LIBS_BASIC basic_postTools basic_CFD  basic_MRF basic_field basic_configure basic_mesh basic_geometry basic_common)

# 工具编译
if (ARI_BUILD_TOOLS)

    # generateBoundaryFile编译
    if (ON)
        set (APP_NAME generateBoundaryFile)
        set (LINK_LIBS meshProcess_meshConverter ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif ()

    # flowPreprocessor编译
    if (ON)
        set (APP_NAME flowPreprocessor)
        set (LIBS_MESHPROCESS meshProcess_meshProcess meshProcess_agglomeration meshProcess_decompose meshProcess_dualMesh meshProcess_meshConverter)
        set (LINK_LIBS ${LIBS_MESHPROCESS} ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif ()

    # flowPreprocessor_AeroDynamic编译
    if (ARI_BUILD_AERO)
        set (APP_NAME flowPreprocessor_AeroDynamic)
        set (LINK_LIBS meshProcess_meshConverter feilian-specialmodule_meshAeroStatic ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif ()

    # flowPostprocessor编译
    if (ON)
        set (APP_NAME flowPostprocessor)

        if(ARI_ENABLE_MULTISPECIES)
            set(LIBS_SPECIALMODULE feilian-specialmodule_multiSpecies)
        endif(ARI_ENABLE_MULTISPECIES)

        set (LINK_LIBS sourceFlow_postprocessor sourceFlow_configure sourceFlow_material ${LIBS_SPECIALMODULE} ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif ()

    # flowForceNondimensionalize编译
    if (ON)
        set (APP_NAME flowForceNondimensionalize)

        if(ARI_ENABLE_MULTISPECIES)
            set(LIBS_SPECIALMODULE feilian-specialmodule_multiSpecies)
        endif(ARI_ENABLE_MULTISPECIES)

        set (LINK_LIBS sourceFlow_postprocessor sourceFlow_configure sourceFlow_material ${LIBS_SPECIALMODULE} ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif ()

    # initialFields编译
    if (ON)
        set (APP_NAME initialFields)

        if(ARI_ENABLE_MULTISPECIES)
            set(LIBS_SPECIALMODULE feilian-specialmodule_multiSpecies)
        endif(ARI_ENABLE_MULTISPECIES)

        set (LINK_LIBS sourceFlow_configure sourceFlow_material ${LIBS_SPECIALMODULE}  ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif ()

    # meshDeform编译
    if (ARI_ENABLE_MESHDEFORM)
        set (APP_NAME mainDeform)
        set (LINK_LIBS meshProcess_meshDeform meshProcess_meshConverter ${LIBS_BASIC})
        include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)
    endif(ARI_ENABLE_MESHDEFORM)

endif (ARI_BUILD_TOOLS)

# 流场求解器编译
if (ARI_BUILD_FLOWSOLVER)

    # 可执行文件名称（与cpp文件同名）
    set (APP_NAME mainFlowSolver)

    # 流场相关库
    set (LIBS_SOURCEFLOW sourceFlow_flowSolver sourceFlow_postprocessor sourceFlow_resultsProcess
                         sourceFlow_timeScheme sourceFlow_fluxScheme sourceFlow_turbulence sourceFlow_boundaryCondition
                         sourceFlow_package sourceFlow_configure sourceFlow_material)

    # 网格相关库
    set (LIBS_MESHPROCESS meshProcess_meshSorting meshProcess_wallDistance meshProcess_zone)

    # Overset库
    if(ARI_ENABLE_OVERSET)
        set(LIBS_SPECIALMODULE feilian-specialmodule_oversetMesh meshProcess_motion)
    endif(ARI_ENABLE_OVERSET)

    # 机器学习湍流模型
    if(ARI_ENABLE_MLTURBMODEL)
        set(LIBS_SPECIALMODULE feilian-specialmodule_mlTurbModel)
    endif(ARI_ENABLE_MLTURBMODEL)

    # 多组分
    if(ARI_ENABLE_MULTISPECIES)
        set(LIBS_SPECIALMODULE feilian-specialmodule_multiSpecies)
    endif(ARI_ENABLE_MULTISPECIES)

    # Flutter库
    if(ARI_ENABLE_FLUTTER)
        set(LIBS_SPECIALMODULE ${LIBS_SPECIALMODULE} flutter_method flutter_solver)
    endif(ARI_ENABLE_FLUTTER)

    # 流场求解所需库
    set (LINK_LIBS ${LIBS_SOURCEFLOW} ${LIBS_SPECIALMODULE} ${LIBS_MESHPROCESS} ${LIBS_BASIC})

    # 调用生成函数
    include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)

endif (ARI_BUILD_FLOWSOLVER)

# 流场求解器编译
if (ARI_BUILD_FLOWSOLVERPLUS)

    # 可执行文件名称（与cpp文件同名）
    set (APP_NAME mainFlowSolverPlus)

	# 前处理相关库
    set (LIBS_MESHPROCESS meshProcess_meshProcess meshProcess_meshSorting meshProcess_wallDistance
                          meshProcess_zone meshProcess_agglomeration meshProcess_decompose
                          meshProcess_dualMesh meshProcess_meshConverter)

    # 流场相关库
    set (LIBS_SOURCEFLOW sourceFlow_flowSolver sourceFlow_postprocessor sourceFlow_resultsProcess
                         sourceFlow_timeScheme sourceFlow_turbulence sourceFlow_boundaryCondition sourceFlow_fluxScheme
                         sourceFlow_package sourceFlow_configure sourceFlow_material)

    # Overset库
    if(ARI_ENABLE_OVERSET)
        set(LIBS_SPECIALMODULE feilian-specialmodule_oversetMesh meshProcess_motion)
    endif(ARI_ENABLE_OVERSET)

    # 机器学习湍流模型
    if(ARI_ENABLE_MLTURBMODEL)
        set(LIBS_SPECIALMODULE feilian-specialmodule_mlTurbModel)
    endif(ARI_ENABLE_MLTURBMODEL)

    # 流场求解所需库
    set (LINK_LIBS ${LIBS_SOURCEFLOW} ${LIBS_SPECIALMODULE} ${LIBS_MESHPROCESS} ${LIBS_BASIC})

    # 调用生成函数
    include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)

endif (ARI_BUILD_FLOWSOLVERPLUS)

# 颗粒求解器编译
if (ARI_BUILD_PARTICLE)

    # 流场相关库
    set (LIBS_SOURCEFLOW sourceFlow_flowSolver sourceFlow_postprocessor sourceFlow_resultsProcess
                         sourceFlow_timeScheme sourceFlow_fluxScheme sourceFlow_turbulence sourceFlow_boundaryCondition
                         sourceFlow_package sourceFlow_configure sourceFlow_material)

	# 前处理相关库
    set (LIBS_MESHPROCESS meshProcess_meshProcess meshProcess_meshSorting meshProcess_wallDistance
                          meshProcess_zone meshProcess_agglomeration meshProcess_decompose
                          meshProcess_dualMesh meshProcess_meshConverter)

    # 定义可执行文件自定义依赖库列表，若存在依赖关系时，应将被依赖库排在依赖库之后
    set(LIBS_PARTICLE particle_solver particle_integration particle_contact particle_force particle_backgroundGrid
                      particle_property particle_distribution particle_configure particle_basic)

    # 多组分
    if(ARI_ENABLE_MULTISPECIES)
        set(LIBS_SPECIALMODULE feilian-specialmodule_multiSpecies)
    endif(ARI_ENABLE_MULTISPECIES)

    # 颗粒求解器编译
    set (APP_NAME mainParticle)
    set(LINK_LIBS ${LIBS_PARTICLE} ${LIBS_SOURCEFLOW} ${LIBS_SPECIALMODULE} ${LIBS_MESHPROCESS} ${LIBS_BASIC})
    include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)

    # 流场颗粒耦合求解器编译
    set (APP_NAME mainCFDDEMSolver)
    set (LINK_LIBS ${LIBS_PARTICLE} ${LIBS_SOURCEFLOW} ${LIBS_SPECIALMODULE} ${LIBS_MESHPROCESS} ${LIBS_BASIC})
    include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)

endif (ARI_BUILD_PARTICLE)

# mainAeroStatic编译
 if (ARI_ENABLE_AEROSTATIC)
    set (APP_NAME mainAeroStatic)

    # 流场相关库
    set (LIBS_SOURCEFLOW sourceFlow_flowSolver sourceFlow_postprocessor sourceFlow_resultsProcess
                     sourceFlow_timeScheme sourceFlow_fluxScheme sourceFlow_turbulence sourceFlow_boundaryCondition
                     sourceFlow_package sourceFlow_configure sourceFlow_material)

        # 网格相关库
        set (LIBS_MESHPROCESS meshProcess_meshSorting meshProcess_wallDistance meshProcess_zone meshProcess_meshConverter meshProcess_meshDeform)

        # 静气弹相关库
        set(LIBS_SPECIALMODULE staticAeroelastic_solver staticAeroelastic_method staticAeroelastic_meshData)

        # Overset库 - 放在前面以解决链接依赖
        if(ARI_ENABLE_OVERSET)
            set(LIBS_SPECIALMODULE feilian-specialmodule_oversetMesh ${LIBS_SPECIALMODULE})
            list(APPEND LIBS_MESHPROCESS meshProcess_motion)
        endif(ARI_ENABLE_OVERSET)

    # 流场求解所需库 - 重复链接overset库以解决循环依赖
    if(ARI_ENABLE_OVERSET)
        set (LINK_LIBS ${LIBS_SPECIALMODULE} ${LIBS_SOURCEFLOW}  ${LIBS_MESHPROCESS} ${LIBS_BASIC} feilian-specialmodule_oversetMesh)
    else()
        set (LINK_LIBS ${LIBS_SPECIALMODULE} ${LIBS_SOURCEFLOW}  ${LIBS_MESHPROCESS} ${LIBS_BASIC})
    endif()

    # 调用生成函数
    include(${ARI_SRCDIR}/ARI_FeiLian/add_executable.cmake)

endif(ARI_ENABLE_AEROSTATIC)
