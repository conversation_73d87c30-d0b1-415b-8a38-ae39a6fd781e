﻿#include "basic/field/DataPackage.h"

DataPackage::DataPackage(Mesh* pmesh_)
{
    IntElementPackageSize = 0;
    ScalarElementPackageSize = 0;
    VectorElementPackageSize = 0;
    TensorElementPackageSize = 0;

    ScalarBoundaryPackageSize = 0;
    VectorBoundaryPackageSize = 0;
    TensorBoundaryPackageSize = 0;

    IntNodePackageSize = 0;
    ScalarNodePackageSize = 0;
    VectorNodePackageSize = 0;
    TensorNodePackageSize = 0;

	nIntElementTempFieldMax = 0;
	nScalarElementTempFieldMax = 0;
	nVectorElementTempFieldMax = 0;

	nIntBoundaryTempFieldFixed = 0;
	nScalarBoundaryTempFieldFixed = 0;
	nVectorBoundaryTempFieldFixed = 0;

    pmesh = pmesh_;
}

DataPackage::~DataPackage()
{
    for (int i = 0; i < IntElementPackageSize; ++i) DeleteFieldPointer(intElementFieldPackage[i]);
    for (int i = 0; i < ScalarElementPackageSize; ++i) DeleteFieldPointer(scalarElementFieldPackage[i]);
    for (int i = 0; i < VectorElementPackageSize; ++i) DeleteFieldPointer(vectorElementFieldPackage[i]);
    for (int i = 0; i < TensorElementPackageSize; ++i) DeleteFieldPointer(tensorElementFieldPackage[i]);
        
    for (int i = 0; i < ScalarBoundaryPackageSize; ++i) DeleteFieldPointer(scalarBoundaryFieldPackage[i]);
    for (int i = 0; i < VectorBoundaryPackageSize; ++i) DeleteFieldPointer(vectorBoundaryFieldPackage[i]);
    for (int i = 0; i < TensorBoundaryPackageSize; ++i) DeleteFieldPointer(tensorBoundaryFieldPackage[i]);
    
    for (int i = 0; i < IntNodePackageSize; ++i) DeleteFieldPointer(intNodeFieldPackage[i]);
    for (int i = 0; i < ScalarNodePackageSize; ++i) DeleteFieldPointer(scalarNodeFieldPackage[i]);
    for (int i = 0; i < VectorNodePackageSize; ++i) DeleteFieldPointer(vectorNodeFieldPackage[i]);
    for (int i = 0; i < TensorNodePackageSize; ++i) DeleteFieldPointer(tensorNodeFieldPackage[i]);
    
    for (int i = 0; i < intElementFieldTemp.size(); ++i) DeleteFieldPointer(intElementFieldTemp[i]);
    for (int i = 0; i < scalarElementFieldTemp.size(); ++i) DeleteFieldPointer(scalarElementFieldTemp[i]);
    for (int i = 0; i < vectorElementFieldTemp.size(); ++i) DeleteFieldPointer(vectorElementFieldTemp[i]);

	for (int i = 0; i < intBoundaryFieldTemp.size(); ++i) DeleteFieldPointer(intBoundaryFieldTemp[i]);
	for (int i = 0; i < scalarBoundaryFieldTemp.size(); ++i) DeleteFieldPointer(scalarBoundaryFieldTemp[i]);
	for (int i = 0; i < vectorBoundaryFieldTemp.size(); ++i) DeleteFieldPointer(vectorBoundaryFieldTemp[i]);
}

ElementField<int> &DataPackage::GetIntElementField(const int & fieldName)
{
    if (intElementFieldPackage[fieldName] == nullptr || fieldName >= IntElementPackageSize)
        FatalError("This Int ElementField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *intElementFieldPackage[fieldName];
}

ElementField<Scalar> &DataPackage::GetScalarElementField(const int & fieldName)
{
    if (scalarElementFieldPackage[fieldName] == nullptr || fieldName >= ScalarElementPackageSize)
        FatalError("This Scalar ElementField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *scalarElementFieldPackage[fieldName];
}

ElementField<Vector>& DataPackage::GetVectorElementField(const int & fieldName)
{
    if (vectorElementFieldPackage[fieldName] == nullptr || fieldName >= VectorElementPackageSize)
        FatalError("This Vector ElementField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *vectorElementFieldPackage[fieldName];
}

ElementField<Tensor>& DataPackage::GetTensorElementField(const int & fieldName)
{
    if (tensorElementFieldPackage[fieldName] == nullptr || fieldName >= TensorElementPackageSize)
        FatalError("This Tensor ElementField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *tensorElementFieldPackage[fieldName];
}

BoundaryField<Scalar> &DataPackage::GetScalarBoundaryField(const int & fieldName)
{
    if (scalarBoundaryFieldPackage[fieldName] == nullptr || fieldName >= ScalarBoundaryPackageSize)
        FatalError("This Scalar BoundaryField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *scalarBoundaryFieldPackage[fieldName];
}

BoundaryField<Vector>& DataPackage::GetVectorBoundaryField(const int & fieldName)
{
    if (vectorBoundaryFieldPackage[fieldName] == nullptr || fieldName >= VectorBoundaryPackageSize)
        FatalError("This Vector BoundaryField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *vectorBoundaryFieldPackage[fieldName];
}

BoundaryField<Tensor>& DataPackage::GetTensorBoundaryField(const int & fieldName)
{
    if (tensorBoundaryFieldPackage[fieldName] == nullptr || fieldName >= TensorBoundaryPackageSize)
        FatalError("This Tensor BoundaryField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *tensorBoundaryFieldPackage[fieldName];
}

NodeField<int> &DataPackage::GetIntNodeField(const int & fieldName)
{
    if (intNodeFieldPackage[fieldName] == nullptr || fieldName >= IntNodePackageSize)
        FatalError("This Int NodeField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *intNodeFieldPackage[fieldName];
}

NodeField<Scalar> &DataPackage::GetScalarNodeField(const int & fieldName)
{
    if (scalarNodeFieldPackage[fieldName] == nullptr || fieldName >= ScalarNodePackageSize)
        FatalError("This Scalar NodeField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *scalarNodeFieldPackage[fieldName];
}

NodeField<Vector>& DataPackage::GetVectorNodeField(const int & fieldName)
{
    if (vectorNodeFieldPackage[fieldName] == nullptr || fieldName >= VectorNodePackageSize)
        FatalError("This Vector NodeField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *vectorNodeFieldPackage[fieldName];
}

NodeField<Tensor>& DataPackage::GetTensorNodeField(const int & fieldName)
{
    if (tensorNodeFieldPackage[fieldName] == nullptr || fieldName >= TensorNodePackageSize)
        FatalError("This Tensor NodeField [" + ToString(fieldName) +  "] Not Packed in the Package");
    
    return *tensorNodeFieldPackage[fieldName];
}

template<>
std::vector<ElementField<Scalar> *> &DataPackage::GetTempElementFieldAll(const Scalar initialValue)
{
	return scalarElementFieldTemp;
}

template<>
std::vector<ElementField<Vector> *> &DataPackage::GetTempElementFieldAll(const Vector initialValue)
{
	return vectorElementFieldTemp;
}

template<>
std::vector<BoundaryField<Scalar> *> &DataPackage::GetTempBoundaryFieldAll(const Scalar initialValue)
{
	return scalarBoundaryFieldTemp;
}

template<>
std::vector<BoundaryField<Vector> *> &DataPackage::GetTempBoundaryFieldAll(const Vector initialValue)
{
	return vectorBoundaryFieldTemp;
}

template<>
ElementField<int> &DataPackage::GetTempElementField(const std::string &name, const int initialValue)
{
	int index;
	for (index = 0; index < intElementFieldTemp.size(); ++index)
	{
		auto field = intElementFieldTemp[index];
		if (field->Assignment() && field->GetName() == name) return *field;
	}

	for (index = 0; index < intElementFieldTemp.size(); ++index)
	{
		auto field = intElementFieldTemp[index];
		if (field->Existence() && !field->Assignment())
		{
			field->SetName(name);
			field->Initialize(initialValue);
			return *field;
		}
	}

	if (index == intElementFieldTemp.size())
	{
		if (nIntElementTempFieldMax > 0 && index + 1 > nIntElementTempFieldMax)
            FatalError("DataPackage::GetTempElementField<int>: no empty temp field!");

		intElementFieldTemp.push_back(new ElementField<Int>(pmesh, initialValue, name));
		intElementFieldTemp[index]->Create();
	}

	return *intElementFieldTemp[index];
}

template<>
ElementField<Scalar> &DataPackage::GetTempElementField(const std::string &name, const Scalar initialValue)
{
	int index;
	for (index = 0; index < scalarElementFieldTemp.size(); ++index)
	{
		auto field = scalarElementFieldTemp[index];
		if (field->Assignment() && field->GetName() == name) return *field;
	}

	for (index = 0; index < scalarElementFieldTemp.size(); ++index)
	{
		auto field = scalarElementFieldTemp[index];
		if (field->Existence() && !field->Assignment())
		{
			field->SetName(name);
			field->Initialize(initialValue);
			return *field;
		}
	}

	if (index == scalarElementFieldTemp.size())
	{
		if (nScalarElementTempFieldMax > 0 && index + 1 > nScalarElementTempFieldMax)
            FatalError("DataPackage::GetTempElementField<Scalar>: no empty temp field!");

		scalarElementFieldTemp.push_back(new ElementField<Scalar>(pmesh, initialValue, name));
		scalarElementFieldTemp[index]->Create();
	}

	return *scalarElementFieldTemp[index];
}

template<>
ElementField<Vector> &DataPackage::GetTempElementField(const std::string &name, const Vector initialValue)
{
	int index;
	for (index = 0; index < vectorElementFieldTemp.size(); ++index)
	{
		auto field = vectorElementFieldTemp[index];
		if (field->Assignment() && field->GetName() == name) return *field;
	}

	for (index = 0; index < vectorElementFieldTemp.size(); ++index)
	{
		auto field = vectorElementFieldTemp[index];
		if (field->Existence() && !field->Assignment())
		{
			field->SetName(name);
			field->Initialize(initialValue);
			return *field;
		}
	}

	if (index == vectorElementFieldTemp.size())
	{
		if (nVectorElementTempFieldMax > 0 && index + 1 > nVectorElementTempFieldMax)
            FatalError("DataPackage::GetTempElementField<Vector>: no empty temp field!");

		vectorElementFieldTemp.push_back(new ElementField<Vector>(pmesh, initialValue, name));
		vectorElementFieldTemp[index]->Create();
	}

	return *vectorElementFieldTemp[index];
}

ElementField<int> &DataPackage::GetIntTempField(const std::string &name, const int initialValue)
{
	return GetTempElementField(name, initialValue);
}

ElementField<Scalar> &DataPackage::GetScalarTempField(const std::string &name, const Scalar initialValue)
{
	return GetTempElementField(name, initialValue);
}

ElementField<Vector> &DataPackage::GetVectorTempField(const std::string &name, const Vector initialValue)
{
	return GetTempElementField(name, initialValue);
}

template<>
BoundaryField<Scalar> &DataPackage::GetTempBoundaryField(const std::string &name, const Scalar initialValue)
{
	int index;
	for (index = 0; index < scalarBoundaryFieldTemp.size(); ++index)
	{
		auto field = scalarBoundaryFieldTemp[index];
		if (field->Assignment() && field->GetName() == name) return *field;
	}

	for (index = 0; index < scalarBoundaryFieldTemp.size(); ++index)
	{
		auto field = scalarBoundaryFieldTemp[index];
		if (field->Existence() && !field->Assignment())
		{
			field->SetName(name);
			field->Initialize(initialValue);
			return *field;
		}
	}

	if (index == scalarBoundaryFieldTemp.size())
	{
		if (nScalarBoundaryTempFieldFixed > 0) FatalError("DataPackage::GetTempBoundaryField<Scalar>: no empty temp field!");

		scalarBoundaryFieldTemp.push_back(new BoundaryField<Scalar>(pmesh));
		scalarBoundaryFieldTemp[index]->Create();
	}

	return *scalarBoundaryFieldTemp[index];
}

template<>
BoundaryField<Vector> &DataPackage::GetTempBoundaryField(const std::string &name, const Vector initialValue)
{
	int index;
	for (index = 0; index < vectorBoundaryFieldTemp.size(); ++index)
	{
		if (vectorBoundaryFieldTemp[index]->GetName() == name) return *vectorBoundaryFieldTemp[index];
	}

	for (index = 0; index < vectorBoundaryFieldTemp.size(); ++index)
	{
		if (!vectorBoundaryFieldTemp[index]->Assignment())
		{
			vectorBoundaryFieldTemp[index]->SetName(name);
			vectorBoundaryFieldTemp[index]->Initialize(initialValue);
			return *vectorBoundaryFieldTemp[index];
		}
	}

	if (index == vectorBoundaryFieldTemp.size())
	{
		if (nVectorBoundaryTempFieldFixed > 0) FatalError("DataPackage::GetTempBoundaryField<Vector>: no empty temp field!");

		vectorBoundaryFieldTemp.push_back(new BoundaryField<Vector>(pmesh));
		vectorBoundaryFieldTemp[index]->Create();
	}

	return *vectorBoundaryFieldTemp[index];
}

template<>
void DataPackage::FreeTempField(ElementField<int> &phi)
{
	int index;
	for (index = 0; index < intElementFieldTemp.size(); ++index)
	{
		if (&phi == intElementFieldTemp[index])
		{
			phi.Free();
			return;
		}
	}

	if (index == intElementFieldTemp.size())
		FatalError("DataPackage::FreeTempField<Int>: pointer phi is not in intElementFieldTemp!");
}

template<>
void DataPackage::FreeTempField(ElementField<Scalar> &phi)
{
    int index;
    for (index = 0; index < scalarElementFieldTemp.size(); ++index)
    {
        if(&phi == scalarElementFieldTemp[index])
        {
            phi.Free();
            return;
        }
    }

    if(index == scalarElementFieldTemp.size())
        FatalError("DataPackage::FreeTempField<Scalar>: pointer phi is not in scalarElementFieldTemp!");
}

template<>
void DataPackage::FreeTempField(ElementField<Vector> &phi)
{
    int index;
    for (index = 0; index < vectorElementFieldTemp.size(); ++index)
    {
        if(&phi == vectorElementFieldTemp[index])
        {
            phi.Free();
            return;
        }
    }
    if(index == vectorElementFieldTemp.size())
        FatalError("DataPackage::FreeTempField<Vector>: pointer phi is not in vectorElementFieldTemp!");
}

template<>
void DataPackage::FreeTempField(BoundaryField<Scalar> &phi)
{
	int index;
	for (index = 0; index < scalarBoundaryFieldTemp.size(); ++index)
	{
		if (&phi == scalarBoundaryFieldTemp[index])
		{
			phi.Free();
			return;
		}
	}

	if (index == scalarBoundaryFieldTemp.size())
		FatalError("DataPackage::FreeTempField<Scalar>: pointer phi is not in scalarBoundaryFieldTemp!");
}

template<>
void DataPackage::FreeTempField(BoundaryField<Vector> &phi)
{
	int index;
	for (index = 0; index < vectorBoundaryFieldTemp.size(); ++index)
	{
		if (&phi == vectorBoundaryFieldTemp[index])
		{
			phi.Free();
			return;
		}
	}
	if (index == vectorBoundaryFieldTemp.size())
		FatalError("DataPackage::FreeTempField<Vector>: pointer phi is not in vectorBoundaryFieldTemp!");
}

void DataPackage::FreeIntTempField(ElementField<Int> &phi){ FreeTempField(phi); }

void DataPackage::FreeScalarTempField(ElementField<Scalar> &phi){ FreeTempField(phi); }

void DataPackage::FreeVectorTempField(ElementField<Vector> &phi){ FreeTempField(phi); }

bool DataPackage::CheckField(const int& dataPos)
{
    return (  scalarElementFieldPackage[dataPos] == nullptr
           || vectorElementFieldPackage[dataPos] == nullptr
           || tensorElementFieldPackage[dataPos] == nullptr

           || scalarBoundaryFieldPackage[dataPos] == nullptr
           || vectorBoundaryFieldPackage[dataPos] == nullptr
           || tensorBoundaryFieldPackage[dataPos] == nullptr

           || scalarNodeFieldPackage[dataPos] == nullptr
           || vectorNodeFieldPackage[dataPos] == nullptr
           || tensorNodeFieldPackage[dataPos] == nullptr );
}

ElementField<int> *DataPackage::CreatIntElementField(const int& dataPos, const std::string &name)
{
    if (dataPos > IntElementPackageSize - 1)
    {
        intElementFieldPackage.resize(dataPos + 1);
        intElementFieldPackage.at(dataPos) = new ElementField<int> (pmesh, 0, name) ;
        IntElementPackageSize = (int)intElementFieldPackage.size();
    }
    else
    {
        intElementFieldPackage.at(dataPos) = new ElementField<int>(pmesh, 0, name);
    }
    return intElementFieldPackage[dataPos];
}

ElementField<Scalar> *DataPackage::CreatScalarElementField(const int& dataPos, const std::string &name)
{
    if (dataPos > ScalarElementPackageSize - 1)
    {
        scalarElementFieldPackage.resize(dataPos + 1);
        scalarElementFieldPackage.at(dataPos) = new ElementField<Scalar> (pmesh, Scalar0, name) ;
        ScalarElementPackageSize = (int)scalarElementFieldPackage.size();
    }
    else
    {
        scalarElementFieldPackage.at(dataPos) = new ElementField<Scalar>(pmesh, Scalar0, name);
    }
    return scalarElementFieldPackage[dataPos];
}

ElementField<Vector> *DataPackage::CreatVectorElementField(const int& dataPos, const std::string &name)
{
    if (dataPos > VectorElementPackageSize - 1)
    {
        vectorElementFieldPackage.resize(dataPos + 1);
        vectorElementFieldPackage.at(dataPos) = new ElementField<Vector>(pmesh, Vector0, name);
        VectorElementPackageSize = (int)vectorElementFieldPackage.size();
    }
    else
    {
        vectorElementFieldPackage.at(dataPos) = new ElementField<Vector>(pmesh, Vector0, name);
    }
    return vectorElementFieldPackage[dataPos];
}

ElementField<Tensor> *DataPackage::CreatTensorElementField(const int& dataPos, const std::string &name)
{
    if (dataPos > TensorElementPackageSize - 1)
    {
        tensorElementFieldPackage.resize(dataPos + 1);
        tensorElementFieldPackage.at(dataPos) = new ElementField<Tensor>(pmesh, Tensor0,name);
        TensorElementPackageSize = (int)tensorElementFieldPackage.size();
    }
    else
    {
        tensorElementFieldPackage.at(dataPos) = new ElementField<Tensor>(pmesh, Tensor0,name);
    }
    return tensorElementFieldPackage[dataPos];
}

BoundaryField<Scalar> *DataPackage::CreatScalarBoundaryField(const int& dataPos, const std::string &name)
{
    if (dataPos > ScalarBoundaryPackageSize - 1)
    {
        scalarBoundaryFieldPackage.resize(dataPos + 1);
        scalarBoundaryFieldPackage.at(dataPos) = new BoundaryField<Scalar> (pmesh, Scalar0, name) ;
        ScalarBoundaryPackageSize = (int)scalarBoundaryFieldPackage.size();
    }
    else
    {
        scalarBoundaryFieldPackage.at(dataPos) = new BoundaryField<Scalar>(pmesh, Scalar0, name);
    }
    return scalarBoundaryFieldPackage[dataPos];
}

BoundaryField<Vector> *DataPackage::CreatVectorBoundaryField(const int& dataPos, const std::string &name)
{
    if (dataPos > VectorBoundaryPackageSize - 1)
    {
        vectorBoundaryFieldPackage.resize(dataPos + 1);
        vectorBoundaryFieldPackage.at(dataPos) = new BoundaryField<Vector>(pmesh, Vector0, name);
        VectorBoundaryPackageSize = (int)vectorBoundaryFieldPackage.size();
    }
    else
    {
        vectorBoundaryFieldPackage.at(dataPos) = new BoundaryField<Vector>(pmesh, Vector0, name);
    }
    return vectorBoundaryFieldPackage[dataPos];
}

BoundaryField<Tensor> *DataPackage::CreatTensorBoundaryField(const int& dataPos, const std::string &name)
{
    if (dataPos > TensorBoundaryPackageSize - 1)
    {
        tensorBoundaryFieldPackage.resize(dataPos + 1);
        tensorBoundaryFieldPackage.at(dataPos) = new BoundaryField<Tensor>(pmesh, Tensor0, name);
        TensorBoundaryPackageSize = (int)tensorBoundaryFieldPackage.size();
    }
    else
    {
        tensorBoundaryFieldPackage.at(dataPos) = new BoundaryField<Tensor>(pmesh, Tensor0, name);
    }
    return tensorBoundaryFieldPackage[dataPos];
}

NodeField<int> *DataPackage::CreatIntNodeField(const int& dataPos, const std::string &name)
{
    if (dataPos > IntNodePackageSize - 1)
    {
        intNodeFieldPackage.resize(dataPos + 1);
        intNodeFieldPackage.at(dataPos) = new NodeField<int> (pmesh, 0, name) ;
        IntNodePackageSize = (int)intNodeFieldPackage.size();
    }
    else
    {
        intNodeFieldPackage.at(dataPos) = new NodeField<int>(pmesh, 0, name);
    }
    return intNodeFieldPackage[dataPos];
}

NodeField<Scalar> *DataPackage::CreatScalarNodeField(const int& dataPos, const std::string &name)
{
    if (dataPos > ScalarNodePackageSize - 1)
    {
        scalarNodeFieldPackage.resize(dataPos + 1);
        scalarNodeFieldPackage.at(dataPos) = new NodeField<Scalar> (pmesh, Scalar0, name) ;
        ScalarNodePackageSize = (int)scalarNodeFieldPackage.size();
    }
    else
    {
        scalarNodeFieldPackage.at(dataPos) = new NodeField<Scalar>(pmesh, Scalar0, name);
    }
    return scalarNodeFieldPackage[dataPos];
}

NodeField<Vector> *DataPackage::CreatVectorNodeField(const int& dataPos, const std::string &name)
{
    if (dataPos > VectorNodePackageSize - 1)
    {
        vectorNodeFieldPackage.resize(dataPos + 1);
        vectorNodeFieldPackage.at(dataPos) = new NodeField<Vector>(pmesh, Vector0, name);
        VectorNodePackageSize = (int)vectorNodeFieldPackage.size();
    }
    else
    {
        vectorNodeFieldPackage.at(dataPos) = new NodeField<Vector>(pmesh, Vector0, name);
    }
    return vectorNodeFieldPackage[dataPos];
}

NodeField<Tensor> *DataPackage::CreatTensorNodeField(const int& dataPos, const std::string &name)
{
    if (dataPos > TensorNodePackageSize - 1)
    {
        tensorNodeFieldPackage.resize(dataPos + 1);
        tensorNodeFieldPackage.at(dataPos) = new NodeField<Tensor>(pmesh, Tensor0, name);
        TensorNodePackageSize = (int)tensorNodeFieldPackage.size();
    }
    else
    {
        tensorNodeFieldPackage.at(dataPos) = new NodeField<Tensor>(pmesh, Tensor0, name);
    }
    return tensorNodeFieldPackage[dataPos];
}

void DataPackage::SetTempField(const int &nScalarTempField_, const int &nVectorTempField_)
{
	nScalarElementTempFieldMax = nScalarTempField_;
	nVectorElementTempFieldMax = nVectorTempField_;

	// for (int i = 0; i < nScalarElementTempFieldMax; ++i)
    // {
    //     scalarElementFieldTemp.push_back(new ElementField<Scalar>(pmesh));
    //     scalarElementFieldTemp[i]->Create();
    // }
    // 
	// for (int i = 0; i < nVectorElementTempFieldMax; ++i)
    // {
    //     vectorElementFieldTemp.push_back(new ElementField<Vector>(pmesh));
    //     vectorElementFieldTemp[i]->Create();
    // }
}
