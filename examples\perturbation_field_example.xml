<?xml version="1.0" encoding="UTF-8"?>
<!-- 颤振摄动场前处理配置示例 -->
<root>
    <!-- 工程名称 -->
    <caseName>flutter_wing_example</caseName>
    
    <!-- 网格配置 -->
    <mesh>
        <meshNumber>1</meshNumber>
        <dimension>3D</dimension>
        <mesh0>
            <meshPath>./mesh/</meshPath>
            <fileName>wing.cgns</fileName>
            <meshType>CGNS</meshType>
        </mesh0>
    </mesh>
    
    <!-- 前处理配置 -->
    <preprocess>
        <!-- 基本前处理参数 -->
        <threadNumber>4</threadNumber>
        <partitionNumber>8</partitionNumber>
        <partitionMethod>METIS</partitionMethod>
        <dualMeshFlag>false</dualMeshFlag>
        <outputPath>./preMesh/</outputPath>
        <binaryFileFlag>true</binaryFileFlag>
        
        <!-- 多重网格配置 -->
        <multigrid>
            <totalLevel>3</totalLevel>
            <agglomerateType>UNSTRUCTURED</agglomerateType>
            <minCoarseRation>2</minCoarseRation>
            <maxCoarseRation>4</maxCoarseRation>
            <boundaryLayerNumber>5</boundaryLayerNumber>
            <boundaryLayerCoarseRationNormal>2</boundaryLayerCoarseRationNormal>
            <boundaryLayerCoarseRationTangent>2</boundaryLayerCoarseRationTangent>
            <singletonsRemovementFlag>true</singletonsRemovementFlag>
        </multigrid>
        
        <!-- 摄动场配置 -->
        <perturbationField>
            <!-- 启用摄动场处理 -->
            <perturbationFlag>true</perturbationFlag>
            
            <!-- 模态数量 -->
            <numModes>3</numModes>
            
            <!-- 摄动场文件根路径 -->
            <perturbationPath>./perturbation/</perturbationPath>
            
            <!-- 文件格式：true为二进制，false为ASCII -->
            <binaryFormat>false</binaryFormat>
            
            <!-- 是否验证摄动场数据 -->
            <validateData>true</validateData>
            
            <!-- 各模态摄动场文件名 -->
            <mode0File>mode1_bending.dat</mode0File>
            <mode1File>mode2_torsion.dat</mode1File>
            <mode2File>mode3_combined.dat</mode2File>
        </perturbationField>
    </preprocess>
    
    <!-- 边界条件配置 -->
    <boundary>
        <filePath>./boundary/</filePath>
    </boundary>
</root>
