/*//////////////////////////////////////////////////////////////////////////////
    Copyright (c) 2011 Jamboree

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//////////////////////////////////////////////////////////////////////////////*/
#ifndef BOOST_SPIRIT_INCLUDE_QI_REPOSITORY_SEEK
#define BOOST_SPIRIT_INCLUDE_QI_REPOSITORY_SEEK

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/repository/home/<USER>/directive/seek.hpp>

#endif
