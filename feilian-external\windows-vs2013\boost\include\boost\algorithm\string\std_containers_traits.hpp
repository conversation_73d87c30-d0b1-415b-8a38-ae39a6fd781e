//  Boost string_algo library std_containers_traits.hpp header file  ---------------------------//

//  Copyright Pavol Droba 2002-2003.
//
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/ for updates, documentation, and revision history.

#ifndef BOOST_STRING_STD_CONTAINERS_TRAITS_HPP
#define BOOST_STRING_STD_CONTAINERS_TRAITS_HPP

/*!\file 
    This file includes sequence traits for stl containers.
*/

#include <boost/config.hpp>
#include <boost/algorithm/string/std/string_traits.hpp>
#include <boost/algorithm/string/std/list_traits.hpp>

#ifdef BOOST_HAS_SLIST
#   include <boost/algorithm/string/std/slist_traits.hpp>
#endif

#endif  // BOOST_STRING_STD_CONTAINERS_TRAITS_HPP
