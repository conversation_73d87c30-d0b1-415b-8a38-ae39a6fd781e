 #if defined EXTERN
 #  undef EXTERN
 #endif
 #if defined ___3860
 #  define EXTERN
 #else
 #  define EXTERN extern
 #endif
 #if !defined ARRLIST_h
 #  error "Include ARRLIST.h before including STRLIST.h"
 #endif
EXTERN ___372     ___3848(___3839 ___3820); EXTERN void          ___3824(___3839 ___3820); EXTERN void          ___3841(___3839 ___3820, ___2227     ___3854, ___2227     ___684); EXTERN void          ___3840(___3839 ___3820, ___2227     ___3854); EXTERN void          ___3828(___3839* ___3820); EXTERN ___3839 ___3821(void); EXTERN ___372     ___3823(___3839 ___3820, char   const* ___3813); EXTERN ___2227     ___3826(___3839 ___3820); EXTERN char*         ___3834(___3839 ___3820, ___2227     ___3854);
 #if defined USE_MACROS_FOR_FUNCTIONS
 #  define ___3835 ___3837
 #else
 #  define ___3835 ___3836
 #endif
 #if !defined USE_MACROS_FOR_FUNCTIONS
EXTERN char const* ___3836(___3839 ___3820, ___2227     ___3854);
 #endif
 #define ___3837(___3820, ___3854) \
 static_cast<char const*>(___100(reinterpret_cast<___134>(___3820), ___3854))
EXTERN ___372     ___3843(___3839 ___3820, ___2227     ___3854, char const*   ___3813); EXTERN ___372     ___3838(___3839 ___3820, ___2227     ___3854, char const*   ___3813); EXTERN ___3839 ___3825(___3839 ___3820); EXTERN ___372     ___3822(___3839 ___3946, ___3839 ___3642); EXTERN char*         ___3847(___3839 ___3820); EXTERN ___3839 ___3831(char const* ___3813); EXTERN char**        ___3845(___3839 ___3820); EXTERN ___3839 ___3829(char const** ___3816, ___2227    ___684); EXTERN ___3839 ___3830(char const* ___3813); EXTERN char*         ___3846(___3839 ___3820, char          ___1818, char const*   ___475);
