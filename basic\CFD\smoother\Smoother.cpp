﻿#include "basic/CFD/smoother/Smoother.h"

namespace Smoother
{
FieldSmoother::FieldSmoother(Mesh *mesh_, const Scheme &smoothType_, const Scalar &smoothWeight_, const bool &nodeCenter_, const std::vector<int> symmetryPatchID_)
    : mesh(mesh_), smoothType(smoothType_), smoothWeight(smoothWeight_),
    nodeCenter(nodeCenter_), symmetryPatchID(symmetryPatchID_),
    coefficentField(mesh_, "coefficentField")
{
	epsilon = 0.25 * (smoothWeight * smoothWeight - 1.0);

    this->CalculateFaceAndElementWeight();
    
    if (smoothType == Scheme::NONE_SMOOTH)
    {
        GetFaceWeight = nullptr;
    }
    else if (smoothType == Scheme::DISTANCE_WEIGHT)
    {
        GetFaceWeight = &FieldSmoother::GetFaceWeightDistance;
    }
    else if (smoothType == Scheme::CONSTANT)
    {
        GetFaceWeight = &FieldSmoother::GetFaceWeightConstant;
    }
    else
    {
		FatalError("FieldSmoother::Smooth: type isnot supported!");
		return;
    }
}

FieldSmoother::~FieldSmoother()
{
}

void SubtractNormal(const Vector &faceNormal, Scalar &value)
{
}

void SubtractNormal(const Vector &faceNormal, Vector &value)
{
    value = value - (value & faceNormal) * faceNormal;
}

template void FieldSmoother::Smooth(ElementField<Scalar> &field, ElementField<Scalar> *fieldOld, ElementField<Scalar> *fieldNew, const int &smoothSteps);
template void FieldSmoother::Smooth(ElementField<Vector> &field, ElementField<Vector> *fieldOld, ElementField<Vector> *fieldNew, const int &smoothSteps);
template<class Type>
void FieldSmoother::Smooth(ElementField<Type> &field, ElementField<Type> *fieldOld, ElementField<Type> *fieldNew, const int &smoothSteps)
{
    if (GetFaceWeight == nullptr) return;

    *fieldOld = field;
	for (int step = 0; step < smoothSteps; ++step)
	{
		// 更新边界的虚单元值
		field.SetGhostlValueParallel();
		// field.SetGhostlValueBoundary();
		field.SetGhostlValueOverset();

		fieldNew->Initialize();

        if (nodeCenter)
        {
    	    // 内部面循环（不包括对称边界边所对应的内部面）
    	    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    	    for (int index = 0; index < innerFaceNumber; ++index)
    	    {
		    	// 得到面相关信息
    	        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		    	const Face &face = mesh->GetFace(faceID);
		    	const int &ownerID = face.GetOwnerID();
		    	const int &neighID = face.GetNeighborID();

                // 边界边跳过
                if (mesh->JudgeHalfFaceCrossSymmetryBoundary(faceID)) continue;

		    	// 累加相邻单元物理量
                const Scalar faceWeightTemp = (this->*GetFaceWeight)(faceID);
                fieldNew->AddValue(ownerID, faceWeightTemp * field.GetValue(neighID));
                fieldNew->AddValue(neighID, faceWeightTemp * field.GetValue(ownerID));
		    }

    	    // 对称边界上半个控制体单元
            for (int i = 0; i < symmetryPatchID.size(); ++i)
            {
                const int &patchID = symmetryPatchID[i];
                const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
                for (int index = 0; index < faceSize; ++index)
                {
                    // 得到面相关信息
                    const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                    const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                    const Vector &normal = mesh->GetFace(faceID).GetNormal();
                    Type value = fieldNew->GetValue(ownerID);
                    SubtractNormal(normal, value);
                    fieldNew->SetValue(ownerID, 2.0 * value);
                }
            }

            // 对称边界边所对应的面循环
            for (int index = 0; index < innerFaceNumber; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetInnerFaceIDInDomain(index);
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                const int &neighID = mesh->GetFace(faceID).GetNeighborID();
            
                // 边界边跳过
                if (!mesh->JudgeHalfFaceCrossSymmetryBoundary(faceID)) continue;

                // 累加相邻单元物理量
                const Scalar faceWeightTemp = (this->*GetFaceWeight)(faceID);
                fieldNew->AddValue(ownerID, faceWeightTemp * field.GetValue(neighID));
                fieldNew->AddValue(neighID, faceWeightTemp * field.GetValue(ownerID));
            }
        }
        else
        {
    	    // 内部面循环（含并行边界）
    	    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    	    for (int index = 0; index < innerFaceNumber; ++index)
    	    {
		    	// 得到面相关信息
    	        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		    	const Face &face = mesh->GetFace(faceID);
		    	const int &ownerID = face.GetOwnerID();
		    	const int &neighID = face.GetNeighborID();
    
		    	// 累加相邻单元物理量
		    	const Scalar faceWeightTemp = (this->*GetFaceWeight)(faceID);
                fieldNew->AddValue(ownerID, faceWeightTemp * field.GetValue(neighID));
                fieldNew->AddValue(neighID, faceWeightTemp * field.GetValue(ownerID));
		    }

            // 物理边界面循环
            for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
            {
                const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
                for (int index = 0; index < faceSize; ++index)
                {
                    // 得到面相关信息
                    const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                    const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                    const int &neighID = mesh->GetFace(faceID).GetNeighborID();
                    
			        // 累加相邻单元物理量
                    const Scalar faceWeightTemp = (this->*GetFaceWeight)(faceID);
                    fieldNew->AddValue(ownerID, faceWeightTemp * field.GetValue(neighID));
                }
            }
        }

		//单元循环
    	const int elementNumber = mesh->GetElementNumberInDomain();
    	for (int index = 0; index < elementNumber; ++index)
    	{
    	    const int &elementID = mesh->GetElementIDInDomain(index);
            const Scalar elementWeightTemp = (smoothType == Scheme::DISTANCE_WEIGHT) ? (this->elementWeight[elementID]) : 1.0;
            const Type valueTemp = (fieldOld->GetValue(elementID) + epsilon * elementWeightTemp * fieldNew->GetValue(elementID)) * coefficentField.GetValue(elementID);
			field.SetValue(elementID, valueTemp);
		}
	}
}

void FieldSmoother::CalculateFaceAndElementWeight()
{
    if (smoothType == Scheme::DISTANCE_WEIGHT)
    {
        faceWeight.clear();
        elementWeight.clear();
	    faceWeight.resize(mesh->GetFaceNumber(), Scalar0);
        elementWeight.resize(mesh->GetElementNumberAll(), Scalar0);
    }

    coefficentField.Initialize(Scalar0);
    
    if (nodeCenter)
    {
        // 内部面循环（不含对称边界边所对应的内部面）
        const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
        for (int index = 0; index < innerFaceNumber; ++index)
        {
            // 得到面相关信息
            const int &faceID = mesh->GetInnerFaceIDInDomain(index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();

            // 对称面边界边跳过
            if (mesh->JudgeHalfFaceCrossSymmetryBoundary(faceID)) continue;

            if (smoothType == Scheme::DISTANCE_WEIGHT)
            {
                const Scalar distance = (mesh->GetElement(ownerID).GetCenter() - mesh->GetElement(neighID).GetCenter()).Mag();
                const Scalar &faceArea = mesh->GetFace(faceID).GetArea();
                faceWeight[faceID] = faceArea * faceArea / distance;
                elementWeight[ownerID] += faceWeight[faceID];
                elementWeight[neighID] += faceWeight[faceID];
            }

            coefficentField.AddValue(ownerID, 1.0);
            coefficentField.AddValue(neighID, 1.0);
        }

        for (int i = 0; i < symmetryPatchID.size(); ++i)
        {
            const int &patchID = symmetryPatchID[i];
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                if (smoothType == Scheme::DISTANCE_WEIGHT) elementWeight[ownerID] *= 2.0;
                coefficentField.MultiplyValue(ownerID, 2.0);
            }
        }

        // 对称面边界边单独处理
        for (int i = 0; i < symmetryPatchID.size(); ++i)
        {
            const int &patchID = symmetryPatchID[i];
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID0 = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const int &ownerID0 = mesh->GetFace(faceID0).GetOwnerID();
                const Vector &normal0 = mesh->GetFace(faceID0).GetNormal();
        
                const Element &element = mesh->GetElement(ownerID0);
                for (int m = 0; m < element.GetFaceSize(); m++)
                {
                    const int &faceID1 = element.GetFaceID(m);
                    if (mesh->JudgeHalfFaceCrossSymmetryBoundary(faceID1))
                    {
                        const int &ownerID1 = mesh->GetFace(faceID1).GetOwnerID();
                        const int &neighID1 = mesh->GetFace(faceID1).GetNeighborID();
                        if (smoothType == Scheme::DISTANCE_WEIGHT)
                        {
                            const Vector &normal1 = mesh->GetFace(faceID1).GetNormal();
                            const Scalar distance = (mesh->GetElement(ownerID1).GetCenter() - mesh->GetElement(neighID1).GetCenter()).Mag();
                            const Scalar &faceArea = 2.0 * mesh->GetFace(faceID1).GetArea() * (1.0 - (normal1 & normal0));
                            faceWeight[faceID1] = faceArea * faceArea / distance;
                            elementWeight[ownerID0] += faceWeight[faceID1];
                        }
                        coefficentField.AddValue(ownerID0, 1.0);
                    }
                }
            }
        }
    }
    else
    {
        // 内部面循环
        const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
        for (int index = 0; index < innerFaceNumber; ++index)
        {
            // 得到面相关信息
            const int &faceID = mesh->GetInnerFaceIDInDomain(index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();

            if (smoothType == Scheme::DISTANCE_WEIGHT)
            {
                const Scalar distance = (mesh->GetElement(ownerID).GetCenter() - mesh->GetElement(neighID).GetCenter()).Mag();
                const Scalar &faceArea = mesh->GetFace(faceID).GetArea();
                faceWeight[faceID] = faceArea * faceArea / distance;
                elementWeight[ownerID] += faceWeight[faceID];
                elementWeight[neighID] += faceWeight[faceID];
            }
            coefficentField.AddValue(ownerID, 1.0);
            coefficentField.AddValue(neighID, 1.0);
        }

        // 边界面循环
        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                const int &neighID = mesh->GetFace(faceID).GetNeighborID();

                if (smoothType == Scheme::DISTANCE_WEIGHT)
                {
                    const Scalar distance = (mesh->GetElement(ownerID).GetCenter() - mesh->GetElement(neighID).GetCenter()).Mag();
                    const Scalar &faceArea = mesh->GetFace(faceID).GetArea();
                    faceWeight[faceID] = faceArea * faceArea / distance;
                    elementWeight[ownerID] += faceWeight[faceID];
                }
                coefficentField.AddValue(ownerID, 1.0);
            }
        }
    }

	for (int elementID = 0; elementID < mesh->GetElementNumberInDomain(); ++elementID)
	{
        if (smoothType == Scheme::DISTANCE_WEIGHT) elementWeight[elementID] = coefficentField.GetValue(elementID) / elementWeight[elementID];
        coefficentField.SetValue(elementID, 1.0 / (1.0 + epsilon * coefficentField.GetValue(elementID)));
	}
}

} // namespace Smoother