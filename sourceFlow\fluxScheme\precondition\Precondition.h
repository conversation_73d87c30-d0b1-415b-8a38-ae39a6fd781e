﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Precondition.h
//! <AUTHOR>
//! @brief 低速预处理类。
//! @date 2021-07-05
//
//------------------------------修改日志----------------------------------------
//
// 2021-07-05 李艳亮,唐海龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_precondition_Precondition_
#define _sourceFlow_fluxScheme_precondition_Precondition_

#include "basic/field/ElementField.h"
#include "sourceFlow/package/FlowPackage.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 条件预处理命名空间
 * 
 */
namespace Precondition
{
/**
 * @brief 流场条件预处理类
 * 
 */
class Precondition
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     */
    Precondition(Package::FlowPackage &data);    

    /**
     * @brief 计算预处理矩阵的系数
     * 
     */
    void Calculate();

	/**
	* @brief 获取单元elementID处的beta值
	*
	* @param[in] elementID 单元编号
	* @return const Scalar&
	*/
	const Scalar &GetBeta(const int &elementID){ return beta.GetValue(elementID);}
    
	/**
	* @brief 预处理给定单元的残值
	*
	* @param[in] elementID 单元编号
	* @param[in,out] massFlux 质量通量
	* @param[in,out] momentumFlux 动量通量
	* @param[in,out] energyFlux 能量通量
	*/
	void CalculateNewFlux(const int &elementID, Scalar &massFlux, Vector &momentumFlux, Scalar &energyFlux);

	/**
	* @brief 预处理给定控制面的人工粘性残值
	*
	* @param[in] faceID 面编号
	* @param[in,out] massFlux 质量通量
	* @param[in,out] momentumFlux 动量通量
	* @param[in,out] energyFlux 能量通量
	*/
	void CalculatePrecondionDissipationResidual(const int &faceID, Scalar &massFlux, Vector &momentumFlux, Scalar &energyFlux);
	
private:
    Package::FlowPackage &flowPackage; ///< 包含流场和残差场的数据包
    Mesh *mesh; ///< 网格指针
    const Material::Flow::Materials &material; ///< 材料对象

    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度
	ElementField<Scalar> &A; ///< 声速

	ElementField<Scalar> beta; ///< 预处理中类似人工压缩性系数，忽略面心值的特殊处理，通过体心平均来处理

	Scalar Precon[5][5]; ///< 预处理矩阵

    Scalar MaMin2; ///< K * Ma_inf * Ma_inf
	const Scalar &gamma1; ///< \gamma - 1.0
    const Scalar gamma1Inv; ///< 1.0 / (\gamma - 1.0); 
};

} // namespace Precondition
} // namespace Flow
} // namespace Flux

#endif