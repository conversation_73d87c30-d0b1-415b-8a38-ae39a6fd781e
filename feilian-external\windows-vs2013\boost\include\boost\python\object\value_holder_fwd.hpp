// Copyright <PERSON> 2002.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
#ifndef VALUE_HOLDER_FWD_DWA2002311_HPP
# define VALUE_HOLDER_FWD_DWA2002311_HPP

namespace boost { namespace python { namespace objects { 

struct no_back_reference;

template <class CallbackType = no_back_reference> struct value_holder_generator;

}}} // namespace boost::python::object

#endif // VALUE_HOLDER_FWD_DWA2002311_HPP
