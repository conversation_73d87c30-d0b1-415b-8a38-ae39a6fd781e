// Copyright (C) 2007 <PERSON>
//
// Use, modification and distribution is subject to the Boost Software
// License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//
// This file contains helper data structures for use in transmitting
// properties. The basic idea is to optimize away any storage for the
// properties when no properties are specified.

// File moved
#include <boost/property_map/parallel/detail/untracked_pair.hpp>
