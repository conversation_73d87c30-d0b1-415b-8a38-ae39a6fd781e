﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowConfigureMacro.h
//! <AUTHOR>
//! @brief 流场解算器相关枚举
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-31 乔龙
//     说明：添加注释
//
// 2021-03-29 乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _basic_configure_ConfigureMacro_XX_
#define _basic_configure_ConfigureMacro_XX_

#include <vector>
#include <map>
#include <string>
#include <algorithm>

#include "basic/configure/ConfigureMacro.h"

/**
 * @brief 参数命名空间
 * 
 */
namespace Configure
{

const std::map<std::string, Turbulence::WallDistance> wallDistanceMap =
{
    { "NONE_WALL_DISTANCE", Turbulence::WallDistance::NONE_WALL_DISTANCE },
    { "TRAVERSE", Turbulence::WallDistance::TRAVERSE },
    { "ADT", Turbulence::WallDistance::ADT},
	{ "KDT", Turbulence::WallDistance::KDT }
};

const std::map<Turbulence::WallDistance, std::string> wallDistanceReverseMap =
{
    { Turbulence::WallDistance::NONE_WALL_DISTANCE, "NONE_WALL_DISTANCE" },
    { Turbulence::WallDistance::TRAVERSE, "TRAVERSE" },
    { Turbulence::WallDistance::ADT, "ADT" },
	{ Turbulence::WallDistance::KDT, "KDT" }
};

const std::map<std::string, MultigridType::Type> multigridTypeMap =
{
    { "NONE_MULTIGRID", MultigridType::Type::NONE_MULTIGRID },
    { "V", MultigridType::Type::V },
    { "W", MultigridType::Type::W }
};

const std::map<MultigridType::Type, std::string> multigridTypeReverseMap =
{
    { MultigridType::Type::NONE_MULTIGRID, "NONE_MULTIGRID" },
    { MultigridType::Type::V, "V" },
    { MultigridType::Type::W, "W" }
};

const std::map<std::string, MultigridType::TransferOperator> multigridTransferOperatorMap =
{
    { "NONE_TRANSFER", MultigridType::TransferOperator::NONE_TRANSFER },
    { "INJECTION", MultigridType::TransferOperator::INJECTION },
    { "LINEAR", MultigridType::TransferOperator::LINEAR },
    { "LINEAR_LIMITER", MultigridType::TransferOperator::LINEAR_LIMITER }
};

const std::map<MultigridType::TransferOperator, std::string> multigridTransferOperatorReverseMap =
{
    { MultigridType::TransferOperator::NONE_TRANSFER, "NONE_TRANSFER" },
    { MultigridType::TransferOperator::INJECTION, "INJECTION" },
    { MultigridType::TransferOperator::LINEAR, "LINEAR" },
    { MultigridType::TransferOperator::LINEAR_LIMITER, "LINEAR_LIMITER" }
};

const std::map<std::string, Smoother::Scheme> smoothMap =
{
    { "NONE_SMOOTH", Smoother::Scheme::NONE_SMOOTH },
    { "CONSTANT", Smoother::Scheme::CONSTANT },
    { "DISTANCE_WEIGHT", Smoother::Scheme::DISTANCE_WEIGHT }
};

const std::map<Smoother::Scheme, std::string> smoothReverseMap =
{
    { Smoother::Scheme::NONE_SMOOTH, "NONE_SMOOTH" },
    { Smoother::Scheme::CONSTANT, "CONSTANT" },
    { Smoother::Scheme::DISTANCE_WEIGHT, "DISTANCE_WEIGHT" }
};

const std::map<std::string, FieldManipulation::GradientScheme> gradientMap =
{
    { "GREEN_GAUSS", FieldManipulation::GradientScheme::GREEN_GAUSS },
    { "GREEN_GAUSS_M1", FieldManipulation::GradientScheme::GREEN_GAUSS_M1},
    { "GREEN_GAUSS_M2", FieldManipulation::GradientScheme::GREEN_GAUSS_M2},
    { "GREEN_GAUSS_M3", FieldManipulation::GradientScheme::GREEN_GAUSS_M3},
    { "LEAST_SQUARE", FieldManipulation::GradientScheme::LEAST_SQUARE},
    { "LEAST_SQUARE_VERTEX", FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX}
};

const std::map<FieldManipulation::GradientScheme, std::string> gradientReverseMap =
{
    { FieldManipulation::GradientScheme::GREEN_GAUSS, "GREEN_GAUSS" },
    { FieldManipulation::GradientScheme::GREEN_GAUSS_M1, "GREEN_GAUSS_M1" },
    { FieldManipulation::GradientScheme::GREEN_GAUSS_M2, "GREEN_GAUSS_M2" },
    { FieldManipulation::GradientScheme::GREEN_GAUSS_M3, "GREEN_GAUSS_M3" },
    { FieldManipulation::GradientScheme::LEAST_SQUARE, "LEAST_SQUARE" },
    { FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX, "LEAST_SQUARE_VERTEX" }
};

const std::map<std::string, Preprocessor::MeshType> meshTypeMap =
{
	{ "CAS", Preprocessor::MeshType::FLUENT },
	{ "MSH", Preprocessor::MeshType::FLUENT },
    { "CGNS", Preprocessor::MeshType::CGNS },
#if defined(_DevelopMode_)
    { "ADLG", Preprocessor::MeshType::ADLG },
    { "BDLG", Preprocessor::MeshType::BDLG }
#endif
};

const std::map<Preprocessor::MeshType, std::string> meshTypeReverseMap =
{
	{ Preprocessor::MeshType::FLUENT, "Fluent" },
    { Preprocessor::MeshType::CGNS, "CGNS" },
#if defined(_DevelopMode_)
    { Preprocessor::MeshType::ADLG, "ADLG" },
    { Preprocessor::MeshType::BDLG, "BDLG" }
#endif
};

const std::map<std::string, Preprocessor::DecomposeType> decomposeTypeMap =
{
    { "NONE_DECOMPOSE", Preprocessor::DecomposeType::NONE_DECOMPOSE },
    { "METIS", Preprocessor::DecomposeType::METIS }
};

const std::map<Preprocessor::DecomposeType, std::string> decomposeTypeReverseMap =
{
    { Preprocessor::DecomposeType::NONE_DECOMPOSE, "NONE_DECOMPOSE" },
    { Preprocessor::DecomposeType::METIS, "METIS" }
};

const std::map<std::string, Preprocessor::AgglomerateType> agglomerateTypeMap =
{
    { "NONE_AGGLOMERATE", Preprocessor::AgglomerateType::NONE_AGGLOMERATE },
    { "MGRIDGEN", Preprocessor::AgglomerateType::MGRIDGEN },
    { "SEED", Preprocessor::AgglomerateType::SEED }
};

const std::map<Preprocessor::AgglomerateType, std::string> agglomerateTypeReverseMap =
{
    { Preprocessor::AgglomerateType::NONE_AGGLOMERATE, "NONE_AGGLOMERATE" },
    { Preprocessor::AgglomerateType::MGRIDGEN, "MGRIDGEN" },
    { Preprocessor::AgglomerateType::SEED, "SEED" }
};

const std::map<std::string, Preprocessor::RenumberType> renumberTypeMap =
{
    { "NONE_RENUMBER", Preprocessor::RenumberType::NONE_RENUMBER },
    { "RCM", Preprocessor::RenumberType::RCM }
};

const std::map<Preprocessor::RenumberType, std::string> renumberTypeReverseMap =
{
    { Preprocessor::RenumberType::NONE_RENUMBER, "NONE_RENUMBER" },
    { Preprocessor::RenumberType::RCM, "RCM" }
};

const std::map<std::string, Post::Type> resultTypeMap =
{
    { "TECPLOT", Post::Type::TECPLOT },
    { "ENSIGHT", Post::Type::ENSIGHT },
	{ "VTK", Post::Type::VTK },
	{ "CGNS", Post::Type::CGNS },
	{ "AUTO", Post::Type::AUTO }
};

const std::map<Post::Type, std::string> resultTypeReverseMap =
{
    { Post::Type::TECPLOT, "TECPLOT" },
    { Post::Type::ENSIGHT, "ENSIGHT" },
	{ Post::Type::VTK, "VTK" },
	{ Post::Type::CGNS, "CGNS" },
	{ Post::Type::AUTO, "AUTO" }
};

const std::map<std::string, Post::Position> resultPositionMap =
{
    { "CELL_CENTER", Post::Position::CELL_CENTER },
    { "CELL_VERTICE", Post::Position::CELL_VERTICE }
};

const std::map<Post::Position, std::string> resultPositionReverseMap =
{
    { Post::Position::CELL_CENTER, "CELL_CENTER" },
    { Post::Position::CELL_VERTICE, "CELL_VERTICE" }
};

const std::map<std::string, Boundary::Type> boundaryTypeMap =
{
    { "INTERIOR", Boundary::Type::INTERIOR },
    { "EXTRAPOLATION", Boundary::Type::EXTRAPOLATION },
    { "SYMMETRY", Boundary::Type::SYMMETRY },
    { "FARFIELD", Boundary::Type::FARFIELD },

#if defined(_DevelopMode_)
	{ "PERIODIC", Boundary::Type::PERIODIC },
    { "OVERSET", Boundary::Type::OVERSET },
#endif

    { "INFLOW_SPECIFY", Boundary::Type::INFLOW_SPECIFY },
    { "INFLOW_TOTAL_CONDITION", Boundary::Type::INFLOW_TOTAL_CONDITION },
    { "INFLOW_TOTAL_CONDITION_MACH", Boundary::Type::INFLOW_TOTAL_CONDITION_MACH },
    { "MASSFLOW_INLET", Boundary::Type::MASSFLOW_INLET },
    { "MASSFLOW_OUTLET", Boundary::Type::MASSFLOW_OUTLET },
    { "OUTFLOW_PRESSURE", Boundary::Type::OUTFLOW_PRESSURE },
	{ "NACELLE_INLET", Boundary::Type::NACELLE_INLET },
	{ "NACELLE_EXHAUST", Boundary::Type::NACELLE_EXHAUST },

    { "WIND_TUNNEL_WALL_PRESSURE", Boundary::Type::WIND_TUNNEL_WALL_PRESSURE },
    
    { "WALL_ADIABATIC", Boundary::Type::WALL_ADIABATIC },
    { "WALL_ISOTHERMAL", Boundary::Type::WALL_ISOTHERMAL },
    { "WALL_HEAT_FLUX", Boundary::Type::WALL_HEAT_FLUX },
#if defined(_DevelopMode_)
    { "WALL_HEATFLUX_FILE", Boundary::Type::WALL_HEATFLUX_FILE },
    { "Wall_TEMPERATURE_FILE", Boundary::Type::Wall_TEMPERATURE_FILE },
#endif
	{ "WALL_MOVING", Boundary::Type::WALL_MOVING },
	{ "WALL_SLIPPING", Boundary::Type::WALL_SLIPPING },
#if defined(_DevelopMode_)
    { "WALL_ROTATE", Boundary::Type::WALL_ROTATE },
    { "WALL_RIBLETS", Boundary::Type::WALL_RIBLETS },
#endif

	{ "SYNTHETIC_JET", Boundary::Type::SYNTHETIC_JET }
};

const std::map< Boundary::Type, std::vector<std::string>> boundaryPropertyMap =
{
    { Boundary::Type::INTERIOR, std::vector<std::string>{} },
    { Boundary::Type::EXTRAPOLATION, std::vector<std::string>{} },
    { Boundary::Type::SYMMETRY, std::vector<std::string>{} },
    { Boundary::Type::FARFIELD, std::vector<std::string>{} },

	{ Boundary::Type::PERIODIC, std::vector<std::string>{ "couplePatchID", "translation_XYZ", "rotationAxis1_XYZ", "rotationAxis2_XYZ", "rotationAngle"} },
	{ Boundary::Type::OVERSET, std::vector<std::string>{} },

    { Boundary::Type::INFLOW_SPECIFY, std::vector<std::string>{ "density", "velocity_XYZ", "pressure" } },
    { Boundary::Type::INFLOW_TOTAL_CONDITION, std::vector<std::string>{ "totalPressure", "totalTemperature" } },
    { Boundary::Type::INFLOW_TOTAL_CONDITION_MACH, std::vector<std::string>{"mach", "totalPressure", "totalTemperature", "alpha", "beta"} },
    { Boundary::Type::MASSFLOW_INLET, std::vector<std::string>{"massFlowRate"} },
    { Boundary::Type::MASSFLOW_OUTLET, std::vector<std::string>{"massFlowRate"} },
    { Boundary::Type::OUTFLOW_PRESSURE, std::vector<std::string>{"staticPressure"} },
	{ Boundary::Type::NACELLE_INLET, std::vector<std::string>{"eps", "massFlow", "couplePatchIDGlobal"} },
	{ Boundary::Type::NACELLE_EXHAUST, std::vector<std::string>{"totalPressure", "totalTemperature"} },

    { Boundary::Type::WIND_TUNNEL_WALL_PRESSURE, std::vector<std::string>{"fileName"} },

    { Boundary::Type::WALL_ADIABATIC, std::vector<std::string>{} },
    { Boundary::Type::WALL_ISOTHERMAL, std::vector<std::string>{"temperature"} },
    { Boundary::Type::WALL_HEAT_FLUX, std::vector<std::string>{"heatFlux"} },
    { Boundary::Type::WALL_HEATFLUX_FILE, std::vector<std::string>{"fileName"} },
    { Boundary::Type::Wall_TEMPERATURE_FILE, std::vector<std::string>{"fileName"} },
	{ Boundary::Type::WALL_MOVING, std::vector<std::string>{"velocity_XYZ"} },
    { Boundary::Type::WALL_SLIPPING, std::vector<std::string>{} },
    { Boundary::Type::WALL_ROTATE, std::vector<std::string>{"Omega","origin"} },
    { Boundary::Type::WALL_RIBLETS, std::vector<std::string>{"s"} },

	{ Boundary::Type::SYNTHETIC_JET, std::vector<std::string>{"direction_XYZ", "velocityMean", "velocityAmplitude", "frequency", "theta"} }
};

const std::map<Boundary::Type, std::string> boundaryTypeReverseMap =
{
    { Boundary::Type::INTERIOR, "INTERIOR" },
    { Boundary::Type::EXTRAPOLATION, "EXTRAPOLATION" },
    { Boundary::Type::SYMMETRY, "SYMMETRY" },
    { Boundary::Type::FARFIELD, "FARFIELD" },

#if defined(_DevelopMode_)
	{ Boundary::Type::PERIODIC, "PERIODIC" },
	{ Boundary::Type::OVERSET, "OVERSET" },
#endif

    { Boundary::Type::INFLOW_SPECIFY, "INFLOW_SPECIFY" },
    { Boundary::Type::INFLOW_TOTAL_CONDITION, "INFLOW_TOTAL_CONDITION" },
    { Boundary::Type::INFLOW_TOTAL_CONDITION_MACH, "INFLOW_TOTAL_CONDITION_MACH" },
	{ Boundary::Type::OUTFLOW_PRESSURE, "OUTFLOW_PRESSURE" },
	{ Boundary::Type::MASSFLOW_INLET, "MASSFLOW_INLET" },
	{ Boundary::Type::MASSFLOW_OUTLET, "MASSFLOW_OUTLET" },
	{ Boundary::Type::NACELLE_INLET, "NACELLE_INLET" },
	{ Boundary::Type::NACELLE_EXHAUST, "NACELLE_EXHAUST" },

    { Boundary::Type::WIND_TUNNEL_WALL_PRESSURE, "WIND_TUNNEL_WALL_PRESSURE" },

    { Boundary::Type::WALL_ADIABATIC, "WALL_ADIABATIC" },
    { Boundary::Type::WALL_ISOTHERMAL, "WALL_ISOTHERMAL" },
    { Boundary::Type::WALL_HEAT_FLUX, "WALL_HEAT_FLUX" },
    { Boundary::Type::WALL_HEATFLUX_FILE, "WALL_HEATFLUX_FILE" },
    { Boundary::Type::Wall_TEMPERATURE_FILE, "Wall_TEMPERATURE_FILE" },
	{ Boundary::Type::WALL_MOVING, "WALL_MOVING" },
	{ Boundary::Type::WALL_SLIPPING, "WALL_SLIPPING" },
    { Boundary::Type::WALL_ROTATE, "WALL_ROTATE" },
    { Boundary::Type::WALL_RIBLETS, "WALL_RIBLETS" },

	{ Boundary::Type::SYNTHETIC_JET, "SYNTHETIC_JET" }
};

const std::map< std::string, OversetType::InterpolationType > oversetMap
{
	{ "INVERSE_DISTANCE", OversetType::InverseDistance },
    { "LINEAR", OversetType::Linear },
    { "LEASTSQUARE", OversetType::LeastSquare }
};

const std::map< OversetType::InterpolationType, std::string > oversetReverseMap
{
	{ OversetType::InverseDistance, "INVERSE_DISTANCE" },
    { OversetType::Linear, "LINEAR" },
    { OversetType::LeastSquare, "LEASTSQUARE" }
};

const std::map< std::string, MotionType::Type > motionMap
{
	{ "STATIONARY", MotionType::Type::STATIONARY },
	{ "TRANSLATION_ROTATION", MotionType::Type::TRANSLATION_ROTATION },
	{ "XDOF", MotionType::Type::XDOF },
	{ "MORPHING", MotionType::Type::MORPHING }
};

const std::map< MotionType::Type, std::string > motionReverseMap
{
	{ MotionType::Type::STATIONARY, "STATIONARY" },
	{ MotionType::Type::TRANSLATION_ROTATION, "TRANSLATION_ROTATION" },
	{ MotionType::Type::XDOF, "XDOF" },
	{ MotionType::Type::MORPHING, "MORPHING" }
};
} // namespace Configure

#endif