//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_FUNCTIONAL_HPP
#define BOOST_COMPUTE_FUNCTIONAL_HPP

/// \file
///
/// Meta-header to include all Boost.Compute functional headers.

#include <boost/compute/functional/as.hpp>
#include <boost/compute/functional/atomic.hpp>
#include <boost/compute/functional/common.hpp>
#include <boost/compute/functional/convert.hpp>
#include <boost/compute/functional/field.hpp>
#include <boost/compute/functional/geometry.hpp>
#include <boost/compute/functional/get.hpp>
#include <boost/compute/functional/hash.hpp>
#include <boost/compute/functional/identity.hpp>
#include <boost/compute/functional/integer.hpp>
#include <boost/compute/functional/logical.hpp>
#include <boost/compute/functional/math.hpp>
#include <boost/compute/functional/operator.hpp>
#include <boost/compute/functional/popcount.hpp>
#include <boost/compute/functional/relational.hpp>

#endif // BOOST_COMPUTE_FUNCTIONAL_HPP
