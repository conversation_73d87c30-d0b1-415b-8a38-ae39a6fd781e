 #ifndef _TECGLBL_H
 #define _TECGLBL_H
#include "MASTER.h"
#include "GLOBAL.h"
extern ___372 ___2028;
 #  define MANAGESTATE
 #define ___4039 (0)
 #define TECUTILINVALIDMAP   (0)
 #define TECUTILINVALIDZONE  (0)
 #define TECUTILINVALIDVAR   (0)
 #define TECUTILINVALIDELEM  (0)
typedef struct ___92 *___91;
 #define TECUTILBADZONENUMBER 0
 #define TECUTILBADVARNUMBER  0
 #define TECUTILAUTOMNEMONIC  1
 #define TECUTIL_NO_NEIGHBORING_ZONE 0
 #define TECUTIL_NO_NEIGHBORING_ELEM 0
 #define TECUTIL_BOUNDARY_FACE (-1)
 #endif  
