﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshSupport.h
//! <AUTHOR>
//! @brief 网格的基础数据
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化
//    本文件主要包含如下7种与网格相关类的定义：
//    1.坐标点
//    2.网格点
//    3.网格面
//    4.网格单元
//    5.面构成的区域
//    6.虚拟单元
//    7.嵌套网格区域
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_mesh_MeshSupport_
#define _basic_mesh_MeshSupport_

#include "basic/common/ConfigUtility.h"

// 用于友元类的声明（暂时）
class Mesh;
class BaseMesh;
class DecomposeManager;
class SubMesh;
class MeshConverter;
class MeshSorting;
class CgnsMeshBase;
class CgnsMesh;
class CgnsMeshStructured;
class DlgMesh;
class DualMesh;
class FluentMeshBlock;
class SU2_Mesh;
class OversetMesh;
class WallDistanceManager;
class BackgroundGrid;

/*************************************************************************************************/
/// 坐标点的定义
typedef Vector Node;

/*************************************************************************************************/
/// 网格点的定义
class Vertice
{
public:
    /// 构造函数
    Vertice(){}

protected:    
    std::vector<int> v_elemID; ///< 与点邻接的单元编号容器    
    std::vector<int> v_faceID; ///< 与点邻接的面编号容器

#if defined(_BaseParallelMPI_)
public:
    /// 并行数据发送使用
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & v_elemID;
        ar & v_faceID;
    }
#endif

    friend class DlgMesh;    
};

/*************************************************************************************************/
// 网格面的定义
class Face
{
public:    
    Face(); ///< 构造函数
    Face(const int &Node1, const int &Node2); ///< 构造函数: 两点成面    
    Face(const int &Node1, const int &Node2, const int &Node3); ///< 构造函数: 三点成面    
    Face(const int &Node1, const int &Node2, const int &Node3, const int &Node4); ///< 构造函数: 四点成面
    Face(const std::vector<int> &nodeIDList); ///< 构造函数：多点成面
    Face(const Face &face); ///< 构造函数
    ~Face(); ///< 析构函数

    /// 获得面的面心
    const Vector &GetCenter()const { return this->center; }

    /// 获得面的面积模
    const Scalar &GetArea()const { return this->areaMag; }

    /// 获得面的单位法矢
    const Vector &GetNormal()const { return this->normal; }

    /// 获得面的拥有单元编号
    const int &GetOwnerID()const { return this->n_owner; }

    /// 获得面的邻接单元编号
    const int &GetNeighborID()const { return this->n_neighbor; }

    /// 获得面的点数量
    const int GetNodeSize()const { return (int)this->v_nodeID.size(); }
    
    /// 获得面的点构成
    const int &GetNodeID(const int &index)const { return this->v_nodeID[index]; }

    /// 计算面心和面积
    void CalculateCenterAndArea(std::vector<Node>&);

    /// 修正面的旋转方向
    void CorrectSide();    

    /// 点编号反向排列
    void ReverseNodeID();

    void operator=(const Face &face_);
    
    void Write(std::fstream &file, const bool &binary = true) const;
    void Read(std::fstream &file, const bool &binary = true);

protected:
    int n_owner; ///< 面的拥有单元编号    
    int n_neighbor; ///< 面的邻接单元编号
    Scalar areaMag; ///< 面积大小
    Vector center; ///< 面心
    Vector normal; ///< 面的单位法矢
    std::vector<int> v_nodeID; ///< 面的点构成列表
    
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & n_owner;
        ar & n_neighbor;
        ar & areaMag;
        ar & center;
        ar & normal;
        ar & v_nodeID;
    }
#endif    

    // 友元类（暂时）
    friend class Element;
    friend class Mesh;
    friend class BaseMesh;
    friend class DecomposeManager;
    friend class AgglomerateManager;    
    friend class DualMesh;
    friend class CgnsMeshBase;
    friend class CgnsMesh;
    friend class CgnsMeshStructured;
    friend class DlgMesh;
    friend class FluentMeshBlock;
    friend class SU2_Mesh;
    friend class SubMesh;
    friend class MeshConverter;
    friend class MeshSorting;
    friend class FaceZone; 
    friend class WallDistanceManager;   
    friend class OversetMesh;
};

/*************************************************************************************************/
// 网格单元的定义
class Element
{
public:
    enum ElemType ///< 枚举：单元存在状态
    {
        real,
        ghost,
        ghostBoundary,
        ghostParallel,
        ghostOverset,
        ghostMultigrid,
        unavailable
    };
    enum ElemShapeType ///< 枚举：单元几何类型
    {
        estMixed            = 0,
        estTriangular        = 1,
        estTetrahedral        = 2,
        estQuadrilateral    = 3,
        estHexahedral        = 4,
        estPyramid            = 5,
        estWedge            = 6,
        estPolyhedron        = 7,
        estPolygon             = 8,
        estLine                = 9,
        estNoType
    };

public:
    Element(Element::ElemShapeType elemType = estNoType); ///< 构造函数
    Element(const Element &e); ///< 构造函数
    ~Element(); ///< 析构函数

    /// 获取单元类型
    const ElemType &GetElemType()const { return this->et_type; }

    /// 获取单元形状类型
    const ElemShapeType &GetElemShapeType()const { return this->est_shapeType; }

    /// 获得单元的体心
    const Vector &GetCenter()const { return this->center; }

    /// 获得单元的体积
    const Scalar &GetVolume()const { return this->volume; }
    
    /// 获得单元的点数量
    const int GetNodeSize()const { return (int)this->v_nodeID.size(); }

    /// 获得单元的点编号
    const int &GetNodeID(const int &index)const { return v_nodeID[index]; }

    /// 获得单元的面数量
    const int GetFaceSize()const { return (int)this->v_faceID.size(); }

    /// 获得单元的面编号
    const int &GetFaceID(const int &index)const { return v_faceID[index]; }

    /// 根据给定的点容器计算体心和体积
    void CalculateCenterAndVolume(std::vector<Node>& gridNode);

    /// 根据给定的面容器计算体心和体积
    void CalculateCenterAndVolume(std::vector<Face>& gridFace);

    void operator=(const Element &element);

    void Write(std::fstream &file, const bool &binary = true)const;
    void Read(std::fstream &file, const bool &binary = true);

protected:
    ElemType et_type; ///< 单元存在状态，实单元还是虚单元
    ElemShapeType est_shapeType; ///< 单元几何类型
    Scalar volume; ///< 单元的体积
    Vector center; ///< 单元的体心
    std::vector<int> v_faceID; ///< 单元的面编号构成
    std::vector<int> v_nodeID; ///< 单元的点编号构成
    Vector deformation; ///< 单元变形量

    // 以下成员数据和函数将被舍弃
    // *****************************************************************************************
    int n_ElementZoneID; ///< 单元域编号
    // *****************************************************************************************

#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & est_shapeType;
        ar & et_type;
        ar & n_ElementZoneID;
        ar & volume;
        ar & center;
        ar & v_faceID;
        ar & v_nodeID;
    }
#endif    

    // 友元类（暂时）
    friend class Mesh;
    friend class BaseMesh;
    friend class DecomposeManager;
    friend class CgnsMesh;
    friend class CgnsMeshStructured;
    friend class DlgMesh;
    friend class DualMesh;
    friend class AgglomerateManager;
    friend class MeshConverter;
    friend class MeshSorting;
    friend class FluentMeshBlock;
    friend class SU2_Mesh;
    friend class SubMesh;
    friend class OversetMesh;
    friend class GhostElement;    
};

/*************************************************************************************************/
/// 面构成的区域
class FaceZone
{
public:
    enum FZType ///< 面区域的类型（暂时没有使用）
    {
        fztInterior = 0,
        fztExternal = 1,
        fztInternal = 2,
        fztMixed = 3,
        fztParaPartitions = 4,
        fztNoType
    };
    
    enum BCType ///< 面区域的边界类型（暂时没有使用）
    {
        bcInterior = 2,
        bcWall = 3,
        bcPressureInlet = 4,
        bcPressureOutlet = 5,
        bcSymmetry = 7,
        bcPeriodicShadow = 8,
        bcPressureFarfield = 9,
        bcVelocityInlet = 10,
        bcPeriodic = 12,
        bcFan = 14,
        bcMassFlowInlet = 20,
        bcInterface = 24,
        bcParent = 31,
        bcOutflow = 36,
        bcAxis = 37,
        bcNoType = 0
    };

public:
    FaceZone() :n_faceZoneID(-1), bc_Type(bcNoType), faceZoneType(fztNoType){};
    const int GetFaceSize()const  { return (int)this->v_faceID.size(); }
    const int &GetFaceID(const int &index)const  { return this->v_faceID[index]; }
    const std::string &GetZoneName()const  { return this->name; }    

protected:
    int n_faceZoneID;
    std::string    name;
    std::vector<int> v_faceID;
    BCType bc_Type;
    FZType faceZoneType;
    std::vector<Node> v_node;
    std::vector<Face> v_face;
    std::vector<size_t> v_globalID;
    std::vector<Node> v_center;
    std::vector<Scalar> v_volume;
    
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & n_faceZoneID;
        ar & name;
        ar & v_faceID;
        ar & bc_Type;
        ar & faceZoneType;
        ar & v_node;
        ar & v_face;
        ar & v_globalID;
        ar & v_center;
        ar & v_volume;
    }
#endif   

    // 友元类（暂时）
    friend class Mesh;
    friend class BaseMesh;
    friend class DecomposeManager;
    friend class AgglomerateManager;
    friend class FluentMeshBlock;
    friend class DualMesh;
    friend class SU2_Mesh;
    friend class CgnsMesh;
    friend class DlgMesh;
    friend class MeshConverter;
    friend class MeshSorting;   
};

/*************************************************************************************************/
/// 虚单元的定义
class GhostElement
{
public:
    GhostElement()
        : procPair(std::make_pair(-1, -1)), localIDPair(std::make_pair(-1, -1)), ID(-1)
        {}
    
    GhostElement(const std::pair<int, int> &procPair_,
                 const std::pair<int, int> &localIDPair_,
                 const int &ID_ = -1)
        : procPair(procPair_), localIDPair(localIDPair_), ID(ID_)
        {}
    
    const int &GetID()const {return this->ID;}
    const std::pair<int, int> &GetProcessorIDPair()const {return this->procPair;}
    const std::pair<int, int> &GetLocalIDPair()const {return this->localIDPair;}

    void Write(std::fstream &file, const bool &binary = true)const;
    void Read(std::fstream &file, const bool &binary = true);
    
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & ID;
        ar & procPair;
        ar & localIDPair;
    }
#endif

protected:
    int ID; ///< 粗网格虚单元时为粗单元编号，并行虚单元时为面编号
    std::pair<int, int> procPair; ///< ghost processor ID (owner processor) and real processor ID (neighbor processor)
    std::pair<int, int> localIDPair; ///< ghost local ID and real local ID

    //友元类（暂时）
    friend class Mesh;
    friend class SubMesh;
    friend class BackgroundGrid;
};

/*************************************************************************************************/
/// 重叠网格定义，启用时用specialmodule中的真实定义，不启用时用此处的空定义
#if defined(_EnableOverset_)
    #include "feilian-specialmodule/oversetMesh/Acceptor.h"
    #include "feilian-specialmodule/oversetMesh/Donor.h"
#endif
#ifndef _specialModule_oversetMesh_Acceptor_
    class Acceptor{};
#endif
#ifndef _specialModule_oversetMesh_Donor_
    class Donor{};
#endif

/// 重叠网格区域的定义
class OversetRegion
{
public:
    OversetRegion(){};
    ~OversetRegion(){};

public:
    // 插值关系容器的可修改引用
    std::vector<std::vector<Acceptor>> &GetAcceptorList() { return this->acceptorList;} 
    std::vector<std::vector<Donor>> &GetDonorList() { return this->donorList;} 
    // 插值关系容器的不可修改引用
    const std::vector<std::vector<Acceptor>> &GetAcceptorList() const { return this->acceptorList; }
    const std::vector<std::vector<Donor>> &GetDonorList() const { return this->donorList; }
private:
    std::vector<std::vector<Acceptor>> acceptorList; //当前进程中作为插值单元的信息，第一层按照贡献单元所在进程排列
    std::vector<std::vector<Donor>> donorList; //当前进程中作为贡献单元的信息，第一层按插值单元所在进程排列
};

#endif // _basic_mesh_MeshSupport_

