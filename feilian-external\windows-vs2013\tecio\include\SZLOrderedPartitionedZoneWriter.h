 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <map>
#include <vector>
#include <boost/unordered_set.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "SZLOrderedPartitionedZoneHeaderWriter.h"
#include "IJKPartitionTree.h"
#include "IJKZoneInfo.h"
#include "PartitionTecUtilDecorator.h"
#include "ZoneWriterAbstract.h"
namespace tecplot { namespace ___3933 { class ___1352; class ZoneInfoCache; class ItemSetIterator; class SZLOrderedPartitionedZoneWriter : public ___4709 { public: SZLOrderedPartitionedZoneWriter( ItemSetIterator&              varIter, ___4636                   zone, ___4636                   ___341, std::vector<___372> const& ___4564, ___372                     ___4499, ___37&                   ___36, ZoneInfoCache&                zoneInfoCache); virtual ~SZLOrderedPartitionedZoneWriter(); protected: typedef std::map<___2090::SubzoneOffset_t, std::vector<___2479> > SubzoneMinMaxMap; void applyCellMinMaxesToNeighborNodeSubzones( ___2090::___2980 ___2977, std::vector<___1352> const& fieldDatas, std::vector<boost::shared_ptr<___1881> >& partitionInfos, ___1863 const& partitionTree); void getPartitionExtentsWithGhostNodes( ___2090::___2980 ___2977, ___1844 &partitionMinIJK, ___1844 &partitionMaxIJK); void trimGhostNodes(___1844 &partitionMaxIJK); void getPartitionExtentsWithoutGhostNodes( ___2090::___2980 ___2977, ___1844 &partitionMinIJK, ___1844 &partitionMaxIJK); void retrieveNodalFieldDataPtrsForPartition( ___37& partitionTecUtilDecorator, ___2090::___2980 ___2977, std::vector<___1352> &nodalFieldDatas); void throwIfBadIntersectionRange( ___1855 const& intersectionRange, ___2090::___2980 ___2977, ___2090::___2980 neighborPartition); void getCellMinMaxes( std::vector<___2479>& cellMinMaxes, ___2227 ___462, ___1844 const& dimensions, std::vector<___1352> const& fieldDatas); virtual uint64_t zoneConnectivityFileSize(bool ___2002); virtual uint64_t zoneDataFileSize(bool ___2002); virtual uint64_t zoneHeaderFileSize(bool ___2002); virtual ___372 writeZoneConnectivity(FileWriterInterface& szpltFile); virtual ___372 writeZoneData(FileWriterInterface& szpltFile); virtual ___372 writeZoneHeader(FileWriterInterface& szpltFile); SZLOrderedPartitionedZoneHeaderWriter m_headerWriter; ZoneInfoCache& ___2680; PartitionTecUtilDecorator m_partitionTecUtil; ___2240<int32_t> m_partitionFileNums; UInt64Array m_partitionHeaderFilePositions; UInt64Array m_partitionMinNodeNumbers; UInt64Array m_partitionMaxNodeNumbers; VarZoneMinMaxArray m_varPartitionMinMaxes; std::map<___4636, boost::shared_ptr<___4709> > m_partitionWriters; private: void exchangeGhostInfo( std::vector<boost::shared_ptr<___1881> >& partitionInfos, std::vector<___1864> const& ___2981); void applyCellMinMaxesToNeighborsInRange( std::vector<___1864> const& neighborItems, ___2090::___2980 ___2977, ___1855 const& partitionRange, ___1844 const& partitionOffsetIJK, ___1844 const& partitionDimensionsIJK, std::vector<___1352> const& fieldDatas, std::vector<boost::shared_ptr<___1881> >& partitionInfos); void createPartitionWriters(); }; }}
