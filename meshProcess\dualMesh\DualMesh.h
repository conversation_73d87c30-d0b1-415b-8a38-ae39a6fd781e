﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DualMesh.h
//! <AUTHOR>
//! @brief 基于原始网格生成对偶网格。
//! @date 2021-07-29
//
//------------------------------修改日志----------------------------------------
// 2021-07-29 乔龙
//    说明：建立对偶网格转化功能。
//------------------------------------------------------------------------------

#ifndef _meshProcess_dualMesh_DualMesh_
#define _meshProcess_dualMesh_DualMesh_

#include "basic/mesh/Mesh.h"

/**
 * @brief 基于adlg格式的对偶网格类
 * 
 */
class DualMesh  
{
public:    
	/**
	* @brief 构造函数
	*
	* @param[in] oldMesh_ 原始网格
	*/
    DualMesh(Mesh *oldMesh_);

	/**
	* @brief 析构函数
	*
	*/
    ~DualMesh();

	/**
	* @brief 将原始网格转化为对偶网格的主函数
	*
	*/
    void PrimaryToDual();

	/**
	* @brief 将对偶网格和给定网格交换
	*
	* @param[in,out] oldMesh_ 将对偶网格交换给oldMesh_
	*/
    void SwapMesh(Mesh *oldMesh_);

private:
	/**
	* @brief 创建边结构
	*
	*/
    void CreateEdgeData();
    
	/**
	* @brief 建立拓扑信息
	*
	*/
    int BuildTopology();
    
	/**
	* @brief 建立对偶网格的点
	*
	*/
    void CreateDualNodes();
    
	/**
	* @brief 计算体积和面积
	*
	*/
    void CalculateVolumeAndArea();
    
	/**
	* @brief 查找nodeList中与nodeID1相邻的另外一个节点（非nodeID2）
	*
	* @param[in] nodeList 待查找列表
	* @param[in] nodeID1 点1编号
	* @param[in] nodeID2 点2编号
	*/
    int FindAdjacentNodeID(const std::vector<int> &nodeList, const int &nodeID1, const int &nodeID2);
    
	/**
	* @brief 在v0表中插入一个新值（不重复）
	*
	* @param[in,out] v0 待插入列表
	* @param[in] value 新值
	*/
	void InsertWithNorepeat(std::vector<int> &v0, const int &value);

	/**
	* @brief 判定有向边是否在面中，不在返回0，正向返回1，反正返回-1
	*
	* @param[in] start 边的起点
	* @param[in] end 边的终点
	* @param[in] faceNodeList 面的点构成
	*/
	int JudgeEdgeAndFace(const int &start, const int &end, const std::vector<int> &faceNodeList);

private: 
    Mesh* oldMesh; ///< 原始网格指针
	Mesh* dualMesh; ///< 对偶网格指针
	bool dim2; ///< 二维标识，是二维为真

    /// 原始网格面的边构成
    std::vector<std::vector<int>> faceEdgeIDList;

    /// 原始网格边的点构成
    std::vector<std::pair<int, int>> edgeNodes;

    /// 原始网格点相邻的边
    std::vector<std::vector<int>> nodeEdgesList;

    /// 原始网格边界面点构成容器
    std::vector<std::vector<int>> boundaryNodesList;

    /// 内部面编号容器
    std::vector<int> v_interiorFaceID;    
};
#endif