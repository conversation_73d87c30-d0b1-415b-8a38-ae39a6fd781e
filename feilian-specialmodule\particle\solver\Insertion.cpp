﻿#include "feilian-specialmodule/particle/solver/Insertion.h"

namespace Particle
{
	Insertion::Insertion(const Configure::Particle::ParticleConfigure &configure_,
		std::vector<Particle *> &particles_,
		DistributionProperty *distributionProperty_)
		: configure(configure_), particles(particles_), distributionProperty(distributionProperty_)
{
	if (configure.insertion.method == Configure::Particle::InsertionMethod::BOX)
	{
		Print("从平面进行注入...", 3);
		this->totalSteps = configure.insertion.insertionBox->steps;
	}
	else if (configure.insertion.method == Configure::Particle::InsertionMethod::FILE)
	{
		Print("从文件注入...", 3);
		this->totalSteps = 1;
	}

    this->numParticles = 0;
    this->numParticlesLast = 0;
	this->numParticlesMax = configure.distribution.numParticles;

    this->maxIteration = 100;
    
	minIteration = Min(300, totalSteps);
	if (int(totalSteps / minIteration) < 10) minIteration = Max(1, totalSteps / 10);
    
	minInsertion = 1;
	if (int(totalSteps / minIteration * minInsertion) > numParticlesMax)
		minIteration = Max(1, int(totalSteps / numParticlesMax) - 1);
    
	this->numParticlesInsertedNext = Min(minInsertion, numParticlesMax);
    this->numIterationInsertedNext = 0;
}

void Insertion::SetParticlesNumber(const int &numParticles_)
{
	this->numParticles = numParticles_;
	this->totalSteps += 2 * this->minInsertion;
	this->Calculate_AllParams();
}

bool Insertion::InsertParticle(int &numPrtcl0, int &iter)
{
    if( iter != this->numIterationInsertedNext ) return false;
    if( this->numParticles == this->numParticlesMax ) return false;
	
	const Vector &minDomain = configure.reference.minDomain;
	const Vector &maxDomain = configure.reference.maxDomain;
	const bool dim2 = (maxDomain.Z() - minDomain.Z()) > SMALL;

	Scalar maxDiameter = 0.0;
	bool finished = false;

	if (configure.insertion.method == Configure::Particle::InsertionMethod::BOX)
	{
		const bool planeInsFlag = configure.insertion.method == Configure::Particle::InsertionMethod::BOX;

		int size = this->maxIteration * this->numParticlesInsertedNext;
		insertedIDs.clear();
		insertedIDs.reserve(numParticlesInsertedNext);
		for (int k = 0; k < size; ++k)
		{
			const int index = insertedIDs.size() + this->numParticles;

			const std::vector<Vector> &nodes = configure.insertion.insertionBox->insertionShapeNodes;
			
			particles[index] = new Particle();

			// TODO::颗粒分布与初始化
			// 根据颗粒分布属性初始化颗粒信息
			this->particles[index]->ID = this->distributionProperty->GetParticleID(index);
			this->particles[index]->type = this->distributionProperty->GetParticlePropertyID(index);
			this->particles[index]->diameter = this->distributionProperty->GetParticleDiameter(index);
			// this->particles[index]->position = this->distributionProperty->GetParticlePosition(index);
			this->particles[index]->flag = Particle::ParticleFlag::NO_INSERTED;

			particles[index]->position = this->GetParticlePosition(nodes);
			
			// 检查新注入颗粒与已有颗粒是否接触     
			// if (!this->isInContact(*particles[index]))
			{
				particles[index]->linearVelocity = configure.insertion.insertionBox->velocity;
				particles[index]->flag = Particle::ParticleFlag::IN_DOMAIN;
				maxDiameter = Max(maxDiameter, particles[index]->diameter);
				insertedIDs.push_back(index);
			}

			if (insertedIDs.size() == this->numParticlesInsertedNext)
			{
				finished = true;
				break;
			}
		}

		numPrtcl0 += insertedIDs.size();
		this->numParticles = numPrtcl0;
	}
	else if (configure.insertion.method == Configure::Particle::InsertionMethod::FILE)
	{
		// 颗粒信息文件
		std::fstream solutionFile;
		const std::string solutionFileName = configure.insertion.insertionFile->fileName;
		const bool binary = configure.insertion.insertionFile->binary;
		if (binary) solutionFile.open(solutionFileName, std::ios::in | std::ios::binary);
		else        solutionFile.open(solutionFileName, std::ios::in);

		std::string strTemp; int numParticlesInDomain;
		IO::Read(solutionFile, strTemp, binary);
		IO::Read(solutionFile, numParticlesInDomain, binary);
		IO::Read(solutionFile, strTemp, binary);

		for (int i = 0; i < numParticlesInDomain; ++i)
		{
			particles[i] = new Particle();
			this->particles[i]->ID = this->distributionProperty->GetParticleID(i);
			this->particles[i]->type = this->distributionProperty->GetParticlePropertyID(i);

			particles[i]->flag = Particle::ParticleFlag::IN_DOMAIN;
			IO::Read(solutionFile, particles[i]->position, binary);
			IO::Read(solutionFile, particles[i]->diameter, binary);
			IO::Read(solutionFile, particles[i]->ID, binary);
			IO::Read(solutionFile, particles[i]->linearVelocity, binary);
			IO::Read(solutionFile, particles[i]->force, binary);
			maxDiameter = Max(maxDiameter, particles[i]->diameter);
		}

		solutionFile.close();

		finished = true;
		this->numParticlesLast = 0;
		this->numParticles = numParticlesInDomain;
	}

	// // 注入未结束，增大注入速度
	// if (!finished) this->insertionVelocity = Max(1.2 * this->insertionVelocity, 0.03);

    this->Calculate_AllParams();

    return true;
}

Vector Insertion::GetParticlePosition(const std::vector<Vector> &nodes)
{
    const int nodeSize = nodes.size();

    // 生成随机数并单位化使其和为1
    Scalar invSum = Scalar0;
    std::vector<Scalar> rands(nodeSize);
    for (int i = 0; i < nodeSize; i++)
    {
        rands[i] = rand();
        invSum += rands[i];
    }
    invSum = 1.0 / invSum;
    for (int i = 0; i < nodeSize; i++) rands[i] *= invSum;

    // 根据平面端点进行加权，得到平面内的随机点
    Vector value = Vector0;
    for (int i = 0; i < nodeSize; i++) value += rands[i] * nodes[i];
    return value;
}

bool Insertion::isInContact(Particle &particle0)
{
    // 遍历当前颗粒
	for (int i = 0; i < this->insertedIDs.size(); ++i)
	{
		if (Overlap(particle0, *particles[insertedIDs[i]]) > Scalar0) return true;
	}
    
    return false;
}

void Insertion::Calculate_AllParams()
{
    // 剩余颗粒数量
    const Scalar numParticlesLeft = this->numParticlesMax - this->numParticles;

    // 剩余迭代步数
    const Scalar iterationLeft     = this->totalSteps - this->numIterationInsertedNext;

    // 注入颗粒数量
	int numParticlesNext = Max(minInsertion, int(1.0 * numParticlesLeft / (iterationLeft / minIteration)) + 1);

    // 当前注入总迭代步数
    this->numIterationInsertedNext += minIteration;
    
    if( this->numParticles + numParticlesNext > this->numParticlesMax )
        numParticlesNext = this->numParticlesMax - this->numParticles;
    
    this->numParticlesInsertedNext = numParticlesNext;
}

} // namespace Particle