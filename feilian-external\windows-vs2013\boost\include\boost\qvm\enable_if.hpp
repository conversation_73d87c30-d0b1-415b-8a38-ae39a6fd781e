//Copyright (c) 2008-2016 <PERSON> and Reverge Studios, Inc.

//Distributed under the Boost Software License, Version 1.0. (See accompanying
//file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef UUID_A4FA0794391911DF85A6622B56D89593
#define UUID_A4FA0794391911DF85A6622B56D89593

#include <boost/utility/enable_if.hpp>

namespace
boost
    {
    namespace
    qvm
        {
        using boost::enable_if;
        using boost::enable_if_c;
        using boost::lazy_enable_if;
        using boost::lazy_enable_if_c;
        }
    }

#endif
