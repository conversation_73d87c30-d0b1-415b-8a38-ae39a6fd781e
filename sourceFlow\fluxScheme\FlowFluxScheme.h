﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowFluxScheme.h
//! <AUTHOR>
//! @brief 进行NS方程通量求解的基类，用于派生具体通量类。
//! @date 2021-03-29
//
//------------------------------修改日志----------------------------------------
// 2021-03-29 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_FlowFluxScheme_
#define _sourceFlow_fluxScheme_FlowFluxScheme_

#include "sourceFlow/package/FlowPackage.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{

/**
 * @brief 包含左右面值的结构体
 * 
 */
struct NSFaceValue
{
public:
    Scalar rhoLeft; ///< 左面密度
    Vector ULeft; ///< 左面速度
    Scalar pLeft; ///< 左面压强
    Scalar rhoRight; ///< 右面密度
    Vector URight; ///< 右面速度
    Scalar pRight; ///< 右面压强

#if defined(_EnableMultiSpecies_)
    std::vector<Scalar> massFractionLeft; ///< 左面组分
    std::vector<Scalar> massFractionRight; ///< 右面组分
#endif
    
    NSFaceValue(const int speciesSize = 0)
    {
        rhoLeft = Scalar0;
        ULeft = Vector0;
        pLeft = Scalar0;
        rhoRight = Scalar0;
        URight = Vector0;
        pRight = Scalar0;

#if defined(_EnableMultiSpecies_)
        massFractionLeft.resize(speciesSize, Scalar0);
        massFractionRight.resize(speciesSize, Scalar0);
#endif
    }
};

/**
 * @brief NS三个方程的面通量值结构体
 * 
 */
struct NSFaceFlux
{
    Scalar massFlux; ///< 质量方程的面通量
    Vector momentumFlux; ///< 动量方程的面通量
    Scalar energyFlux; ///< 能量方程的面通量
#if defined(_EnableMultiSpecies_)
    std::vector<Scalar> massFractionFlux; ///< 组分方程的面通量
#endif

    NSFaceFlux(const int speciesSize = 0)
    {
        massFlux = Scalar0;
        momentumFlux = Vector0;
        energyFlux = Scalar0;
#if defined(_EnableMultiSpecies_)
        massFractionFlux.resize(speciesSize, Scalar0);
#endif
    }
};

/**
 * @brief NS方程通量基类
 * 用于派生具体通量格式，不能直接实例化
 * 
 */
class  FlowFluxScheme
{
public:
    /**
     * @brief 构造函数，创建NS方程通量基类对象
     * 
     * @param[in, out] data 流场包
     */
    FlowFluxScheme(Package::FlowPackage &data);

    /**
     * @brief 通量置零
     * 
     */
    void SetResidualZero();

protected:
    Package::FlowPackage &flowPackage; ///< 包含流场和残差场的数据包
    Mesh *mesh; ///< 网格指针
    const Material::Flow::Materials &material; ///< 材料对象

    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度
    ElementField<Scalar> &H; ///< 总焓
	ElementField<Scalar> &A; ///< 音速

    ElementField<Scalar> &residualMass; ///< 质量残值
    ElementField<Vector> &residualMomentum; ///< 动量残值
    ElementField<Scalar> &residualEnergy; ///< 能量残值

#if defined(_EnableMultiSpecies_)
	const int speciesSize; ///< 组分方程数量
	const std::vector<ElementField<Scalar> *> &massFraction; ///< 组分指针容器
	const std::vector<ElementField<Scalar> *> &residualMassFraction; ///< 组分残值场指针容器
#endif

    const bool nodeCenter; ///< 格点标识，true为格点

	BlockSparseMatrix *jacobian; ///< 隐式方法的jacobian
    const bool &updateJacobian; ///< Jacobian是否更新标识

    int nDim; ///< 网格维度，二维取2，三维取3
    int nVar; ///< 变量数量，二维取4，三维取5

	Matrix Jacobian_i; ///< 面通量对owner单元的Jacobian
    Matrix Jacobian_j; ///< 面通量对neigh单元的Jacobian

    NSFaceFlux faceFlux; ///< 面通量
    NSFaceValue faceValue; ///< 左右面心值
};

} // namespace Flow
} // namespace Flux
#endif 