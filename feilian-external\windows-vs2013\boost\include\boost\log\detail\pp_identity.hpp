/*
 *          Copyright <PERSON><PERSON> 2007 - 2015.
 * Distributed under the Boost Software License, Version 1.0.
 *    (See accompanying file LICENSE_1_0.txt or copy at
 *          http://www.boost.org/LICENSE_1_0.txt)
 */
/*!
 * \file   pp_identity.hpp
 * \author <PERSON><PERSON>
 * \date   12.02.2011
 *
 * This header is the Boost.Log library implementation, see the library documentation
 * at http://www.boost.org/doc/libs/release/libs/log/doc/html/index.html.
 */

#ifndef BOOST_LOG_DETAIL_PP_IDENTITY_HPP_INCLUDED_
#define BOOST_LOG_DETAIL_PP_IDENTITY_HPP_INCLUDED_

#include <boost/log/detail/config.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#define BOOST_LOG_PP_IDENTITY(z, n, data) data

#endif // BOOST_LOG_DETAIL_PP_IDENTITY_HPP_INCLUDED_
