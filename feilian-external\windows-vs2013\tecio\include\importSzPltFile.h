 #pragma once
#include "ThirdPartyHeadersBegin.h"
#  include <string>
#include "ThirdPartyHeadersEnd.h"
#include "xyz.h"
#include "fileStuff.h"
#include "FileReaderInterface.h"
#include "ioDescription.h"
#include "FileDescription.h"
#include "ClassicZoneFileLocations.h"
namespace tecplot { namespace ___3933 { struct ___4707; ___372 readDataFileHeader( ___1399& file, ___1388&     ___1387, uint64_t&            byteOrderIndicator, uint64_t&            dataSetHeaderLocation); ___372 readDataSetHeaderTags( ___1399& file, ___1388&     ___1387, ___3945&         ___3944); ___372 applyDataSetHeaderScalarTagValues( ___3945 const& ___3944, ___4352& ___2843, ___4636& ___2846); ___372 readDatasetTitle( ___1399& file, ___3945&         ___3944, std::string&         datasetTitle); class ZoneMetadata; class PartitionMetadata; ___372 readDataSetHeader( ___1399& file, ___3945&         ___3944, std::string&         datasetTitle, ___4707&     ___4706, ___1392&        ___4629); ZoneType_e zoneTypeFromFileChar(char c); void storeZoneMetadata( ___4707 const& ___4706, ___4636            zone, ___4636            targetBaseZoneOffset, ZoneMetadata&          zoneMetadata); ___372 readAuxDataCount( ___1399& file, ___3945&         ___3944, uint32_t&            auxDataCount); ___372 readAuxData( ___1399& file, ___3945&         ___3944, ___4352           varOffset, ___4636          zoneOffset, uint32_t             auxDataOffset, AuxDataArray&        auxDataArray); ___372 readGeometriesCount( ___1399& file, ___3945&         ___3944, uint32_t&            geomCount); ___372 readGeometries( ___1399& file, ___3945&         ___3944, uint32_t             geomOffset, GeomArray&           geomArray); ___372 readTextsCount( ___1399& file, ___3945&         ___3944, uint32_t&            textCount); ___372 readTexts( ___1399& file, ___3945&         ___3944, uint32_t             textOffset, TextArray&           textArray); ___372 readCustomLabelsCount( ___1399& file, ___3945&         ___3944, uint32_t&            customLabelsCount); ___372 readCustomLabels( ___1399& file, ___3945&         ___3944, uint32_t&            customLabelsOffset, CustomLabelsArray&   customLabelsArray); ___372 readZoneHeaderTags( ___1399& file, ___4636          expectedZone, ___3945&         ___3944); ___372 readZoneHeaders( ___1399&             file, ___1388 const&           ___1387, ___4636                      zoneOffset, ___4636                      expectedZone, ___4352                       varOffset, ___4352                       numFileVars, VarZoneMinMaxArray const&        vzMinMax, ___4382 const& vzFieldDataType, ZoneMetadata&                    zoneMetadata); ___372 readFaceNeighbors( ___1399& file, ___4636          expectedZone, uint64_t&            numValues, ___1965&          ___4299); ___372 readPartitionHeader( ___1399&             file, ___1388 const&           ___1387, ___4352                       numFileVars, ___4352                       targetBaseVarOffset, ___4636                      fileInfoZoneOffset, ___2090::___2980         ___2977, ZoneMetadata const&              zoneMetadata, ___4382 const& vzFieldDataType, PartitionMetadata&               partitionMetadata); ___372 readCszConnectivity( ___1399&         fileWrapper, ___1388 const&       ___1387, ZoneMetadata const&          zoneMetadata, ___2090::___2980     curPartition, ___2090::SubzoneOffset_t cszOffset, CszConnectivity&             cszConnectivity); ___372 readNszConnectivity( ___1399&     fileWrapper, ___1388 const&   ___1387, ___2090::___2980 numRefPtns, ___2090::___2980 curPartition, PartitionArray const&    refPartitions, RefSubzoneOffset_t       numRefCszs, NszConnectivity&         nszConnectivity); }}
