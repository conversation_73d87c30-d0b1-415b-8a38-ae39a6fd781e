/*//////////////////////////////////////////////////////////////////////////////
    Copyright (c) 2011 Jamboree

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//////////////////////////////////////////////////////////////////////////////*/
#ifndef BOOST_SPIRIT_REPOSITORY_SUPPORT_SEEK
#define BOOST_SPIRIT_REPOSITORY_SUPPORT_SEEK

#if defined(_MSC_VER)
#pragma once
#endif


#include <boost/spirit/home/<USER>/terminal.hpp>


namespace boost { namespace spirit { namespace repository
{
    // The seek terminal
    BOOST_SPIRIT_DEFINE_TERMINALS_NAME(( seek, seek_type ))

}}}

#endif
