#ifndef _meshProcess_meshDeform_cgnsTocgns_
#define _meshProcess_meshDeform_cgnsTocgns_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <array>
#include <algorithm>

#include "meshProcess/meshConverter/CgnsMesh.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

class cgnsTocgns
{
	
public:
  
    cgnsTocgns(const std::string &SourceFile_,const std::string &Newfile_);
	~cgnsTocgns();
	
    int CopyFile();
	void cgns2cgns( std::vector<Node> &v_globalNode);
	std::vector<Node> cgns2cgns_send();
	void RebuildOriginalGlobalNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode, Scalar &R, Scalar &scale);
	std::vector<Vector> cgnsTocgns::RebuildNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode,std::vector<Vector> v_Node, Scalar &R_deform);
	
private:

	const std::string SourceFile;
	const std::string Newfile;
	int processorID;
	int npart;

#if defined(_BaseParallelMPI_)
    mpi::communicator mpi_world;
#endif 


};
#endif