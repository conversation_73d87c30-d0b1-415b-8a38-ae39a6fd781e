﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OutflowPressure.h
//! <AUTHOR>
//! @brief 流动出口给定压力比例系数（与参考值的比例），其他值由内场插值得到
//! @date  2021-4-20
//
//------------------------------修改日志----------------------------------------
//
// 2021-04-20 李艳亮
// 说明：新建
// 
//------------------------------------------------------------------------------
#ifndef  _sourceFlow_boundaryCondition_OutflowPressure_
#define  _sourceFlow_boundaryCondition_OutflowPressure_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 给定出口压力的边界条件类
 * 
 */
class OutflowPressure : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，初始化出流压力边界条件
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] pOut_ 出口压强
     */
    OutflowPressure(const int &boundaryPatchID, Package::FlowPackage &data, const Scalar &pOut_);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 更新边界条件
     * 
     */
    void UpdateBoundaryCondition();

private:
    Scalar pOut; ///< 出口压力值
};

} // namespace Flow
} // namespace Boundary

#endif
