 #pragma once
#include "ThirdPartyHeadersBegin.h"
#   include <algorithm>
#include "ThirdPartyHeadersEnd.h"
#include "SzlFileLoader.h"
#include "IJK.h"
namespace tecplot { namespace ___3933 { class IJKSubzoneInfo { private: ___1844 m_zoneIJKDim; ___1844 m_zoneLastIJKIndex; ___1844 m_subzoneItemsMaxIJKDim; ___2090::___2980 m_partition; ___1844 m_subzoneTilingIJKDim; ___1844 m_subzoneTilingIJKShift; ___2090::SubzoneOffset_t m_numSubzones;
 #if !defined NO_ASSERTS
bool ___2492;
 #endif
public: IJKSubzoneInfo( ___1844 const&               zoneIJKDim, ___1844 const&               subzoneMaxIJKDim, ___2090::___2980 ___2977, bool                     minimizeNumSubzones) : m_zoneIJKDim(zoneIJKDim) , m_zoneLastIJKIndex(zoneIJKDim.maxOp(1)-1) , m_subzoneItemsMaxIJKDim(subzoneMaxIJKDim) , m_partition(___2977) , m_subzoneTilingIJKDim(numSubzonesForDirection(zoneIJKDim.i(), ___2090::ItemOffset_t(subzoneMaxIJKDim.i()), minimizeNumSubzones), numSubzonesForDirection(zoneIJKDim.___2105(), ___2090::ItemOffset_t(subzoneMaxIJKDim.___2105()), minimizeNumSubzones), numSubzonesForDirection(zoneIJKDim.___2134(), ___2090::ItemOffset_t(subzoneMaxIJKDim.___2134()), minimizeNumSubzones)) , m_subzoneTilingIJKShift(subzoneShiftForDirection(zoneIJKDim.i(), ___2090::ItemOffset_t(subzoneMaxIJKDim.i()), minimizeNumSubzones), subzoneShiftForDirection(zoneIJKDim.___2105(), ___2090::ItemOffset_t(subzoneMaxIJKDim.___2105()), minimizeNumSubzones), subzoneShiftForDirection(zoneIJKDim.___2134(), ___2090::ItemOffset_t(subzoneMaxIJKDim.___2134()), minimizeNumSubzones)) , m_numSubzones( ___2090::SubzoneOffset_t(m_subzoneTilingIJKDim.blockSize()) )
 #if !defined NO_ASSERTS
, ___2492(minimizeNumSubzones)
 #endif
{ REQUIRE(zoneIJKDim>0); REQUIRE(subzoneMaxIJKDim>0); } IJKSubzoneInfo() : m_zoneIJKDim(0,0,0) , m_zoneLastIJKIndex(0,0,0) , m_subzoneItemsMaxIJKDim(0,0,0) , m_subzoneTilingIJKDim(0,0,0) , m_subzoneTilingIJKShift(0,0,0) , m_numSubzones(0) {} ~IJKSubzoneInfo() {} inline void swap(IJKSubzoneInfo& ___2888) { using std::swap; swap(m_zoneIJKDim, ___2888.m_zoneIJKDim); swap(m_zoneLastIJKIndex, ___2888.m_zoneLastIJKIndex); swap(m_subzoneItemsMaxIJKDim, ___2888.m_subzoneItemsMaxIJKDim); swap(m_subzoneTilingIJKDim, ___2888.m_subzoneTilingIJKDim); swap(m_subzoneTilingIJKShift, ___2888.m_subzoneTilingIJKShift); swap(m_numSubzones, ___2888.m_numSubzones); } inline void reset( ___1844 const& zoneIJKSize, ___1844 const& subzoneIJKMax, bool       minimizeNumSubzones = DEFAULT_MINIMIZE_IJK_NUM_SUBZONES) { REQUIRE(zoneIJKSize>0); REQUIRE(subzoneIJKMax>0); REQUIRE(minimizeNumSubzones==DEFAULT_MINIMIZE_IJK_NUM_SUBZONES); m_zoneIJKDim = zoneIJKSize; m_zoneLastIJKIndex = zoneIJKSize.maxOp(1)-1; m_subzoneItemsMaxIJKDim = subzoneIJKMax; m_subzoneTilingIJKDim = ___1844( numSubzonesForDirection(zoneIJKSize.i(), ___2090::ItemOffset_t(subzoneIJKMax.i()), minimizeNumSubzones), numSubzonesForDirection(zoneIJKSize.___2105(), ___2090::ItemOffset_t(subzoneIJKMax.___2105()), minimizeNumSubzones), numSubzonesForDirection(zoneIJKSize.___2134(), ___2090::ItemOffset_t(subzoneIJKMax.___2134()), minimizeNumSubzones) ); m_subzoneTilingIJKShift = ___1844( subzoneShiftForDirection(zoneIJKSize.i(), ___2090::ItemOffset_t(subzoneIJKMax.i()), minimizeNumSubzones), subzoneShiftForDirection(zoneIJKSize.___2105(), ___2090::ItemOffset_t(subzoneIJKMax.___2105()), minimizeNumSubzones), subzoneShiftForDirection(zoneIJKSize.___2134(), ___2090::ItemOffset_t(subzoneIJKMax.___2134()), minimizeNumSubzones) ); m_numSubzones = ___2090::SubzoneOffset_t(m_subzoneTilingIJKDim.blockSize()); } inline bool ___2067() const { return m_zoneIJKDim > 0 && m_subzoneItemsMaxIJKDim > 0; } inline bool validSubzone(___2090::SubzoneOffset_t ___3880) const { return ___3880 < getNumSzs(); } inline bool validSzAddress(___2090::SubzoneAddress szAddress) const { return validSubzone(szAddress.subzoneOffset()) && szAddress.___2977() == m_partition; } inline bool validItemAddress(___2090 ___2089) const { return validSzAddress(___2089.subzoneAddress()) && ___2089.itemOffset() < numItemsInSz(___2089.subzoneOffset()); } ___81 getNumItems(void) const { return m_zoneIJKDim.blockSize(); } ___1844 const& ijkDim(void) const { return m_zoneIJKDim; } ___1844 const& ijkLastIndex(void) const { ___478(m_zoneLastIJKIndex == m_zoneIJKDim.maxOp(1)-1); return m_zoneLastIJKIndex; } ___2090::SubzoneOffset_t getNumSzs(void) const { ENSURE(m_numSubzones == ___2090::SubzoneOffset_t(m_subzoneTilingIJKDim.blockSize())); ENSURE(m_numSubzones == calcNumSubzones(m_zoneIJKDim, m_subzoneItemsMaxIJKDim, ___2492)); return m_numSubzones; } static inline ___2090::SubzoneOffset_t numSubzonesForDirection( ___81                itemIndexDim, ___2090::ItemOffset_t subzoneIndexMax, bool                      minimizeNumSubzones = DEFAULT_MINIMIZE_IJK_NUM_SUBZONES) { REQUIRE(itemIndexDim>0); REQUIRE(subzoneIndexMax<=___2090::MAX_ITEM_OFFSET+1); ___2090::SubzoneOffset_t numSubzonesForDir; if ( minimizeNumSubzones ) { numSubzonesForDir = ___2090::SubzoneOffset_t( (itemIndexDim+subzoneIndexMax-1)/subzoneIndexMax ); } else { numSubzonesForDir = ___2090::SubzoneOffset_t( 1+(itemIndexDim/subzoneIndexMax) ); } ENSURE(numSubzonesForDir>0); ENSURE((___81)numSubzonesForDir*subzoneIndexMax>=itemIndexDim); ENSURE( IMPLICATION(minimizeNumSubzones, ___81(numSubzonesForDir-1)*subzoneIndexMax<itemIndexDim) ); return numSubzonesForDir; } static ___2090::SubzoneOffset_t calcNumSubzones( ___1844 const& zoneIJKDim, ___1844 const& subzoneIJKMax, bool       minimizeIJKNumSubzones) { REQUIRE(zoneIJKDim>0); REQUIRE(subzoneIJKMax>0); ___1844 const subzoneTilingIJK( numSubzonesForDirection(zoneIJKDim.i(), ___2090::ItemOffset_t(subzoneIJKMax.i()), minimizeIJKNumSubzones), numSubzonesForDirection(zoneIJKDim.___2105(), ___2090::ItemOffset_t(subzoneIJKMax.___2105()), minimizeIJKNumSubzones), numSubzonesForDirection(zoneIJKDim.___2134(), ___2090::ItemOffset_t(subzoneIJKMax.___2134()), minimizeIJKNumSubzones)); ___2090::SubzoneOffset_t const numSubzones = ___2090::SubzoneOffset_t(subzoneTilingIJK.blockSize()); ENSURE(numSubzones>0 && numSubzones <= zoneIJKDim.blockSize()); return numSubzones; } static inline ___2090::ItemOffset_t subzoneShiftForDirection( ___81                itemIndexDim, ___2090::ItemOffset_t subzoneIndexMax, bool                      minimizeNumSubzones = DEFAULT_MINIMIZE_IJK_NUM_SUBZONES) { REQUIRE(itemIndexDim>0); REQUIRE(subzoneIndexMax>=3 && subzoneIndexMax<=___2090::MAX_ITEM_OFFSET+1); ___2090::ItemOffset_t const remainder = ___2090::ItemOffset_t(itemIndexDim % subzoneIndexMax); ___2090::ItemOffset_t subzoneShift = (subzoneIndexMax-remainder)/2; if ( minimizeNumSubzones && remainder == 0 )
subzoneShift = 0; ENSURE(subzoneShift<subzoneIndexMax); return subzoneShift; } public: ___1844 itemIJKStart(___1844 const& szIJK) const { INVARIANT(m_subzoneTilingIJKShift<m_subzoneItemsMaxIJKDim); ___1844 const indexStart = m_subzoneTilingIJKShift.maxOp(szIJK*m_subzoneItemsMaxIJKDim) - m_subzoneTilingIJKShift; ENSURE(indexStart >= 0); return indexStart; } ___1844 itemIJKEnd(___1844 const& szIJK) const { INVARIANT(m_subzoneTilingIJKShift < m_subzoneItemsMaxIJKDim); ___1844 const indexEnd = m_zoneIJKDim.minOp((szIJK + 1)*m_subzoneItemsMaxIJKDim - m_subzoneTilingIJKShift) - 1; ENSURE(indexEnd<m_zoneIJKDim); return indexEnd; } ___1844 szIJKAtSzIndex(___2090::SubzoneOffset_t ___3880) const { REQUIRE(validSubzone(___3880)); ___1844 const subzoneIJK = m_subzoneTilingIJKDim.ijkAtOffset(___3880); ENSURE(subzoneIJK<m_subzoneTilingIJKDim); return subzoneIJK; } ___2090::SubzoneAddress szAddressAtSzIJK(___1844 const& szIJK) const { REQUIRE(szIJK<m_subzoneTilingIJKDim); ___2090::SubzoneAddress const szAddress(m_partition, static_cast<___2090::SubzoneOffset_t>(m_subzoneTilingIJKDim.offsetAtIJK(szIJK))); ENSURE(validSzAddress(szAddress)); ENSURE(szIJKAtSzIndex(szAddress.subzoneOffset())==szIJK); return szAddress; } void subzoneIJKStartAndEnd( ___2090::SubzoneOffset_t ___3880, ___1844&                         ___1880, ___1844&                         ___1852) const { REQUIRE(___3880 < getNumSzs()); ___1844 const szIJK = szIJKAtSzIndex(___3880); ___1880 = itemIJKStart(szIJK); ___1852 = itemIJKEnd(szIJK); ENSURE(___1880<m_zoneIJKDim); ENSURE(___1852<m_zoneIJKDim); ENSURE(szAddressAtItemIJK(___1880).subzoneOffset() == ___3880); ENSURE(szAddressAtItemIJK(___1852).subzoneOffset()==___3880); } ___1844 subzoneIJKStart(___2090::SubzoneOffset_t ___3880) const { REQUIRE(___3880 < getNumSzs()); ___1844 const szIJK = szIJKAtSzIndex(___3880); ___1844 const ___1880 = itemIJKStart(szIJK); ENSURE(___1880<m_zoneIJKDim); ENSURE(szAddressAtItemIJK(___1880).subzoneOffset() == ___3880); return ___1880; } ___1844 subzoneIJKEnd(___2090::SubzoneOffset_t ___3880) const { REQUIRE(___3880 < getNumSzs()); ___1844 const szIJK = szIJKAtSzIndex(___3880); ___1844 const ___1852 = itemIJKEnd(szIJK); ENSURE(___1852 < m_zoneIJKDim); ENSURE(szAddressAtItemIJK(___1852).subzoneOffset() == ___3880); return ___1852; } ___1844 szIJKAtItemIJK(___1844 const& itemIJK) const { REQUIRE(itemIJK < m_zoneIJKDim); ___1844 const szIJK = (itemIJK+m_subzoneTilingIJKShift) / m_subzoneItemsMaxIJKDim; ENSURE(szIJK < m_subzoneTilingIJKDim); return szIJK; } ___2090::SubzoneAddress szAddressAtItemIJK(___1844 const& itemIJK) const { REQUIRE(itemIJK<m_zoneIJKDim); ___1844 const subzoneIJK = szIJKAtItemIJK(itemIJK); ___2090::SubzoneAddress const szAddress(m_partition, static_cast<___2090::SubzoneOffset_t>(m_subzoneTilingIJKDim.offsetAtIJK(subzoneIJK))); ENSURE(validSzAddress(szAddress)); return szAddress; } ___2090::SubzoneAddress szAddressAtItemIndex(___81 itemIndex) const { REQUIRE(itemIndex<getNumItems()); ___1844 const itemIJK = m_zoneIJKDim.ijkAtOffset(itemIndex); ___2090::SubzoneAddress const szAddress = szAddressAtItemIJK(itemIJK); ENSURE(validSzAddress(szAddress)); return szAddress; } ___2090 itemAddressAtItemIJK( ___1844 const& itemIJK, ___1844 const& szIJK) const { REQUIRE(itemIJK<m_zoneIJKDim); REQUIRE(szIJK<m_subzoneTilingIJKDim); ___2090::SubzoneOffset_t const szOffset = static_cast<___2090::SubzoneOffset_t>(m_subzoneTilingIJKDim.offsetAtIJK(szIJK)); ___1844 const ___1880 = itemIJKStart(szIJK); ___1844 const ___1852   = itemIJKEnd(szIJK); ___1844 const localMax = ___1852-___1880+1; ___1844 const localIJK = itemIJK-___1880; ___478(localIJK<=localMax); ___2090::ItemOffset_t const itemOffset = ___2090::ItemOffset_t(localMax.offsetAtIJK(localIJK)); ENSURE(itemOffset<localMax.blockSize()); return ___2090(m_partition,szOffset,itemOffset); } ___1844 subzoneIJKDim(___2090::SubzoneOffset_t ___3880) const { REQUIRE(validSubzone(___3880)); ___1844 szIJKStart, szIJKEnd; subzoneIJKStartAndEnd(___3880, szIJKStart, szIJKEnd); ___1844 const szIJKDim = szIJKEnd-szIJKStart+1; return szIJKDim; } ___2090::ItemOffset_t numItemsInSz(___2090::SubzoneOffset_t ___3880) const { REQUIRE(validSubzone(___3880)); return ___2090::ItemOffset_t(subzoneIJKDim(___3880).blockSize()); } ___2090 itemAddressAtItemIJK(___1844 const& itemIJK) const { REQUIRE(itemIJK<m_zoneIJKDim); ___1844 const szIJK = szIJKAtItemIJK(itemIJK); return itemAddressAtItemIJK(itemIJK, szIJK); } ___1844 itemIJKAtItemAddress(___2090 ___2089) const { REQUIRE(validItemAddress(___2089)); ___2090::SubzoneOffset_t const ___3880 = ___2089.subzoneOffset(); ___1844 ___1880, ___1852; subzoneIJKStartAndEnd(___3880, ___1880, ___1852); ___2090::ItemOffset_t const ___2865 = ___2089.itemOffset(); ___1844 const ijkRange = ___1852-___1880+1; ___1844 const ___1862 = ijkRange.ijkAtOffset(___2865); ___1844 const itemIJK = ___1880 + ___1862; ENSURE(itemIJK<m_zoneIJKDim); ENSURE(itemAddressAtItemIJK(itemIJK)==___2089); return itemIJK; } inline ___2090 itemAddressAtItemIndex(___81 itemIndex) const
{ REQUIRE(itemIndex<getNumItems()); ___1844 const itemIJK = itemIJKAtItemIndex(itemIndex); ___2090 const ___2089 = itemAddressAtItemIJK(itemIJK); return ___2089; } inline ___81 itemIndexAtItemAddress(___2090 ___2089) const { REQUIRE(validItemAddress(___2089)); ___1844 const itemIJK = itemIJKAtItemAddress(___2089); ___81 const itemIndex = itemIndexAtItemIJK(itemIJK); ENSURE(itemIndex<getNumItems()); ENSURE(itemAddressAtItemIndex(itemIndex)==___2089); return itemIndex; } inline ___81 itemIndexAtItemIJK(___1844 const& itemIJK) const { REQUIRE(itemIJK < m_zoneIJKDim); ___81 const itemIndex = m_zoneIJKDim.offsetAtIJK(itemIJK); ENSURE(itemIndex<getNumItems()); return itemIndex; } ___1844 itemIJKAtItemIndex(___81 itemIndex) const { REQUIRE(itemIndex<getNumItems()); ___1844 const itemIJK = m_zoneIJKDim.ijkAtOffset(itemIndex); ENSURE(itemIJK < m_zoneIJKDim); ENSURE(itemIndexAtItemIJK(itemIJK)==itemIndex); return itemIJK; } ___372 getSubzonesOnIndexPlane( IJKPlanes_e                            whichPlane, ___81                             planeIndex, ___3269<___2090::SubzoneAddress>& szAddresses) const; ___372 getSubzonesOnIndexLine( IJKLines_e                             whichLine, ___81                             mIndex, ___81                             nIndex, ___3269<___2090::SubzoneAddress>& szAddresses) const; }; }}
