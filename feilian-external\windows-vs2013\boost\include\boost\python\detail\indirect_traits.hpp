// Copyright <PERSON> 2004. Distributed under the Boost
// Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
#ifndef INDIRECT_TRAITS_DWA2004915_HPP
# define INDIRECT_TRAITS_DWA2004915_HPP

# include <boost/detail/indirect_traits.hpp>

namespace boost { namespace python {
namespace indirect_traits = boost::detail::indirect_traits;
}} // namespace boost::python::detail

#endif // INDIRECT_TRAITS_DWA2004915_HPP
