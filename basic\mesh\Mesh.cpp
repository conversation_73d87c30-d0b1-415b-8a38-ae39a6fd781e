﻿#include "basic/mesh/Mesh.h"
//#include "basic/common/GeometryTools.h"
//#include <stdlib.h>

Mesh::Mesh()
	:BaseMesh(),	
	n_elemNum_ghostBoundary(0),
	n_elemNum_ghostParallel(0),
	n_elemNum_ghostOverlap(0)
{
}

Mesh::Mesh(const std::string& MshFileName)
	:BaseMesh(MshFileName),	
	n_elemNum_ghostBoundary(0),
	n_elemNum_ghostParallel(0),
	n_elemNum_ghostOverlap(0)
{
}

void Mesh::CreateBoundaryGhostElement()
{
	// 已存在边界虚单元不再重复调用
	if (this->n_elemNum_ghostBoundary > 0)
	{
		this->n_elemNum_all = this->v_elem.size();
		return;
	}

	this->n_elemNum_ghostBoundary = 0;
	const int boundarySize = this->vv_boundaryFaceID.size();
	for (int i = 0; i < boundarySize; i++)
	{
		const int faceSize = this->vv_boundaryFaceID[i].size();
		this->n_elemNum_ghostBoundary += faceSize;
		for (int j = 0; j < faceSize; j++)
		{
			const int &faceID = this->vv_boundaryFaceID[i][j];
			const int &ownerID = this->v_face[faceID].n_owner;
			const Vector &faceNormal = this->v_face[faceID].normal;
			Element e;
			e.et_type = Element::ElemType::ghostBoundary;
			e.volume = this->v_elem[ownerID].volume;
			Vector distance = this->v_face[faceID].center - this->v_elem[ownerID].center;
			distance = (distance & faceNormal) * faceNormal;
			e.center = this->v_elem[ownerID].center + 2.0 * distance;
			this->v_elem.push_back(e);
			this->v_face[faceID].n_neighbor = this->v_elem.size() - 1;
		}
	}
	this->n_elemNum_all = this->v_elem.size();
}

void Mesh::SetInnerElementIDForBoundaryElement()
{
    if (this->v_boundaryElementFlag.empty()) this->UpdateBoundaryElementFlag();

    this->vv_innerElementID.resize(this->GetBoundarySize());
    for (int patchID = 0; patchID < (int)this->GetBoundarySize(); patchID++)
    {
        this->vv_innerElementID[patchID].resize(this->GetBoundaryFaceSize(patchID));
        for (int index = 0; index < (int)this->GetBoundaryFaceSize(patchID); index++)
        {
            this->vv_innerElementID[patchID][index] = -1;

            std::vector<int> adjacentIDListBoundary, adjacentIDListOther;
            const int &boundaryFaceID = this->GetBoundaryFaceID(patchID, index);
            const int &boundaryElementID = this->v_face[boundaryFaceID].n_owner;
            const Element &element = this->v_elem[boundaryElementID];

            for (int k = 0; k < element.GetFaceSize(); k++)
            {
                const int &faceID = element.v_faceID[k];
                const int &ownerID = this->v_face[faceID].n_owner;
                const int &neighID = this->v_face[faceID].n_neighbor;
                int adjacentID = neighID;
                if (adjacentID == boundaryElementID) adjacentID = ownerID;

                if (this->v_elem[adjacentID].GetElemType() == Element::ElemType::real)
                {
                    // 真实单元，判断相邻单元是否是物理边界处的单元
                    if (this->v_boundaryElementFlag[adjacentID]) adjacentIDListBoundary.push_back(adjacentID);
                    else                                         adjacentIDListOther.push_back(adjacentID);
                }
                else if (this->v_elem[adjacentID].GetElemType() == Element::ElemType::ghostParallel)
                {
                    // 并行边界虚单元，判断相邻单元是否是物理边界处的单元
                    if (this->v_boundaryElementFlag[adjacentID]) adjacentIDListBoundary.push_back(adjacentID);
                    else                                         adjacentIDListOther.push_back(adjacentID);
                }
                else
                {
                    // 物理边界虚单元跳过
                }
            }

            // 如果内部相邻单元不为空，优先在内部单元中寻找相邻单元
            // 如果内部相邻单元为空，在相邻边界单元中寻找
            std::vector<int> *adjacentIDCList;
            if (adjacentIDListOther.size() > 0) adjacentIDCList = &adjacentIDListOther;
            else                                adjacentIDCList = &adjacentIDListBoundary;

            if (adjacentIDCList->empty())
				FatalError("Mesh::SetInnerElementIDForBoundaryElement: inner elementID is wrong");
			
            // 选择最近的相邻单元
            Scalar distance = INF;
            for (int k = 0; k < adjacentIDCList->size(); k++)
            {
                const int &adjacentID = (*adjacentIDCList)[k];

                const Scalar distanceTemp = (this->v_elem[boundaryElementID].center - this->v_elem[adjacentID].center).Mag();
                if (distanceTemp < distance)
                {
                    distance = distanceTemp;
                    this->vv_innerElementID[patchID][index] = adjacentID;
                }
            }
        }
    }
}

void Mesh::WriteMesh(std::fstream &file, const bool &binary, const bool &fullFlag) const
{
	// 基本信息
	IO::Write(file, this->st_meshName, binary);
	IO::Write(file, this->st_fileName, binary);
	IO::Write(file, this->zoneID, binary);
	IO::Write(file, this->n_elemNum, binary);
	IO::Write(file, this->n_elemNum_ghostBoundary, binary);
	IO::Write(file, this->n_elemNum_ghostParallel, binary);
	IO::Write(file, this->n_elemNum_ghostOverlap, binary);
	IO::Write(file, this->n_elemNum_all, binary);
	IO::Write(file, this->n_faceNum, binary);
	IO::Write(file, this->n_nodeNum, binary);
	IO::Write(file, (int)this->md_meshDim, binary);
	IO::Write(file, (int)this->est_shapeType, binary);

	// 单元信息
	if (fullFlag)
	{
		std::vector<Vector> elemCenters(this->n_elemNum_all);
		std::vector<Scalar> elemVolumes(this->n_elemNum_all);
		for (size_t elementID = 0; elementID < this->n_elemNum_all; ++elementID)
		{
			const Element &element = this->v_elem[elementID];
			elemCenters[elementID] = element.center;
			elemVolumes[elementID] = element.volume;
		}
		IO::Write(file, elemCenters, binary);
		IO::Write(file, elemVolumes, binary);
		std::vector<Vector>().swap(elemCenters);
		std::vector<Scalar>().swap(elemVolumes);
	}

	std::vector<int> est_shapeTypes(this->n_elemNum_all);
	size_t elemNodeSizeAll = this->n_elemNum_all;
	for (size_t elementID = 0; elementID < this->n_elemNum_all; ++elementID)
	{
		const Element &element = this->v_elem[elementID];
		est_shapeTypes[elementID] = element.est_shapeType;
		elemNodeSizeAll += element.v_nodeID.size();
	}
	
	std::vector<int> elemNodeIDs(elemNodeSizeAll);
	for (size_t elementID = 0, index = 0; elementID < this->n_elemNum_all; ++elementID)
	{
		const Element &element = this->v_elem[elementID];
		const size_t nodeSize = element.v_nodeID.size();
		elemNodeIDs[index++] = nodeSize;
		for (size_t i = 0; i < nodeSize; i++)
			elemNodeIDs[index++] = element.v_nodeID[i];
	}

	IO::Write(file, elemNodeIDs, binary);
	IO::Write(file, est_shapeTypes, binary);
	std::vector<int>().swap(elemNodeIDs);
	std::vector<int>().swap(est_shapeTypes);

	// 面信息
	if (fullFlag)
	{
		std::vector<Vector> faceCenters(this->n_faceNum);
		std::vector<Scalar> faceAreaMags(this->n_faceNum);
		std::vector<Vector> faceNormals(this->n_faceNum);
		for (size_t faceID = 0; faceID < this->n_faceNum; ++faceID)
		{
			const Face &face = this->v_face[faceID];
			faceCenters[faceID] = face.center;
			faceAreaMags[faceID] = face.areaMag;
			faceNormals[faceID] = face.normal;
		}
		IO::Write(file, faceCenters, binary);
		IO::Write(file, faceAreaMags, binary);
		IO::Write(file, faceNormals, binary);
		std::vector<Vector>().swap(faceCenters);
		std::vector<Scalar>().swap(faceAreaMags);
		std::vector<Vector>().swap(faceNormals);
	}

	std::vector<int> ownerIDs(this->n_faceNum);
	std::vector<int> neighIDs(this->n_faceNum);
	size_t faceNodeSizeAll = this->n_faceNum;
	for (size_t faceID = 0; faceID < this->n_faceNum; ++faceID)
	{
		const Face &face = this->v_face[faceID];
		ownerIDs[faceID] = face.n_owner;
		neighIDs[faceID] = face.n_neighbor;
		faceNodeSizeAll += face.v_nodeID.size();
	}

	std::vector<int> faceNodeIDs(faceNodeSizeAll);
	for (size_t faceID = 0, index = 0; faceID < this->n_faceNum; ++faceID)
	{
		const Face &face = this->v_face[faceID];
		const size_t nodeSize = face.v_nodeID.size();
		faceNodeIDs[index++] = nodeSize;
		for (size_t i = 0; i < nodeSize; i++)
			faceNodeIDs[index++] = face.v_nodeID[i];
	}

	IO::Write(file, ownerIDs, binary);
	IO::Write(file, neighIDs, binary);
	IO::Write(file, faceNodeIDs, binary);
	std::vector<int>().swap(ownerIDs);
	std::vector<int>().swap(neighIDs);
	std::vector<int>().swap(faceNodeIDs);

	// 点信息
	IO::Write(file, this->v_node, binary);

	// 物理边界信息
	IO::Write(file, this->v_boundaryIDGlobal, binary);
	IO::Write(file, this->v_boundaryName, binary);
	IO::Write(file, this->vv_boundaryFaceID, binary);

	// 并行边界信息
	const size_t num1 = vv_ghostElement_parallel.size();
	IO::Write(file, (int)num1, binary);
	for (size_t i = 0; i < num1; ++i)
	{
		const size_t num2 = vv_ghostElement_parallel[i].size();
		IO::Write(file, (int)num2, binary);
		for (size_t j = 0; j < num2; ++j)
			vv_ghostElement_parallel[i][j].Write(file, binary);
	}
}

void Mesh::ReadMesh(std::fstream &file, const bool &binary, const bool &fullFlag)
{
    int num;

    //基本信息
	IO::Read(file, this->st_meshName, binary);
    IO::Read(file, this->st_fileName, binary);
    IO::Read(file, this->zoneID, binary);
    IO::Read(file, this->n_elemNum, binary);
    IO::Read(file, this->n_elemNum_ghostBoundary, binary);
    IO::Read(file, this->n_elemNum_ghostParallel, binary);
    IO::Read(file, this->n_elemNum_ghostOverlap, binary);
    IO::Read(file, this->n_elemNum_all, binary);
    IO::Read(file, this->n_faceNum, binary);
    IO::Read(file, this->n_nodeNum, binary);
    IO::Read(file, num, binary);
    this->md_meshDim = (MeshDim)num;
    IO::Read(file, num, binary);
    this->est_shapeType = (Element::ElemShapeType)num;

    if (this->n_elemNum <= 0)
    {
        FatalError("Mesh::ReadMesh: mesh info is wrong!"); return;
    }

    //单元信息
    this->v_elem.resize(this->n_elemNum_all);
	if (fullFlag)
	{
		std::vector<Vector> elemCenters;
		std::vector<Scalar> elemVolumes;
		IO::Read(file, elemCenters, binary);
		IO::Read(file, elemVolumes, binary);
		for (size_t elementID = 0; elementID < this->n_elemNum_all; ++elementID)
		{
			Element &element = this->v_elem[elementID];
			element.center = elemCenters[elementID];
			element.volume = elemVolumes[elementID];
		}
		std::vector<Vector>().swap(elemCenters);
		std::vector<Scalar>().swap(elemVolumes);
	}

	std::vector<int> elemNodeIDs;
	std::vector<int> est_shapeTypes;
	IO::Read(file, elemNodeIDs, binary);
	IO::Read(file, est_shapeTypes, binary);
	for (size_t elementID = 0, index = 0; elementID < this->n_elemNum_all; ++elementID)
	{
		Element &element = this->v_elem[elementID];
		element.est_shapeType = (Element::ElemShapeType)est_shapeTypes[elementID];
		const size_t nodeSize = elemNodeIDs[index++];
		element.v_nodeID.resize(nodeSize);
		for (size_t i = 0; i < nodeSize; i++)element.v_nodeID[i] = elemNodeIDs[index++];
	}
	std::vector<int>().swap(elemNodeIDs);
	std::vector<int>().swap(est_shapeTypes);

    //面信息
    this->v_face.resize(this->n_faceNum);
	if (fullFlag)
	{
		std::vector<Vector> faceCenters;
		std::vector<Scalar> faceAreaMags;
		std::vector<Vector> faceNormals;
		IO::Read(file, faceCenters, binary);
		IO::Read(file, faceAreaMags, binary);
		IO::Read(file, faceNormals, binary);
		for (size_t faceID = 0; faceID < this->n_faceNum; ++faceID)
		{
			Face &face = this->v_face[faceID];
			face.center = faceCenters[faceID];
			face.areaMag = faceAreaMags[faceID];
			face.normal = faceNormals[faceID];
		}
		std::vector<Vector>().swap(faceCenters);
		std::vector<Scalar>().swap(faceAreaMags);
		std::vector<Vector>().swap(faceNormals);
	}

	std::vector<int> ownerIDs;
	std::vector<int> neighIDs;
	std::vector<int> faceNodeIDs;
	IO::Read(file, ownerIDs, binary);
	IO::Read(file, neighIDs, binary);
	IO::Read(file, faceNodeIDs, binary);
	for (size_t faceID = 0, index = 0; faceID < this->n_faceNum; ++faceID)
	{
		Face &face = this->v_face[faceID];
		face.n_owner = ownerIDs[faceID];
		face.n_neighbor = neighIDs[faceID];
		const size_t nodeSize = faceNodeIDs[index++];
		face.v_nodeID.resize(nodeSize);
		for (size_t i = 0; i < nodeSize; i++)
			face.v_nodeID[i] = faceNodeIDs[index++];
	}
	std::vector<int>().swap(ownerIDs);
	std::vector<int>().swap(neighIDs);
	std::vector<int>().swap(faceNodeIDs);

	//点信息
	IO::Read(file, this->v_node, binary);

	//物理边界信息
    IO::Read(file, this->v_boundaryIDGlobal, binary);
	IO::Read(file, this->v_boundaryName, binary);
	IO::Read(file, this->vv_boundaryFaceID, binary);

	//并行边界信息
    int num1;
    IO::Read(file, num1, binary);
    vv_ghostElement_parallel.resize(num1);
    for (size_t i = 0; i < num1; ++i)
    {
        int num2;
        IO::Read(file, num2, binary);
        vv_ghostElement_parallel[i].resize(num2);
        for (size_t j = 0; j < num2; ++j)
            vv_ghostElement_parallel[i][j].Read(file, binary);
    }
}

void Mesh::UpdateBoundaryElementFlag()
{
	this->v_boundaryElementFlag.clear();
	this->v_boundaryElementFlag.resize(this->n_elemNum_all, false);
	for (int patchID = 0; patchID < this->vv_boundaryFaceID.size(); ++patchID)
	{
		for (int index = 0; index < this->vv_boundaryFaceID[patchID].size(); ++index)
		{
			const int &faceID = this->vv_boundaryFaceID[patchID][index];
			const int &ownerID = this->v_face[faceID].n_owner;
			this->v_boundaryElementFlag[ownerID] = true;
		}
	}

    if (GetMPISize() == 1) return;

#if defined(_BaseParallelMPI_)
    const auto &vv_ghostElement = this->vv_ghostElement_parallel;
    const int ghostBoundarySize = vv_ghostElement.size();

    std::vector<boost::mpi::request> sendRequests(ghostBoundarySize);
    std::vector<boost::mpi::request> recvRequests(ghostBoundarySize);
    std::vector < std::pair<int, int> > recvTags;

    //Owner elem value list set
    std::vector<std::vector<bool>> vv_recvList(ghostBoundarySize);
    for (int i = 0; i < ghostBoundarySize; ++i)
    {
        const std::pair<int, int> &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
        recvRequests[i] = MPI::mpiWorld.irecv(procPair.second, procPair.second, vv_recvList[i]);
        recvTags.push_back(std::make_pair(procPair.second, procPair.second));
    }

    for (int i = 0; i < ghostBoundarySize; i++)
    {
        const int ghostElementSize = vv_ghostElement[i].size();
        std::vector<bool> v_sendList(ghostElementSize, false);
        for (int j = 0; j < ghostElementSize; j++)
        {
            const int &faceID = vv_ghostElement[i][j].ID;
            const int &ownerID = this->v_face[faceID].n_owner;
            v_sendList[j] = this->v_boundaryElementFlag[ownerID];
        }
        const std::pair<int, int> &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
        sendRequests[i] = MPI::mpiWorld.isend(procPair.second, procPair.first, v_sendList);
    }
    MPIIprobe(recvTags);
    MPIWaitAll(recvRequests);
    MPIWaitAll(sendRequests);
	
    for (int i = 0; i < ghostBoundarySize; ++i)
    {
        for (int j = 0; j < (int)vv_ghostElement[i].size(); ++j)
        {
            const int &faceID = vv_ghostElement[i][j].GetID();
            const int &neighborID = this->GetFace(faceID).GetNeighborID();
            this->v_boundaryElementFlag[neighborID] = vv_recvList[i][j];
        }
    }
#endif
}

void Mesh::UpdateHalfFaceCrossSymmetryBoundaryFlag(const std::vector<int> &symmetryPatchID)
{
	this->halfFaceCrossSymmetryBoundaryFlag.clear();
    this->halfFaceCrossSymmetryBoundaryFlag.resize(this->n_faceNum, false);

    for (int i = 0; i < symmetryPatchID.size(); i++)
    {
        const int &patchID = symmetryPatchID[i];
        for (int index = 0; index < this->vv_boundaryFaceID[patchID].size(); index++)
        {
            const int &faceID0 = this->vv_boundaryFaceID[patchID][index];
            const int &ownerID = this->v_face[faceID0].GetOwnerID();
            const Vector &center0 = this->GetFace(faceID0).GetCenter();
            const Vector &normal0 = this->GetFace(faceID0).GetNormal();
            
            const Element &element = this->v_elem[ownerID];
            for (int m = 0; m < element.GetFaceSize(); m++)
            {
                const int &faceID1 = element.GetFaceID(m);
                if (this->JudgeBoundaryFace(faceID1)) continue;

                const Vector &center1 = this->GetFace(faceID1).GetCenter();
                if (fabs((center1 - center0) & normal0) < SMALL)
                {
                    this->halfFaceCrossSymmetryBoundaryFlag[faceID1] = true;
                }
            }
        }
    }
}

void Mesh::UpdateInDomainInfo()
{
    v_elementIDInDomain.clear();
    v_innerFaceIDInDomain.clear();
    vv_boundaryFaceIndexInDomain.clear();
    
	v_elementIDInDomain.reserve(this->n_elemNum);
	for (int elementID = 0; elementID < this->n_elemNum; ++elementID)
	{
		if(this->v_elem[elementID].et_type == Element::ElemType::real)
		{
			v_elementIDInDomain.push_back(elementID);
		}
	}

	v_innerFaceIDInDomain.reserve(this->n_faceNum);
	for (int faceID = 0; faceID < this->n_faceNum; ++faceID)
	{
		const int &ownerID = this->v_face[faceID].n_owner;
		const int &neighID = this->v_face[faceID].n_neighbor;
		
		if(this->v_elem[ownerID].et_type == Element::ElemType::real &&
		   this->v_elem[neighID].et_type != Element::ElemType::ghostBoundary &&
           this->v_elem[neighID].et_type != Element::ElemType::unavailable)
		{
			v_innerFaceIDInDomain.push_back(faceID);
		}
        else if (this->v_elem[ownerID].et_type == Element::ElemType::ghostOverset &&
                 this->v_elem[neighID].et_type == Element::ElemType::real)
        {
            v_innerFaceIDInDomain.push_back(faceID);
        }
	}

	const int boundarySize = this->vv_boundaryFaceID.size();
	vv_boundaryFaceIndexInDomain.resize(boundarySize);
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int faceSize = this->vv_boundaryFaceID[patchID].size();
		vv_boundaryFaceIndexInDomain[patchID].reserve(faceSize);
		for (int index = 0; index < faceSize; ++index)
		{
			const int &faceID = this->vv_boundaryFaceID[patchID][index];
			const int &ownerID = this->v_face[faceID].n_owner;
			if(this->v_elem[ownerID].et_type == Element::ElemType::real)
			{
				vv_boundaryFaceIndexInDomain[patchID].push_back(index);
			}
		}
	}
}
