﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LinearSystemSolver.h
//! <AUTHOR>
//! @brief 大型线性方程组的求解类.
//! @date 2024-12-12
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-12 李艳亮、孔名驰
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#if defined(_EnablePETSC_)

#ifndef _basic_CFD_linearSystemSolver_LinearSystemSolverPetsc_
#define _basic_CFD_linearSystemSolver_LinearSystemSolverPetsc_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrixPetsc.h"
#include "basic/CFD/linearSystemSolver/LinearSystemSolver.h"
#include "basic/mesh/Mesh.h"
#include "sourceFlow/configure/FlowConfigure.h"

/**
 * @brief 大型线性方程组的求解类
 * 
 */
class LinearSystemSolverPetsc : public LinearSystemSolver
{
public:
    /**
     * @brief 线性方程组求解的构造函数
     * 
     * @param[in] mesh_ 当前网格
	 * @param[in] flowConfig_ 参数文件
     * @param[in] nVariable_ 待求解物理量个数
     */
	LinearSystemSolverPetsc(Mesh *mesh_, const int nVariable_);

    /**
     * @brief 线性方程组求解的析构函数
     * 
     */
	~LinearSystemSolverPetsc();

    /**
     * @brief 线性方程组求解的初始化
     * 
     * @param[in] scheme 求解方法
     * @param[in] paramters 求解方法对应参数
     * @param[in] CSRMatrix_ 矩阵
     * @param[in] resVector_ 右端列向量
	 * @param[in, out] solVector_ 解向量
     */
	void Initialize(const Time::Scheme &scheme,
                    const Configure::Flow::ExactJacobianStruct &paramters,
					BlockSparseMatrix *CSRMatrix_,
					LinearSystemVector *resVector_,
					LinearSystemVector *solVector_);

    /**
     * @brief 线性方程组求解
     * 
     */
	void Solve();

private:
	Mat *A;
	Vec *b;
	Vec *x;
    
    // 加入PETSc求解器设置
    KSP ksp;
	PC pc;
};

#endif
#endif
