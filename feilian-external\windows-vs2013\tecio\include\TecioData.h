 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <map>
#include <set>
#include <stdexcept>
#include <string>
#include <boost/shared_ptr.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "basicTypes.h"
#include "fileio.h"
#include "Geom_s.h"
#include "IJK.h"
#include "SzlFileLoader.h"
#include "Text_s.h"
#include "Zone_s.h"
namespace tecplot { namespace tecioszl { class ___3970 { public: class Error : public std::runtime_error { public: explicit Error(std::string const& ___2432) : runtime_error(___2432) {} }; ___3970(); ___3970( std::string const& ___4177, std::string const& ___4350, int32_t            defaultVarType); virtual ~___3970(); void ___19( char    const* ___4690, int32_t const  ___4692, int64_t const  ___1909, int64_t const  ___2116, int64_t const  ___2161, double  const  ___3640, int32_t const  ___3785, int32_t const  ___2974, int64_t const  ___2802, int32_t const  ___1284, int64_t const  ___4192, int64_t const  ___2786, int64_t const  ___4188, int32_t const* varTypes, int32_t const* ___2982, int32_t const* ___4326, int32_t const* ___3551, int32_t const  ___3549); void renumberLastZone(int32_t zoneNumber); void setPartitionOwners(std::vector<int> const& partitionOwners); template <typename T> void addFEZonePartition( int32_t                     zone, int32_t                     ___2977, int64_t                     ___2821, int64_t                     ___2781, std::vector<T>       const& gnodes, std::vector<int32_t> const& gnpartitions, std::vector<T>       const& gnpnodes, std::vector<T>       const& gcells); void addIJKZonePartition( int32_t zone, int32_t ___2977, int64_t iMin, int64_t jMin, int64_t kMin, int64_t iMax, int64_t jMax, int64_t kMax); void ___8(std::string const& ___2685, std::string const& ___4314); void ___20(int32_t zone, std::string const& ___2685, std::string const& ___4314); void ___18(int32_t ___4336, std::string const& ___2685, std::string const& ___4314); void ___6(std::vector<std::string> const& ___2173); void ___9( double ___4574, double ___4591, double ___4715, CoordSys_e ___3159, ___372 ___2004, ___1172 zone, ___516 color, ___516 ___1411, ___372 ___2022, GeomType_e ___1651, LinePattern_e ___2263, double ___2986, double ___2289, uint16_t ___2793, ArrowheadStyle_e arrowheadStyle, ArrowheadAttachment_e arrowheadAttachment, double arrowheadSize, double arrowheadAngle, Scope_e ___3442, Clipping_e ___495, ___2227 ___2835, ___2227 const* ___2837, float const* ___4572, float const* ___4589, float const* ___4712, char const* ___2327); void ___9(tecioszl::___1556 const& ___1555); void ___16( double ___4574, double ___4591, double ___4713, CoordSys_e ___3159, ___372 ___2004, ___1172 zone, Font_e ___1443, Units_e ___1452, double ___1450, TextBox_e ___410, double ___408, double ___406, ___516 ___402, ___516 ___404, double ___56, TextAnchor_e ___38, double ___2287, ___516 ___4080, Scope_e ___3442, Clipping_e ___495, char const* ___4042, char const* ___2327); void ___16(tecioszl::Text const& ___4042); void ___430(___2227 count, void const* data, bool ___2013); void ___432(___2227 count, int32_t const* ___2723); template <typename T> void ___431(Zone_s* zonePtr, T const* faceConnections); void copyConnectivity(___3970 const& ___2888); int32_t ___2843() const; int32_t ___2846() const; int32_t numZonePartitions(int32_t zone) const; std::string ___1394() const; void ___4166(bool checkConnectivity) const; void clear(int32_t numZonesToRetain, int32_t const* zonesToRetain); bool empty(); std::string const& ___4177() const { return ___2649; } std::vector<std::string> const& variableNames() const { return ___2674; } int32_t defaultVarType() const { return m_defaultVarType; } std::set<___3493> zoneSet() const; std::set<___3493> unflushedZoneSet() const; Zone_s* zonePtr(___3933::___4636 ___4658  ) const; boost::shared_ptr<AuxData_s> const& dataSetAuxData() const { return ___2399; } std::vector<boost::shared_ptr<AuxData_s> > const& varAuxData() const { return ___2672; } std::vector<___1556> const& geoms() const { return ___2465; } std::vector<Text> const& texts() const { return ___2640; } std::vector<std::vector<std::string> > const& customLabels() const { return ___2398; } bool validZoneNum(___3933::___4636 ___4658) const { return zonePtr(___4658) != NULL; } bool validPartitionNum(___3933::___4636 ___4658, ___3933::___4636 partitionNum) const; void flattenSinglePartitionZones(); ___3933::___1393 sizeInFile(bool ___4480) const; void writeToFile(___3933::FileWriterInterface& outputFile, ___3933::___1393 fileLoc, bool ___4480) const; ___3970(___3933::___1399& inputFile, bool readASCII); private: Zone_s* getAndCheckZonePtr(int32_t zone) const; std::string ___2649; std::vector<std::string> ___2674; int32_t m_defaultVarType; ___3933::___4636 m_nextZoneIndex; ___3933::___4636 m_lastZoneFlushedToDisk; Zone_s::ZoneMap m_zoneMap; boost::shared_ptr<AuxData_s> ___2399;
std::vector<boost::shared_ptr<AuxData_s> > ___2672; std::vector<___1556> ___2465; std::vector<Text> ___2640; std::vector<std::vector<std::string> > ___2398; }; }}
