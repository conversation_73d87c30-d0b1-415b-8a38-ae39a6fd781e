!===============================================================================
! Copyright 2008-2018 Intel Corporation.
!
! This software and the related documents are Intel copyrighted  materials,  and
! your use of  them is  governed by the  express license  under which  they were
! provided to you (License).  Unless the License provides otherwise, you may not
! use, modify, copy, publish, distribute,  disclose or transmit this software or
! the related documents without Intel's prior written permission.
!
! This software and the related documents  are provided as  is,  with no express
! or implied  warranties,  other  than those  that are  expressly stated  in the
! License.
!===============================================================================

!  Content:
!      Intel(R) Math Kernel Library (Intel(R) MKL) FORTRAN interface for BLAS-like
!      extensions
!*******************************************************************************

      INTERFACE
      subroutine MKL_SIMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &AB, lda, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      real               alpha
      real               AB( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_DIMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &AB, lda, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      double precision   alpha
      double precision   AB( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_CIMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &AB, lda, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      complex            alpha
      complex            AB( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_ZIMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &AB, lda, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      double complex     alpha
      double complex     AB( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_SOMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &A, lda, B, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      real               alpha
      real               A( * )
      real               B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_DOMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &A, lda, B, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      double precision   alpha
      double precision   A( * )
      double precision   B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_COMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &A, lda, B, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      complex            alpha
      complex            A( * )
      complex            B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_ZOMATCOPY ( ordering, trans, rows, cols, alpha,           &
     &A, lda, B, ldb )
      character*1        ordering, trans
      integer            rows, cols, lda, ldb
      double complex     alpha
      double complex     A( * )
      double complex     B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_SOMATCOPY2 ( ordering, trans, rows, cols, alpha,          &
     &A, lda, stridea, B, ldb, strideb )
      character*1        ordering, trans
      integer            rows, cols, lda, stridea, ldb, strideb
      real               alpha
      real               A( * )
      real               B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_DOMATCOPY2 ( ordering, trans, rows, cols, alpha,          &
     &A, lda, stridea, B, ldb, strideb )
      character*1        ordering, trans
      integer            rows, cols, lda, stridea, ldb, strideb
      double precision   alpha
      double precision   A( * )
      double precision   B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_COMATCOPY2 ( ordering, trans, rows, cols, alpha,          &
     &A, lda, stridea, B, ldb, strideb )
      character*1        ordering, trans
      integer            rows, cols, lda, stridea, ldb, strideb
      complex            alpha
      complex            A( * )
      complex            B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_ZOMATCOPY2 ( ordering, trans, rows, cols, alpha,          &
     &A, lda, stridea, B, ldb, strideb )
      character*1        ordering, trans
      integer            rows, cols, lda, stridea, ldb, strideb
      double complex     alpha
      double complex     A( * )
      double complex     B( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_SOMATADD ( ordering, transa, transb, rows, cols,          &
     &alpha, A, lda, beta, B, ldb, C, ldc )
      character*1        ordering, transa, transb
      integer            rows, cols, lda, ldb, ldc
      real               alpha, beta
      real               A( * )
      real               B( * )
      real               C( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_DOMATADD ( ordering, transa, transb, rows, cols,          &
     &alpha, A, lda, beta, B, ldb, C, ldc )
      character*1        ordering, transa, transb
      integer            rows, cols, lda, ldb, ldc
      double precision   alpha, beta
      double precision   A( * )
      double precision   B( * )
      double precision   C( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_COMATADD ( ordering, transa, transb, rows, cols,          &
     &alpha, A, lda, beta, B, ldb, C, ldc )
      character*1        ordering, transa, transb
      integer            rows, cols, lda, ldb, ldc
      complex            alpha, beta
      complex            A( * )
      complex            B( * )
      complex            C( * )
      END
      END INTERFACE

      INTERFACE
      subroutine MKL_ZOMATADD ( ordering, transa, transb, rows, cols,          &
     &alpha, A, lda, beta, B, ldb, C, ldc )
      character*1        ordering, transa, transb
      integer            rows, cols, lda, ldb, ldc
      double complex     alpha, beta
      double complex     A( * )
      double complex     B( * )
      double complex     C( * )
      END
      END INTERFACE

!*******************************************************************************
