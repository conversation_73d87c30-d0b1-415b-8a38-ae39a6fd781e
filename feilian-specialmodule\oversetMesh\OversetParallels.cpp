﻿#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

// 阻塞通信
template void OversetMesh::AllGatherAndMergeList(List<Vector> &srcList);
template void OversetMesh::AllGatherAndMergeList(List<TreeInfo> &srcList);
template void OversetMesh::AllGatherAndMergeList(List<Acceptor> &srcList);
template void OversetMesh::AllGatherAndMergeList(List<Scalar> &srcList);
template void OversetMesh::AllGatherAndMergeList(List<List<Vector>> &srcList);
template void OversetMesh::AllGatherAndMergeList(List<std::pair<int, int>> &srcList);
template <typename T>
void OversetMesh::AllGatherAndMergeList(List<T> &srcList)
{
#if defined(_BaseParallelMPI_)
	List<List<T>> tgtList;
	mpi::all_gather(mpi_world, srcList, tgtList);

	srcList.clear();
	for (int i = 0; i < tgtList.size(); i++)
	{
		if (tgtList[i].size() > 0)
		{
			srcList.insert(srcList.end(), tgtList[i].begin(), tgtList[i].end());
		}
	}

	tgtList.clear();
#endif
}

void OversetMesh::CollectAcceptors(const List<List<Acceptor>> &srcAcceptors,
								   List<List<Acceptor>> &collectedAcceptors)
{
	collectedAcceptors.resize(GetMPISize());
	for (int i = 0; i < GetMPISize(); i++)
	{
		boost::mpi::gather(mpi_world, srcAcceptors[i], collectedAcceptors, i);
	}
}

void OversetMesh::CollectAcceptorsReverse(const List<List<Acceptor>> &collectedAcceptors,
										  List<Acceptor> &searchResults)
{
	searchResults.clear();
	List<List<Acceptor>> tgtList;
	for (int i = 0; i < GetMPISize(); i++)
	{
		boost::mpi::gather(mpi_world, collectedAcceptors[i], tgtList, i);
	}

	for (int i = 0; i < tgtList.size(); i++)
	{
		if (tgtList[i].size() > 0)
		{
			searchResults.insert(searchResults.end(), tgtList[i].begin(), tgtList[i].end());
		}
	}

	tgtList.clear();
}
