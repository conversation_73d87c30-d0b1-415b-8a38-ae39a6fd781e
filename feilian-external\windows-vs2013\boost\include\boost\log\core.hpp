/*
 *          Copyright <PERSON><PERSON> 2007 - 2015.
 * Distributed under the Boost Software License, Version 1.0.
 *    (See accompanying file LICENSE_1_0.txt or copy at
 *          http://www.boost.org/LICENSE_1_0.txt)
 */
/*!
 * \file   log/core.hpp
 * \author <PERSON><PERSON>
 * \date   19.04.2007
 *
 * This header includes Boost.Log headers related to the logging core.
 */

#ifndef BOOST_LOG_CORE_HPP_INCLUDED_
#define BOOST_LOG_CORE_HPP_INCLUDED_

#include <boost/log/detail/config.hpp>

#include <boost/log/core/core.hpp>
#include <boost/log/core/record.hpp>
#include <boost/log/core/record_view.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_LOG_CORE_HPP_INCLUDED_
