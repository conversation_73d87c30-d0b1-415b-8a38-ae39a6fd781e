//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_KARMA_OPERATOR_FEB_28_2007_0351PM)
#define BOOST_SPIRIT_KARMA_OPERATOR_FEB_28_2007_0351PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/operator/sequence.hpp>
#include <boost/spirit/home/<USER>/operator/and_predicate.hpp>
#include <boost/spirit/home/<USER>/operator/not_predicate.hpp>
#include <boost/spirit/home/<USER>/operator/alternative.hpp>
#include <boost/spirit/home/<USER>/operator/kleene.hpp>
#include <boost/spirit/home/<USER>/operator/plus.hpp>
#include <boost/spirit/home/<USER>/operator/optional.hpp>
#include <boost/spirit/home/<USER>/operator/list.hpp>

#endif
