﻿#ifndef _particle_force_ExternalForce_
#define _particle_force_ExternalForce_

#include "basic/common/ConfigUtility.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/basic/IndexVector.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"
#include "feilian-specialmodule/particle/property/PhysicalProperty.h"
#include "sourceFlow/package/FlowPackage.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

class ExternalForce
{
public:
    ExternalForce(const Configure::Particle::ParticleConfigure &configure_,
                  std::vector<Particle *> &particles_, 
                  PhysicalProperty *physicalProperty_,
                  const std::vector<int> &backgroundID_ = std::vector<int>(),
                  const Package::FlowPackage *flowPackage_ = nullptr);

    ~ExternalForce();

    void CalculateExternalForce();
    
    void SetParticleNumber(const int &num_){numParticleCurrent = num_;}

	void UpdateFlowVariables();

private:

    void CalculateGravity(const int &particleID);

	void CalculateDragForce(const int &particleID, const Scalar &dP, const Vector &uP, const Scalar &rhoF, const Vector &uF, const Scalar &muF);

	void CalculateLiftForce(const int &particleID, const Scalar &dP, const Vector &uP, const Scalar &rhoF, const Vector &uF, const Scalar &muF, const Vector &omegaF);

	void CalculatePressureGradientForce(const int &particleID, const Scalar &dp, const Vector &gradPI);

    void CalculateAddedMassForce(const int &particleID, const int &elemID);

	void CalculateRotationlDrag(const int &particleID, const int &elemID);

    Scalar ClaculateDragCoefficient(const int &particleID, const Scalar &Rep);

	template<class Type>
	Type GetValue(const Vector &pos, const int &elemID, ElementField<Type> *field);

	template<class Type, class TypeGradient>
	Type GetValue(const Vector &pos, const int &elemID, ElementField<Type> *field, ElementField<TypeGradient> *gradientField );

private:
    const Configure::Particle::ParticleConfigure &configure;
    std::vector<Particle *> &particles;
    PhysicalProperty *physicalProperty;

    ElementField<Scalar> *rho;
    ElementField<Vector> *U;
    ElementField<Scalar> *p;
	ElementField<Scalar> *muL;

	ElementField<Vector> *gradRho;
	ElementField<Tensor> *gradU;
	ElementField<Vector> *gradP;
	ElementField<Vector> *gradMu;

	ElementField<Vector> *rhoU;
	ElementField<Tensor> *gradRhoU;

	ElementField<Tensor> *gradGradP;

	bool gradRhoFlag;
	bool gradUFlag;
	bool gradPFlag;
	bool muLFlag;
	Scalar muLValue;

	bool dragForce;
	bool liftForce;
	bool pressureGradientForce;
	bool addedMassForce;
	bool rotationlDrag;
	Configure::Particle::InterpolationMethod interpolationMethod;
	bool conservationFlag;

    int numParticleCurrent;
    bool dim2;

    int elementSize; ///< 全局网格单元数量

    const std::vector<int> &backgroundID;
};

} // namespace Particle

#endif