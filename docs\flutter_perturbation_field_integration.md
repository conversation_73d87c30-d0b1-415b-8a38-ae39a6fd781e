# 颤振摄动场前处理集成方案

## 概述

本方案将颤振计算中的摄动场处理功能集成到ARI-CFD前处理程序中，实现摄动场数据的读取、验证、处理和分区，为后续的颤振分析提供预处理支持。

## 设计原则

1. **最密网格关联**：摄动场仅与最密网格（Level 0）对应，与聚合网格无关
2. **模态数量匹配**：摄动场数组维度与结构模态数量相等
3. **节点数量一致**：摄动场数组维度与气动网格节点数相等
4. **分区同步处理**：在网格分区时同步处理摄动场数据
5. **数据完整性**：提供完整的数据验证和错误检查机制

## 实现架构

### 1. 配置扩展

#### 新增配置结构体
- `PerturbationFieldStruct`：摄动场参数配置
- 集成到 `PreprocessStruct` 中

#### 配置参数
```xml
<perturbationField>
    <perturbationFlag>true</perturbationFlag>
    <numModes>3</numModes>
    <perturbationPath>./perturbation/</perturbationPath>
    <binaryFormat>false</binaryFormat>
    <validateData>true</validateData>
    <mode0File>mode1_bending.dat</mode0File>
    <mode1File>mode2_torsion.dat</mode1File>
    <mode2File>mode3_combined.dat</mode2File>
</perturbationField>
```

### 2. 数据结构设计

#### 核心类
- `PerturbationFieldManager`：摄动场管理器
- `ModalPerturbationData`：单模态摄动场数据

#### 主要功能
- 多格式文件读取（ASCII/二进制）
- 数据验证和完整性检查
- 模态叠加计算
- 分区数据输出

### 3. 前处理流程集成

#### 处理步骤
```
Step1: 网格读取与转化
Step2: 对偶网格转化
Step3: 网格聚合
Step4: 摄动场处理 ← 新增
Step5: 网格分区
```

#### 摄动场处理流程
1. 检查摄动场配置
2. 创建摄动场管理器
3. 加载摄动场数据
4. 验证数据完整性
5. 在分区时同步处理

## 文件结构

### 新增文件
```
meshProcess/perturbationField/
├── PerturbationField.h          # 摄动场数据结构定义
├── PerturbationField.cpp        # 摄动场功能实现
examples/
├── perturbation_field_example.xml    # 配置示例
├── perturbation_data_format.md       # 数据格式说明
docs/
└── flutter_perturbation_field_integration.md  # 本文档
```

### 修改文件
```
basic/configure/Configure.h      # 添加摄动场配置结构
basic/configure/Configure.cpp    # 添加摄动场参数读取
meshProcess/meshProcess/MeshProcess.h    # 添加摄动场处理接口
meshProcess/meshProcess/MeshProcess.cpp  # 实现摄动场处理逻辑
```

## 数据格式

### ASCII格式
```
# 节点ID  位移X  位移Y  位移Z
0  0.0000  0.0000  0.0000
1  0.0001  0.0000  0.0000
2  0.0002  0.0001  0.0000
```

### 二进制格式
```
[4字节] 节点数量
[12字节] 节点0位移 (dx, dy, dz)
[12字节] 节点1位移 (dx, dy, dz)
...
```

## 使用方法

### 1. 配置文件设置
在前处理配置文件中添加摄动场配置节点，指定模态数量、文件路径等参数。

### 2. 准备摄动场数据
按照指定格式准备各模态的摄动场数据文件，确保节点数量与网格一致。

### 3. 运行前处理
```bash
ARI_FlowPreprocessor.exe case_with_perturbation.xml
```

### 4. 验证结果
检查输出日志，确认摄动场数据加载和验证成功。

## 验证机制

### 数据完整性检查
- 文件存在性和可读性
- 节点数量匹配性
- 数据格式正确性

### 物理合理性检查
- 位移幅值范围检查
- 边界条件一致性
- 数值异常检测

### 一致性检查
- 多模态数据一致性
- 网格节点对应关系

## 性能考虑

### 内存管理
- 按需加载摄动场数据
- 及时释放不需要的数据
- 支持大规模网格处理

### 并行处理
- 支持MPI并行环境
- 分区数据同步处理
- 负载均衡考虑

## 扩展性

### 支持更多格式
- 可扩展支持其他摄动场文件格式
- 支持不同的结构分析软件输出

### 高级功能
- 摄动场插值和映射
- 多物理场耦合支持
- 自适应网格摄动场处理

## 注意事项

1. **网格一致性**：确保摄动场数据与CFD网格完全对应
2. **单位统一**：摄动场位移单位应与网格坐标单位一致
3. **边界条件**：固定边界处的摄动位移应为零
4. **数据备份**：重要的摄动场数据应进行备份
5. **版本管理**：对摄动场数据进行版本控制

## 后续开发

### 待完善功能
1. **完整的分区处理**：实现基于节点映射的摄动场分区
2. **高级验证**：添加更多物理约束检查
3. **性能优化**：大规模数据处理优化
4. **可视化支持**：摄动场数据可视化工具

### 集成测试
1. 单元测试：各组件功能测试
2. 集成测试：完整流程测试
3. 性能测试：大规模网格测试
4. 兼容性测试：不同格式数据测试

## 总结

本方案成功将颤振摄动场处理功能集成到ARI-CFD前处理程序中，提供了完整的数据结构、处理流程和验证机制。通过模块化设计，确保了系统的可扩展性和可维护性，为后续的颤振分析奠定了坚实的基础。
