 #pragma once
#include "SzlFileLoader.h"
#include "AltTecUtil.h"
#include "FieldData.h"
namespace tecplot { namespace ___3933 { class OrthogonalBisection { UNCOPYABLE_CLASS(OrthogonalBisection); public: enum BisectionType_e { BisectionType_ZoneCells, BisectionType_ZoneNodes }; private: static ___4352 const NUM_BISECTION_DIRECTIONS = 3; static ___2090::SubzoneOffset_t const  MAX_THREADS_PER_CORE = 4; static ___81 const MIN_THREADED_ITEM_RANGE_SIZE = 256; static ___81 const MIN_THREADED_SUBZONE_RANGE_SIZE = 1; ___4636 const               ___2677; BisectionType_e const           m_bisectionType; ___2090::ItemOffset_t const m_maxDomainSize; ___2090::SubzoneOffset_t m_maxThreadedJobs; ___2090::SubzoneOffset_t m_maxRecursion; ___81                   m_numItems; ___81                   m_numGhostItems; ___2090::SubzoneOffset_t m_numRecursionDepths; ___2090::SubzoneOffset_t m_numDomains; ___2240<___81>   m_itemList; ZoneType_e                      ___2684; ___2240<FieldDataPtr> m_nodalFieldDataPtrs; ___2240<FieldDataPtr> m_ccFieldDataPtrs; ___2240<UInt8Array>   m_reducedPrecisionSortDataArrays; public: OrthogonalBisection( ___4636               zone, BisectionType_e           bisectionType, ___2090::ItemOffset_t maxDomainSize); ~OrthogonalBisection(); ___372 performBisection(___37& ___36); ___372 getSzCoordByOriginalItemArray( ___37&               ___36, ItemAddressArray& szCoordsOfOriginalItems); ___81 queryNumItems() const { return m_numItems; } ___81 queryNumGhostItems() const { return m_numGhostItems; } ___81 queryPositionbyOffset(___81 ___2865) const { return m_itemList[___2865]; } ___2090::SubzoneOffset_t queryNumberDomains() const { return m_numDomains; } inline ___2090::ItemOffset_t getDomainSize(___2090::SubzoneOffset_t domain) const { REQUIRE(domain<m_numDomains); ___2090::ItemOffset_t domainSize; if ( domain < m_numDomains-1 ) domainSize = m_maxDomainSize; else { domainSize = ( m_numItems % m_maxDomainSize ); if ( domainSize == 0 ) domainSize = m_maxDomainSize; ___478(m_maxDomainSize*(m_numDomains-1) + domainSize == m_numItems); } ENSURE(domainSize > 0 && domainSize <= ___2090::MAX_ITEM_OFFSET+1); return domainSize; } void ___937(void); private: ___372 updateStatusLine( ___37&                  ___36, ___2090::SubzoneOffset_t recursionDepth); ___372 loadCoordinateVarZoneFieldData(___37& ___36, char axis); inline void getJobStartEndForRange( ___81                   rangeStart, ___81                   ___3268, ___2090::SubzoneOffset_t ___2118, ___2090::SubzoneOffset_t numJobs, ___81&                  jobStart, ___81&                  jobEnd) { REQUIRE(rangeStart < m_numItems); REQUIRE(___3268 > 0 && rangeStart+___3268 <= m_numItems); REQUIRE(___2118 < numJobs); REQUIRE(numJobs > 0); jobStart = ___81( rangeStart + (uint64_t(___3268)*___2118)/numJobs ); jobEnd = ___81( rangeStart + (uint64_t(___3268)*(___2118+1))/numJobs ); ENSURE(rangeStart<=jobStart && jobStart<jobEnd && jobEnd<=m_numItems); ENSURE(IMPLICATION(___2118==0, jobStart==rangeStart)); ENSURE(IMPLICATION(___2118==numJobs-1, jobEnd==rangeStart+___3268)); } void getMinMaxWeightOverRange( ___81                   rangeStart, ___81                   rangeEnd, ___2090::SubzoneOffset_t ___2118, ___2090::SubzoneOffset_t numJobs, ___4352                   axisDirToSplit, ___2479&                      weightMinMax); void rescaleWeightsOverRange( ___81                   rangeStart, ___81                   ___3268, ___2090::SubzoneOffset_t ___2118, ___2090::SubzoneOffset_t numJobs, ___4352                   axisDirToSplit, double                       scale, double                       ___2865); ___372 calcReducedPrecisionValues( ___37&                        ___36, ___81 const                   domainStart, ___81 const                   domainSize, ___4352 const                   axisDirToSplit, ___2090::SubzoneOffset_t const recursionDepth); ___372 splitDomain( ___37&                        ___36, ___81 const                   domainStart, ___81 const                   domainSize, ___4352 const                   axisDirToSplit, ___2090::SubzoneOffset_t const recursionDepth, ___2120                      ___2119); void getSzCoordsByRange( ___81                   rangeStart, ___81                   ___3268, ___2090::SubzoneOffset_t ___2118, ___2090::SubzoneOffset_t numJobs, ItemAddressArray&            szCoordArray); ___2090::SubzoneOffset_t calcNumDomains() const { return ___2090::SubzoneOffset_t((m_numItems+m_maxDomainSize-1)/m_maxDomainSize); } void getSubdomainInfo( ___81  domainStart, ___81  domainSize, ___81& leftOffset, ___81& leftSize, ___81& rightOffset, ___81& rightSize) const { REQUIRE(domainSize>0); INVARIANT(m_maxDomainSize>0); ___81 localDomainSplitSize = m_maxDomainSize;
while ( localDomainSplitSize < (domainSize+1)/2 ) { localDomainSplitSize *= 2; ___478(localDomainSplitSize>0); } ___478(localDomainSplitSize<domainSize && domainSize<=2*int64_t(localDomainSplitSize)); leftOffset = domainStart; leftSize =  localDomainSplitSize * ___81( (domainSize+int64_t(localDomainSplitSize)*2-1) / (int64_t(localDomainSplitSize)*2) ); rightOffset = domainStart + leftSize; rightSize   = domainSize - leftSize; ENSURE(leftSize>=localDomainSplitSize && leftSize < domainSize); ENSURE(leftSize>=localDomainSplitSize && rightSize < domainSize); } ___2090::SubzoneOffset_t calcNumRecursionDepths() const { ___2090::SubzoneOffset_t numRecursionDepths = 0; ___81 largestDomainOffset = 0; ___81 largestDomainSize = m_numItems; while ( largestDomainSize > m_maxDomainSize ) { ___81 leftOffset; ___81 leftSize; ___81 rightOffset; ___81 rightSize; getSubdomainInfo(largestDomainOffset, largestDomainSize, leftOffset, leftSize, rightOffset, rightSize); if ( leftSize >= rightSize ) { largestDomainSize = leftSize; largestDomainOffset = leftOffset; } else { largestDomainSize = rightSize; largestDomainOffset = rightOffset; } numRecursionDepths++; } return numRecursionDepths; } }; }}
