﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file GeometryTools.h
//! <AUTHOR> 张帅（数峰科技/西交大）
//! @brief 基本几何计算功能.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_GeometryTools_
#define _basic_common_GeometryTools_

#include "basic/common/ConfigUtility.h"

/**
 * @brief Calculating center point and length of a line segment.
 * 
 * @param P1 strart point
 * @param P2 end point 
 * @param center center of line segment
 * @param length length of line segment
 */
void CalculateLineSegment
(
    const Vector & P1,
    const Vector & P2,
    Vector & center,
    Vector & length
);

/**
 * @brief Calculating center point and area of a triangle
 * 
 * @param P1 point 1
 * @param P2 point 2 
 * @param P3 point 3 
 * @param center center of triangle
 * @param area area of triangle
 */
void CalculateTriangle
(
    const Vector & P1,
    const Vector & P2,
    const Vector & P3,
    Vector & center,
    Vector & area
);

/**
 * @brief Calculating center point and area of a polygon.
 * 
 * @param vertice vertices 
 * @param center center of the geometry
 * @param area  area of the geometry
 * @note (1) line segment, if 2 vertices are provided; (2) triangle, if 3 vertices are provided;
 */
void GetCenterAndArea
(
    const std::vector<Vector > & vertice,
    Vector & center,
    Vector & area
);

/**
 * @brief Calculating center point and volume of a pyramid.
 * 
 * @param apex apex of pyramid
 * @param base base of pyramid
 * @param center center of pyramid
 * @param volume volume of pyramid
 */
void GetCenterAndVolume
(
    const Vector & apex,
    const std::vector<Vector > & base,
    Vector & center,
    Scalar & volume
);

/**
 * @brief Calculating center point and volume of a polyhedron (from vertices).
 * 
 * @param polygonVertice vertices of polygon
 * @param center center of polygon
 * @param volume volume of polygon
 */
void GetCenterAndVolume
(
    const std::vector<std::vector<Vector > >& polygonVertice,
    Vector& center,
    Scalar& volume
);

/**
 * @brief Calculating center point and volume of a polyhedron 
 * 
 * @param faceCenter center of face
 * @param faceArea area of face
 * @param center center of polyhedron
 * @param volume volume of polyhedron
 * @note from center points and areas of its surrounding faces
 */
void GetCenterAndVolume
(
    const std::vector<Vector > & faceCenter,
    const std::vector<Vector > & faceArea,
    Vector& center,
    Scalar& volume
);

/**
 * @brief rotate a given point around a given axis at a given angle 
 * 
 * @param GivenPoint given point 
 * @param AxisPoint given axis point
 * @param axis given axis
 * @param theeta given angle
 * @return Vector 
 * @ref https://en.wikipedia.org/wiki/Rotation_matrix#cite_note-5
 */
Vector RotatePoint
(
    const Vector & GivenPoint,
    const Vector & AxisPoint,
    Vector axis,
    const Scalar & theeta
);

#endif
