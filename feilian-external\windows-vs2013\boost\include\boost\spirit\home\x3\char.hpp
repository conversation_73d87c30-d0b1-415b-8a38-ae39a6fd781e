/*=============================================================================
    Copyright (c) 2001-2014 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_X3_CHAR_FEBRUARY_02_2007_0921AM)
#define BOOST_SPIRIT_X3_CHAR_FEBRUARY_02_2007_0921AM

#include <boost/spirit/home/<USER>/char/char_parser.hpp>
#include <boost/spirit/home/<USER>/char/negated_char_parser.hpp>
#include <boost/spirit/home/<USER>/char/char.hpp>
#include <boost/spirit/home/<USER>/char/char_class.hpp>
#include <boost/spirit/home/<USER>/char/char_set.hpp>

#if defined(BOOST_SPIRIT_X3_UNICODE)
#include <boost/spirit/home/<USER>/char/unicode.hpp>
#endif

#endif
