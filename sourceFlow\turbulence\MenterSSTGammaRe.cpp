﻿#include "sourceFlow/turbulence/MenterSSTGammaRe.h"

namespace Turbulence
{

	MenterSSTGammaRe::MenterSSTGammaRe(Package::FlowPackage &flowPackage)
		:
		MenterSST(flowPackage),
		Ga(*GetTurbulencePointer(FlowMacro::Scalar::GAMMA)),
		Re(*GetTurbulencePointer(FlowMacro::Scalar::ReThet)),
		residualRe(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_ReThet)),
		residualGa(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_GAMMA)),
		gradientRe(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_ReThet)),
		gradientGa(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_GAMMA)),
		cRef(flowPackage.GetFlowConfigure().GetMeshParameters().reference.cRef),
		sigmaGamma(1.0), Ce1(1.0), Ca1(2.0), Ca2(0.06),
		sigmaThetaT(2.0), CThetaT(0.03), Ce2(50.0)
	{
		// 获取远场值
		const std::vector<Scalar> &farValue = turbulenceBoundary.GetFarValue();

		for (int m = 0; m < turbulenceSize; ++m)
		{
			if (turbulenceMacro[m] == FlowMacro::Scalar::K) kFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::OMEGA) omegaFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::ReThet) ReFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::GAMMA) GaFree = farValue[m];
		}
	}

	MenterSSTGammaRe::~MenterSSTGammaRe()
	{
	}

	void MenterSSTGammaRe::AddSourceResidual()
	{
		if (currentLevel > 0) return;
		// 计算源项
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &rhoI = rho.GetValue(elementID);
			const Vector &UI = U.GetValue(elementID);
			const Scalar &GaI = Ga.GetValue(elementID);
			const Scalar &ReI = Re.GetValue(elementID);
			const Scalar &kI = k.GetValue(elementID);
			const Scalar &wI = omega.GetValue(elementID);
			const Scalar &muL = muLaminar.GetValue(elementID);
			const Scalar &d = mesh->GetNearWallDistance(elementID);
			const Scalar d2 = d * d;

			// 1. 计算Ga方程的生成项
			// Flength用来调节转捩区长度
			Scalar UMag = UI.Mag();
			const Scalar Rew2 = pow(rhoI * wI * d2 / muL, 2.0);
			Scalar Flength = CalculateFlenght(ReI, Rew2);

			// 计算转捩判据相关参数
			Scalar Omega = OmegaTensorMag(elementID);
			Scalar Strain = STensorMag(elementID);
			Scalar ReV = rhoI * Strain * d2 / muL;
			Scalar ReThetaC = ReThetaCorrelation(ReI);

			// Fonset用来启动转捩，超过1时Ga的源项开始增长
			Scalar RT = rhoI * kI / (wI * muL);
			Scalar Fonset1 = ReV / 2.193 / ReThetaC;
			Scalar Fonset2 = Min(Max(Fonset1, pow(Fonset1, 4.0)), 2.0);
			Scalar Fonset3 = Max(1.0 - pow(RT * 0.4, 3.0), Scalar0);
			Scalar Fonset = Max(Fonset2 - Fonset3, Scalar0);

			// Ga方程的生成项
			Scalar p_Ga_prod = Ca1 * rhoI * Strain * Flength * pow(Fonset * GaI, 0.5) * (1.0 - Ce1 * GaI);

			// 2. 计算Ga方程的破坏项
			Scalar Fturb = exp(-pow(RT * 0.25, 4.0));
			Scalar coef = Ca2 * Omega * Fturb * (Ce2 * GaI - 1.0);
			Scalar p_Ga_dest = coef * rhoI * GaI;

			// 3. 计算Re方程的生成项
			Scalar Fwake = exp(-Rew2 / 1.0e10);
			Scalar thetaBL = ReI * muL / (rhoI * UMag);
			Scalar delta = 50.0 * Omega * d * (7.5 * thetaBL) / UMag;
			Scalar temp1 = Fwake * exp(-pow(d / delta, 4.0));
			Scalar temp2 = (GaI - 1.0 / Ce2) / (1.0 - 1.0 / Ce2);
			Scalar FThetaT = Min(Max(temp1, 1.0 - temp2 * temp2), 1.0);
			Scalar tScale = 500.0 * muL / (rhoI * UMag * UMag);
			Scalar re = ReThetaT(rhoI, muL, kI, UI, gradientU.GetValue(elementID));
			Scalar p_Re_prod = CThetaT * rhoI * (re - ReI) * (1.0 - FThetaT) / tScale;

			// 4. 将源项加入残值
			const Scalar &volume = mesh->GetElement(elementID).GetVolume();
			const Scalar p_Ga_source = volume * (p_Ga_prod - p_Ga_dest);
			const Scalar p_Re_source = volume * p_Re_prod;
			residualGa.AddValue(elementID, -p_Ga_source);
			residualRe.AddValue(elementID, -p_Re_source);

			// 5. 点隐式Jacobian
			const Scalar jacobianGa = -coef * volume;
			jacobianTurbulence[2]->SetValue(elementID, jacobianGa);

			// 6. 计算有效间歇因子
			Scalar FreAttach = exp(-pow(RT * 0.05, 4.0));
			Scalar GaSeparated = 2.0 * Min(FreAttach * Max(ReV / ReThetaC / 3.235 - 1.0, 0.0), 1.0) * FThetaT;
			Scalar GaEff = Max(GaSeparated, GaI);

			// 7. 计算SST模型的源项
			MenterSST::AddSourceResidual(elementID, GaEff, 1.0);
		}

		return;
	}

	Scalar MenterSSTGammaRe::ReThetaCorrelation(const Scalar &ReI)
	{
		Scalar ReThetaC;
		if (ReI <= 1870)
		{
			Scalar ReI2 = ReI * ReI;
			Scalar ReI3 = ReI2 * ReI;
			Scalar ReI4 = ReI3 * ReI;
			ReThetaC = ReI - (396.035e-2 - 120.656e-4 * ReI + 868.23e-6 * ReI2 - 696.506e-9 * ReI3 + 174.105e-12 * ReI4);
		}
		else
		{
			ReThetaC = ReI - (593.11 + (ReI - 1870.0) * 0.482);
		}

		return ReThetaC;
	}

	Scalar MenterSSTGammaRe::CalculateFlenght(const Scalar &ReI, const Scalar &Rew2)
	{
		Scalar Flength1;
		if (ReI <= 400.0)
		{
			Flength1 = 39.8189 - 119.27e-4 * ReI - 132.567e-6 * ReI * ReI;
		}
		else if (ReI <= 569.0)
		{
			Flength1 = 263.404 - 123.929e-2 * ReI + 194.548e-5 * ReI * ReI - 101.695e-8 * ReI * ReI * ReI;
		}
		else if (ReI <= 1200.0)
		{
			Flength1 = 0.5 - (ReI - 596.0) * 3.e-4;
		}
		else
		{
			Flength1 = 0.3188;
		}

		const Scalar Fsublayer = exp(-Rew2 / 40000.0);
		Scalar Flength = Flength1 * (1.0 - Fsublayer) + 40.0 * Fsublayer;

		return Flength;
	}

	int MenterSSTGammaRe::CheckAndLimit()
	{
		////参考了Overset的rudnik limiter
		//const Scalar scoeff = 0.5 * sqrt(3.0);
		const Scalar limitCoff = 1.e-5;
		const Scalar kMin = limitCoff * kFree;
		const Scalar wMin = limitCoff * omegaFree;

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
		  	const Scalar &kI = k.GetValue(elementID);
		  	Scalar kLimited = Max(kI, kMin);
		  	k.SetValue(elementID, kLimited);

		  	//const Scalar Strain = STensorMag(elementID);
		  	//const Scalar wMin = scoeff * Strain;
		  	const Scalar &wI = omega.GetValue(elementID);
		  	Scalar wLimited = Max(wI, wMin);
		  	omega.SetValue(elementID, wLimited);

			const Scalar &GaI = Ga.GetValue(elementID);
			Scalar GaLimited = Min(Max(GaI, 0.0), 1.0);
			Ga.SetValue(elementID, GaLimited);
		}

		int elementID = -1;
		elementID = Max(elementID, Ga.CheckAndLimit(0.0, 1.05));
		elementID = Max(elementID, Re.CheckAndLimit(0.0, INF));
		elementID = Max(elementID, k.CheckAndLimit(0.0, INF));
		elementID = Max(elementID, omega.CheckAndLimit(0.0, INF));
		return elementID;
	}

	Scalar MenterSSTGammaRe::ReThetaT(const Scalar &rhoI, const Scalar &muL, const Scalar &kI, const Vector &UI, const Tensor &Ug)
	{
		// 计算沿流线方向的速率导数
		Scalar UMag = UI.Mag();

		Scalar flowX = (UI.X() * Ug.XX() + UI.Y() * Ug.XY() + UI.Z() * Ug.XZ());
		Scalar flowY = (UI.X() * Ug.YX() + UI.Y() * Ug.YY() + UI.Z() * Ug.YZ());
		Scalar flowZ = (UI.X() * Ug.ZX() + UI.Y() * Ug.ZY() + UI.Z() * Ug.ZZ());
		Scalar dVds = (UI.X() * flowX + UI.Y() * flowY + UI.Z() * flowZ) / (UMag * UMag);

		// 计算当地相关参数
		//Scalar K = dVds * muL / (rhoI * UMag * UMag);

		Scalar Tu = Max(100.0 * sqrt(2.0 * kI / 3.0) / UMag, 0.027);
		Scalar coff;
		if (Tu <= 1.3)
		{
			coff = 1173.51 - 589.428 * Tu + 0.2196 / (Tu * Tu + SMALL);
		}
		else
		{
			coff = 331.50 * pow(Tu - 0.5658, -0.671);
		}

		//Scalar FOverFd, errorTemp, FTemp, FdTemp;
		Scalar errorTemp, FTemp, FdTemp;
		Scalar re = coff;
		//Scalar re_old = coff;

		Scalar xnew = coff * muL / rhoI / UMag;
		Scalar xold = 0.0;
		//Scalar lambda = Max(Min(rhoI * xnew * xnew /muL * dVds, 0.1), -0.1);
		Scalar lambda = rhoI * xnew * xnew /muL * dVds;
	        FTemp = rhoI * UMag * xnew / muL - coff * F_lambdaTheta(lambda, Tu);

		//for (int step = 0; step < 2000000000; ++step)
		//{
		//	lambda = Max(Min(re * re * K, 0.1), -0.1);
		//	FTemp = coff * F_lambdaTheta(lambda, Tu);
		//	FdTemp = coff * F_diff(lambda, Tu) * 2.0 * re * K;

		//	FOverFd = (re - FTemp) / (1.0 - FdTemp + SMALL);
		//	re_old = re;

		//	re = re_old - FOverFd;

		//	//errorTemp = abs( FOverFd / (re + SMALL) );
		//	errorTemp = abs(re - re_old);
                //        if(step == 1999999999) std::cout<<"err"<<"++++++"<<errorTemp<< "----------" << errorTemp/re <<std::endl;
		//	//if (errorTemp < 1.e-9) break;
		//	if ( abs(errorTemp/re) < 1.e-3) break;
		//}

		for (int step = 0; step < 20000; ++step)
		{
			FdTemp = rhoI * UMag / muL - coff * F_diff(lambda, Tu) * 2.0 * rhoI * xnew /muL * dVds;

			xold = xnew;
			xnew = xold - FTemp / (FdTemp + SMALL);

		        //lambda = Max(Min(rhoI * xnew * xnew /muL * dVds, 0.1), -0.1);
		        lambda = rhoI * xnew * xnew /muL * dVds;
	                FTemp = rhoI * UMag * xnew / muL - coff * F_lambdaTheta(lambda, Tu);

			errorTemp = abs(xnew-xold);

                        //if(step == 19999) std::cout<<"err"<<"++++++"<<errorTemp<< "----------" << abs(errorTemp/xnew) <<std::endl;

			if (abs(errorTemp/xold) < 1.e-5) break;
			//if ( abs(errorTemp/xnew) < 1.e-3) break;
		}

		//lambda = Max(Min(re * re * K, 0.1), -0.1);
		//re = Max(coff * F_lambdaTheta(lambda, Tu), 20.0);
                re = Max(rhoI * UMag /muL * xnew -FTemp, 20.0);
		return re;
	}

	Scalar MenterSSTGammaRe::F_lambdaTheta(const Scalar &lambda, const Scalar &Tu)
	{
		Scalar F_lambda;
		if (lambda <= 0)
		{
			Scalar lambda2 = lambda * lambda;
			Scalar lambda3 = lambda2 * lambda;
			F_lambda = 1.0 - (-12.986 * lambda - 123.66 * lambda2 - 405.689 * lambda3) * exp(-pow(Tu / 1.5, 1.5));
		}
		else
		{
			F_lambda = 1.0 + 0.275 * (1.0 - exp(-35.0 * lambda)) * exp(-Tu * 2.0);
		}
		return F_lambda;
	}

	Scalar MenterSSTGammaRe::F_diff(const Scalar &lambda, const Scalar &Tu)
	{
		Scalar F_diff;
		if (lambda <= 0)
		{
			Scalar lambda2 = lambda * lambda;
			F_diff = (12.986 + 247.32 * lambda + 1217.067 * lambda2) * exp(-pow(Tu / 1.5, 1.5));
		}
		else
		{
			F_diff = 9.625 * exp(-Tu * 2.0) * exp(-35.0 * lambda);
		}
		return F_diff;
	}

	std::vector<std::pair<Scalar, Scalar>> MenterSSTGammaRe::CalculateGammaFace(const int &faceID)
	{
		//得到面相关信息
		const int &leftID = mesh->GetFace(faceID).GetOwnerID();
		const int &rightID = mesh->GetFace(faceID).GetNeighborID();

		//计算面心值
		const Scalar muLaminarFace = 0.5 * (muLaminar.GetValue(leftID) + muLaminar.GetValue(rightID));
		const Scalar muTurbulentFace = 0.5 * (muTurbulent->GetValue(leftID) + muTurbulent->GetValue(rightID));

		//计算面的扩散项通量
		std::vector<std::pair<Scalar, Scalar>> gamma = MenterSST::CalculateGammaFace(faceID);

		Scalar gammaGa = muLaminarFace + muTurbulentFace / sigmaGamma; //Ga方程的扩散系数
		Scalar gammaRe = sigmaThetaT * (muLaminarFace + muTurbulentFace); //Re方程的扩散系数	

		gamma.reserve(4);
		gamma.push_back(std::pair<Scalar, Scalar>{gammaGa, gammaGa});
		gamma.push_back(std::pair<Scalar, Scalar>{gammaRe, gammaRe});


		return gamma;
	}

}//namespace Turbulence
