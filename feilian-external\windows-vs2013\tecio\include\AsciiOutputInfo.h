 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <utility>
#include "ThirdPartyHeadersEnd.h"
#include "MASTER.h"
#include "GLOBAL.h"
#include "CodeContract.h"
#include "fileStuff.h"
#include "StandardIntegralTypes.h"
namespace tecplot { namespace ___3933 { typedef std::pair<float, float> StdPairFloat; typedef std::pair<double, double> StdPairDouble; typedef std::pair<int32_t, int32_t> StdPairInt32; typedef std::pair<int16_t, int16_t> StdPairInt16; typedef std::pair<uint8_t, uint8_t> StdPairUInt8; template <typename T, bool ___2025> struct ___199; template <> struct ___199<char, false>                 { static int32_t const size = ___194;      static char const* ___1474; }; template <> struct ___199<uint8_t, false>              { static int32_t const size = ___211;     static char const* ___1474; }; template <> struct ___199<int16_t, false>              { static int32_t const size = ASCII_INT16_LEN;     static char const* ___1474; }; template <> struct ___199<uint16_t, false>             { static int32_t const size = ___208;    static char const* ___1474; }; template <> struct ___199<int32_t, false>              { static int32_t const size = ASCII_INT32_LEN;     static char const* ___1474; }; template <> struct ___199<uint32_t, false>             { static int32_t const size = ___209;    static char const* ___1474; }; template <> struct ___199<int64_t, false>              { static int32_t const size = ASCII_INT64_LEN;     static char const* ___1474; }; template <> struct ___199<uint64_t, false>             { static int32_t const size = ___210;    static char const* ___1474; }; template <> struct ___199<float, false>                { static int32_t const size = ___196;     static char const* ___1474; }; template <> struct ___199<double, false>               { static int32_t const size = ___195;    static char const* ___1474; }; template <> struct ___199<StdPairUInt8, false>         { static int32_t const size = ASCII_UINT8_MINMAX_LEN;                                  }; template <> struct ___199<StdPairInt16, false>         { static int32_t const size = ASCII_INT16_MINMAX_LEN;                                  }; template <> struct ___199<StdPairInt32, false>         { static int32_t const size = ASCII_INT32_MINMAX_LEN;                                  }; template <> struct ___199<StdPairFloat, false>         { static int32_t const size = ASCII_FLOAT_MINMAX_LEN;                                  }; template <> struct ___199<StdPairDouble, false>        { static int32_t const size = ASCII_DOUBLE_MINMAX_LEN;                                 }; template <> struct ___199<uint8_t, true>               { static int32_t const size = ASCII_HEXUINT8_LEN;  static char const* ___1474; }; template <> struct ___199<uint16_t, true>              { static int32_t const size = ASCII_HEXUINT16_LEN; static char const* ___1474; }; template <> struct ___199<uint64_t, true>              { static int32_t const size = ___197; static char const* ___1474; }; template <typename T, bool ___2025, int base> ___372 encodeAsciiValue(char *str, int ___418, T const& ___4298) { REQUIRE(VALID_REF(str)); REQUIRE(___418 > 0); int nChars = 0; if (___4298 == ___4298) nChars = snprintf(str, ___418, ___199<T, ___2025>::___1474, -___199<T, ___2025>::size, ___4298 + base); else nChars = snprintf(str, ___418, ___199<T, ___2025>::___1474, -___199<T, ___2025>::size, (T)0); ___372 ___2039 = (nChars > 0 && nChars < ___418); ENSURE(VALID_BOOLEAN(___2039)); return ___2039; } }}
