﻿#include "meshProcess/meshDeform/RBFGreedyAlg.h"
#include <functional>
#include <fstream>
#include <sstream>
#include <numeric>  // for std::iota

#if defined(_BasePlatformWinddows_)
#include <windows.h>
#endif

#if defined(_BaseParallelMPI_)
#include "basic/common/MPI.h"
#include <boost/mpi.hpp>
#endif

// Intel MKL ScaLAPACK头文件包含
#if defined(_EnableMKL_)
#if defined(small)
#undef small
#endif
#include "mkl.h"
#include "mkl_pblas.h"
#include "mkl_scalapack.h"
#include "mkl_blacs.h"
#endif

RBFGreedyAlg::RBFGreedyAlg(std::vector<Node> &v_wallNode_,std::vector<Node> &v_wallNode_deform_,Scalar &R_ )
					:v_wallNode(v_wallNode_), v_wallNode_deform(v_wallNode_deform_),R(R_)
{

	  limx = 1.0e-4;
	  n_wallNode =  v_wallNode.size();
	  cycle = 0;

}

RBFGreedyAlg::~RBFGreedyAlg()
{
}

void RBFGreedyAlg::rbfGreedy()
{
   	AllocateDim(Matrix_all, n_wallNode, n_wallNode);
	  AllocateDim(v_wallDelta_all, n_wallNode);

	  for (int i = 0; i < n_wallNode; i++)
	  {
		  for (int j = 0; j < n_wallNode; j++)
		  {
			 Scalar yita = sqrt(pow((v_wallNode[j].X() - v_wallNode[i].X()), 2)
			  	+ pow((v_wallNode[j].Y() - v_wallNode[i].Y()), 2)
			  	+ pow((v_wallNode[j].Z() - v_wallNode[i].Z()), 2)) / R;

			  if (yita > 1.0)
			  {
			  	Matrix_all[i][j] = 0.0;
			  }
			  else
			  {
			  	Matrix_all[i][j] = pow((1 - yita), 4)*(4 * yita + 1.0);
			  }

	  	}
	  }

	  for (int i = 0; i < v_wallNode.size(); i++)
	   {

		   v_wallDelta_all[i].SetX( v_wallNode_deform[i].X() - v_wallNode[i].X());
		   v_wallDelta_all[i].SetY( v_wallNode_deform[i].Y() - v_wallNode[i].Y());
		   v_wallDelta_all[i].SetZ( v_wallNode_deform[i].Z() - v_wallNode[i].Z());

	  }
}

 std::vector<Vector> RBFGreedyAlg:: DirectGreedyAlg()
{

	std::vector<Node> v_node1;
	std::vector<Node> v_node2;
	std::vector<Vector> v_wallDelta1;
	std::vector<Vector> v_wallDelta2;
	std::vector<std::vector<Scalar>> Matrix;
	std::vector<Vector> v_weight;
	std::vector<Vector> v_weightall;

	std::vector<int> nlim;
	std::vector<int> n_weightnum;
	std::vector<Scalar> lim;
	std::vector<Scalar> fai;

	Scalar yita;

	Node Delta;
	Node xyz;

	int n = v_wallNode.size();
	int n_node = 100;
	int n0 = 5;

	AllocateDim(v_weightall, n);

	AllocateDim(nlim, n_node);
	AllocateDim(lim, n_node);

	lim[0] = R;
	int n1 = n / n0;

	AllocateDim(v_node1, n1);
	AllocateDim(v_wallDelta1, n1);
	AllocateDim(v_wallDelta2, n1);

	for (int i = 0; i < n1; i++)
	{
		v_node1[i] = v_wallNode[i*n0];
		v_wallDelta2[i] = v_wallDelta_all[i*n0];
		v_wallDelta1[i] = v_wallDelta2[i];
		n_weightnum.push_back(i*n0);
	}

	while (lim[0] > limx)
	{
		AllocateDim(Matrix, n1, n1);

			for (int i = 0; i < n1; i++)
			{
				for (int j = i; j < n1; j++)
				{
					yita = sqrt(pow((v_node1[j].X() - v_node1[i].X()), 2)
						+ pow((v_node1[j].Y() - v_node1[i].Y()), 2)
						+ pow((v_node1[j].Z() - v_node1[i].Z()), 2)) / R;

					if (yita > 1.0)
					{
						Matrix[i][j] = 0.0;
						Matrix[j][i] = 0.0;
					}
					else
					{
						Matrix[i][j] = pow((1 - yita), 4)*(4 * yita + 1.0);
						Matrix[j][i] = Matrix[i][j];
					}
				}
			}

			AllocateDim(v_weight, n1);
			v_weight = Gaussin_L(Matrix, v_wallDelta1);

			Matrix.clear();

			AllocateDim(fai, n1);

			for (int i = 0; i < n_node; i++)
			{
				lim[i] = 0.0;
			}

			for (int i = 0; i < n; i++)
			{
				Delta = Vector0;
				yita = 0.0;
				Node &nodeTemp1 = v_wallNode[i];

				for (int j = 0; j < n1; j++)
				{
					Node nodeTemp2 = v_node1[j];   //物面坐标点
					yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2)
						+ pow((nodeTemp2.Y() - nodeTemp1.Y()), 2)
						+ pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R;

					if (yita > 1.0)
					{
						fai[j] = 0.0;
					}
					else
					{
						fai[j] = pow((1 - yita), 4)*(4 * yita + 1);
					}

				}
				for (int j = 0; j < n1; j++)
				{
					Delta.SetX( Delta.X() + v_weight[j].X() * fai[j] );
					Delta.SetY( Delta.Y() + v_weight[j].Y() * fai[j] );
					Delta.SetZ( Delta.Z() + v_weight[j].Z() * fai[j] );
				}

				xyz.SetX( fabs(v_wallDelta_all[i].X() - Delta.X()) );
				xyz.SetY( fabs(v_wallDelta_all[i].Y() - Delta.Y()) );
				xyz.SetZ( fabs(v_wallDelta_all[i].Z() - Delta.Z()) );

				//求x、y、z中最大值

				if (xyz.X() < xyz.Y())
				{
					xyz.SetX( xyz.Y() );
				}

				if (xyz.X() < xyz.Z())
				{
					xyz.SetX( xyz.Z() );
				}

				for (int j = 0; j<n_node; j++)
				{

					if (xyz.X() > lim[j])
					{
						for (int k = n_node - 1; k>j; k--)  //!!
						{
							lim[k] = lim[k - 1];
							nlim[k] = nlim[k - 1];
						}
						lim[j] = xyz.X();
						nlim[j] = i;
						break;
					}
				}
			}

			Print("\t循环.." + std::to_string((long double)lim[0]) + "......" + std::to_string((long long int)n1));

			if (lim[0]>limx)
			{
				AllocateDim(v_node2, n1 + n_node);

				for (int i = 0; i<n1; i++)
				{
					v_node2[i] = v_node1[i];
				}

				v_node1.clear();
				v_wallDelta1.clear();
				AllocateDim(v_wallDelta1, n1 + n_node);

				for (int i = 0; i<n1; i++)
				{
					v_wallDelta1[i] = v_wallDelta2[i];
				}

				v_wallDelta2.clear();

				for (int i = 0; i<n_node; i++)
				{
					if (lim[i] > 1.0e-7)
					{
						v_node2[n1 + i] = v_wallNode[nlim[i]];
						v_wallDelta1[n1 + i] = v_wallDelta_all[nlim[i]];
						n_weightnum.push_back(nlim[i]);
					}
					else
					{
						n_node = i + 1;
						break;
					}
				}

				n1 += n_node;

				AllocateDim(v_node1, n1);
				AllocateDim(v_wallDelta2, n1);

				for (int i = 0; i<n1; i++)
				{
					v_node1[i] = v_node2[i];
					v_wallDelta2[i] = v_wallDelta1[i];
				}

				v_wallDelta1.clear();
				AllocateDim(v_wallDelta1, n1);

				for (int i = 0; i<n1; i++)
				{
					v_wallDelta1[i] = v_wallDelta2[i];
				}

				v_node2.clear();
				fai.clear();
				v_weight.clear();

			}

       }

	   v_wallDelta2.clear();

	   for (int i = 0; i <n_wallNode; i++)
	   {
		   v_weightall[i] = Vector0;
	   }

	   for (int i = 0; i < n_weightnum.size(); i++)
	   {
		   v_weightall[n_weightnum[i]].SetX( v_weight[i].X());
		   v_weightall[n_weightnum[i]].SetY( v_weight[i].Y());
		   v_weightall[n_weightnum[i]].SetZ( v_weight[i].Z());
	   }

	   v_weight.clear();

	   return v_weightall;

}

std::vector<Vector> RBFGreedyAlg::GreedyAlgImp()
{
		std::vector<Vector> v_weight;
		std::vector<int> nlim;
		std::vector<Scalar> lim;
		std::vector<Scalar> fai;

		Node Delta;
		Node xyz;
		Scalar max;

		int n = v_wallNode.size();

		std::vector<Vector> v_Delta;
		std::vector<Vector> v_delta;

		AllocateDim(lim, n);
		AllocateDim(nlim, n);

		AllocateDim(v_weight, n);
		AllocateDim(v_wallDelta, n);

		for (int i = 0; i < n; i++)
		 {
			v_weight[i] = Vector0;
			v_wallDelta[i] = v_wallDelta_all[i];
		 }
		lim[0] = R;

		//根据误差循环求解
		while (lim[0] > limx)
		 {
			cycle++;

			AllocateDim(v_delta, n);

			for (int i = 0; i < n; i++)
			{
				lim[i] = 0.0;
				v_delta[i] = Vector0;
			}

			AllocateDim(v_weightM, n);
			GreedyAlgorithm();

			for (int i = 0; i < n; i++)
			{
			   for (int j = 0; j < n; j++)
				{
					v_delta[i].SetX(  v_delta[i].X() + Matrix_all[i][j] * v_weightM[j].X() );
					v_delta[i].SetY(  v_delta[i].Y() + Matrix_all[i][j] * v_weightM[j].Y() );
					v_delta[i].SetZ(  v_delta[i].Z() + Matrix_all[i][j] * v_weightM[j].Z() );
				}
			}

			AllocateDim(v_Delta, n);

			for (int i = 0; i < n; i++)
			{
				v_Delta[i].SetX( v_wallDelta[i].X() - v_delta[i].X() );
				v_Delta[i].SetY( v_wallDelta[i].Y() - v_delta[i].Y() );
				v_Delta[i].SetZ( v_wallDelta[i].Z() - v_delta[i].Z() );

				if (fabs(v_Delta[i].X()) > fabs(v_Delta[i].Y()))
				{
					max = fabs(v_Delta[i].X());
				}
				else
				{
					max = fabs(v_Delta[i].Y());
				}

				if (max < fabs(v_Delta[i].Z()))
				{
					max = fabs(v_Delta[i].Z());
				}

				//判断物面节点最大误差值，并排序
				for (int j = 0; j<n; j++)
				{
					if (max > lim[j])
					{
						for (int k = n - 1; k>j; k--)
						{
							lim[k] = lim[k - 1];
							nlim[k] = nlim[k - 1];
						}
						lim[j] = max;
						nlim[j] = i;

						break;
					}
				}


			}

			v_wallDelta.clear();
			AllocateDim(v_wallDelta, n);

			for (int i = 0; i < n; i++)
			{
				v_wallDelta[i].SetX( v_Delta[i].X() );
				v_wallDelta[i].SetY( v_Delta[i].Y() );
				v_wallDelta[i].SetZ( v_Delta[i].Z() );
			}

			for (int i = 0; i < n; i++)
			{
				v_weight[i].SetX(  v_weight[i].X() + v_weightM[i].X() );
				v_weight[i].SetY(  v_weight[i].Y() + v_weightM[i].Y() );
				v_weight[i].SetZ(  v_weight[i].Z() + v_weightM[i].Z() );
			}

			v_Delta.clear();
			v_delta.clear();
			v_weightM.clear();

			Print("\t循环.." + std::to_string((long long int)cycle) + "......" + std::to_string((long double)lim[0]));

	}

			Print("\t算法结束..............\n");

			lim.clear();
			nlim.clear();

			return v_weight;
}

void RBFGreedyAlg::GreedyAlgorithm()
{

	std::vector<Node> v_node1;
	std::vector<Node> v_node2;
	std::vector<Vector> v_wallDelta1;
	std::vector<Vector> v_wallDelta2;
	std::vector<Vector> v_weightG;
	std::vector<std::vector<Scalar>> Matrix;

	std::vector<int> nlim;
	std::vector<int> n_weightID;
	std::vector<Scalar> lim;
	std::vector<Scalar> fai;

	std::vector<int> num_rand;

	Scalar yita;
	int a;
	Node Delta;
	Node xyz;

	int n = v_wallNode.size();
	int n_node = 300;
	int n1 = 500;

	AllocateDim(nlim, n_node);
	AllocateDim(lim, n_node);

	lim[0] = 1.0;

	AllocateDim(v_node1, n1);
	AllocateDim(v_wallDelta1, n1);
	AllocateDim(v_wallDelta2, n1);


	for (int i = 0; i < v_wallNode.size(); i++)
	{
		num_rand.push_back(i);
	}
	std::random_shuffle(num_rand.begin(), num_rand.end());

	for (int i = 0; i < n1; i++)
	{
		a = num_rand[i];
		v_node1[i] = v_wallNode[a];
		v_wallDelta2[i] = v_wallDelta[a];
		v_wallDelta1[i] = v_wallDelta2[i];
		n_weightID.push_back(a);

	}

	while (lim[0]>limx)
	{

		AllocateDim(Matrix, n1, n1);

		for (int i = 0; i < n1; i++)
		{
			for (int j = i; j < n1; j++)
			{

				yita = sqrt(pow((v_node1[j].X() - v_node1[i].X()), 2)
					+ pow((v_node1[j].Y() - v_node1[i].Y()), 2)
					+ pow((v_node1[j].Z() - v_node1[i].Z()), 2)) / R;

				if (yita > 1.0)
				{
					Matrix[i][j] = 0.0;
					Matrix[j][i] = 0.0;
				}
				else
				{
					Matrix[i][j] = pow((1 - yita), 4)*(4 * yita + 1.0);
					Matrix[j][i] = Matrix[i][j];
				}
			}
		}
		//矩阵求解
		AllocateDim(v_weightG, n1);
		v_weightG = Gaussin_L(Matrix, v_wallDelta1);

		//误差求解
		Matrix.clear();
		AllocateDim(fai, n1);

		for (int i = 0; i < n_node; i++)
		{
			lim[i] = 0.0;
		}

		for (int i = 0; i < n; i++)
		{
			Delta = Vector0;
			yita = 0.0;
			Node nodeTemp1 = v_wallNode[i];

			for (int j = 0; j < n1; j++)
			{
				Node nodeTemp2 = v_node1[j];   //物面坐标点
				yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2)
					+ pow((nodeTemp2.Y() - nodeTemp1.Y()), 2)
					+ pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R;

				if (yita > 1.0)
				{
					fai[j] = 0.0;
				}
				else
				{
					fai[j] = pow((1 - yita), 4)*(4 * yita + 1);
				}

			}

			for (int j = 0; j < n1; j++)
			{
				Delta.SetX( Delta.X() + v_weightG[j].X() * fai[j] );
				Delta.SetY( Delta.Y() + v_weightG[j].Y() * fai[j] );
				Delta.SetZ( Delta.Z() + v_weightG[j].Z() * fai[j] );
			}

			xyz.SetX( fabs(Delta.X() - v_wallDelta[i].X()) );
			xyz.SetY( fabs(Delta.Y() - v_wallDelta[i].Y()) );
			xyz.SetZ( fabs(Delta.Z() - v_wallDelta[i].Z()) );

			//求x、y、z中最大值

			if (xyz.X() < xyz.Y())
			{
				xyz.SetX( xyz.Y() );
			}

			if (xyz.X() < xyz.Z())
			{
				xyz.SetX( xyz.Z() );
			}

			//判断物面节点最大误差值，并排序
			for (int j = 0; j<n_node; j++)
			{
				if (xyz.X()>lim[j])
				{
					for (int k = n_node - 1; k>j; k--)
					{
						lim[k] = lim[k - 1];
						nlim[k] = nlim[k - 1];
					}
					lim[j] = xyz.X();
					nlim[j] = i;

					break;
				}
			}


		}

		Print("\t使用贪心算法求RBF权重系数...............");
		Print("\t循环.." + std::to_string((long double)lim[0]) + "......" + std::to_string((long long int)n1));

		//根据误差值及其他条件在集合中加入物面节点
		//不满足误差线及集合点数小于1000
		if (lim[0] > limx && n1 + n_node < 1001)
		{

			AllocateDim(v_node2, n1 + n_node);

			for (int i = 0; i<n1; i++)
			{
				v_node2[i] = v_node1[i];
			}

			v_node1.clear();
			v_wallDelta1.clear();
			AllocateDim(v_wallDelta1, n1 + n_node);

			for (int i = 0; i<n1; i++)
			{
				v_wallDelta1[i] = v_wallDelta2[i];
			}

			v_wallDelta2.clear();

			for (int i = 0; i<n_node; i++)

			{
				if (lim[i] > limx)
				{
					v_node2[n1 + i] = v_wallNode[nlim[i]];
					v_wallDelta1[n1 + i] = v_wallDelta[nlim[i]];
					n_weightID.push_back(nlim[i]);
				}
				else
				{
					n_node = i;
					break;
				}

			}

			n1 += n_node;

			AllocateDim(v_node1, n1);
			AllocateDim(v_wallDelta2, n1);

			for (int i = 0; i<n1; i++)
			{
				v_node1[i] = v_node2[i];
			}

			for (int i = 0; i<n1; i++)
			{
				v_wallDelta2[i] = v_wallDelta1[i];
			}

			v_wallDelta1.clear();
			AllocateDim(v_wallDelta1, n1);

			for (int i = 0; i<n1; i++)
			{
				v_wallDelta1[i] = v_wallDelta2[i];
			}

			v_node2.clear();
			fai.clear();
			v_weightG.clear();

		}
		//满足误差线及集合点数小于1000
		else if (lim[0] < limx && n1 + n_node < 1001)
		{
			break;
		}
		//不满足误差线及集合点数大于1000
		else if (lim[0] > limx && n1 + n_node >1001)
		{
			break;
		}
		//满足误差线及集合点数大于1000
		else if (lim[0] < limx && n1 + n_node >1001)
		{
			break;
		}

	}

	for (int i = 0; i < n; i++)
	{
		v_weightM[i] = Vector0;
	}

	for (int i = 0; i < n_weightID.size(); i++)
	{
		v_weightM[n_weightID[i]] = v_weightG[i];
	}

	v_weightG.clear();
	n_weightID.clear();

}

std::vector<Vector> RBFGreedyAlg::DirectRBFAlg()
{
	std::vector<Vector> v_weight;

	v_weight = Gaussin_L(Matrix_all, v_wallDelta_all);

	return v_weight;
}

std::vector<Vector> RBFGreedyAlg::MemoryOptimizedRBFAlg(int skipInterval, int maxNodes, int parallelThreshold)
{
    int myrank = GetMPIRank();
	if(myrank == 0) Print("\t使用内存优化的RBF算法，采用分组节点选取降低矩阵规模...\n");
	if(myrank == 0) Print("\t原始节点数: " + std::to_string(n_wallNode) + ", 每组选取节点数: " + std::to_string(skipInterval));

	// 显示当前系统内存状态
	size_t availableMemory = GetAvailableMemoryMB();
	if(myrank == 0) Print("\t当前可用内存: " + std::to_string(availableMemory) + " MB");

	// 使用内存监控的节点选择
	int adjustedSkipInterval = skipInterval;
	int adjustedMaxNodes = maxNodes;
	int groupSize = 10;  // 默认组大小，可以从配置中获取
	std::vector<int> selectedIndices = SelectNodeSubsetWithMemoryControl(adjustedSkipInterval, adjustedMaxNodes, groupSize);
	int selectedSize = selectedIndices.size();

	if(myrank == 0) Print("\t最终选取的节点数: " + std::to_string(selectedSize));
	if (adjustedSkipInterval != skipInterval || adjustedMaxNodes != maxNodes) {
		if(myrank == 0) Print("\t参数已自动调整：每组选取 " + std::to_string(adjustedSkipInterval) + " 个，最大节点数 " + std::to_string(adjustedMaxNodes));
	}

	// 最终内存检查
	size_t finalMemoryNeeded = EstimateRBFMatrixMemoryMB(selectedSize);
	if(myrank == 0) Print("\t预计内存需求: " + std::to_string(finalMemoryNeeded) + " MB");

	// 使用ScaLAPACK求解RBF系统
	std::vector<Vector> v_weight = SolveRBFWithScaLAPACK(selectedIndices, parallelThreshold);

	if(myrank == 0) Print("\t内存优化RBF算法完成");
	return v_weight;
}

std::vector<int> RBFGreedyAlg::SelectNodeSubset(int skipInterval, int maxNodes, int groupSize)
{
	std::vector<int> selectedIndices;
    int myrank = GetMPIRank();
    if (myrank == 0) {
        Print("\t智能节点选取策略：");
        Print("\t  原始节点数: " + std::to_string(n_wallNode));
        Print("\t  每组大小: " + std::to_string(groupSize) + ", 每组选择: " + std::to_string(skipInterval) + " 个");
        Print("\t  目标最大节点数: " + std::to_string(maxNodes));
    }

	// 策略1：简单检查maxNodes是否充足
	int totalGroups = (n_wallNode + groupSize - 1) / groupSize;  // 向上取整
	int expectedNodes = totalGroups * std::min(skipInterval, groupSize);

	if (maxNodes >= expectedNodes) {
		// maxNodes充足，使用标准分组策略
		if (myrank == 0) Print("\t  maxNodes充足，使用标准分组策略");
		int selectPerGroup = std::min(skipInterval, groupSize);

		for (int groupStart = 0; groupStart < n_wallNode; groupStart += groupSize) {
			int groupEnd = std::min(groupStart + groupSize, n_wallNode);
			int actualGroupSize = groupEnd - groupStart;
			int actualSelectPerGroup = std::min(selectPerGroup, actualGroupSize);

			// 在该组内均匀选择节点
			for (int j = 0; j < actualSelectPerGroup; j++) {
				int nodeIndex = groupStart + (j * actualGroupSize) / actualSelectPerGroup;
				selectedIndices.push_back(nodeIndex);
			}
		}
	} else {
		// maxNodes不足，使用均匀分布策略
		if (myrank == 0) Print("\t  maxNodes不足，使用均匀分布策略");

		// 方法1：调整每组选择数保持分组结构
		int totalGroups = (n_wallNode + groupSize - 1) / groupSize;
		if (totalGroups > 0) {
			int adjustedSkipInterval = maxNodes / totalGroups;
			if (adjustedSkipInterval >= 1) {
				if (myrank == 0) Print("\t  调整每组选择数至: " + std::to_string(adjustedSkipInterval));

				bool reachedMaxNodes = false;
				for (int groupStart = 0; groupStart < n_wallNode && !reachedMaxNodes; groupStart += groupSize) {
					int groupEnd = std::min(groupStart + groupSize, n_wallNode);
					int actualGroupSize = groupEnd - groupStart;
					int selectInThisGroup = std::min(adjustedSkipInterval, actualGroupSize);

					// 在该组内均匀选择
					for (int j = 0; j < selectInThisGroup && !reachedMaxNodes; j++) {
						int nodeIndex = groupStart + (j * actualGroupSize) / selectInThisGroup;
						selectedIndices.push_back(nodeIndex);
						if (selectedIndices.size() >= maxNodes) {
							reachedMaxNodes = true;
						}
					}
				}
			}
		}

		// 方法2：如果调整分组策略还不够，使用全局均匀采样
		if (selectedIndices.size() < maxNodes * 0.8) {
			selectedIndices.clear();
			if (myrank == 0) Print("\t  使用全局均匀采样策略");

			// 计算均匀间隔
			double step = static_cast<double>(n_wallNode) / maxNodes;

			for (int i = 0; i < maxNodes && i < n_wallNode; i++) {
				int nodeIndex = static_cast<int>(i * step);
				if (nodeIndex < n_wallNode) {
					selectedIndices.push_back(nodeIndex);
				}
			}
		}
	}

	if (myrank == 0) Print("\t  最终选择节点数: " + std::to_string(selectedIndices.size()));
	if (!selectedIndices.empty()) {
		if (myrank == 0) Print("\t  覆盖范围: [" + std::to_string(selectedIndices.front()) + ", " + std::to_string(selectedIndices.back()) + "]");
	}

	return selectedIndices;
}


// 获取当前系统可用内存（MB）
size_t RBFGreedyAlg::GetAvailableMemoryMB()
{
#if defined(_BasePlatformWinddows_)
	MEMORYSTATUSEX memInfo;
	memInfo.dwLength = sizeof(MEMORYSTATUSEX);
	GlobalMemoryStatusEx(&memInfo);
	return static_cast<size_t>(memInfo.ullAvailPhys / (1024 * 1024));
#else
	// Linux系统
	std::ifstream meminfo("/proc/meminfo");
	std::string line;
	size_t memAvailable = 0;

	while (std::getline(meminfo, line)) {
		if (line.find("MemAvailable:") == 0) {
			std::istringstream iss(line);
			std::string label;
			size_t value;
			std::string unit;
			iss >> label >> value >> unit;
			memAvailable = value / 1024; // 转换为MB
			break;
		}
	}

	// 如果没有MemAvailable，使用MemFree + Buffers + Cached的近似值
	if (memAvailable == 0) {
		meminfo.clear();
		meminfo.seekg(0, std::ios::beg);
		size_t memFree = 0, buffers = 0, cached = 0;

		while (std::getline(meminfo, line)) {
			if (line.find("MemFree:") == 0) {
				std::istringstream iss(line);
				std::string label;
				size_t value;
				iss >> label >> value;
				memFree = value / 1024;
			} else if (line.find("Buffers:") == 0) {
				std::istringstream iss(line);
				std::string label;
				size_t value;
				iss >> label >> value;
				buffers = value / 1024;
			} else if (line.find("Cached:") == 0) {
				std::istringstream iss(line);
				std::string label;
				size_t value;
				iss >> label >> value;
				cached = value / 1024;
			}
		}
		memAvailable = memFree + buffers + cached;
	}

	return memAvailable;
#endif
}

// 估算RBF矩阵所需内存（MB）
size_t RBFGreedyAlg::EstimateRBFMatrixMemoryMB(int nodeCount)
{
	// RBF矩阵：nodeCount × nodeCount × sizeof(Scalar)
	size_t matrixMemory = static_cast<size_t>(nodeCount) * nodeCount * sizeof(Scalar);

	// 右端项向量：nodeCount × 3 × sizeof(Vector)
	size_t rhsMemory = static_cast<size_t>(nodeCount) * 3 * sizeof(Vector);

	// 解向量：nodeCount × sizeof(Vector)
	size_t solutionMemory = static_cast<size_t>(nodeCount) * sizeof(Vector);

	// MKL/LAPACK工作空间（估算为矩阵大小的2倍）
	size_t workspaceMemory = matrixMemory * 2;

	// 总内存需求（转换为MB）
	size_t totalMemory = (matrixMemory + rhsMemory + solutionMemory + workspaceMemory) / (1024 * 1024);

	return totalMemory;
}

// 检查内存是否足够
bool RBFGreedyAlg::CheckMemoryAvailability(int nodeCount, double safetyFactor)
{
    int myrank = GetMPIRank();
	size_t availableMemory = GetAvailableMemoryMB();
	size_t requiredMemory = EstimateRBFMatrixMemoryMB(nodeCount);

	if (myrank == 0) Print("\t内存检查：可用内存 " + std::to_string(availableMemory) + " MB，需要内存 " + std::to_string(requiredMemory) + " MB");

	return (requiredMemory <= static_cast<size_t>(availableMemory * safetyFactor));
}



// 内存监控和自适应节点选择
std::vector<int> RBFGreedyAlg::SelectNodeSubsetWithMemoryControl(int& skipInterval, int& maxNodes, int groupSize)
{
    int myrank = GetMPIRank();
	if (myrank == 0) Print("\t开始内存监控的自适应节点选择...");

	// 首先检查原始参数是否可行（使用智能选点策略）
	std::vector<int> selectedIndices = SelectNodeSubset(skipInterval, maxNodes, groupSize);
	int selectedSize = selectedIndices.size();

	// 检查内存是否足够
	if (CheckMemoryAvailability(selectedSize, 0.8)) {
		if (myrank == 0) Print("\t内存充足，使用原始节点选择策略");
		return selectedIndices;
	}

	if (myrank == 0) Print("\t内存不足，开始自适应调整节点选择参数...");

	// 内存不足时的自适应策略
	int originalSkipInterval = skipInterval;
	int originalMaxNodes = maxNodes;
	int attempts = 0;
	const int maxAttempts = 10;

	while (attempts < maxAttempts) {
		attempts++;

		// 策略1：减少每组选择的节点数
		if (skipInterval > 1) {
			skipInterval = std::max(1, skipInterval - 1);
			if (myrank == 0) Print("\t尝试 " + std::to_string(attempts) + "：减少每组选择节点数至 " + std::to_string(skipInterval));
		}
		// 策略2：减少最大节点数
		else if (maxNodes > 500) {
			maxNodes = static_cast<int>(maxNodes * 0.8);
			if (myrank == 0) Print("\t尝试 " + std::to_string(attempts) + "：减少最大节点数至 " + std::to_string(maxNodes));
		}
		// 策略3：进一步大幅减少最大节点数
		else {
			maxNodes = static_cast<int>(maxNodes * 0.5);
			if (maxNodes < 100) {
				maxNodes = 100; // 最小保证100个节点
				if (myrank == 0) Print("\t警告：节点数已减少到最小值 " + std::to_string(maxNodes));
				break;
			}
			if (myrank == 0) Print("\t尝试 " + std::to_string(attempts) + "：大幅减少最大节点数至 " + std::to_string(maxNodes));
		}

		// 重新选择节点（使用智能选点策略）
		selectedIndices = SelectNodeSubset(skipInterval, maxNodes, groupSize);
		selectedSize = selectedIndices.size();

		// 检查新的内存需求
		if (CheckMemoryAvailability(selectedSize, 0.8)) {
			if (myrank == 0) Print("\t内存调整成功！最终选择节点数：" + std::to_string(selectedSize));
			if (myrank == 0) Print("\t调整后参数：每组选择 " + std::to_string(skipInterval) + " 个，最大节点数 " + std::to_string(maxNodes));
			return selectedIndices;
		}
	}

	// 如果所有尝试都失败，使用最保守的策略
	if (myrank == 0) Print("\t警告：内存严重不足，使用最保守的节点选择策略");
	skipInterval = 1;
	maxNodes = 100;
	selectedIndices = SelectNodeSubset(skipInterval, maxNodes, groupSize);

	if (myrank == 0) Print("\t最终选择节点数：" + std::to_string(selectedIndices.size()));
	return selectedIndices;
}

std::vector<Vector> RBFGreedyAlg::SolveRBFWithScaLAPACK(const std::vector<int>& selectedIndices, int parallelThreshold)
{
	int selectedSize = selectedIndices.size();
	std::vector<Vector> v_weight_selected;

#if defined(_EnableMKL_) && defined(_BaseParallelMPI_)
	// 使用分布式ScaLAPACK求解RBF系统（完全避免全局矩阵构建）
	if(GetMPIRank() == 0) Print("\t使用分布式Intel MKL ScaLAPACK求解RBF系统（内存优化）");
	SolveRBFWithDistributedScaLAPACK(selectedIndices, v_weight_selected);

#else
	// 串行LAPACK求解：构建全局矩阵进行求解
	if(GetMPIRank() == 0) Print("\t使用串行LAPACK求解RBF系统");

	// 构建选取节点的RBF矩阵（串行模式）
	std::vector<std::vector<Scalar>> Matrix_selected;
	std::vector<Vector> v_wallDelta_selected;
	AllocateDim(Matrix_selected, selectedSize, selectedSize);
	AllocateDim(v_wallDelta_selected, selectedSize);

	// 填充选取的矩阵和右端项
	for(int i = 0; i < selectedSize; i++) {
		int idx_i = selectedIndices[i];
		v_wallDelta_selected[i].SetX(v_wallNode_deform[idx_i].X() - v_wallNode[idx_i].X());
		v_wallDelta_selected[i].SetY(v_wallNode_deform[idx_i].Y() - v_wallNode[idx_i].Y());
		v_wallDelta_selected[i].SetZ(v_wallNode_deform[idx_i].Z() - v_wallNode[idx_i].Z());

		for(int j = 0; j < selectedSize; j++) {
			int idx_j = selectedIndices[j];
			Scalar yita = sqrt(pow((v_wallNode[idx_j].X() - v_wallNode[idx_i].X()), 2)
			                 + pow((v_wallNode[idx_j].Y() - v_wallNode[idx_i].Y()), 2)
			                 + pow((v_wallNode[idx_j].Z() - v_wallNode[idx_i].Z()), 2)) / R;

			if (yita > 1.0) {
				Matrix_selected[i][j] = 0.0;
			} else {
				Matrix_selected[i][j] = pow((1 - yita), 4) * (4 * yita + 1.0);
			}
		}
	}

	AllocateDim(v_weight_selected, selectedSize);
	SolveRBFWithSerialLAPACK(Matrix_selected, v_wallDelta_selected, v_weight_selected);
#endif

	// 将选取节点的权重系数扩展到全部节点
	std::vector<Vector> v_weight_full;
	AllocateDim(v_weight_full, n_wallNode);

	for(int i = 0; i < n_wallNode; i++) {
		v_weight_full[i] = Vector0;
	}

	for(int i = 0; i < selectedSize; i++) {
		v_weight_full[selectedIndices[i]] = v_weight_selected[i];
	}

	return v_weight_full;
}

// 串行LAPACK求解RBF系统（使用标准LAPACK，不依赖MKL）
void RBFGreedyAlg::SolveRBFWithSerialLAPACK(const std::vector<std::vector<Scalar>>& matrix,
                                            const std::vector<Vector>& rhs,
                                            std::vector<Vector>& solution)
{
#if defined(_EnableMKL_)
	int n = matrix.size();
	int nrhs = 3;
	int myrank = GetMPIRank();

	if(myrank == 0) Print("\t使用标准LAPACK求解 " + std::to_string(n) + "×" + std::to_string(n) + " RBF系统");

	// 准备数据（双精度）
	std::vector<double> B(n * nrhs);
    double *A = new double[n * n];
    double *A_inv = new double[n * n];
    lapack_int *ipiv = new lapack_int[n];

	// 填充矩阵A (列主序)
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < n; j++)
            A[i * n + j] = static_cast<double>(matrix[i][j]);
    }

    for (int i = 0; i < n; ++i)
    {
        for (int j = 0; j < n; ++j)
        {
            A_inv[i * n + j] = (i == j) ? 1.0 : 0.0;
        }
    }

	// 使用标准LAPACK求解 (dgesv_)
	int info;
    info = LAPACKE_dgesv(LAPACK_ROW_MAJOR, n, n, A, n, ipiv, A_inv, n);

	if (info != 0) {
		if(myrank == 0) {
			Print("\t警告：LAPACK求解失败，错误代码: " + std::to_string(info));
		}

		// 简化的错误处理：返回零解
		solution.resize(n);
		for(int i = 0; i < n; i++) {
			solution[i] = Vector0;
		}
		if(myrank == 0) Print("\t使用零权重作为备用方案");
	} else {
		if (myrank == 0) Print("\tLAPACK求解成功");
		// 提取解
		solution.resize(n);
		for(int i = 0; i < n; i++) {
            for(int j = 0; j < n; j++) {
                solution[i].SetX(solution[i].X()+A_inv[i * n + j]*rhs[i].X());
                solution[i].SetY(solution[i].Y()+A_inv[i * n + j]*rhs[i].Y());
                solution[i].SetZ(solution[i].Z()+A_inv[i * n + j]*rhs[i].Z());
            }
		}
	}
#endif
}

// 分布式RBF矩阵构建和ScaLAPACK求解的实现在文件末尾


// 智能求解器选择：根据配置自动选择最优求解策略
std::vector<Vector> RBFGreedyAlg::SolveWithOptimalStrategy(const RBFSolverConfig& config)
{
	int nodeCount = v_wallNode.size();
	int nPart = GetMPISize();
    int processorID = GetMPIRank();

	if(processorID == 0) Print("\n节点数: " + std::to_string(nodeCount) + ", 进程数: " + std::to_string(nPart));

	// 根据配置和节点数自动选择策略
	RBFSolverType selectedSolver = config.solverType;

	// 简化的自动选择策略：基于节点数和内存状况
	if (selectedSolver == RBFSolverType::TRADITIONAL_GAUSS) {
		if (processorID == 0) Print("\t开始简化的智能求解策略选择...");

		// 节点数小于5000：直接使用无缩减串行RBF求解
		if (nodeCount < config.serialThreshold) {
			if (config.enableMKL) {
				selectedSolver = RBFSolverType::MKL_SERIAL;
				if (processorID == 0) Print("\t节点数 " + std::to_string(nodeCount) + " < " + std::to_string(config.serialThreshold) + "，使用无缩减串行RBF求解");
			} else {
				selectedSolver = RBFSolverType::TRADITIONAL_GAUSS;
				if (processorID == 0) Print("\t节点数 " + std::to_string(nodeCount) + " < " + std::to_string(config.serialThreshold) + "，使用传统高斯消元");
			}
		}
		// 节点数大于等于5000：检查内存，决定是否缩减点，然后使用分布式求解
		else {
			bool memoryConstrained = false;
			if (config.enableMemoryControl) {
				size_t availableMemory = GetAvailableMemoryMB();
				size_t requiredMemory = EstimateRBFMatrixMemoryMB(nodeCount);
				if (processorID == 0) Print("\t内存检查：可用 " + std::to_string(availableMemory) + " MB，全矩阵需要 " + std::to_string(requiredMemory) + " MB");

				if (requiredMemory > static_cast<size_t>(availableMemory * config.memorySafetyFactor)) {
					memoryConstrained = true;
					if (processorID == 0) Print("\t内存不足，将使用缩减点分布式求解");
				} else {
					if (processorID == 0) Print("\t内存充足，将使用不缩减点分布式求解");
				}
			}

			if (config.enableParallel && nPart > 1) {
				if (memoryConstrained) {
					selectedSolver = RBFSolverType::MEMORY_OPTIMIZED;
					if (processorID == 0) Print("\t选择：内存不足缩减点分布式求解");
				} else {
					selectedSolver = RBFSolverType::MKL_PARALLEL;
					if (processorID == 0) Print("\t选择：内存充足不缩减点分布式求解");
				}
			} else {
				// 单进程环境下的大规模问题处理
				selectedSolver = RBFSolverType::MEMORY_OPTIMIZED;
				if (processorID == 0) Print("\t单进程环境，节点数 >= " + std::to_string(config.serialThreshold) + "，使用内存优化算法");
			}
		}
	}

	// 执行选定的求解策略
	std::vector<Vector> result;

	switch (selectedSolver) {
		case RBFSolverType::TRADITIONAL_GAUSS:
			if(processorID == 0) Print("\t执行传统高斯消元求解...");
			rbfGreedy();
			result = DirectRBFAlg();
			break;

		case RBFSolverType::GREEDY_ALGORITHM:
			if(processorID == 0) Print("\t执行贪心算法求解...");
			rbfGreedy();
			result = DirectGreedyAlg();
			break;

		case RBFSolverType::GREEDY_IMPROVED:
			if(processorID == 0) Print("\t执行改进贪心算法求解...");
			rbfGreedy();
			result = GreedyAlgImp();
			break;

		case RBFSolverType::MKL_SERIAL:
			if(processorID == 0) Print("\t执行串行LAPACK求解...");
			rbfGreedy();
			AllocateDim(result, nodeCount);
			SolveRBFWithSerialLAPACK(Matrix_all, v_wallDelta_all, result);
			break;

		case RBFSolverType::MKL_PARALLEL:
#if defined(_EnableMKL_) && defined(_BaseParallelMPI_)
			if(processorID == 0) Print("\t执行MKL分布式并行求解（不缩减点）...");
			// 直接使用分布式求解，不进行节点缩减
			{
				std::vector<int> allIndices(nodeCount);
				std::iota(allIndices.begin(), allIndices.end(), 0);  // 使用所有节点
				result = SolveRBFWithScaLAPACK(allIndices, config.serialThreshold);
			}
#else
			if(processorID == 0) Print("\t警告：MKL并行未启用，回退到改进贪心算法");
			rbfGreedy();
			result = GreedyAlgImp();
#endif
			break;

		case RBFSolverType::MEMORY_OPTIMIZED:
			if(processorID == 0) Print("\t执行内存优化算法求解（缩减点）...");
			result = MemoryOptimizedRBFAlg(config.skipInterval, config.maxNodes, config.serialThreshold);
			break;

		default:
			if(processorID == 0) Print("\t未知求解器类型，使用传统高斯消元");
			rbfGreedy();
			result = DirectRBFAlg();
			break;
	}

	if(processorID == 0) Print("\t求解完成，权重系数计算成功");
	return result;
}

#if defined(_EnableMKL_) && defined(_BaseParallelMPI_)
// 分布式RBF矩阵构建和ScaLAPACK求解
void RBFGreedyAlg::SolveRBFWithDistributedScaLAPACK(const std::vector<int>& selectedIndices,
                                                     std::vector<Vector>& solution)
{
	lapack_int n = selectedIndices.size();
	lapack_int nrhs = 3;
	lapack_int info;

	// MPI参数
	int nprocs = GetMPISize();
	int myrank = GetMPIRank();

	if(myrank == 0) {
	    Print("\t使用分布式ScaLAPACK求解 " + std::to_string(n) + "×" + std::to_string(n) + " RBF系统");
	    Print("\t进程数: " + std::to_string(nprocs) + "，避免全局矩阵构建");
        size_t availableMemory = GetAvailableMemoryMB();
        Print("\t剩余内存 " + std::to_string(availableMemory));
    }

	// BLACS网格设置
	lapack_int ictxt, nprow, npcol, myrow, mycol;
	lapack_int zero = 0, one = 1;

	// 计算进程网格
	nprow = static_cast<lapack_int>(sqrt(static_cast<double>(nprocs)));
	npcol = nprocs / nprow;
	while (nprow * npcol != nprocs) {
		nprow--;
		npcol = nprocs / nprow;
	}

	blacs_get_(&zero, &zero, &ictxt);
	blacs_gridinit_(&ictxt, "R", &nprow, &npcol);
	blacs_gridinfo_(&ictxt, &nprow, &npcol, &myrow, &mycol);

	// 块大小设置
	lapack_int nb = std::min(static_cast<lapack_int>(64), n / nprow);
	nb = std::max(static_cast<lapack_int>(1), nb);

	// 计算本地矩阵大小
	lapack_int mp = numroc_(&n, &nb, &myrow, &zero, &nprow);
	lapack_int nq = numroc_(&n, &nb, &mycol, &zero, &npcol);
	lapack_int nrhsq = numroc_(&nrhs, &nb, &mycol, &zero, &npcol);

	// 描述符
	lapack_int desca[9], descb[9];
	lapack_int lda = std::max(static_cast<lapack_int>(1), mp);
	lapack_int ldb = std::max(static_cast<lapack_int>(1), mp);

	descinit_(desca, &n, &n, &nb, &nb, &zero, &zero, &ictxt, &lda, &info);
	descinit_(descb, &n, &nrhs, &nb, &nb, &zero, &zero, &ictxt, &ldb, &info);

	// 分配本地矩阵内存
	std::vector<double> A_local(lda * nq);
	std::vector<double> B_local(ldb * nrhsq);
	std::vector<lapack_int> ipiv(mp + nb);

	if(myrank == 0) {
        Print("\t开始分布式RBF矩阵构建");
        size_t availableMemory = GetAvailableMemoryMB();
        Print("\t剩余内存 " + std::to_string(availableMemory));
    }

	// 分布式构建RBF矩阵：每个进程只计算自己负责的矩阵块
	for (int j = 0; j < nq; j++) {
		int global_j = (j / nb) * npcol * nb + mycol * nb + (j % nb);
		if (global_j < n) {
			int idx_j = selectedIndices[global_j];
			for (int i = 0; i < mp; i++) {
				int global_i = (i / nb) * nprow * nb + myrow * nb + (i % nb);
				if (global_i < n) {
					int idx_i = selectedIndices[global_i];

					// 直接计算RBF矩阵元素
					Scalar yita = sqrt(pow((v_wallNode[idx_j].X() - v_wallNode[idx_i].X()), 2)
					                 + pow((v_wallNode[idx_j].Y() - v_wallNode[idx_i].Y()), 2)
					                 + pow((v_wallNode[idx_j].Z() - v_wallNode[idx_i].Z()), 2)) / R;

					if (yita > 1.0) {
						A_local[j * lda + i] = 0.0;
					} else {
						A_local[j * lda + i] = static_cast<double>(pow((1 - yita), 4) * (4 * yita + 1.0));
					}
				}
			}
		}
	}

	// 分布式构建右端项
	for (int j = 0; j < nrhsq; j++) {
		int global_j = (j / nb) * npcol * nb + mycol * nb + (j % nb);
		if (global_j < nrhs) {
			for (int i = 0; i < mp; i++) {
				int global_i = (i / nb) * nprow * nb + myrow * nb + (i % nb);
				if (global_i < n) {
					int idx_i = selectedIndices[global_i];
					if (global_j == 0) {
						B_local[j * ldb + i] = static_cast<double>(v_wallNode_deform[idx_i].X() - v_wallNode[idx_i].X());
					} else if (global_j == 1) {
						B_local[j * ldb + i] = static_cast<double>(v_wallNode_deform[idx_i].Y() - v_wallNode[idx_i].Y());
					} else if (global_j == 2) {
						B_local[j * ldb + i] = static_cast<double>(v_wallNode_deform[idx_i].Z() - v_wallNode[idx_i].Z());
					}
				}
			}
		}
	}

	if(myrank == 0) {
    	Print("\t分布式矩阵构建完成，开始ScaLAPACK求解");
        size_t availableMemory = GetAvailableMemoryMB();
        Print("\t剩余内存 " + std::to_string(availableMemory));
    }

	// 使用ScaLAPACK求解
	pdgesv_(&n, &nrhs, A_local.data(), &one, &one, desca, ipiv.data(),
	        B_local.data(), &one, &one, descb, &info);

	if (info != 0) {
		if (myrank == 0) {
			Print("\t警告：ScaLAPACK求解失败，错误代码: " + std::to_string(info));
		}
	} else {
		if (myrank == 0) {
			Print("\tScaLAPACK求解成功");
		}
	}

	// 收集解向量
	std::vector<double> B_global_result(n * nrhs, 0.0);

	// 从本地矩阵收集到全局矩阵
	for (int j = 0; j < nrhsq; j++) {
		int global_j = (j / nb) * npcol * nb + mycol * nb + (j % nb);
		if (global_j < nrhs) {
			for (int i = 0; i < mp; i++) {
				int global_i = (i / nb) * nprow * nb + myrow * nb + (i % nb);
				if (global_i < n) {
					B_global_result[global_j * n + global_i] = B_local[j * ldb + i];
				}
			}
		}
	}

	// 使用boost::mpi::all_reduce收集所有进程的结果
	std::vector<double> B_final(n * nrhs);
	boost::mpi::all_reduce(MPI::mpiWorld, B_global_result.data(), n * nrhs, B_final.data(), std::plus<double>());

	// 转换回solution格式
	solution.resize(n);
	for (int i = 0; i < n; i++) {
		solution[i].SetX(static_cast<Scalar>(B_final[0 * n + i]));
		solution[i].SetY(static_cast<Scalar>(B_final[1 * n + i]));
		solution[i].SetZ(static_cast<Scalar>(B_final[2 * n + i]));
	}

	// 清理BLACS网格
	blacs_gridexit_(&ictxt);

	if(myrank == 0) Print("\t分布式ScaLAPACK求解完成，内存使用已优化");
}
#endif