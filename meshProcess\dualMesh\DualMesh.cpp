﻿#include "meshProcess/dualMesh/DualMesh.h"

DualMesh::DualMesh(Mesh *oldMesh_)
    : oldMesh(oldMesh_)
{
    dualMesh = new Mesh;
    dim2 = false;
    if(oldMesh->md_meshDim == Mesh::MeshDim::md2D) dim2 = true;
};

DualMesh::~DualMesh()
{
    faceEdgeIDList.clear();
    edgeNodes.clear();
    nodeEdgesList.clear();
    boundaryNodesList.clear();

    dualMesh->ClearMesh();
    if (dualMesh != nullptr) { delete dualMesh; dualMesh = nullptr; }
}

//将原始网格转化为对偶网格的主函数
void DualMesh::PrimaryToDual()
{
	Print("\t开始对偶网格转化...");
	std::ostringstream stringStream;

	Print("\t创建边相关信息...");
	this->CreateEdgeData();

	Print("\t建立对偶网格拓扑关系...");
	this->BuildTopology();

	Print("\t计算对偶网格体积和面积...");
	this->CalculateVolumeAndArea();
}

void DualMesh::CreateEdgeData()
{
	std::vector<int> count(oldMesh->n_nodeNum, 0);	
	for (auto faceID = 0; faceID < oldMesh->n_faceNum; ++faceID)
	{
		for (int index = 0; index < oldMesh->v_face[faceID].v_nodeID.size(); ++index)
			count[oldMesh->v_face[faceID].v_nodeID[index]]++;
	}

	std::vector<std::vector<int>> nodeFaceIDList(oldMesh->n_nodeNum);
	for (auto i = 0; i < count.size(); ++i) nodeFaceIDList[i].reserve(count[i]);

	for (auto faceID = 0; faceID < oldMesh->n_faceNum; ++faceID)
	{
		for (int index = 0; index < oldMesh->v_face[faceID].v_nodeID.size(); ++index)
		{
			nodeFaceIDList[oldMesh->v_face[faceID].v_nodeID[index]].push_back(faceID);
		}
	}
	
	int index0, index1;
	std::vector<std::vector<int>> newEdgeFlag(oldMesh->n_nodeNum);
	ARI_OMP(parallel for schedule(static) private(index0, index1))
	for (int nodeID = 0; nodeID < oldMesh->n_nodeNum; ++nodeID)
	{
		for (index0 = 0; index0 < nodeFaceIDList[nodeID].size(); ++index0)
		{
			const int &faceID = nodeFaceIDList[nodeID][index0];
			const int nodeIDSize = oldMesh->v_face[faceID].v_nodeID.size();
            for (index1 = 0; index1 < nodeIDSize; ++index1)
			{
				if (nodeID == oldMesh->v_face[faceID].v_nodeID[index1])
				{
					const int &nodeIDP1 = oldMesh->v_face[faceID].v_nodeID[(index1 + 1) % nodeIDSize];
					if (nodeIDP1 > nodeID) InsertWithNorepeat(newEdgeFlag[nodeID], nodeIDP1);

					if (!dim2)
					{
						const int &nodeIDM1 = oldMesh->v_face[faceID].v_nodeID[(index1 - 1 + nodeIDSize) % nodeIDSize];
						if (nodeIDM1 > nodeID) InsertWithNorepeat(newEdgeFlag[nodeID], nodeIDM1);
					}

					break;
				}
			}
		}
	}
	std::vector<std::vector<int>>().swap(nodeFaceIDList);
	
	int edgeSize = 0;
	std::vector<int> edgeID0(oldMesh->n_nodeNum);
	for (int nodeID = 0; nodeID < oldMesh->n_nodeNum; ++nodeID)
	{
		edgeID0[nodeID] = edgeSize;
		edgeSize += newEdgeFlag[nodeID].size();
	}

	this->edgeNodes.resize(edgeSize);
	this->nodeEdgesList.resize(oldMesh->n_nodeNum);
	ARI_OMP(parallel for schedule(static) private(index0))
	for (int nodeID = 0; nodeID < newEdgeFlag.size(); ++nodeID)
	{
		nodeEdgesList[nodeID].resize(newEdgeFlag[nodeID].size());
		for (index0 = 0; index0 < newEdgeFlag[nodeID].size(); ++index0)
		{
			const int edgeID = edgeID0[nodeID] + index0;
			edgeNodes[edgeID].first = nodeID;
			edgeNodes[edgeID].second = newEdgeFlag[nodeID][index0];
			nodeEdgesList[nodeID][index0] = edgeID;
		}
	}
	std::vector<std::vector<int>>().swap(newEdgeFlag);

	// 面的边构成
	faceEdgeIDList.resize(oldMesh->n_faceNum);

	int nodeID1, nodeID2;
	ARI_OMP(parallel for schedule(static) private(index0, index1, nodeID1, nodeID2))
	for (int faceID = 0; faceID < oldMesh->n_faceNum; ++faceID)
	{
		const std::vector<int> &nodeIDList = oldMesh->v_face[faceID].v_nodeID;
		const int nodeIDSize = nodeIDList.size();
		const int nEdge = dim2 ? 1 : nodeIDSize;
		faceEdgeIDList[faceID].reserve(nEdge);
		for (index0 = 0; index0 < nEdge; ++index0)
		{
			// 边的点构成要求编号小的节点在前
			nodeID1 = oldMesh->v_face[faceID].v_nodeID[index0];
			nodeID2 = oldMesh->v_face[faceID].v_nodeID[(index0 + 1) % nodeIDSize];
			if (nodeID1 > nodeID2)
			{
				nodeID1 = nodeID2;
				nodeID2 = oldMesh->v_face[faceID].v_nodeID[index0];
			}
			for (index1 = 0; index1 < nodeEdgesList[nodeID1].size(); ++index1)
			{
				const int &edgeID = nodeEdgesList[nodeID1][index1];
				if (nodeID2 == edgeNodes[edgeID].second)
				{
					faceEdgeIDList[faceID].push_back(edgeID);
					break;
				}
			}
		}
	}
}

int DualMesh::BuildTopology()
{
    // 对偶网格单元数等于原始网格节点数
	dualMesh->n_elemNum = oldMesh->n_nodeNum;
	dualMesh->v_elem.resize(dualMesh->n_elemNum);
    
    // 对偶网格面分为内部面和边界面，其中内部面数等于原始网格边数
    dualMesh->n_faceNum = edgeNodes.size();

	// 对偶网格边界面数等于原始网格边界节点数量
    boundaryNodesList.resize(oldMesh->GetBoundarySize());
	for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); ++patchID)
    {
		// 原始网格边界面节点容器创建
		std::vector<bool> nodeFlag(oldMesh->n_nodeNum, false);
		for (int index = 0; index < oldMesh->GetBoundaryFaceSize(patchID); ++index)
		{
			const int &faceID = oldMesh->GetBoundaryFaceID(patchID, index);
			for (int i = 0; i < oldMesh->v_face[faceID].GetNodeSize(); ++i)
			{
				const int &nodeID = oldMesh->v_face[faceID].v_nodeID[i];

				if (!nodeFlag[nodeID])
				{
					boundaryNodesList[patchID].push_back(nodeID);
					nodeFlag[nodeID] = true;
				}
			}
		}
		dualMesh->n_faceNum += boundaryNodesList[patchID].size();
    }
    dualMesh->v_face.reserve(dualMesh->n_faceNum);
    
    // 边界面信息更新
	dualMesh->v_boundaryName.resize(oldMesh->GetBoundarySize());
	dualMesh->vv_boundaryFaceID.resize(oldMesh->GetBoundarySize());
	for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); ++patchID)
    {
        // 边界名称
		dualMesh->v_boundaryName[patchID] = oldMesh->v_boundaryName[patchID];

        // 边界面生成，并修改其相邻单元编号
        std::vector<int> &nodes = boundaryNodesList[patchID];
		dualMesh->vv_boundaryFaceID[patchID].reserve(nodes.size());
		for (int index = 0; index<nodes.size(); ++index)
        {
            dualMesh->v_face.push_back(Face());
            const int dualFaceID = dualMesh->v_face.size()-1;
            dualMesh->v_face[dualFaceID].n_owner = nodes[index];
            dualMesh->v_face[dualFaceID].n_neighbor = -1;
			dualMesh->vv_boundaryFaceID[patchID].push_back(dualFaceID);
        }
    }
    
    // 遍历原始网格边，生成对偶网格内部面，其相邻单元编号为原始网格边的开始和结束节点编号
    v_interiorFaceID.reserve(this->edgeNodes.size());
	for (int edgeID = 0; edgeID<this->edgeNodes.size(); ++edgeID)
    {
        dualMesh->v_face.push_back(Face());
        const int dualFaceID = dualMesh->v_face.size()-1;
        Face &dualFace = dualMesh->v_face[dualFaceID];
        dualFace.n_owner = this->edgeNodes[edgeID].first;
        dualFace.n_neighbor = this->edgeNodes[edgeID].second;
        v_interiorFaceID.push_back(dualFaceID);
    }
    
    // 根据对偶网格面左右单元编号，填充单元面构成列表
	for (int faceID = 0; faceID < dualMesh->n_faceNum; ++faceID)
    {
        const int &ownerID = dualMesh->v_face[faceID].n_owner;
        const int &neighID = dualMesh->v_face[faceID].n_neighbor;
        dualMesh->v_elem[ownerID].v_faceID.push_back(faceID);
        if(neighID != -1) dualMesh->v_elem[neighID].v_faceID.push_back(faceID);
    }

    const int minFaceSize = dim2 ? 3 : 4;
    for (int elementID = 0; elementID < dualMesh->n_elemNum; ++elementID)
    {
        if (dualMesh->v_elem[elementID].v_faceID.size() < minFaceSize)
        {
            FatalError("DualMesh::BuildTopology: element face size is wrong!");
        }
    }
	
    return 0;
}

void DualMesh::CalculateVolumeAndArea()
{
	// 常数
	const Scalar half = 1.0 / 2.0;
	const Scalar oneThird = 1.0 / 3.0;

	// 对偶网格每个边所对应的面积
	std::vector<Scalar> areaX(this->edgeNodes.size(), Scalar0);
	std::vector<Scalar> areaY(this->edgeNodes.size(), Scalar0);
	std::vector<Scalar> areaZ(this->edgeNodes.size(), Scalar0);

    // 计算每个单元内每个边所对应的面积
	std::map<int, std::vector<int>> edgeFaceIDMap;
	std::map<int, std::vector<int>>::iterator iter;
	Vector area;
    int index0, index1;
	ARI_OMP(parallel for schedule(static) private(index0, index1, area, edgeFaceIDMap, iter))
	for (int elementID = 0; elementID < oldMesh->n_elemNum; ++elementID)
	{
		// 原始网格每个单元的边的相邻面编号
		std::map<int, std::vector<int>>().swap(edgeFaceIDMap);
		for (index0 = 0; index0 < oldMesh->v_elem[elementID].v_faceID.size(); ++index0)
		{
			const int &faceID = oldMesh->v_elem[elementID].v_faceID[index0];
			for (index1 = 0; index1 < faceEdgeIDList[faceID].size(); ++index1)
			{
				const int &edgeID = faceEdgeIDList[faceID][index1];
				iter = edgeFaceIDMap.find(edgeID);
				if (iter == edgeFaceIDMap.end()) edgeFaceIDMap.insert(std::make_pair(edgeID, std::vector<int>{faceID}));
				else                             iter->second.push_back(faceID);
			}
		}

		// 遍历原始网格每个单元内的所有边，根据边与面的关系计算面积矢量
		for (iter = edgeFaceIDMap.begin(); iter != edgeFaceIDMap.end(); ++iter)
        {
			const int &edgeID = iter->first;
	        const int &nodeID1 = edgeNodes[edgeID].first;
	        const int &nodeID2 = edgeNodes[edgeID].second;
			const Node edgeCenter = half * (oldMesh->v_node[nodeID1] + oldMesh->v_node[nodeID2]);
			const Vector &elementCenter = oldMesh->v_elem[elementID].center;

			// 计算面积矢量
            if(dim2)
			{
				// 二维情况下，遍历单元elementID内所有面，寻找与nodeID1相邻且非nodeID2的节点
				for (index0 = 0; index0 < oldMesh->v_elem[elementID].v_faceID.size(); ++index0)
				{
					// 计算面积矢量
					const int &faceID1 = oldMesh->v_elem[elementID].v_faceID[index0];
					area = (elementCenter - edgeCenter) ^ Vector(0.0, 0.0, 1.0);

					// 寻找单元elementID内与nodeID1节点相邻且非nodeID2的节点，并根据几何关系调整面积矢量方向
					const int nodeIDTemp1 = FindAdjacentNodeID(oldMesh->v_face[faceID1].v_nodeID, nodeID1, nodeID2);
					if (nodeIDTemp1 != -1)
					{
						const Scalar value = ((oldMesh->v_node[nodeID2] - oldMesh->v_node[nodeID1])
							               ^ (oldMesh->v_node[nodeIDTemp1] - oldMesh->v_node[nodeID1])) & Vector(0.0, 0.0, 1.0);
						if (value < 0.0) area = -area;
						break;
					}
				}
            }
            else
			{
				// 三维情况下，过edgeID边的两个面
				const int &faceID1 = iter->second[0];
				const int &faceID2 = iter->second[1];
				
				// 计算面积矢量
				area = half * ((oldMesh->v_face[faceID1].center - oldMesh->v_face[faceID2].center) ^ (elementCenter - edgeCenter));

				// 同一个单元内，当所有面法向矢量都向外时，对于两个共边的面，该边两个节点在面的点构成列表中前后顺序相反
				// 将边节点顺序和面内节点顺序一致的面标记分别为顺序面，另一个面为逆序面
				// 面积矢量计算时，采用（逆序面面心 - 顺序面面心）^ （单元体心 - 边心）

				// 以下对两个面中的第一个面进行初步判断，顺序返回1，逆序返回-1
				// 先在v_nodeID中找到nodeID1，在判断前一个及后一个是否为nodeID2
				// 当后一个为nodeID2时为顺序，返回1；当前一个为nodeID2时为逆序，返回-1
				int flag = JudgeEdgeAndFace(nodeID1, nodeID2, oldMesh->v_face[faceID1].v_nodeID);

				// 判断该面方向向外还是向内，ownerID = elementID时向外，ownerID != elementID时向内
				// 当该面方向向内时，采用JudgeEdgeAndFace函数判断得到的顺序或逆序标识需要反号
				// 即之前判断是顺序，当该面是内部面时，面积计算时应该当做逆序面
				if (oldMesh->v_face[faceID1].n_owner != elementID) flag *= -1;

				// 如果第一个面在面积计算时是顺序，则原始面积矢量应反号
				if (flag == 1) area = -area;
            }

			ARI_OMP(atomic)
			areaX[edgeID] += area.X();

			ARI_OMP(atomic)
			areaY[edgeID] += area.Y();

			ARI_OMP(atomic)
			areaZ[edgeID] += area.Z();
		}
    }
	std::map<int, std::vector<int>>().swap(edgeFaceIDMap);

	Scalar volume;

	// 计算面积、体积
	ARI_OMP(parallel for schedule(static) private(volume))
	for (int edgeID = 0; edgeID < this->edgeNodes.size(); ++edgeID)
	{
		//对偶网格内部面编号与原始网格边编号对应
		const int &dualFaceID = v_interiorFaceID[edgeID];

		// 边的节点
		const int &nodeID1 = edgeNodes[edgeID].first;
		const int &nodeID2 = edgeNodes[edgeID].second;

		// 当前边所对应对偶网格面积矢量
		const Vector faceArea(areaX[edgeID], areaY[edgeID], areaZ[edgeID]);

		// 计算对偶网格面积大小、面单位法向矢量及面心
		// 对偶网格面心为原始网格边心
		dualMesh->v_face[dualFaceID].areaMag = faceArea.Mag();
		dualMesh->v_face[dualFaceID].normal = faceArea / dualMesh->v_face[dualFaceID].areaMag;
		dualMesh->v_face[dualFaceID].center = half * (oldMesh->v_node[nodeID1] + oldMesh->v_node[nodeID2]);

		// 计算当前边对对偶网格单元体积的贡献
		const Vector halfEdge = half * (oldMesh->v_node[nodeID2] - oldMesh->v_node[nodeID1]);
		if (dim2) volume = half * faceArea & halfEdge;
		else      volume = oneThird * faceArea & halfEdge;

		ARI_OMP(atomic)
		dualMesh->v_elem[nodeID1].volume += volume;

		ARI_OMP(atomic)
		dualMesh->v_elem[nodeID2].volume += volume;
	}
	std::vector<Scalar>().swap(areaX);
	std::vector<Scalar>().swap(areaY);
	std::vector<Scalar>().swap(areaZ);

	// 对偶网格体心为原始网格节点
	ARI_OMP(parallel for schedule(static))
	for (int nodeID = 0; nodeID < oldMesh->n_nodeNum; ++nodeID)
	{
		dualMesh->v_elem[nodeID].center = oldMesh->v_node[nodeID];
	}

	// 边界几何信息计算
	for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); ++patchID)
	{
		// 当前边界所有节点列表
		const std::vector<int> &nodesIDList = boundaryNodesList[patchID];

		// 对偶网格边界面数量等于原始网格边界节点数量
		std::vector<Vector> surfaceArea(nodesIDList.size(), Vector0);

		// 边界面面积计算
		std::vector<int> pos(oldMesh->n_nodeNum, -1); //用于加速查找
		for (int i = 0; i < nodesIDList.size(); ++i) pos[nodesIDList[i]] = i;

		for (int index = 0; index < oldMesh->vv_boundaryFaceID[patchID].size(); ++index)
		{
			const int &faceID = oldMesh->vv_boundaryFaceID[patchID][index];
			for (int i = 0; i< faceEdgeIDList[faceID].size(); ++i)
			{
				const int &edgeID = faceEdgeIDList[faceID][i];
				const int &nodeID1 = edgeNodes[edgeID].first;
				const int &nodeID2 = edgeNodes[edgeID].second;
				const Node edgeCenter = half * (oldMesh->v_node[nodeID1] + oldMesh->v_node[nodeID2]);
				const Vector halfEdge = half * (oldMesh->v_node[nodeID2] - oldMesh->v_node[nodeID1]);

				// 计算当前边对对偶网格边界面面积的贡献
				Vector area;
				if (dim2) area = halfEdge ^ Vector(0.0, 0.0, 1.0);
				else      area = half * (halfEdge ^ (oldMesh->v_face[faceID].center - edgeCenter));

				// 对偶网格面积矢量与原始网格面积矢量同向
				if ((area & oldMesh->v_face[faceID].normal) < 0) area *= -1.0;
				surfaceArea[pos[nodeID1]] += area;
				surfaceArea[pos[nodeID2]] += area;
			}
		}

		// 边界面面心、面积和单位法矢计算
		for (int index = 0; index < dualMesh->GetBoundaryFaceSize(patchID); ++index)
		{
			const int &dualFaceID = dualMesh->GetBoundaryFaceID(patchID, index);
			dualMesh->v_face[dualFaceID].areaMag = surfaceArea[index].Mag();
			dualMesh->v_face[dualFaceID].normal = surfaceArea[index] / dualMesh->v_face[dualFaceID].areaMag;
			dualMesh->v_face[dualFaceID].center = this->oldMesh->v_node[nodesIDList[index]];
		}
	}
}

int DualMesh::FindAdjacentNodeID(const std::vector<int> &nodeList, const int &nodeID1, const int &nodeID2)
{
    // 查找nodeList中与nodeID1相邻的另外一个节点（非nodeID2）
    const int nodeSize = nodeList.size();
	if (nodeSize == 2)
	{
		if (nodeList[0] == nodeID1 && nodeList[1] != nodeID2) return nodeList[1];
		if (nodeList[1] == nodeID1 && nodeList[0] != nodeID2) return nodeList[0];
	}
	else
	{
		for (int j = 0; j < nodeSize; ++j)
		{
			if (nodeList[j] == nodeID1)
			{
				const int nodeIDM1 = (j - 1 + nodeSize) % nodeSize;
				const int nodeIDP1 = (j + 1) % nodeSize;
				if (nodeList[nodeIDM1] == nodeID2) return nodeList[nodeIDP1];
				else                               return nodeList[nodeIDM1];
				break;
			}
		}
	}
    return -1;
}

void DualMesh::SwapMesh(Mesh *oldMesh_)
{
	//oldMesh->ClearMesh();
	//*oldMesh = *dualMesh;

    std::vector<Element>().swap(oldMesh_->v_elem);
    std::vector<Face>().swap(oldMesh_->v_face);
    std::vector<Node>().swap(oldMesh_->v_node);
    std::vector<std::vector<int>>().swap(oldMesh_->vv_boundaryFaceID);

    oldMesh_->n_elemNum = dualMesh->v_elem.size();
    oldMesh_->n_faceNum = dualMesh->v_face.size();
    oldMesh_->n_nodeNum = dualMesh->v_node.size();
    oldMesh_->n_elemNum_all = oldMesh_->n_elemNum;

    oldMesh_->v_elem.resize(oldMesh_->n_elemNum);
    oldMesh_->v_face.resize(oldMesh_->n_faceNum);
    oldMesh_->v_node.resize(oldMesh_->n_nodeNum);
    oldMesh_->vv_boundaryFaceID = dualMesh->vv_boundaryFaceID;

    ARI_OMP(parallel for schedule(static))
    for (int i = 0; i < oldMesh_->n_elemNum; ++i) oldMesh_->v_elem[i] = dualMesh->v_elem[i];

    ARI_OMP(parallel for schedule(static))
    for (int i = 0; i < oldMesh_->n_faceNum; ++i) oldMesh_->v_face[i] = dualMesh->v_face[i];

    ARI_OMP(parallel for schedule(static))
    for (int i = 0; i < oldMesh_->n_nodeNum; ++i) oldMesh_->v_node[i] = dualMesh->v_node[i];
}

void DualMesh::CreateDualNodes()
{
    // 原始网格体心和边的中点为对偶网格节点
	dualMesh->v_node.reserve(oldMesh->n_elemNum + edgeNodes.size());

    // step1: 遍历原始网格单元，计算体心，并将其作为对偶网格节点
    for (int oldElementID = 0; oldElementID < oldMesh->n_elemNum; oldElementID++)
    {
        /*
        // 仅将原始网格边界面所对应单元的体心作为对偶网格节点
        bool boundaryElementFlag = false;
        for (int index = 0; index < oldMesh->v_elem[oldElementID].GetFaceSize(); index++)
        {
            const int &oldFaceID = oldMesh->v_elem[oldElementID].v_faceID[index];
            if(oldMesh->v_face[oldFaceID].n_neighbor == -1)
            {
                boundaryElementFlag = true;
                break;
            }
        }
        if(!boundaryElementFlag) continue;
*/
        const std::vector<int> &nodes = oldMesh->v_elem[oldElementID].v_nodeID;
        const int &nodeSize = oldMesh->v_elem[oldElementID].GetNodeSize();
        // 计算原始网格体心
        Vector oldElementCenter = Vector0;
        for (int j=0; j<nodeSize; j++)
        {
            // 计算原始网格体心坐标，平均在循环结束后计算
            oldElementCenter += this->oldMesh->v_node[nodes[j]];
        }
        oldElementCenter /= (Scalar)nodeSize;

        // 将原始网格体心作为对偶网格新增节点
        Node &dualNode = oldElementCenter;
        dualMesh->v_node.push_back(dualNode);

        // 将此对偶网格新增节点编号放入所有与其连接的对偶网格单元的节点构成中
        const int dualNodeID = dualMesh->v_node.size() - 1;
        for (int j=0; j<nodeSize; j++)
        {
            //原始网格节点编号为对偶网格单元编号
            const int &dualElemID = nodes[j];
            dualMesh->v_elem[dualElemID].v_nodeID.push_back(dualNodeID);
        }

        // 将此对偶网格新增节点编号放入所有与其连接的对偶网格面的节点构成中
        // 按照原始网格单元的节点构成循环查找原始网格的所有边
		for (int j = 0; j<nodeSize; ++j)
        {
            const std::vector<int> &edgeIDList = this->nodeEdgesList[nodes[j]];
			for (int i = 0; i<edgeIDList.size(); ++i)
            {
                //查找过该节点的所有
                const int &adjacentNodeID = this->edgeNodes[edgeIDList[i]].second;
                for (int k = 0; k < nodeSize; ++k)
                {
                    if(adjacentNodeID == nodes[k])
                    {
                        // 内部面按照边的顺序存储
                        const int &faceID = v_interiorFaceID[edgeIDList[i]];
                        dualMesh->v_face[faceID].v_nodeID.push_back(dualNodeID);
                        break;
                    }
                }
            }
        }
    }
    
    // 原始网格边界node编号与其边界索引信息的图
    std::vector<std::map<int, int>> boundaryNodeMaps(oldMesh->GetBoundarySize());
	for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); ++patchID)
    {
		for (int index = 0; index<this->boundaryNodesList[patchID].size(); ++index)
        {
            const int &dualFaceID = dualMesh->GetBoundaryFaceID(patchID, index);
            const int &oldNodeID = this->boundaryNodesList[patchID][index];
            boundaryNodeMaps[patchID].insert(std::make_pair(oldNodeID, dualFaceID));
        }
    }
    /*
    // 原始网格边界面面编号与对偶网格边界节点的映射关系
    std::map<int, int> boundaryNodeFaceIDmap;
    */
    // step2: 原始网格边界面心作为对偶网格节点
    for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); patchID++)
    {
        for (int index = 0; index < oldMesh->GetBoundaryFaceSize(patchID); index++)
        {
            const int &faceID = oldMesh->GetBoundaryFaceID(patchID, index);
            const std::vector<int> &nodes = oldMesh->v_face[faceID].v_nodeID;
            const int &nodeSize = oldMesh->v_face[faceID].GetNodeSize();
            
            // 计算原始网格边界面面心
            Vector oldFaceCenter = Vector0;
            for (int j = 0; j < nodeSize; j++)
            {
                // 获取原始网格节点编号
                const int &oldNodeID = nodes[j];

                // 计算原始网格边界面面心坐标，平均在循环结束后计算
                oldFaceCenter += this->oldMesh->v_node[oldNodeID];
            }
            oldFaceCenter /= (Scalar)nodeSize;

            // 将原始网格边界面面心作为对偶网格新增节点
            Node &dualNode = oldFaceCenter;
            dualMesh->v_node.push_back(dualNode);
            const int dualNodeID = dualMesh->v_node.size() - 1;

            //// 填充由原始网格边界面面编号与对偶网格边界节点的映射关系
            //boundaryNodeFaceIDmap.insert(std::make_pair(faceID, dualNodeID));

            // 将此对偶网格新增节点编号放入所有与其连接的对偶网格单元的节点构成中
            for (int j = 0; j < nodeSize; j++)
            {
                //原始网格节点编号为对偶网格单元编号
                const int &dualElemID = nodes[j];
                dualMesh->v_elem[dualElemID].v_nodeID.push_back(dualNodeID);
            }

            // 将此对偶网格新增节点编号放入所有与其连接的对偶网格面的节点构成中
            for (int j = 0; j < nodeSize; j++)
            {
                const int &oldNodeID = nodes[j];
                std::map<int, int>::iterator iter = boundaryNodeMaps[patchID].find(oldNodeID);
                const int &dualFaceID = iter->second;
                dualMesh->v_face[dualFaceID].v_nodeID.push_back(dualNodeID);
            }
            
			for (int j = 0; j < nodeSize; ++j)
            {
                //遍历过该节点的所有边，查询属于该单元的边
				for (int k = 0; k < this->nodeEdgesList[nodes[j]].size(); ++k)
                {
					const int &edgeID = this->nodeEdgesList[nodes[j]][k];
                    for (int m = 0; m < nodeSize; ++m)
                    {
						if (this->edgeNodes[edgeID].second == nodes[m])
                        {
                            const int &dualFaceID = v_interiorFaceID[edgeID];
                            dualMesh->v_face[dualFaceID].v_nodeID.push_back(dualNodeID);
                        }
                    }
                }
            }
        }
    }

    // step3：两个边界的交接边的中点为对偶网格的节点，仅三维需要
    std::map<int, int> boundaryFaceIDMap;
    for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); patchID++)
    {
        for (int index = 0; index < oldMesh->GetBoundaryFaceSize(patchID); index++)
        {
            const int &faceID = oldMesh->GetBoundaryFaceID(patchID, index);
            boundaryFaceIDMap.insert(std::make_pair(faceID, patchID));
        }
    }

	// 原始网格边的邻接面
	std::vector<std::vector<int>> edgeFaceIDList(this->edgeNodes.size());
	std::vector<int> count(this->edgeNodes.size());
	for (auto it = faceEdgeIDList.begin(); it != faceEdgeIDList.end(); ++it)
		for (int i = 0; i < it->size(); i++) count[(*it)[i]]++;
	for (int i = 0; i < count.size(); ++i)
		edgeFaceIDList[i].reserve(count[i]);
	for (auto it = faceEdgeIDList.begin(); it != faceEdgeIDList.end(); ++it)
        for (int i = 0; i < it->size(); i++) edgeFaceIDList[(*it)[i]].push_back((int)(it - faceEdgeIDList.begin()));

	for (int edgeID = 0; edgeID < edgeFaceIDList.size(); ++edgeID)
	{
		bool boundaryEdgeFlag = false;
		for (int i = 0; i < edgeFaceIDList[edgeID].size(); i++)
		{
			if (oldMesh->v_face[edgeFaceIDList[edgeID][i]].n_neighbor == -1)
			{
				boundaryEdgeFlag = true;
				break;
			}
		}

		if (!boundaryEdgeFlag) continue;

        std::vector<int> patchIDList; 
        for (int index = 0; index < edgeFaceIDList[edgeID].size(); index++)
        {
            const int &faceID = edgeFaceIDList[edgeID][index];
            std::map<int,int>::iterator iter = boundaryFaceIDMap.find(faceID);
            if(iter != boundaryFaceIDMap.end()) patchIDList.push_back(iter->second);
        }
        patchIDList = GetNonRepeatedList(patchIDList);

        if(patchIDList.size()>=2)
        {
            // 将原始网格边界面面心作为对偶网格新增节点
            const int &nodeID1 = edgeNodes[edgeID].first;
            const int &nodeID2 = edgeNodes[edgeID].second;
            Node edgeCenter = 0.5 * (oldMesh->v_node[nodeID1] + oldMesh->v_node[nodeID2]);
            dualMesh->v_node.push_back(edgeCenter);
            const int dualNodeID = (int)dualMesh->v_node.size() - 1;
            dualMesh->v_elem[nodeID1].v_nodeID.push_back(dualNodeID);
            dualMesh->v_elem[nodeID2].v_nodeID.push_back(dualNodeID);
            
            for (int j = 0; j < patchIDList.size(); j++)
            {
                const int &patchID = patchIDList[j];
                std::map<int, int>::iterator iter1 = boundaryNodeMaps[patchID].find(nodeID1);
                const int &dualFaceID1 = iter1->second;
                dualMesh->v_face[dualFaceID1].v_nodeID.push_back(dualNodeID);
                
                std::map<int, int>::iterator iter2 = boundaryNodeMaps[patchID].find(nodeID2);
                const int &dualFaceID2 = iter2->second;
                dualMesh->v_face[dualFaceID2].v_nodeID.push_back(dualNodeID);
            }
            
            // 原始网格边的编号与对偶网格内部面容器对应
            const int &dualFaceID = v_interiorFaceID[edgeID];
            dualMesh->v_face[dualFaceID].v_nodeID.push_back(dualNodeID);
        }
    }

    // step4：多个边界的角点为对偶网格的节点
    std::vector<std::vector<std::vector<int>>> boundaryNodeFaceIDList(this->oldMesh->v_node.size()); 
     for (int nodeID = 0; nodeID < oldMesh->v_node.size(); nodeID++)
        boundaryNodeFaceIDList[nodeID].resize(oldMesh->GetBoundarySize());

    for (int patchID = 0; patchID < oldMesh->GetBoundarySize(); patchID++)
    {
        for (int index = 0; index < oldMesh->GetBoundaryFaceSize(patchID); index++)
        {
            const int &faceID = oldMesh->GetBoundaryFaceID(patchID, index);
            const std::vector<int> &nodeIDList = oldMesh->v_face[faceID].v_nodeID;
            for (int k = 0; k<oldMesh->v_face[faceID].GetNodeSize(); k++)
            {
                const int &nodeID = nodeIDList[k];
                boundaryNodeFaceIDList[nodeID][patchID].push_back(faceID);
            }      
        }
    }
    for (int nodeID = 0; nodeID < oldMesh->v_node.size(); nodeID++)
    {
        for (int patchID = 0; patchID < boundaryNodeFaceIDList[nodeID].size(); patchID++)
            boundaryNodeFaceIDList[nodeID][patchID] = GetNonRepeatedList(boundaryNodeFaceIDList[nodeID][patchID]);
        
        int share = 0;
        std::vector<int> pathcIDVector;
        for (int patchID = 0; patchID < boundaryNodeFaceIDList[nodeID].size(); patchID++)
        {
            if(boundaryNodeFaceIDList[nodeID][patchID].size() > 0)
            {
                share++;
                pathcIDVector.push_back(patchID);
            }
        }

        if((dim2 && share>=2) || (!dim2 && share>=3))  //二维角点和三维角点
        {
            dualMesh->v_node.push_back(this->oldMesh->v_node[nodeID]);
            const int dualNodeID = (int)dualMesh->v_node.size() - 1;

            // 原始网格边界交点属于对偶网格中该交点对所应的单元
            const int &dualElemID = nodeID;
            dualMesh->v_elem[dualElemID].v_nodeID.push_back(dualNodeID);

            for (int j = 0; j < pathcIDVector.size(); j++)
            {
                const int &faceID = boundaryNodeMaps[pathcIDVector[j]].find(nodeID)->second;
                dualMesh->v_face[faceID].v_nodeID.push_back(dualNodeID);
            }
        }
    }
}

void DualMesh::InsertWithNorepeat(std::vector<int> &v0, const int &value)
{
	for (auto it = v0.begin(); it != v0.end(); ++it) if (*it == value) return;
	v0.push_back(value);
}

int DualMesh::JudgeEdgeAndFace(const int &start, const int &end, const std::vector<int> &faceNodeList)
{
	const int size = faceNodeList.size();
	for (int i = 0; i < size; ++i)
	{
		if (faceNodeList[i] == start)
		{
			int next = (i + 1) % size;
			if (faceNodeList[next] == end) return 1;
			
			int prep = (i - 1 + size) % size;
			if (faceNodeList[prep] == end) return -1;
			else return 0;
		}
	}
	return 0;
}
