﻿#include "sourceFlow/boundaryCondition/WallRotate.h"
// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{
// 绝热壁面边界条件
WallRotate::WallRotate(const int &boundaryPatchID, Package::FlowPackage &data, const Vector &Omega,const Vector &origin)
    :
    Wall(boundaryPatchID, data), Omega(Omega)
{
    if (muLaminar == nullptr)
        FatalError("WallMoving::WallMoving： 该边界仅支持粘性流动...");
}

void WallRotate::Initialize()
{
    this->UpdateBoundaryCondition();    
}

void WallRotate::UpdateBoundaryCondition()
{
    this->BoundFromElement(rho);
    this->BoundFromElement(p);
    this->BoundFromElement(T);

    // 更新速度
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        Vector UWall = Omega ^ (mesh->GetFace(faceID).GetCenter() - origin);

        if (nodeCenter)
        {
            U.SetValue(ownerID, UWall);
            U.SetValue(neighID, UWall);
        }
        else
        {
            U.SetValue(neighID, 2.0 * UWall - U.GetValue(ownerID));
        }
    }

    return;
}

void WallRotate::AddConvectiveResidual()
{
    this->AddMRFConvectiveResidualAverage();
}

void WallRotate::AddDiffusiveResidual()
{
    if(!nodeCenter) this->AddDiffusiveResidualCellCenter();

    return;
}

void WallRotate::UpdateBoundaryResidual()
{
    if (!nodeCenter) return;

    if (muLaminar != nullptr) this->UpdateBoundaryResidualStatic();
    else                      this->UpdateBoundaryResidualSlipping();
}

}// namespace Flow
}// namespace Boundary
