﻿#include "sourceFlow/fluxScheme/FlowFluxManager.h"

namespace Flux
{
namespace Flow
{

FlowFluxManager::FlowFluxManager(Package::FlowPackage &data)
    :
    flowPackage(data),
	nodeCenter(data.GetFlowConfigure().GetPreprocess().dualMeshFlag),
    rho(*data.GetField().density),
    U(*data.GetField().velocity),
    p(*data.GetField().pressure),
    T(*data.GetField().temperature),
	gradientRho(flowPackage.GetGradientField().gradientRho),
	gradientU(flowPackage.GetGradientField().gradientU),
	gradientP(flowPackage.GetGradientField().gradientP),
	gradientT(flowPackage.GetGradientField().gradientT)
{
    const int &currentLevel = data.GetMeshStruct().level;
    const auto &flowConfigure = data.GetFlowConfigure();
    
    // 确定空间精度
    spatialOrder = flowConfigure.GetFluxScheme(currentLevel).reconstructOrder;
    
    // 确定梯度计算类型
    gradientScheme = flowConfigure.GetFluxScheme(currentLevel).gradient;

    //确定通量限制器、无粘通量、有粘通量、源项、残值光顺指针
    limiterScheme = flowConfigure.GetFluxScheme(currentLevel).limiter;
    inviscidFluxScheme = flowConfigure.GetFluxScheme(currentLevel).inviscid;
    viscousFluxScheme = flowConfigure.GetFluxScheme(currentLevel).viscous;
    sourceFluxScheme = flowConfigure.GetFluxScheme(currentLevel).source;

    //确定通量限制器、无粘通量、有粘通量、源项、残值光顺指针
    this->SetPreconditionPointer();
    this->SetLimiterPointer();
    this->SetInviscidFluxPointer(); //必须先调用SetPreconditionPointer和SetLimiterPointer
    this->SetViscousFluxPointer();
    this->SetSourceFluxPointer();
}

FlowFluxManager::~FlowFluxManager()
{
	if (limiter != nullptr) { delete limiter; limiter = nullptr; }
	if (inviscidFlux != nullptr) { delete inviscidFlux; inviscidFlux = nullptr; }
	if (viscousFlux != nullptr) { delete viscousFlux; viscousFlux = nullptr; }
	if (sourceFlux != nullptr) { delete sourceFlux; sourceFlux = nullptr; }
	if (precondition != nullptr) { delete precondition; precondition = nullptr; }
}

void FlowFluxManager::AddConvectiveAverageResidual()
{
    // 计算限制器
    if (limiter != nullptr) limiter->CalculatePsi();
    
    if (inviscidFlux != nullptr)
    {
        inviscidFlux->AddAverageResidual();
    }
    else
    {
		FatalError("FlowFluxManager::AddConvectiveAverageResidual: InviscidFlux pointer is empty!");
		return;
    }

    return;
}

void FlowFluxManager::AddConvectiveDissipationResidual()
{
    // 计算耗散项
    if (inviscidFlux != nullptr) inviscidFlux->AddDissipationResidual();

    return;
}

void FlowFluxManager::AddDiffusiveResidual()
{
    if (viscousFlux != nullptr) viscousFlux->AddResidual();

    return;
}

void FlowFluxManager::AddSourceResidual()
{
    const std::shared_ptr<MRFZONE>mrf_tem = flowPackage.GetMRF();
    if(mrf_tem)
    {
        mrf_tem->addCoriolis(flowPackage.GetResidualField().residualMomentum,rho,U);
    }
    if (sourceFlux != nullptr) sourceFlux->AddResidual();
    return;
}

void FlowFluxManager::SetResidualZero()
{    
    inviscidFlux->SetResidualZero();
    return;
}

void FlowFluxManager::CalculateSpectralRadius()
{
    if (inviscidFlux != nullptr) inviscidFlux->CalculateSpectralRadius();
    if (viscousFlux != nullptr) viscousFlux->CalculateSpectralRadius();
}

Flux::Flow::Precondition::Precondition *FlowFluxManager::GetPrecondition()
{
    return precondition;
}

NSFaceValue FlowFluxManager::GetFaceLeftRightValue(const int &faceID)
{
    if (inviscidFlux != nullptr) return inviscidFlux->GetFaceLeftRightValue(faceID);
    return NSFaceValue();
}

void FlowFluxManager::SetInviscidFluxPointer()
{
    switch (inviscidFluxScheme)
    {
    case Inviscid::Scheme::CENTRAL:
    {
        inviscidFlux = new Inviscid::CentralScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::LAX_FRIEDRICHS:
    {
        inviscidFlux = new Inviscid::LaxFriedrichsScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::ROE:
    {
        inviscidFlux = new Inviscid::RoeScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::ROE_MOD:
    {
        inviscidFlux = new Inviscid::RoeSchemeMod(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::VANLEER:
    {
        inviscidFlux = new Inviscid::VanLeerScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::AUSM:
    {
        inviscidFlux = new Inviscid::AUSMScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::AUSMDV:
    {
        inviscidFlux = new Inviscid::AUSMDVScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::AUSMPWP:
    {
        inviscidFlux = new Inviscid::AUSMPWPScheme(flowPackage, limiter, precondition);
        break;
    }
    case Inviscid::Scheme::HLLC:
    {
        inviscidFlux = new Inviscid::HLLCScheme(flowPackage, limiter, precondition);
        break;
    }
    default:
    {
		FatalError("FlowFluxManager::SetInviscidFluxPointer: Inviscid FluxSchemeType is unkown!");
		return;
    }
    }
    return;
}

void FlowFluxManager::SetViscousFluxPointer()
{
    if (!flowPackage.GetTurbulentStatus().viscousFlag)
    {
        viscousFlux = nullptr;
        return;
    }

    switch (viscousFluxScheme)
    {
    case Viscous::Scheme::NONE_VISCOUS:
    {
        viscousFlux = nullptr;
        break;
    }
    case Viscous::Scheme::CENTRAL_DISTANCE:
    case Viscous::Scheme::CENTRAL_FULL:
    {
        viscousFlux = new Viscous::ViscousFluxScheme(flowPackage);
        break;
    }
    default:
    {
		FatalError("FlowFluxManager::SetViscousFluxPointer: Viscous FluxSchemeType is unkown!");
		return;
    }
    }

    return;
}

void FlowFluxManager::SetSourceFluxPointer()
{
    switch (sourceFluxScheme)
    {
    case Source::Scheme::NONE_SOURCE:
    {
        sourceFlux = nullptr;
        break;
    }
    default:
    {
		FatalError("FlowFluxManager::SetSourceFluxPointer: Source FluxSchemeType is unkown!");
		return;
    }
    }
    return;
}

void FlowFluxManager::SetLimiterPointer()
{
    
    if (inviscidFluxScheme == Flux::Flow::Inviscid::CENTRAL ||
        inviscidFluxScheme == Flux::Flow::Inviscid::ROE_MOD ||
        spatialOrder == Flux::ReconstructionOrder::FIRST)
    {
        limiter = nullptr;
    }
    else if (spatialOrder == Flux::ReconstructionOrder::SECOND)
    {
        switch (limiterScheme)
        {
        case Limiter::Scheme::NONE_LIMITER:
        {
            limiter = new Limiter::LimiterNone(flowPackage);
            break;
        }
        case Limiter::Scheme::MINMOD:
        {
            limiter = new Limiter::LimiterMinMod(flowPackage);
            break;
        }
        case Limiter::Scheme::VANLEER:
        {
            limiter = new Limiter::LimiterVanLeer(flowPackage);
            break;
        }
        case Limiter::Scheme::VENKATAKRISHNAN:
        {
            limiter = new Limiter::LimiterVenkatakrishnan(flowPackage);
            break;
        }
        default:
        {
			FatalError("FlowFluxManager::SetLimiterPointer: limiterScheme is unkown!");
			return;
        }
        }
    }
    
    return;
}

void FlowFluxManager::SetPreconditionPointer()
{
    // 确定预处理指针
    if (flowPackage.GetFlowConfigure().GetAcceleration().preconditionFlag)
    {
        precondition = new Flux::Flow::Precondition::Precondition(flowPackage);
    }
    else
    {
        precondition = nullptr;
    }
}

} //namespace Flow
} //namespace Flux