﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ListTools.h
//! <AUTHOR> 张帅（数峰科技/西交大）
//! @brief 容器vector的各种附加功能.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_ListTools_
#define _basic_common_ListTools_

#include "basic/common/ConfigUtility.h"

/**
 * @brief 获取不包含重复元素的列表
 * @param originalList 原始整数向量
 * @return std::vector<int> 去重后的新向量
 * @note 时间复杂度O(n)，空间复杂度O(n)
 * @note Kong Ling补充于2019/12/23
 */
std::vector<int> GetNonRepeatedList(const std::vector<int>& originalList);

/**
 * @brief 向量加法运算符重载
 * @tparam Type 元素类型
 * @param lhs 左操作数向量
 * @param rhs 右操作数向量
 * @return std::vector<Type> 两个向量逐元素相加的结果
 * @throw std::invalid_argument 当向量长度不一致时抛出
 */
template<class Type>
std::vector<Type> operator + (const std::vector<Type>& lhs, const std::vector<Type>& rhs);

/**
 * @brief 向量减法运算符重载  
 * @tparam Type 元素类型
 * @param lhs 左操作数向量
 * @param rhs 右操作数向量
 * @return std::vector<Type> 两个向量逐元素相减的结果
 * @throw std::invalid_argument 当向量长度不一致时抛出
 */
template<class Type>
std::vector<Type> operator - (const std::vector<Type>& lhs, const std::vector<Type>& rhs);

/**
 * @brief 标量乘法运算符重载
 * @tparam Type 元素类型
 * @param scalar 标量值
 * @param vec 向量
 * @return std::vector<Type> 向量每个元素乘以标量的结果
 * @note Kong Ling补充于2019/12/23
 */
template<class Type>
std::vector<Type> operator *(const Scalar& scalar, const std::vector<Type>& vec);

/**
 * @brief 向量乘法运算符重载
 * @tparam Type 元素类型
 * @param scalars 标量向量
 * @param vec 向量
 * @return std::vector<Type> 两个向量逐元素相乘的结果
 * @throw std::invalid_argument 当向量长度不一致时抛出
 */
template<class Type>
std::vector<Type> operator * (const std::vector<Scalar>& scalars, const std::vector<Type>& vec);

/**
 * @brief 向量除法运算符重载
 * @tparam Type 元素类型
 * @param vec 被除数向量
 * @param scalars 标量除数向量
 * @return std::vector<Type> 两个向量逐元素相除的结果
 * @throw std::invalid_argument 当向量长度不一致时抛出
 * @throw std::invalid_argument 当除数为零时抛出
 */
template<class Type>
std::vector<Type> operator / (const std::vector<Type>& vec, const std::vector<Scalar>& scalars);

/**
 * @brief 向量点积运算符重载
 * @param vec1 第一个向量
 * @param vec2 第二个向量
 * @return std::vector<Scalar> 两个向量逐元素点积的结果
 * @throw std::invalid_argument 当向量长度不一致时抛出
 * @note Kong Ling补充于2019/12/22
 */
std::vector<Scalar> operator & (const std::vector<Vector>& vec1, const std::vector<Vector>& vec2);

/**
 * @brief 映射合并运算符重载
 * @tparam T1 键类型
 * @tparam T2 值类型
 * @param left 第一个映射
 * @param right 第二个映射
 * @return std::map<T1, T2> 合并后的新映射
 */
template<class T1, class T2>
std::map<T1, T2> operator + (const std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief 映射合并赋值运算符重载
 * @tparam T1 键类型
 * @tparam T2 值类型
 * @param left 目标映射
 * @param right 源映射
 */
template<class T1, class T2>
void operator += (std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief 映射差集运算符重载
 * @tparam T1 键类型
 * @tparam T2 值类型
 * @param left 第一个映射
 * @param right 第二个映射
 * @return std::map<T1, T2> 差集结果映射
 */
template<class T1, class T2>
std::map<T1, T2> operator - (const std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief 映射差集赋值运算符重载
 * @tparam T1 键类型
 * @tparam T2 值类型
 * @param left 目标映射
 * @param right 源映射
 */
template<class T1, class T2>
void operator -= (std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief 映射交集运算符重载
 * @tparam T1 键类型
 * @tparam T2 值类型
 * @param left 第一个映射
 * @param right 第二个映射
 * @return std::map<T1, T2> 交集结果映射
 */
template<class T1, class T2>
std::map<T1, T2> operator * (const std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief 映射交集赋值运算符重载
 * @tparam T1 键类型
 * @tparam T2 值类型
 * @param left 目标映射
 * @param right 源映射
 */
template<class T1, class T2>
void operator *= (std::map<T1, T2>& left, const std::map<T1, T2>& right);

#endif
