
//          Copyright <PERSON> 2009.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_COROUTINES_FLAGS_H
#define BOOST_COROUTINES_FLAGS_H

namespace boost {
namespace coroutines {

enum flag_unwind_t
{
    stack_unwind = 0,
    no_stack_unwind
};

}}

#endif // BOOST_COROUTINES_FLAGS_H
