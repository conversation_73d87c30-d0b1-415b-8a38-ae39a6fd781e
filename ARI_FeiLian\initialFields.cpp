﻿#include "basic/field/ElementField.h"
#include "sourceFlow/configure/FlowConfigure.h"

int main(int argc, char **argv)
{
    // 读入参数文件
    Configure::Flow::FlowConfigure flowConfigure;
    flowConfigure.ReadCaseXml(argv[1]);
    flowConfigure.PrintInformation();
    
    // 生成全局网格并创建虚单元
    Print("读取全局网格...");
    SubMesh *subMesh = nullptr; Mesh *localMesh = nullptr;

    const auto &meshNumber = flowConfigure.GetMeshParameters().meshNumber;
    for (int zoneID = 0; zoneID < meshNumber; zoneID++)
    {
        const auto &caseName = flowConfigure.GetCaseName();
        const auto &outputPath = flowConfigure.GetPreprocess().outputPath;
        std::string zoneMeshname = outputPath + caseName + "_zone" + ToString(zoneID) + ".bMesh";

        std::fstream bMeshFile;
        bMeshFile.open(zoneMeshname, std::fstream::in | std::fstream::binary);
        if (!bMeshFile.is_open()) FatalError("全局网格文件不存在...");

        subMesh = new SubMesh();

        // 读取全局网格
        subMesh->ReadMesh(bMeshFile, true, false);

        // 创建虚单元
        subMesh->CreateGhostElement(0);

        // 转为mesh
        localMesh = subMesh->GetMultiGrid(0);

        bMeshFile.close();
    }

    ElementField<Scalar> rho(localMesh, Scalar0, "RHO");
    ElementField<Vector> U(localMesh, Vector0, "U");
    ElementField<Scalar> p(localMesh, Scalar0, "P");
    const int elementNumber = localMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = localMesh->GetElementIDInDomain(index);
        if (localMesh->GetElement(elementID).GetCenter().X() < 0.5)
        {
            rho.SetValue(elementID, 1.0);
            p.SetValue(elementID, 1.0);
        }
        else
        {
            rho.SetValue(elementID, 0.125);
            p.SetValue(elementID, 0.1);
        }
    }

    const std::string filePath = flowConfigure.GetControl().initialization.initialFilePath;
    MakeDirectory(filePath);
    rho.WriteFile(filePath + rho.GetName() + ".Field", true);
    U.WriteFile(filePath + U.GetName() + ".Field", true);
    p.WriteFile(filePath + p.GetName() + ".Field", true);
    
    // 删除网格
    subMesh->ClearMesh();
    if (subMesh != nullptr) { delete subMesh; subMesh = nullptr; }
    
    return 0;
}