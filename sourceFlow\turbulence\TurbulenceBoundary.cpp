﻿#include "sourceFlow/turbulence/TurbulenceBoundary.h"

namespace Turbulence
{
TurbulenceBoundary::TurbulenceBoundary(Package::FlowPackage &flowPackage_)
    :
    flowPackage(flowPackage_), mesh(flowPackage_.GetMeshStruct().mesh),
    level(flowPackage_.GetMeshStruct().level),
    rho(*flowPackage_.GetField().density),
    U(*flowPackage_.GetField().velocity),
    muLaminar(*flowPackage_.GetField().muLaminar),
    muTurbulent(flowPackage_.GetField().muTurbulent),
    alphaT(flowPackage_.GetField().alphaT),
    muTurbulentRANS(flowPackage_.GetField().muTurbulentRANS),
    residualEnergy(*flowPackage_.GetResidualField().residualEnergy),
    turbulenceVector(flowPackage_.GetField().turbulence),
	turbulenceGradientVector(flowPackage_.GetGradientField().gradientTurbulence),
    turbulenceResidualVector(flowPackage_.GetResidualField().residualTurbulence),
    nodeCenter(flowPackage_.GetFlowConfigure().GetPreprocess().dualMeshFlag),
    turbulenceMacro(flowPackage_.GetTurbulentStatus().variableMacro),
    freeStreamTurbulenceValue(*flowPackage_.GetTurbulentStatus().freeStreamValue),
	jacobianTur(flowPackage.GetImplicitSolver().jacobianTur),
    updateJacobian(flowPackage.GetImplicitSolver().updateJacobian)
{
    if (!flowPackage.GetTurbulentStatus().turbulenceFlag)
    {
        FatalError("TurbulenceBoundary::TurbulenceBoundary: flow is not turbulent!");
        return;
    }
    
    // 主流的边界条件，用来后续处理湍流边界
    const auto &flowConfigure = flowPackage.GetFlowConfigure();
    for (int i = 0; i < mesh->GetBoundarySize(); i++)
        flowBoundaryType.push_back(flowConfigure.GetLocalBoundary(level, i).type);

    turbulenceSize = turbulenceMacro.size();    

    // 来流的相关参考值
    freeStreamIntensity = flowConfigure.GetFlowReference().turbulentIntensity;
    freeStreamTurbulentViscosityRatio = flowConfigure.GetFlowReference().turbulentViscosityRatio;

    freeStreamDensity = flowConfigure.GetFlowReference().density;
    freeStreamTemperature = flowConfigure.GetFlowReference().staticTemperature;
    freeStreamPressure = flowConfigure.GetFlowReference().staticPressure;
    freeStreamVelocity = flowConfigure.GetFlowReference().velocity;
    freeStreamMuLaminar = flowConfigure.GetFlowReference().muLaminar;

    freeStreamRe = flowConfigure.GetFlowReference().Reynolds;
    freeStreamMa = flowConfigure.GetFlowReference().mach;    

    //计算来流的湍流量
    this->CalculateFreeStreamValue();
    kOmegaV2Flag = false;
    if(flowConfigure.GetModel().type == Model::K_OMEGA_V2) kOmegaV2Flag = true;
}

void TurbulenceBoundary::Initialize()
{
    this->UpdateBoundaryCondition();
}

void TurbulenceBoundary::UpdateBoundaryCondition()
{
    // 更新并行边界
    for (int m = 0; m < turbulenceSize; ++m) turbulenceVector[m]->SetGhostlValueParallel();
    muTurbulent->SetGhostlValueParallel();
    if(alphaT != nullptr) alphaT->SetGhostlValueParallel();
    if(muTurbulentRANS != nullptr) muTurbulentRANS->SetGhostlValueParallel();

    // 更新物理边界
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
    {
        switch (flowBoundaryType[patchID])
        {
        // 物面边界条件
        case Boundary::Type::WALL_SLIPPING: // 滑移边界条件
        case Boundary::Type::WALL_ADIABATIC:  // 绝热壁面边界条件        
        case Boundary::Type::WALL_ISOTHERMAL: // 等温壁面边界条件
        case Boundary::Type::WALL_RIBLETS:     // 微型凹槽壁面边界条件
        case Boundary::Type::WALL_MOVING:     // 运动壁面边界条件
        case Boundary::Type::WALL_ROTATE:     // 旋转壁面边界条件
		case Boundary::Type::WALL_HEATFLUX_FILE: // 给定热通量
		case Boundary::Type::Wall_TEMPERATURE_FILE: // 给定温度
        {
            UpdateWallGeneral(patchID);
            break;
        }

        // 用内场值更新的边界
        case Boundary::Type::SYMMETRY: // 对称面边界条件
        case Boundary::Type::EXTRAPOLATION: // 外插边界   
        case Boundary::Type::MASSFLOW_OUTLET: // 给定出口反压系数的边界
        case Boundary::Type::OUTFLOW_PRESSURE: // 给定出口反压系数的边界
        case Boundary::Type::NACELLE_INLET: // 给定短舱入口的边界（流场的出口）
        case Boundary::Type::NACELLE_EXHAUST: // 给定短舱出口的边界（流场的入口）        
        case Boundary::Type::SYNTHETIC_JET:   // 合成射流边界条件
        {
            UpdateFromInnerField(patchID);
            break;
        }

        // 依据速度方向更新的边界
        case Boundary::Type::FARFIELD: // 远场边界 
        case Boundary::Type::WIND_TUNNEL_WALL_PRESSURE: // 风洞壁面压力边界
        case Boundary::Type::MASSFLOW_INLET: // 给定出口反压系数的边界
        case Boundary::Type::INFLOW_TOTAL_CONDITION: // 给定总条件的入口边界
        case Boundary::Type::INFLOW_TOTAL_CONDITION_MACH: // 给定总条件的入口边界
        {
            UpdateAccordingToVelocity(patchID);
            break;
        }

        // 用远场参考值更新的边界
        case Boundary::Type::INFLOW_SPECIFY:
        {
            UpdateFromFreeStream(patchID);
            break;
        }

        // 重叠区域更新的边界，此处跳过操作，改为在下方统一更新
        case Boundary::Type::OVERSET:
        {
            //for (int m = 0; m < turbulenceSize; ++m) turbulenceVector[m]->SetGhostlValueOverset();
            break;
        }

        // 周期性边界
        case Boundary::Type::PERIODIC:
        {
            FatalError("TurbulenceBoundary::UpdateBoundaryCondition: PERIODIC is error");
			return;
        }

        default:
        {
            std::string error = "Bounday patch( " + ToString(patchID) + ") bcType is unkown!";
            FatalError("TurbulenceBoundary::UpdateBoundaryCondition: " + error);
			return;
        }
        }
    }

    // 重叠边界更新，重叠模块启动时所有进程应当一起更新
	if (flowPackage.GetFlowConfigure().JudgeEnableOversetMesh())
	{
		for (int m = 0; m < turbulenceSize; ++m) turbulenceVector[m]->SetGhostlValueOverset();
        muTurbulent->SetGhostlValueOverset();
    }

    return;
}

void TurbulenceBoundary::UpdateBoundaryResidual()
{
    if (!nodeCenter) return;
    
    // 更新物理边界
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
    {
        if (flowBoundaryType[patchID] < Boundary::WALL) continue; //非物面
            
	    const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	    for (int index = 0; index < faceSize; ++index)
	    {
	    	// 得到面相关信息
	    	const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();

			for (int k = 0; k < turbulenceSize; k++)
            {
                turbulenceResidualVector[k]->SetValue(ownerID, Scalar0);
                if (updateJacobian) jacobianTur->DeleteValsRowi(ownerID, k);
            }
        }
    }
}

const std::vector<Scalar> &TurbulenceBoundary::GetFarValue()
{
    return freeStreamTurbulenceValue;
}

Scalar CalculateNutFree(Scalar nue_ratio, Scalar nue)
{
    const Scalar Cnu1 = 7.1;
    const Scalar Cv1p3 = pow(Cnu1, 3.0);
    const Scalar add = Cv1p3 / (nue_ratio * nue_ratio * nue_ratio);
    Scalar chi = nue_ratio * sqrt(sqrt(1.0 + add));
    Scalar chi2 = chi * chi;
    Scalar chi3 = chi2 * chi;

    int i = 0;
    Scalar dev = INF;
    while (dev > SMALL && i < 999)
    {
        const Scalar f = chi * chi3 - nue_ratio * (chi3 + Cv1p3);
        const Scalar df = chi2 * (4.0 * chi - 3.0 * nue_ratio);
        
        chi -= f / df;
        chi2 = chi * chi;
        chi3 = chi2 * chi;
        Scalar tst = chi * chi3 / (chi3 + Cv1p3);
        dev = fabs(1.0 - tst / nue_ratio);
        i++;
    }

    return chi * nue;
}

void TurbulenceBoundary::CalculateFreeStreamValue()
{
    freeStreamTurbulenceValue.resize(turbulenceSize);

    for (int m = 0; m < turbulenceSize; ++m)
    {
        //计算远场值
        Scalar &farValue = freeStreamTurbulenceValue[m];

        switch (turbulenceMacro[m])
        {
        case FlowMacro::Scalar::NUT:
        {
            // farValue =  freeStreamTurbulentViscosityRatio * freeStreamMuLaminar / freeStreamDensity;
            farValue = CalculateNutFree(freeStreamTurbulentViscosityRatio, freeStreamMuLaminar / freeStreamDensity);
            break;
        }
        case FlowMacro::Scalar::V2BAR:
        case FlowMacro::Scalar::K:
        {
            const Scalar fluctuantVelocity = freeStreamIntensity * freeStreamVelocity.Mag();
            farValue = 1.5 * fluctuantVelocity * fluctuantVelocity;

            //const Scalar soundSpeed = flowPackage.GetFlowConfigure().GetFlowReference().sound;
            //const Scalar K_CFL3D = 9.e-9;
            //farValue = K_CFL3D * soundSpeed * soundSpeed;
            break;
        }
        case FlowMacro::Scalar::OMEGA:
        {
            const Scalar fluctuantVelocity = freeStreamIntensity * freeStreamVelocity.Mag();
            const Scalar kFree = 1.5 * fluctuantVelocity * fluctuantVelocity;
            const Scalar freeStreamMuTurbulent = freeStreamTurbulentViscosityRatio * freeStreamMuLaminar;
            farValue = freeStreamDensity * kFree / freeStreamMuTurbulent;

            //const Scalar W_CFL3D = 1.e-6;
            //const Scalar soundSpeed = flowPackage.GetFlowConfigure().GetFlowReference().sound;
            //farValue = W_CFL3D * soundSpeed * soundSpeed * freeStreamDensity / freeStreamMuLaminar;
            break;
        }
		case FlowMacro::Scalar::EPSILON:
		{
			const Scalar fluctuantVelocity = freeStreamIntensity * freeStreamVelocity.Mag();
			const Scalar kFree = 1.5 * fluctuantVelocity * fluctuantVelocity;
			const Scalar freeStreamMuTurbulent = freeStreamTurbulentViscosityRatio * freeStreamMuLaminar;
			farValue = 0.09 * freeStreamDensity * kFree * kFree / freeStreamMuTurbulent;
			break;
		}
	    case FlowMacro::Scalar::GAMMA:
	    {
	    	farValue = 1.0;
	    	break;
	    }
	    case FlowMacro::Scalar::ReThet:
	    {
	        Scalar Tu = freeStreamIntensity * 100.0;
	        if (Tu <= 1.3)
	        {
                farValue = 1173.51 - 589.428 * Tu + 0.2196 / (Tu * Tu + 1.e-13);
	        }
	        else
	        {
                farValue = 331.50 * pow(Tu - 0.5658, -0.671);
	        }
	        break;
	    }
        default:
        {
            FatalError("TurbulenceBoundary::CalculateFreeStreamValue: turbulenceMacro is unkown!");
			return;
        }
        }
    }
}

void TurbulenceBoundary::UpdateWallGeneral(const int &patchID)
{
    const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
    for (int index = 0; index < faceSize; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        for (int m = 0; m < turbulenceSize; ++m)
        {
            // 计算壁面值
            Scalar turbulenceFaceValue = Scalar0;
            switch (turbulenceMacro[m])
            {
            case FlowMacro::Scalar::NUT:
            case FlowMacro::Scalar::K:
            case FlowMacro::Scalar::V2BAR:
            {
                turbulenceFaceValue = Scalar0;
                break;
            }

            case FlowMacro::Scalar::OMEGA:
            {
                if(kOmegaV2Flag)
                {
                    turbulenceFaceValue = turbulenceVector[m]->GetValue(ownerID);
                }
                else
                {
                    const Scalar betaOmega1 = 0.075;
                    const Scalar &muLaminarBoundary = 0.5 * (muLaminar.GetValue(ownerID) + muLaminar.GetValue(neighID));
                    const Scalar &rhoBoundary = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
                    Scalar y;
                    if (nodeCenter)
                    {
                        const int &innerID = mesh->GetInnerElementIDForBoundaryElement(patchID, index);
                        y = (mesh->GetElement(ownerID).GetCenter() - mesh->GetElement(innerID).GetCenter()).Mag();
                    }
                    else
                    {
                        y = mesh->GetNearWallDistance(ownerID);
                    }
                    turbulenceFaceValue = 10.0 * (6.0 * muLaminarBoundary) / (rhoBoundary * betaOmega1 * y * y);
                }

                break;
            }

            case FlowMacro::Scalar::EPSILON:
            {
                const Scalar &kWall = turbulenceVector[0]->GetValue(ownerID);
                const Scalar &muLaminarBoundary = muLaminar.GetValue(ownerID);
                const Scalar &rhoBoundary = rho.GetValue(ownerID);
                Scalar y;
                if (nodeCenter)
                {
                    const int &innerID = mesh->GetInnerElementIDForBoundaryElement(patchID, index);
                    y = (mesh->GetElement(ownerID).GetCenter() - mesh->GetElement(innerID).GetCenter()).Mag();
                }
                else
                {
                    y = mesh->GetNearWallDistance(ownerID);
                }
                turbulenceFaceValue = 2.0 * muLaminarBoundary / rhoBoundary * kWall / (y * y);

                break;
            }

            case FlowMacro::Scalar::GAMMA:
            case FlowMacro::Scalar::ReThet:
            {
                turbulenceFaceValue = turbulenceVector[m]->GetValue(ownerID);
                break;
            }

            default:
            {
                FatalError("TurbulenceBoundary::UpdateWallAdiabatic: turbulenceMacro is unkown!");
                return;
            }
            }

            // 更新虚单元值
            if (nodeCenter)
            {
                turbulenceVector[m]->SetValue(ownerID, turbulenceFaceValue);
                turbulenceVector[m]->SetValue(neighID, turbulenceFaceValue);
            }
            else
            {
                turbulenceVector[m]->SetValue(neighID, 2.0 * turbulenceFaceValue - turbulenceVector[m]->GetValue(ownerID));
            }
        }
        
        if (nodeCenter)
        {
            muTurbulent->SetValue(ownerID, Scalar0);
            muTurbulent->SetValue(neighID, Scalar0);
            if(alphaT != nullptr) 
            {
                alphaT->SetValue(ownerID, Scalar0);
                alphaT->SetValue(neighID, Scalar0);
            }

            if(muTurbulentRANS != nullptr) 
            {
                muTurbulentRANS->SetValue(ownerID, Scalar0);
                muTurbulentRANS->SetValue(neighID, Scalar0);
            }
        }
        else
        {
            muTurbulent->SetValue(neighID, -muTurbulent->GetValue(ownerID));
            if(alphaT != nullptr) alphaT->SetValue(neighID, -alphaT->GetValue(ownerID));
            if(muTurbulentRANS != nullptr) muTurbulentRANS->SetValue(neighID, -muTurbulentRANS->GetValue(ownerID));
        }
    }
}

Scalar TurbulenceBoundary::WallFunction(const int &elementID)
{
    std::vector<std::vector<Scalar>> a;
    a.resize(3);
    for (int i = 0; i < 3; i++) a[i].resize(8);

    a[0][1] = 2.354039;
    a[0][2] = 0.117984;
    a[0][3] = -4.2899192E-4;
    a[0][4] = 2.0404148E-6;
    a[0][5] = -5.1775775E-9;
    a[0][6] = 6.2687308E-12;
    a[0][7] = -2.9169580E-15;

    a[1][1] = 5.777191;
    a[1][2] = 6.8756983E-2;
    a[1][3] = -7.1582745E-6;
    a[1][4] = 1.5594904E-9;
    a[1][5] = -1.4865778E-13;

    a[2][1] = 31.08654;
    a[2][2] = 5.0429072E-2;
    a[2][3] = -2.0072314E-8;

    const Scalar reM = freeStreamRe / freeStreamMa;

    //计算Rc
    const Scalar &rhoI = rho.GetValue(elementID);
    const Vector &UI = U.GetValue(elementID);
    const Scalar muI = muLaminar.GetValue(elementID) + flowPackage.GetField().muTurbulent->GetValue(elementID);
    
    const Scalar y = mesh->GetNearWallDistance(elementID);

    Scalar Rc = rhoI * UI.Mag() * y * reM / muI;

    //计算nPlus
    Scalar nPlus;
    if (Rc < 20.24)
    {
        nPlus = sqrt(Rc);
    }
    else if (Rc >= 20.24 && Rc < 435)
    {
        nPlus = a[0][1];
        for (int j = 2; j <= 7; j++) nPlus += a[0][j] * std::pow(Rc, j - 1);
    }
    else if (Rc >= 435 && Rc < 4000)
    {
        nPlus = a[1][1];
        for (int j = 2; j <= 5; j++) nPlus += a[1][j] * std::pow(Rc, j - 1);
    }
    else
    {
        nPlus = a[2][1];
        for (int j = 2; j <= 3; j++) nPlus += a[2][j] * std::pow(Rc, j - 1);
    }

    //计算速度的法向梯度
    Scalar temp = nPlus * nPlus * muI / reM / rhoI / Max(UI.Mag() * y, SMALL) - 1.0;
    Scalar muTWall = temp * muI * flowPackage.GetFlowConfigure().GetFlowReference().muLaminar;

    return  muTWall;
}

void TurbulenceBoundary::UpdateFromInnerField(const int &patchID)
{
	const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	for (int index = 0; index < faceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighborID = mesh->GetFace(faceID).GetNeighborID();

        //无论格心还是格点，ownerID单元通过流场计算获得
        for (int m = 0; m < turbulenceSize; ++m)
            turbulenceVector[m]->SetValue(neighborID, turbulenceVector[m]->GetValue(ownerID));

        muTurbulent->SetValue(neighborID, muTurbulent->GetValue(ownerID));

        if(alphaT != nullptr)  alphaT->SetValue(neighborID, alphaT->GetValue(ownerID));
        if(muTurbulentRANS != nullptr)  muTurbulentRANS->SetValue(neighborID, muTurbulentRANS->GetValue(ownerID));
    }
}

void TurbulenceBoundary::UpdateAccordingToVelocity(const int &patchID)
{
	const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	for (int index = 0; index < faceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        const Scalar UNormal = U.GetValue(ownerID) & mesh->GetFace(faceID).GetNormal();
        if (UNormal > 0) // 出流边界
        {
            //无论格心还是格点，ownerID单元通过流场计算获得
            for (int m = 0; m < turbulenceSize; ++m)
                turbulenceVector[m]->SetValue(neighID, turbulenceVector[m]->GetValue(ownerID));
            
            muTurbulent->SetValue(neighID, muTurbulent->GetValue(ownerID));
            if(alphaT != nullptr)  alphaT->SetValue(neighID, alphaT->GetValue(ownerID));
            if(muTurbulentRANS != nullptr)  muTurbulentRANS->SetValue(neighID, muTurbulentRANS->GetValue(ownerID));
        }
        else // 入流边界
        {
            for (int m = 0; m < turbulenceSize; ++m)
                turbulenceVector[m]->SetValue(neighID, 2.0 * freeStreamTurbulenceValue[m] - turbulenceVector[m]->GetValue(ownerID));
            
            muTurbulent->SetValue(neighID, 2.0 * muLaminar.GetValue(neighID) * freeStreamTurbulentViscosityRatio - muTurbulent->GetValue(ownerID));
            if(alphaT != nullptr)  alphaT->SetValue(neighID, 2.0 * muLaminar.GetValue(neighID) * freeStreamTurbulentViscosityRatio - alphaT->GetValue(ownerID));
            if(muTurbulentRANS != nullptr)  muTurbulentRANS->SetValue(neighID, 2.0 * muLaminar.GetValue(neighID) * freeStreamTurbulentViscosityRatio - muTurbulentRANS->GetValue(ownerID));
        }
    }
}

void TurbulenceBoundary::UpdateFromFreeStream(const int &patchID)
{
	const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	for (int index = 0; index < faceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        for (int m = 0; m < turbulenceSize; ++m)
        {
            if (nodeCenter) turbulenceVector[m]->SetValue(ownerID, freeStreamTurbulenceValue[m]);
            turbulenceVector[m]->SetValue(neighID, freeStreamTurbulenceValue[m]);
        }
        
        const Scalar muTurbulentFree = muLaminar.GetValue(neighID) * freeStreamTurbulentViscosityRatio;
        if (nodeCenter) muTurbulent->SetValue(neighID, muTurbulentFree);
        muTurbulent->SetValue(neighID, muTurbulentFree);
    }
}

} //namespace Turbulence
