[![Latest Release](http://172.16.115.4/feilian/feilian/-/badges/release.svg)](http://172.16.115.4/feilian/feilian/-/releases)

# 飞廉软件源码使用说明

- 飞廉源码基于Git进行管理，托管在GitLab服务器的本项目仓库中
- 可通过集群及个人办公电脑进行访问
- 本说明将介绍如何在集群和个人电脑的VS、VSCode等工具中与GitLab服务器进行交互，以及如何进行团队协同开发等
- 总体使用流程为：
  - 创建分支，从本项目的develop（开发分支）创建个人分支
  - 克隆代码，将本项目的个人分支克隆或拉取到“本地” （此处的“本地”既可以是个人办公电脑，也可以是旌旗集群中的某个路径）
  - 修改代码，在本地修改代码，测试无问题
  - 推送代码，将本地代码推送至本项目的个人分支中
  - 发起合并，向develop（开发分支）发起合并请求，等待合并，完成版本更新

## 1. 准备事项

- 集群访问GitLab无需特殊操作：

  - 可使用三号集群feilian账号或其他任意账号登录集群
  - 集群中已经具备Git环境，无需再次安装
  - 三号集群feilian账号下已配置全局git用户信息，为方便区分提交信息，使用该账号进行开发时，请在个人工作目录下配置局部用户信息：

  ```
    git config --local user.name "test"
  ```
- Windows个人办公电脑访问GitLab需先安装Git：

  - 点击下载Git并安装，[git下载地址](http://************:8858/images/1/12/Git-2.42.0.2-64-bit-2.rar)
  - 安装完成后，打开Git Bash终端，配置用户信息：

  ```
    git config --global user.name "test"   
    git config --global user.email "<EMAIL>"
  ```
- 提交代码前注意事项

  - 确保配置是自己的账户，以下两行都应该是自己的账户，尤其是email
  ```
    git config user.name
    git config user.email
  ```
  - 如果不是使用上述方法将账户配置成自己。
  ```
    git config --global user.name "test"   
    git config --global user.email "<EMAIL>"
  ```

## 2.创建分支

- **本项目的main分支为主分支，为保护主分支不受污染，普通个人账户无法对其进行直接修改**
- 所有开发应当先基于develop分支创建个人分支，个人基于该分支进行代码开发，创建个人分支的方式有两种：

  - 远程创建分支，有两种方式：

    - 在本项目上方点击“新建分支”，输入相应信息即可创建分支；该方式是在现有仓库中创建了一个新分支

    ![aa](http://172.16.115.4/feilian/feilian/-/wikis/uploads/7f7998f9ca96ceb4605d19c252b87f1d/%E6%8D%95%E8%8E%B7.jpg)

    - 在本页面点击右上角“派生”按钮，输入相应信息即可创建分支；该方式是复制一个完整仓库到个人账户下
    - 以上两种方式可自由选择一种，推荐第一种
  - 本地创建分支：

    - 先将develop分支克隆至本地，在命令行执行：
      ```
      git branch <新分支名>     #创建新分支
      git checkout <新分支名>   #切换到新分支
      ```
    - 分支的其他常用操作命令：
      ```
      git branch    #查看分支列表
      git branch -a    #查看所有分支列表
      git branch -d <分支名>   #删除分支
      git merge <要合并的分支名> #合并分支，将“要合并的分支”合并至当前所在分支
      ```

## 3.源码克隆与同步

创建分支后，应将服务器代码克隆至“本地”，可分为第一次克隆和后续同步：

## 3.1 第一次克隆代码

第一次从远程克隆代码至本地时，需在本页面点击“克隆”，有两种克隆方式：

- SST克隆，需要配置SSH秘钥
- HTTP克隆，无需特殊配置

复制上述两种克隆方式中的任意一种代码链接，然后有多种方式将该代码克隆至本地，下面列举常用的三种：

- 命令行克隆，在本地终端（集群终端或本地Git Bash终端）中进入要存储代码的文件夹，执行命令：

```
cd <本地文件夹>
git clone <远程项目链接>
```

- VS中克隆，点击“团队资源管理器/克隆”，在第一个地址中填入远程项目链接，第二个地址中输入本地要存储的路径；
- VSCode中克隆，点击左侧“源代码管理”，选择“克隆存储库”，在弹出的地址栏中填入远程项目链接

### 第三方库克隆

飞廉软件的第三方库作为独立仓库进行管理，并作为本项目的一个子模块（submodule），名称为feilian-external，克隆本项目源码时仅会创建feilian-external空文件夹，需要执行以下命令下载子模块文件：

```
cd <项目根目录>
git submodule init
git submodule update
```

执行完以上命令如果未自动下载库文件，可以继续执行：

```
cd feilian-external
git pull
```

## 3.2 本地已有代码后：

- 可通过 `git status` 查看当前状态
- 可通过 `git log` 查看提交记录
- 开发过程中，由于其他人也在持续向develop分支合并，本地版本可能落后于远程版本，此时可通过 `git pull`或 `git fetch`与远程版本进行同步，两者的区别是：
  - `git pull origin <远程分支名>`下载远程代码的特定分支至本地，并会自动尝试合并到本地分支中，如果有冲突，需要手动解决
  - `git fetch origin <远程分支名>`该命令会返回一个指向该远程分支最新状态的FETCH_HEAD，且不会自动合并到当前分支，用户可以自行用 `git diff`查看差异，决定是否合并，需要合并时在当前工作分支下，采用 `git merge FETCH_HEAD`命令合并
  - 两者各有优缺点，可自行尝试

3. 同步指定文件：
   - 开发中，由于其他人也在对远程develop分支进行合并更新，本地分支可能会落后于远程主分支，需要与远程保持同步，但又不想把tutorials等本地进行了修改且不需要与远程保持同步的文件进行同步，因此可以指定同步特定文件或文件夹
   - 在本地分支下，执行：

```
git checkout -p origin/main <需要同步的文件名或文件夹名>
```

## 4.代码开发

- 用户在本地（集群或个人办公电脑）中进入克隆下来的项目文件夹，继续按照自己熟悉的开发流程和工具（VS、VSCode、Vim等）进行程序开发
- 这些工具通常也具有基于Git的版本管理功能，用户可在本地对自己开发的过程进行版本控制，流程与远程版本管理相同
- 代码发生改动后，本地git会自动追踪到所有变化，可通过：
  - 先用 `git add <发生改动文件名>` 或 `git add ./`暂存改动，前者只暂存特定文件，后者批量暂存当前路径下的所有改动
  - 然后 `git commit -m "<message>"` 确认改动，产生git的一个新版本，其中message是用户对该版本发生改动的简短说明和概括
  - 本地commit产生新版本后，即可以推送至远程服务器，也可累积多个本地commit后一起推送

## 5.提交合并

- 提交合并的流程分为两步：
- 先提交至远程服务器中的个人分支中，确认个人分支已被修改，并通过了自动化测试，常用的提交方式有：
  - 终端命令，`git push origin <远程分支名>`
  - VS，在“团队资源管理器”中点击“更改”确认发生的改动，然后点击“同步”，即可同步至服务器
  - VSCode，在“源代码管理”中点击“+”号将改动暂存，然后输入commit信息，点击“√”确认改动，最后点击同步按钮，即可同步至服务器
- 确认自动化测试没有问题，可在服务器页面发起向develop分支的“合并请求”，填写相应信息，“审核者”请选择“飞廉管理员”，等待管理员审核
- 可以将[pre-push](http://************:8858/images/1/1f/Pre-push.zip)文件拷贝至.git/hooks/路径下(需要下载并解压)，每次提交前会进行代码文件编码检查以及代码版本更新

## 6.自动化编译及测试

- 目前GitLab服务器端已经配置了CI/CD服务，进行自动化测试和部署，用户无需操作
- 本项目中的.gitlab-ci.yml文件定义了编译、测试、部署的内容和流程
- 用户提交代码至本项目后会自动启动编译和测试，并可在本页面左侧导航栏的“构建/流水线”中查看自动测试是否通过

## 7.文档编写

- 为便于文档管理，飞廉软件理论手册建议采用Markdown格式编写，其他文档暂时采用Word格式，以下对Markdown格式编写进行说明
  - 推荐Markdown格式文件编辑工具：VSCode或[Typora](http://************:8858/images/4/44/Typora.zip)，以下对Typora配置进行说明
  - 建议采用压缩包中“github.css”文件替换原始Github主题文件（主题文件夹路径：菜单栏“文件”>>“偏好设置”，选择“通用”>>“打开主题文件夹”），修改主题后可在Typora中查看文件结构及章节号
  - 修改图像路径（菜单栏“文件”>>“偏好设置”，选择“图像”），修改为“复制到指定路径”，路径框输入“./media”
  - 修改Markdown属性（菜单栏“文件”>>“偏好设置”，选择“Markdown”），全选“Markdown扩展语法”所有选项
- 飞廉软件代码采用Doxygen可形成网页版说明文档，以下对Doxygen文档形成进行说明
  - Windows系统[下载](http://************:8858/images/2/27/Doxygen.zip)并安装Doxygen和Graphviz，建议采用默认路径安装
  - 打开Doxygen软件，菜单栏"File" >> "Open"，读取配置文件doc/doxygen/Doxyfile
  - 窗口切换到"Run"，点击“Run doxygen”，在doc/doxygen文件夹中生成html格式说明文档
  - 在doc/doxygen/html文件夹中，打开index.html可浏览说明文档

## 8.代码文件编码

- 为兼容Linux系统和Windows系统下代码中的中文注释及中文打印信息等，飞廉软件源代码统一采用UTF-8 with BOM
- 对于Windows下通过Visual Studio新增文件，通常文件编码为GB2312，需要利用“文件”>>“高级保存选项”另存为 `Unicode (UTF-8带签名) --代码页65001`
- Visual Studio Code环境下可点击任务栏编码，选择 `通过编码保存`，然后点击 `UTF-8 with BOM`实现编码转换
- Linux系统下可实现采用命令行实现文件夹下所有文件由UTF-8转换为UTF-8 with BOM或UTF-8 with BOM转换为UTF-8
  - 以basic文件夹为例，命令为 `find basic/ -type f -name "*" -print | xargs -i sed -i '1 s/^/\xef\xbb\xbf&/' {}`
  - 以basic文件夹为例，命令为 `find basic/ -type f -name "*.txt" -print | xargs -i sed -i 's/^\xef\xbb\xbf//g' {}`

## 9.举例说明如何拉取气弹分支
```
# 首先拉取主分支
git clone http://172.16.115.4/feilian/feilian.git

# 切换到AeroElastic分支并拉取最新文件
cd feilian/
git switch AeroElastic
git pull

# 初始化submodule并下载文件
git submodule init
git submodule update

# 进入到submodule，切换分支，拉新
cd feilian-specialmodule/
git switch AeroElastic_specialmodule
git pull

# 进入submodule，切换分支，拉新
cd ../feilian-external/
git switch AeroElastic_external
git pull

# 查看
git status
git log
git branch -a
```

## 10.开发团队成员

李艳亮 @liyanliang		乔龙 @qiaolong	唐海龙 @tanghailong	曾凯 @zengkai

钱琛庚 @qianchengeng	尹强 @yinqiang  	孔名驰 @kongmingchi	石晓璐 @shixiaolu

张巨达 @zhangjuda	王恺祯 @wangkaizhen  	吴奥奇 @wuaoqi	郭承鹏 @wangkaizhen

## 11.版权声明

本软件由中航工业空气动力研究院 计算流体力学中心开发.
