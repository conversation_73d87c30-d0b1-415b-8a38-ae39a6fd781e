﻿#ifndef _particle_Prtcl_Insertion_
#define _particle_Prtcl_Insertion_

#include "basic/geometry/Geometry.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"
#include "feilian-specialmodule/particle/distribution/DistributionProperty.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{
/**
 * @brief 颗粒注入类
 * 
 */
class Insertion
{
public:
    // 颗粒注入对象
	Insertion(const Configure::Particle::ParticleConfigure &configure_, std::vector<Particle *> &particles_, DistributionProperty *distributionProperty_);

    // 注入颗粒
    bool InsertParticle(int &numPrtcl0, int &iter );

	// 获得注入颗粒编号列表
	const std::vector<int> &GetInsertedIDs()const { return this->insertedIDs; }

	// 设置当前颗粒数量
	void SetParticlesNumber(const int &numParticles_);

private:
    /// 生成颗粒位置
	Vector GetParticlePosition(const std::vector<Vector> &nodes);

    /// 判断生成颗粒是否与已有颗粒接触
    bool isInContact(Particle &particle0);

    /// 更新相关参数（颗粒速度、剩余数量等）
    void Calculate_AllParams();

private:
	const Configure::Particle::ParticleConfigure &configure; ///< 控制参数
	std::vector<Particle *> &particles;                      ///< 颗粒对象
	DistributionProperty *distributionProperty;              ///< 颗粒属性

    int numParticles;                        ///< 当前已注入颗粒数量
    int numParticlesLast;                    ///< 最后一次注入颗粒数量
    int numParticlesMax;                     ///< 需注入的最大颗粒数量 
    int totalSteps;                          ///< 注入步数
    
    int numParticlesInsertedNext;            ///< 下次注入颗粒数量
    int numIterationInsertedNext;            ///< 下次注入迭代次数

	int maxIteration;                        ///< 每次注入后的最大迭代次数
	int minIteration;                        ///< 每次注入后的最小迭代次数
	int minInsertion;                        ///< 每次最小注入颗粒数量

	std::vector<int> insertedIDs;            ///< 注入的颗粒编号
};

} // namespace Particle

#endif