
#ifndef BOOST_MPL_BITWISE_HPP_INCLUDED
#define BOOST_MPL_BITWISE_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2003-2004
// Copyright Jaap Suter 2003
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#include <boost/mpl/bitand.hpp>
#include <boost/mpl/bitor.hpp>
#include <boost/mpl/bitxor.hpp>
#include <boost/mpl/shift_left.hpp>
#include <boost/mpl/shift_right.hpp>

#endif // BOOST_MPL_BITWISE_HPP_INCLUDED
