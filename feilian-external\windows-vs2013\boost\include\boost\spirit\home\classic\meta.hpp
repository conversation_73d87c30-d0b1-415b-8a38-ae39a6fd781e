/*=============================================================================
    Copyright (c) 1998-2003 <PERSON>
    Copyright (c) 2001-2003 <PERSON><PERSON><PERSON>
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_META_MAIN_HPP)
#define BOOST_SPIRIT_META_MAIN_HPP

#include <boost/spirit/home/<USER>/version.hpp>

///////////////////////////////////////////////////////////////////////////////
//
//  Master header for Spirit.Meta
//
///////////////////////////////////////////////////////////////////////////////

#include <boost/spirit/home/<USER>/meta/fundamental.hpp>
#include <boost/spirit/home/<USER>/meta/parser_traits.hpp>
#include <boost/spirit/home/<USER>/meta/as_parser.hpp>
#include <boost/spirit/home/<USER>/meta/traverse.hpp>

#endif // BOOST_SPIRIT_CORE_MAIN_HPP

