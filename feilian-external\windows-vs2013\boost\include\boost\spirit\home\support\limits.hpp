//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_SUPPORT_LIMITS_MAR_26_2011_0833PM)
#define BOOST_SPIRIT_SUPPORT_LIMITS_MAR_26_2011_0833PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/include/phoenix_core.hpp>

#if !defined(SPIRIT_ARGUMENTS_LIMIT)
# define SPIRIT_ARGUMENTS_LIMIT BOOST_PHOENIX_LIMIT
#endif
#if !defined(SPIRIT_ATTRIBUTES_LIMIT)
# define SPIRIT_ATTRIBUTES_LIMIT BOOST_PHOENIX_LIMIT
#endif

#endif
