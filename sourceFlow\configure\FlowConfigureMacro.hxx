﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowConfigureMacro.hxx
//! <AUTHOR>
//! @brief 枚举跟字符串之间的关联容器map
//! @date 2021-04-07
//
//------------------------------修改日志----------------------------------------
// 2021-04-07 吴奥奇
//    说明：创建和编写
//            
//------------------------------------------------------------------------------

#ifndef _sourceFlow_configure_FlowConfigureMacro_hXX_
#define _sourceFlow_configure_FlowConfigureMacro_hXX_

#include "sourceFlow/configure/FlowConfigureMacro.h"
#include "sourceFlow/configure/FlowGlobalDataMacro.h"
#include "sourceFlow/material/Materials.h"

namespace Configure
{
namespace Flow
{
const std::map<std::string, Material::Flow::DensityType> materialTypeMap =
{
    { "IDEAL_GAS", Material::Flow::DensityType::IDEAL_GAS },
	{ "MULTISPECIES_GAS", Material::Flow::DensityType::MULTISPECIES_GAS },
	{ "REACTIVE_MULTISPECIES_GAS", Material::Flow::DensityType::REACTIVE_MULTISPECIES_GAS }
};

const std::map<Material::Flow::DensityType, std::string> materialTypeReverseMap =
{
    { Material::Flow::DensityType::IDEAL_GAS, "IDEAL_GAS" },
	{ Material::Flow::DensityType::MULTISPECIES_GAS, "MULTISPECIES_GAS" },
	{ Material::Flow::DensityType::REACTIVE_MULTISPECIES_GAS, "REACTIVE_MULTISPECIES_GAS" }
};

const std::map<std::string, Material::Flow::ViscosityType> viscosityTypeMap =
{
    { "CONSTANT", Material::Flow::ViscosityType::CONSTANT },
    { "SUTHERLAND", Material::Flow::ViscosityType::SUTHERLAND },
	{ "MIXTURE_AVERAGE", Material::Flow::ViscosityType::MIXTURE_AVERAGE }
};

const std::map<Material::Flow::ViscosityType, std::string> viscosityTypeReverseMap =
{
    { Material::Flow::ViscosityType::CONSTANT, "CONSTANT" },
    { Material::Flow::ViscosityType::SUTHERLAND, "SUTHERLAND" },
	{ Material::Flow::ViscosityType::MIXTURE_AVERAGE, "MIXTURE_AVERAGE" }
};

const std::map<std::string, Material::Flow::CpType> cpTypeMap =
{
	{ "CONSTANT", Material::Flow::CpType::cptConstant },
	{ "PIECEWISELINEAR", Material::Flow::CpType::cptPiecewiseLinear },
	{ "KINETICTHEORY", Material::Flow::CpType::cptKineticTheory },
	{ "NASAPOLYNOMIAL", Material::Flow::CpType::cpNASAPolynomial }
};

const std::map<Material::Flow::CpType, std::string> cpTypeReverseMap =
{
	{ Material::Flow::CpType::cptConstant, "CONSTANT" },
	{ Material::Flow::CpType::cptPiecewiseLinear, "PIECEWISELINEAR" },
	{ Material::Flow::CpType::cptKineticTheory, "KINETICTHEORY" },
	{ Material::Flow::CpType::cpNASAPolynomial, "NASAPOLYNOMIAL" }
};

const std::map<std::string, ReferenceType> referenceTypeMap =
{
    { "MACH_REYNOLDS", ReferenceType::MACH_REYNOLDS },
    { "MACH_DENSITY", ReferenceType::MACH_DENSITY },
	{ "MACH_ALTITUDE", ReferenceType::MACH_ALTITUDE },
    { "VELOCITY_PRESSURE", ReferenceType::VELOCITY_PRESSURE }
};

const std::map<ReferenceType, std::string> referenceTypeReverseMap =
{
    { ReferenceType::MACH_REYNOLDS, "MACH_REYNOLDS" },
    { ReferenceType::MACH_DENSITY, "MACH_DENSITY" },
	{ ReferenceType::MACH_ALTITUDE, "MACH_ALTITUDE" },
    { ReferenceType::VELOCITY_PRESSURE, "VELOCITY_PRESSURE" }
};

const std::map<std::string, Flux::ReconstructionOrder> reconstructOrderMap =
{
    { "FIRST", Flux::ReconstructionOrder::FIRST },
    { "SECOND", Flux::ReconstructionOrder::SECOND }
};

const std::map<Flux::ReconstructionOrder, std::string> reconstructOrderReverseMap =
{
    { Flux::ReconstructionOrder::FIRST, "FIRST" },
    { Flux::ReconstructionOrder::SECOND, "SECOND" }
};

const std::map<std::string, Flux::Flow::Limiter::Scheme> limiterMap =
{
    { "NONE_LIMITER", Flux::Flow::Limiter::Scheme::NONE_LIMITER },
    { "MINMOD", Flux::Flow::Limiter::Scheme::MINMOD },
    { "VANLEER", Flux::Flow::Limiter::Scheme::VANLEER },
    { "VENKATAKRISHNAN", Flux::Flow::Limiter::Scheme::VENKATAKRISHNAN }
};

const std::map<Flux::Flow::Limiter::Scheme, std::string> limiterReverseMap =
{
    { Flux::Flow::Limiter::Scheme::NONE_LIMITER, "NONE_LIMITER" },
    { Flux::Flow::Limiter::Scheme::MINMOD, "MINMOD" },
    { Flux::Flow::Limiter::Scheme::VANLEER, "VANLEER" },
    { Flux::Flow::Limiter::Scheme::VENKATAKRISHNAN, "VENKATAKRISHNAN" }
};

const std::map<std::string, Flux::Flow::Inviscid::Scheme> inviscidMap =
{
    { "CENTRAL", Flux::Flow::Inviscid::Scheme::CENTRAL },
    { "ROE", Flux::Flow::Inviscid::Scheme::ROE },
    { "LAX_FRIEDRICHS", Flux::Flow::Inviscid::Scheme::LAX_FRIEDRICHS },
    { "VANLEER", Flux::Flow::Inviscid::Scheme::VANLEER },
#if defined(_DevelopMode_)
    { "ROE_MOD", Flux::Flow::Inviscid::Scheme::ROE_MOD },
    { "AUSM", Flux::Flow::Inviscid::Scheme::AUSM },
#endif
    { "AUSMDV", Flux::Flow::Inviscid::Scheme::AUSMDV },
    { "AUSMPWP", Flux::Flow::Inviscid::Scheme::AUSMPWP },
    { "HLLC", Flux::Flow::Inviscid::Scheme::HLLC }
};

const std::map<Flux::Flow::Inviscid::Scheme, std::string> inviscidReverseMap =
{
    { Flux::Flow::Inviscid::Scheme::CENTRAL, "CENTRAL" },
    { Flux::Flow::Inviscid::Scheme::ROE, "ROE" },
    { Flux::Flow::Inviscid::Scheme::LAX_FRIEDRICHS, "LAX_FRIEDRICHS" },
    { Flux::Flow::Inviscid::Scheme::VANLEER, "VANLEER" },
#if defined(_DevelopMode_)
    { Flux::Flow::Inviscid::Scheme::ROE_MOD, "ROE_MOD" },
    { Flux::Flow::Inviscid::Scheme::AUSM, "AUSM" },
#endif
    { Flux::Flow::Inviscid::Scheme::AUSMDV, "AUSMDV" },
    { Flux::Flow::Inviscid::Scheme::AUSMPWP, "AUSMPWP" },
    { Flux::Flow::Inviscid::Scheme::HLLC, "HLLC" }
};

const std::map<std::string, Flux::Flow::Viscous::Scheme> viscousSchemeMap =
{
    { "NONE_VISCOUS", Flux::Flow::Viscous::Scheme::NONE_VISCOUS },
    { "CENTRAL_DISTANCE", Flux::Flow::Viscous::Scheme::CENTRAL_DISTANCE },
    { "CENTRAL_FULL", Flux::Flow::Viscous::Scheme::CENTRAL_FULL }
};

const std::map<Flux::Flow::Viscous::Scheme, std::string> viscousSchemeReverseMap =
{
    { Flux::Flow::Viscous::Scheme::NONE_VISCOUS, "NONE_VISCOUS" },
    { Flux::Flow::Viscous::Scheme::CENTRAL_DISTANCE, "CENTRAL_DISTANCE" },
    { Flux::Flow::Viscous::Scheme::CENTRAL_FULL, "CENTRAL_FULL" }
};

const std::map<std::string, Flux::Flow::Source::Scheme> sourceMap =
{
    { "NONE_SOURCE", Flux::Flow::Source::Scheme::NONE_SOURCE },
#if defined(_DevelopMode_)
    { "EXPLICIT", Flux::Flow::Source::Scheme::EXPLICIT },
    { "IMPLICIT", Flux::Flow::Source::Scheme::IMPLICIT }
#endif
};

const std::map<Flux::Flow::Source::Scheme, std::string> sourceReverseMap =
{
    { Flux::Flow::Source::Scheme::NONE_SOURCE, "NONE_SOURCE" },
#if defined(_DevelopMode_)
    { Flux::Flow::Source::Scheme::EXPLICIT, "EXPLICIT" },
    { Flux::Flow::Source::Scheme::IMPLICIT, "IMPLICIT" }
#endif
};

const std::map<std::string, Time::UnsteadyType> unsteadyTypeMap =
{
    { "STEADY", Time::UnsteadyType::STEADY },
    { "DUAL_TIME_STEP", Time::UnsteadyType::DUAL_TIME_STEP },
    { "GLOBAL_TIME_STEP",Time::UnsteadyType::GLOBAL_TIME_STEP}
};

const std::map<Time::UnsteadyType, std::string> unsteadyTypeReverseMap =
{
    { Time::UnsteadyType::STEADY, "STEADY" },
    { Time::UnsteadyType::DUAL_TIME_STEP, "DUAL_TIME_STEP" },
    { Time::UnsteadyType::GLOBAL_TIME_STEP,"GLOBAL_TIME_STEP"}
};

const std::map<std::string, Time::UnsteadyOrder> unsteadyOrderMap =
{
    { "FIRST", Time::UnsteadyOrder::FIRST },
    { "SECOND", Time::UnsteadyOrder::SECOND }
};

const std::map<Time::UnsteadyOrder, std::string> unsteadyOrderReverseMap =
{
    { Time::UnsteadyOrder::FIRST, "FIRST" },
    { Time::UnsteadyOrder::SECOND, "SECOND" }
};

const std::map<std::string, Time::Scheme> timeSchemeMap =
{
    { "RUNGE_KUTTA", Time::Scheme::RUNGE_KUTTA },
    { "LUSGS", Time::Scheme::LUSGS },
    { "DPLUR", Time::Scheme::DPLUR },
    { "GMRES", Time::Scheme::GMRES},
#if defined(_EnablePETSC_)
    { "CG", Time::Scheme::CG},
    { "BICG", Time::Scheme::BICG}
#endif
};

const std::map<Time::Scheme, std::string> timeSchemeReverseMap =
{
    { Time::Scheme::RUNGE_KUTTA, "RUNGE_KUTTA" },
    { Time::Scheme::LUSGS, "LUSGS" },
    { Time::Scheme::DPLUR, "DPLUR" },
	{ Time::Scheme::GMRES, "GMRES" },
#if defined(_EnablePETSC_)
	{ Time::Scheme::CG, "CG" },
	{ Time::Scheme::BICG, "BICG" }
#endif
};

const std::map<std::string, Time::RungeKuttaType> RKTypeMap =
{
    { "STANDARD", Time::RungeKuttaType::STANDARD },
    { "HYBRID", Time::RungeKuttaType::HYBRID }
};

const std::map<Time::RungeKuttaType, std::string> RKTypeReverseMap =
{
    { Time::RungeKuttaType::STANDARD, "STANDARD" },
    { Time::RungeKuttaType::HYBRID, "HYBRID" }
};

const std::map<std::string, Time::LinearSolverType> linearSolverTypeMap =
{
    { "PRIVATE", Time::LinearSolverType::PRIVATE },
#if defined(_EnablePETSC_)
    { "PETSC", Time::LinearSolverType::PETSC }
#endif
};

const std::map<Time::LinearSolverType, std::string> linearSolverTypeReverseMap =
{
    { Time::LinearSolverType::PRIVATE, "PRIVATE" },
#if defined(_EnablePETSC_)
    { Time::LinearSolverType::PETSC, "PETSC" }
#endif
};

const std::map<std::string, Time::LinearSolverPreconditionerType> preconditionerTypeMap =
{
    { "JACOBI", Time::LinearSolverPreconditionerType::JACOBI },
    { "ILU", Time::LinearSolverPreconditionerType::ILU }
};

const std::map<Time::LinearSolverPreconditionerType, std::string> preconditionerTypeReverseMap =
{
    { Time::LinearSolverPreconditionerType::JACOBI, "JACOBI" },
    { Time::LinearSolverPreconditionerType::ILU, "ILU" }
};

const std::map<std::string, Turbulence::Model> turbulenceModelMap =
{
    { "INVISCID", Turbulence::Model::INVISCID },
    { "LAMINAR", Turbulence::Model::LAMINAR },

    { "SPALART_ALLMARAS", Turbulence::Model::SPALART_ALLMARAS },
    { "MENTER_SST", Turbulence::Model::MENTER_SST },
    { "K_EPSILON", Turbulence::Model::K_EPSILON },
    { "MENTER_SST_ML", Turbulence::Model::MENTER_SST_ML },
    { "K_OMEGA_V2", Turbulence::Model::K_OMEGA_V2 },

    { "MENTER_SST_GAMMARE", Turbulence::Model::MENTER_SST_GAMMARE },
	{ "SPALART_ALLMARAS_BC", Turbulence::Model::SPALART_ALLMARAS_BC },

    { "SPALART_ALLMARAS_DES",  Turbulence::Model::SPALART_ALLMARAS_DES },
    { "SPALART_ALLMARAS_DDES", Turbulence::Model::SPALART_ALLMARAS_DDES },
	{ "MENTER_SST_DES", Turbulence::Model::MENTER_SST_DES },
    { "MENTER_SST_DDES", Turbulence::Model::MENTER_SST_DDES }
};

const std::map<Turbulence::Model, std::string> turbulenceModelReverseMap =
{
    { Turbulence::Model::INVISCID, "INVISCID" },
    { Turbulence::Model::LAMINAR, "LAMINAR" },

    { Turbulence::Model::SPALART_ALLMARAS, "SPALART_ALLMARAS" },
    { Turbulence::Model::MENTER_SST, "MENTER_SST" },
	{ Turbulence::Model::K_EPSILON, "K_EPSILON" },
    { Turbulence::Model::K_OMEGA_V2, "K_OMEGA_V2" },

    { Turbulence::Model::MENTER_SST_GAMMARE, "MENTER_SST_GAMMARE" },
    { Turbulence::Model::MENTER_SST_ML, "MENTER_SST_ML" },
	{ Turbulence::Model::SPALART_ALLMARAS_BC, "SPALART_ALLMARAS_BC" },

    { Turbulence::Model::SPALART_ALLMARAS_DES,  "SPALART_ALLMARAS_DES" },
    { Turbulence::Model::SPALART_ALLMARAS_DDES, "SPALART_ALLMARAS_DDES" },
    { Turbulence::Model::MENTER_SST_DES, "MENTERSST_DES" },
    { Turbulence::Model::MENTER_SST_DDES, "MENTERSST_DDES" }
};

const std::map<FlowMacro::Scalar, std::string> turbulenceVariableNameReverseMap =
{
    { FlowMacro::Scalar::NUT, "NUT" },
    { FlowMacro::Scalar::K, "K" },
    { FlowMacro::Scalar::OMEGA, "OMEGA" },
    { FlowMacro::Scalar::EPSILON, "EPSILON" },
	{ FlowMacro::Scalar::GAMMA, "GAMMA" },
	{ FlowMacro::Scalar::ReThet, "ReThet" },
    { FlowMacro::Scalar::V2BAR, "V2BAR" },

    { FlowMacro::Scalar::NUT0, "NUT0" },
    { FlowMacro::Scalar::K0, "K0" },
    { FlowMacro::Scalar::OMEGA0, "OMEGA0" },
    { FlowMacro::Scalar::EPSILON0, "EPSILON0" },
	{ FlowMacro::Scalar::GAMMA0, "GAMMA0" },
	{ FlowMacro::Scalar::ReThet0, "ReThet0" },
    { FlowMacro::Scalar::V2BAR0, "V2BAR0" },

    { FlowMacro::Scalar::NUT00, "NUT00" },
    { FlowMacro::Scalar::K00, "K00" },
    { FlowMacro::Scalar::OMEGA00, "OMEGA00" },
    { FlowMacro::Scalar::EPSILON00, "EPSILON00" },
	{ FlowMacro::Scalar::GAMMA00, "GAMMA00" },
	{ FlowMacro::Scalar::ReThet00, "ReThet00" },
    { FlowMacro::Scalar::V2BAR00, "V2BAR00" }
};

const std::map<Turbulence::Model, std::vector<FlowMacro::Scalar>> turbulenceVariableMap =
{
    { Turbulence::Model::INVISCID, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::LAMINAR, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::SPALART_ALLMARAS, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT} },
	{ Turbulence::Model::SPALART_ALLMARAS_BC, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT} },
    { Turbulence::Model::MENTER_SST, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA} },
    { Turbulence::Model::MENTER_BSL, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA} },
    { Turbulence::Model::MENTER_SST_ML, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA} },
    { Turbulence::Model::K_OMEGA_V2, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA,
                                                                    FlowMacro::Scalar::V2BAR} },
	{ Turbulence::Model::MENTER_SST_GAMMARE, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA,
	                                                                        FlowMacro::Scalar::GAMMA, FlowMacro::Scalar::ReThet} },
    { Turbulence::Model::K_EPSILON, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::EPSILON} },
    { Turbulence::Model::K_OMEGA, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA} },
    { Turbulence::Model::SPALART_ALLMARAS_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT} },
    { Turbulence::Model::SPALART_ALLMARAS_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT} },
    { Turbulence::Model::MENTER_SST_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA} },
    { Turbulence::Model::MENTER_SST_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K, FlowMacro::Scalar::OMEGA} }
};

const std::map<Turbulence::Model, std::vector<FlowMacro::Scalar>> turbulenceVariable0Map =
{
    { Turbulence::Model::INVISCID, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::LAMINAR, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::SPALART_ALLMARAS, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT0} },
	{ Turbulence::Model::SPALART_ALLMARAS_BC, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT0} },
    { Turbulence::Model::MENTER_SST, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0} },
    { Turbulence::Model::MENTER_BSL, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0} },
    { Turbulence::Model::MENTER_SST_ML, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0} },
    { Turbulence::Model::K_OMEGA_V2, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0,
                                                                    FlowMacro::Scalar::V2BAR0} },
	{ Turbulence::Model::MENTER_SST_GAMMARE, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0, 
	                                                                        FlowMacro::Scalar::GAMMA0, FlowMacro::Scalar::ReThet0} },
    { Turbulence::Model::K_EPSILON, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::EPSILON0} },
    { Turbulence::Model::K_OMEGA, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0} },
    { Turbulence::Model::SPALART_ALLMARAS_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT0} },
    { Turbulence::Model::SPALART_ALLMARAS_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT0} },
    { Turbulence::Model::MENTER_SST_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0} },
    { Turbulence::Model::MENTER_SST_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K0, FlowMacro::Scalar::OMEGA0} }
};

const std::map<Turbulence::Model, std::vector<FlowMacro::Scalar>> turbulenceVariable00Map =
{
    { Turbulence::Model::INVISCID, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::LAMINAR, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::SPALART_ALLMARAS, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT00} },
	{ Turbulence::Model::SPALART_ALLMARAS_BC, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT00} },
    { Turbulence::Model::MENTER_SST, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00} },
    { Turbulence::Model::MENTER_BSL, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00} },
    { Turbulence::Model::MENTER_SST_ML, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00} },
    { Turbulence::Model::K_OMEGA_V2, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00,
                                                                    FlowMacro::Scalar::V2BAR00} },
    { Turbulence::Model::MENTER_SST_GAMMARE, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00, FlowMacro::Scalar::GAMMA00, FlowMacro::Scalar::ReThet00} },
    { Turbulence::Model::K_EPSILON, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::EPSILON00} },
    { Turbulence::Model::K_OMEGA, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00} },
    { Turbulence::Model::SPALART_ALLMARAS_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT00} },
    { Turbulence::Model::SPALART_ALLMARAS_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::NUT00} },
    { Turbulence::Model::MENTER_SST_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00} },
    { Turbulence::Model::MENTER_SST_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::K00, FlowMacro::Scalar::OMEGA00} }
};

const std::map<FlowMacro::Scalar, std::string> turbulenceResidualNameMap =
{
    { FlowMacro::Scalar::RESIDUAL_NUT, "RESIDUAL_NUT" },
    { FlowMacro::Scalar::RESIDUAL_K, "RESIDUAL_K" },
    { FlowMacro::Scalar::RESIDUAL_OMEGA, "RESIDUAL_OMEGA" },
    { FlowMacro::Scalar::RESIDUAL_EPSILON, "RESIDUAL_EPSILON" },
    { FlowMacro::Scalar::RESIDUAL_V2BAR, "RESIDUAL_V2BAR" },
	{ FlowMacro::Scalar::RESIDUAL_GAMMA, "RESIDUAL_GAMMA" },
	{ FlowMacro::Scalar::RESIDUAL_ReThet, "RESIDUAL_ReThet" }
};

const std::map<Turbulence::Model, std::vector<FlowMacro::Scalar>> turbulenceResidualMap =
{
    { Turbulence::Model::INVISCID, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::LAMINAR, std::vector<FlowMacro::Scalar>{} },
    { Turbulence::Model::SPALART_ALLMARAS, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_NUT} },
	{ Turbulence::Model::SPALART_ALLMARAS_BC, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_NUT} },
    { Turbulence::Model::MENTER_SST, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA} },
    { Turbulence::Model::MENTER_BSL, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA} },
    { Turbulence::Model::MENTER_SST_ML, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA} },
    { Turbulence::Model::K_OMEGA_V2, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA, FlowMacro::Scalar::RESIDUAL_V2BAR} },
    { Turbulence::Model::MENTER_SST_GAMMARE, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA, FlowMacro::Scalar::RESIDUAL_GAMMA, FlowMacro::Scalar::RESIDUAL_ReThet} },
    { Turbulence::Model::K_EPSILON, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_EPSILON} },
    { Turbulence::Model::K_OMEGA, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA} },
    { Turbulence::Model::SPALART_ALLMARAS_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_NUT} },
    { Turbulence::Model::SPALART_ALLMARAS_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_NUT} },
    { Turbulence::Model::MENTER_SST_DES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA} },
    { Turbulence::Model::MENTER_SST_DDES, std::vector<FlowMacro::Scalar>{FlowMacro::Scalar::RESIDUAL_K, FlowMacro::Scalar::RESIDUAL_OMEGA} }
};

const std::map<FlowMacro::Vector, std::string> turbulenceGradientNameMap =
{
    { FlowMacro::Vector::GRADIENT_NUT, "GRADIENT_NUT" },
    { FlowMacro::Vector::GRADIENT_K, "GRADIENT_K" },
    { FlowMacro::Vector::GRADIENT_OMEGA, "GRADIENT_OMEGA" },
    { FlowMacro::Vector::GRADIENT_EPSILON, "GRADIENT_EPSILON" },
    { FlowMacro::Vector::GRADIENT_V2BAR, "GRADIENT_V2BAR" },
	{ FlowMacro::Vector::GRADIENT_GAMMA, "GRADIENT_GAMMA" },
	{ FlowMacro::Vector::GRADIENT_ReThet, "GRADIENT_ReThet" }
};

const std::map<Turbulence::Model, std::vector<FlowMacro::Vector>> turbulenceGradientMap =
{
    { Turbulence::Model::INVISCID, std::vector<FlowMacro::Vector>{} },
    { Turbulence::Model::LAMINAR, std::vector<FlowMacro::Vector>{} },
    { Turbulence::Model::SPALART_ALLMARAS, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_NUT} },
	{ Turbulence::Model::SPALART_ALLMARAS_BC, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_NUT} },
    { Turbulence::Model::MENTER_SST, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA} },
    { Turbulence::Model::MENTER_BSL, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA} },
    { Turbulence::Model::MENTER_SST_ML, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA} },
    { Turbulence::Model::K_OMEGA_V2, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA, FlowMacro::Vector::GRADIENT_V2BAR} },
    { Turbulence::Model::MENTER_SST_GAMMARE, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA, FlowMacro::Vector::GRADIENT_GAMMA, FlowMacro::Vector::GRADIENT_ReThet} },
    { Turbulence::Model::K_EPSILON, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_EPSILON} },
    { Turbulence::Model::K_OMEGA, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA} },
    { Turbulence::Model::SPALART_ALLMARAS_DES, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_NUT} },
    { Turbulence::Model::SPALART_ALLMARAS_DDES, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_NUT} },
	{ Turbulence::Model::MENTER_SST_DES, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA} },
    { Turbulence::Model::MENTER_SST_DDES, std::vector<FlowMacro::Vector>{FlowMacro::Vector::GRADIENT_K, FlowMacro::Vector::GRADIENT_OMEGA} }
};

const std::map<FlowMacro::Scalar, std::string> turbulenceMonitorResidualMap =
{
    { FlowMacro::Scalar::NUT, "ResNut" },
    { FlowMacro::Scalar::K, "ResK" },
    { FlowMacro::Scalar::OMEGA, "ResW" },
    { FlowMacro::Scalar::V2BAR, "ResV2BAR" },
    { FlowMacro::Scalar::EPSILON, "ResE" },
	{ FlowMacro::Scalar::GAMMA, "ResGa" },
	{ FlowMacro::Scalar::ReThet, "ResRe" }
};

const std::map<std::string, Turbulence::WallFunction> wallFunctionMap =
{
    { "NONE_WALL_FUNCTION", Turbulence::WallFunction::NONE_WALL_FUNCTION },
#if defined(_DevelopMode_)
    { "ENHANCED", Turbulence::WallFunction::ENHANCED }
#endif
};

const std::map<Turbulence::WallFunction, std::string> wallFunctionReverseMap =
{
    { Turbulence::WallFunction::NONE_WALL_FUNCTION, "NONE_WALL_FUNCTION" },
#if defined(_DevelopMode_)
    { Turbulence::WallFunction::ENHANCED, "ENHANCED" }
#endif
};

const std::map<std::string, Initialization::Type> initializationMap =
{
    { "REFERENCE", Initialization::Type::REFERENCE },
    { "STATIC_FLOW", Initialization::Type::STATIC_FLOW },
    { "RESTART", Initialization::Type::RESTART },
    { "FILE", Initialization::Type::FILE }
};

const std::map<Initialization::Type, std::string> initializationReverseMap =
{
    { Initialization::Type::REFERENCE, "REFERENCE" },
    { Initialization::Type::STATIC_FLOW, "STATIC_FLOW" },
    { Initialization::Type::RESTART, "RESTART" },
    { Initialization::Type::FILE, "FILE" }
};

} // namespace Flow
} // namespace Configure

#endif
