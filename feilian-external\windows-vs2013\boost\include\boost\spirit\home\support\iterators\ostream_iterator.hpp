//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_OSTREAM_ITERATOR_JAN_13_2010_0211PM)
#define BOOST_SPIRIT_OSTREAM_ITERATOR_JAN_13_2010_0211PM

#include <boost/spirit/home/<USER>/stream/ostream_iterator.hpp>

namespace boost { namespace spirit 
{
    typedef karma::ostream_iterator<char, char> ostream_iterator;
}}

#endif
