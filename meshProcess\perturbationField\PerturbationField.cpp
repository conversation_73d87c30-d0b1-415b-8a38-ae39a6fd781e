////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file PerturbationField.cpp
//! <AUTHOR>
//! @brief 摄动场数据结构实现.
//! @date 2024-12-20
//
//------------------------------修改日志----------------------------------------
// 2024-12-20 李艳亮、乔龙
//     说明：建立并规范化
//
//------------------------------------------------------------------------------

#include "meshProcess/perturbationField/PerturbationField.h"
#include "basic/common/IO.h"
#include <fstream>
#include <sstream>

using namespace PerturbationField;

PerturbationFieldManager::PerturbationFieldManager(Mesh *mesh_)
    : mesh(mesh_), numModes(0), nodeNumber(0), binaryFormat(true), initialized(false)
{
    if (mesh != nullptr)
    {
        nodeNumber = mesh->GetNodeNumber();
    }
}

PerturbationFieldManager::~PerturbationFieldManager()
{
    for (auto &modalData : modalPerturbationData)
    {
        modalData.Clear();
    }
    modalPerturbationData.clear();
}

void PerturbationFieldManager::Initialize(int numModes_, const std::string &perturbationPath_, 
                                        const std::vector<std::string> &fileNames_, bool binaryFormat_)
{
    numModes = numModes_;
    perturbationPath = perturbationPath_;
    fileNames = fileNames_;
    binaryFormat = binaryFormat_;
    
    // 初始化模态摄动场数据容器
    modalPerturbationData.resize(numModes);
    for (int i = 0; i < numModes; i++)
    {
        modalPerturbationData[i].Initialize(i, nodeNumber, fileNames[i]);
    }
    
    initialized = true;
    
    if (GetMPIRank() == 0)
    {
        Print("摄动场管理器初始化完成：");
        Print("  模态数量: " + ToString(numModes));
        Print("  节点数量: " + ToString(nodeNumber));
        Print("  文件路径: " + perturbationPath);
        Print("  文件格式: " + (binaryFormat ? std::string("二进制") : std::string("ASCII")));
    }
}

int PerturbationFieldManager::LoadAllPerturbationFields()
{
    if (!initialized)
    {
        FatalError("PerturbationFieldManager::LoadAllPerturbationFields: 管理器未初始化");
        return -1;
    }
    
    if (GetMPIRank() == 0) Print("开始加载所有摄动场数据...");
    
    for (int i = 0; i < numModes; i++)
    {
        int result = LoadPerturbationField(i);
        if (result != 0)
        {
            FatalError("加载模态 " + ToString(i) + " 摄动场数据失败");
            return result;
        }
    }
    
    if (GetMPIRank() == 0) Print("所有摄动场数据加载完成");
    return 0;
}

int PerturbationFieldManager::LoadPerturbationField(int modeIndex)
{
    if (modeIndex < 0 || modeIndex >= numModes)
    {
        FatalError("PerturbationFieldManager::LoadPerturbationField: 模态索引超出范围");
        return -1;
    }
    
    auto &modalData = modalPerturbationData[modeIndex];
    std::string fullFileName = perturbationPath + modalData.fileName;
    
    if (GetMPIRank() == 0) Print("加载模态 " + ToString(modeIndex) + " 摄动场: " + fullFileName);
    
    int result;
    if (binaryFormat)
    {
        result = ReadPerturbationFieldBinary(fullFileName, modalData);
    }
    else
    {
        result = ReadPerturbationFieldASCII(fullFileName, modalData);
    }
    
    if (result == 0)
    {
        modalData.isLoaded = true;
        if (GetMPIRank() == 0) Print("模态 " + ToString(modeIndex) + " 摄动场加载成功");
    }
    
    return result;
}

int PerturbationFieldManager::ReadPerturbationFieldASCII(const std::string &fileName, ModalPerturbationData &modalData)
{
    std::ifstream file(fileName);
    if (!file.is_open())
    {
        FatalError("无法打开摄动场文件: " + fileName);
        return -1;
    }
    
    std::string line;
    int lineCount = 0;
    int nodeCount = 0;
    
    while (std::getline(file, line) && nodeCount < nodeNumber)
    {
        lineCount++;
        
        // 跳过注释行和空行
        if (line.empty() || line[0] == '#' || line[0] == '!')
            continue;
        
        std::istringstream iss(line);
        int nodeID;
        Scalar dx, dy, dz;
        
        if (!(iss >> nodeID >> dx >> dy >> dz))
        {
            FatalError("摄动场文件格式错误，行 " + ToString(lineCount) + ": " + fileName);
            file.close();
            return -2;
        }
        
        // 验证节点ID
        if (nodeID < 0 || nodeID >= nodeNumber)
        {
            FatalError("摄动场文件中节点ID超出范围，行 " + ToString(lineCount) + ": " + fileName);
            file.close();
            return -3;
        }
        
        modalData.SetNodeDisplacement(nodeID, Vector(dx, dy, dz));
        nodeCount++;
    }
    
    file.close();
    
    if (nodeCount != nodeNumber)
    {
        FatalError("摄动场文件节点数量不匹配: 期望 " + ToString(nodeNumber) + 
                  ", 实际 " + ToString(nodeCount) + " in " + fileName);
        return -4;
    }
    
    return 0;
}

int PerturbationFieldManager::ReadPerturbationFieldBinary(const std::string &fileName, ModalPerturbationData &modalData)
{
    std::ifstream file(fileName, std::ios::binary);
    if (!file.is_open())
    {
        FatalError("无法打开摄动场文件: " + fileName);
        return -1;
    }
    
    // 读取节点数量（用于验证）
    int fileNodeNumber;
    file.read(reinterpret_cast<char*>(&fileNodeNumber), sizeof(int));
    
    if (fileNodeNumber != nodeNumber)
    {
        FatalError("摄动场文件节点数量不匹配: 期望 " + ToString(nodeNumber) + 
                  ", 文件中 " + ToString(fileNodeNumber) + " in " + fileName);
        file.close();
        return -1;
    }
    
    // 读取摄动场数据
    for (int i = 0; i < nodeNumber; i++)
    {
        Scalar dx, dy, dz;
        file.read(reinterpret_cast<char*>(&dx), sizeof(Scalar));
        file.read(reinterpret_cast<char*>(&dy), sizeof(Scalar));
        file.read(reinterpret_cast<char*>(&dz), sizeof(Scalar));
        
        modalData.SetNodeDisplacement(i, Vector(dx, dy, dz));
    }
    
    file.close();
    
    if (file.fail())
    {
        FatalError("读取摄动场文件时发生错误: " + fileName);
        return -2;
    }
    
    return 0;
}

bool PerturbationFieldManager::ValidatePerturbationFields()
{
    if (!initialized)
    {
        if (GetMPIRank() == 0) Print("警告: 摄动场管理器未初始化");
        return false;
    }
    
    if (GetMPIRank() == 0) Print("开始验证摄动场数据...");
    
    bool allValid = true;
    for (int i = 0; i < numModes; i++)
    {
        if (!ValidateModalData(i))
        {
            allValid = false;
        }
    }
    
    if (GetMPIRank() == 0)
    {
        if (allValid)
            Print("摄动场数据验证通过");
        else
            Print("摄动场数据验证失败");
    }
    
    return allValid;
}

bool PerturbationFieldManager::ValidateModalData(int modeIndex)
{
    if (modeIndex < 0 || modeIndex >= numModes)
        return false;
    
    const auto &modalData = modalPerturbationData[modeIndex];
    
    if (!modalData.isLoaded)
    {
        if (GetMPIRank() == 0) Print("警告: 模态 " + ToString(modeIndex) + " 数据未加载");
        return false;
    }
    
    // 检查数据范围合理性
    Scalar maxDisplacement = 0.0;
    for (int i = 0; i < nodeNumber; i++)
    {
        const Vector &disp = modalData.GetNodeDisplacement(i);
        Scalar magnitude = disp.Magnitude();
        maxDisplacement = Max(maxDisplacement, magnitude);
    }
    
    // 简单的合理性检查：位移不应该过大
    const Scalar maxReasonableDisplacement = 1000.0; // 可根据实际情况调整
    if (maxDisplacement > maxReasonableDisplacement)
    {
        if (GetMPIRank() == 0) 
            Print("警告: 模态 " + ToString(modeIndex) + " 最大位移过大: " + ToString(maxDisplacement));
        return false;
    }
    
    return true;
}

const ModalPerturbationData &PerturbationFieldManager::GetModalPerturbationData(int modeIndex) const
{
    static ModalPerturbationData emptyData;
    
    if (modeIndex >= 0 && modeIndex < numModes)
        return modalPerturbationData[modeIndex];
    else
        return emptyData;
}

const Vector &PerturbationFieldManager::GetNodePerturbationDisplacement(int modeIndex, int nodeID) const
{
    if (modeIndex >= 0 && modeIndex < numModes)
        return modalPerturbationData[modeIndex].GetNodeDisplacement(nodeID);
    else
        return Vector0;
}

void PerturbationFieldManager::ComputeModalSuperposition(const std::vector<Scalar> &modalAmplitudes,
                                                       std::vector<Vector> &nodeDisplacements)
{
    if (modalAmplitudes.size() != numModes)
    {
        FatalError("PerturbationFieldManager::ComputeModalSuperposition: 模态幅值数组大小不匹配");
        return;
    }

    nodeDisplacements.resize(nodeNumber, Vector0);

    // 模态叠加: u = Σ φᵢ * qᵢ
    for (int nodeID = 0; nodeID < nodeNumber; nodeID++)
    {
        nodeDisplacements[nodeID] = Vector0;

        for (int modeIndex = 0; modeIndex < numModes; modeIndex++)
        {
            if (modalPerturbationData[modeIndex].isLoaded)
            {
                const Vector &modalDisp = modalPerturbationData[modeIndex].GetNodeDisplacement(nodeID);
                nodeDisplacements[nodeID] += modalDisp * modalAmplitudes[modeIndex];
            }
        }
    }
}

void PerturbationFieldManager::OutputPartitionedPerturbationFields(const std::string &outputPath,
                                                                  const std::string &caseName,
                                                                  int partitionID,
                                                                  const std::vector<int> &nodeMapping)
{
    if (!initialized)
    {
        FatalError("PerturbationFieldManager::OutputPartitionedPerturbationFields: 管理器未初始化");
        return;
    }

    for (int modeIndex = 0; modeIndex < numModes; modeIndex++)
    {
        if (!modalPerturbationData[modeIndex].isLoaded)
            continue;

        std::string fileName = outputPath + caseName + "_perturbation_mode" + ToString(modeIndex) +
                              "_part" + ToString(partitionID) + ".dat";

        std::ofstream file;
        if (binaryFormat)
        {
            file.open(fileName, std::ios::binary);

            // 写入分区节点数量
            int partitionNodeNumber = nodeMapping.size();
            file.write(reinterpret_cast<const char*>(&partitionNodeNumber), sizeof(int));

            // 写入摄动场数据
            for (int localNodeID = 0; localNodeID < partitionNodeNumber; localNodeID++)
            {
                int globalNodeID = nodeMapping[localNodeID];
                const Vector &disp = modalPerturbationData[modeIndex].GetNodeDisplacement(globalNodeID);

                Scalar dx = disp.X(), dy = disp.Y(), dz = disp.Z();
                file.write(reinterpret_cast<const char*>(&dx), sizeof(Scalar));
                file.write(reinterpret_cast<const char*>(&dy), sizeof(Scalar));
                file.write(reinterpret_cast<const char*>(&dz), sizeof(Scalar));
            }
        }
        else
        {
            file.open(fileName);
            file << "# 摄动场数据 - 模态 " << modeIndex << " 分区 " << partitionID << std::endl;
            file << "# 节点ID  位移X  位移Y  位移Z" << std::endl;

            for (int localNodeID = 0; localNodeID < nodeMapping.size(); localNodeID++)
            {
                int globalNodeID = nodeMapping[localNodeID];
                const Vector &disp = modalPerturbationData[modeIndex].GetNodeDisplacement(globalNodeID);

                file << localNodeID << " " << disp.X() << " " << disp.Y() << " " << disp.Z() << std::endl;
            }
        }

        file.close();
    }
}
