 #pragma once
#include "MASTER.h"
#include "GLOBAL.h"
#include "basicTypes.h"
#include "fileStuff.h"
#include "FileWriterInterface.h"
#include "LightweightVector.h"
#include "stringformat.h"
inline void getBitIndexAndOffset(size_t& ind, size_t& ___2865, size_t ___2085) { ind = ___2085 / 8; ___2865 = ___2085 - ind * 8; } template <typename T> inline void setBit(T& uInt8Array, size_t ___2085) { size_t ind; size_t ___2865; getBitIndexAndOffset(ind, ___2865, ___2085); uInt8Array[ind] |= 1 << ___2865; } template <typename T> inline void clearBit(T& uInt8Array, size_t ___2085) { size_t ind; size_t ___2865; getBitIndexAndOffset(ind, ___2865, ___2085); uInt8Array[ind] &= ~(1 << ___2865); } template <typename T> inline uint8_t bitValue(T const& uInt8Array, size_t ___2085) { size_t ind; size_t ___2865; getBitIndexAndOffset(ind, ___2865, ___2085); return (uInt8Array[ind] & (1 << ___2865)); } template <typename T> inline bool inBitSet(T const& uInt8Array, size_t ___2085) { size_t ind; size_t ___2865; getBitIndexAndOffset(ind, ___2865, ___2085); return ((uInt8Array[ind] & (uint8_t(0x1) << ___2865)) != 0); } template <typename T> inline void addToBitSet(T& uInt8Array, size_t ___2085) { size_t ind; size_t ___2865; getBitIndexAndOffset(ind, ___2865, ___2085); uInt8Array[ind] |= (uint8_t(0x1) << ___2865); } template <typename T> inline void removeFromBitSet(T& uInt8Array, size_t ___2085) { size_t ind; size_t ___2865; getBitIndexAndOffset(ind, ___2865, ___2085); uInt8Array[ind] &= (uint8_t(0xFF) - (uint8_t(0x1) << ___2865)); } namespace tecplot { namespace ___3933 { ___81 const ___2745 = BAD_ANY_INDEX; static size_t const ___946 = 10; template <typename T, bool ___2025, int base> ___372 encodeAsciiValue(char *str, int ___418, T const& ___4298); template <typename T, bool ___2025, int base> ___372 ___4563(FileWriterInterface& file, char const*          ___972, ___81           ___1251, size_t               ___2797, T const*             ___4299, size_t               ___4334 = ___946); template <typename OutType> ___372 ___4528(FileWriterInterface&        file, char const*                 ___972, ___81                  ___1251, size_t                      ___2797, ___2479 const*               ___4299, size_t                      ___4334 = ___946 / 2); template <typename T, bool ___2025, int base> ___372 writeValue( FileWriterInterface& file, char const*          ___972, T const&             ___4298) { return ___4563<T, ___2025, base>(file, ___972, ___2745, 1, &___4298); } inline ___372 ___4544( FileWriterInterface& file, char const*          ___972, char const*          ___4298) { ___372 ___2039 = ___4226; REQUIRE(file.___2041()); REQUIRE(VALID_REF(___972) && strlen(___972) < ___206); REQUIRE(VALID_REF(___4298)); if (file.___2002()) ___2039 = (file.fprintf("%*s  %s\r\n", -___206, ___972, ___4298) > 0); else ___2039 = (file.fwrite(___4298, 1, strlen(___4298)) == strlen(___4298)); ENSURE(VALID_BOOLEAN(___2039)); return ___2039; } template <typename T, bool ___2025> uint64_t arrayValueSizeInFile(bool ___2002); template <typename T, bool ___2025> uint64_t arraySizeInFile(size_t ___2797, bool ___2002); template <typename T, bool ___2025> uint64_t valueSizeInFile(bool ___2002); }}
