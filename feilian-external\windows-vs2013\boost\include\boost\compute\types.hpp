//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_TYPES_HPP
#define BOOST_COMPUTE_TYPES_HPP

/// \file
///
/// Meta-header to include all Boost.Compute types headers.

#include <boost/compute/types/complex.hpp>
#include <boost/compute/types/fundamental.hpp>
#include <boost/compute/types/pair.hpp>
#include <boost/compute/types/struct.hpp>
#include <boost/compute/types/tuple.hpp>

#endif // BOOST_COMPUTE_TYPES_HPP
