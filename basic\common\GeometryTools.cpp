﻿#include "basic/common/GeometryTools.h"

#include <cstdlib>
#include <cmath>
#include <iostream>

#include "basic/common/Tensor.h"

//Calculating center point and length of a line segment.
void CalculateLineSegment
(
    const Vector& P1,
    const Vector& P2,
    Vector& center,
    Vector& length
)
{
    center = (P1 + P2) / 2.0;
    length = (P2 - P1) ^ Vector(0, 0, 1);
}

//Calculating center point and area of a triangle.
void CalculateTriangle
(
    const Vector& P1,
    const Vector& P2,
    const Vector& P3,
    Vector& center,
    Vector& area
)
{
    center = (P1 + P2 + P3) / 3.0;
    area = ((P2 - P1) ^ (P3 - P1)) / 2;
}

//Calculating center point and area of a polygon.
//it also supports:
//(1) line segment, if 2 vertices are provided;
//(2) triangle, if 3 vertices are provided;
void GetCenterAndArea
(
    const std::vector<Vector >& vertice,
    Vector& center,
    Vector& area
)
{
    int n = (int)vertice.size();

    if (n < 2)
    {
        FatalError("GetCenterAndArea: you have only " + ToString(n) + " vertices!");
    }

    if (2 == n)
    {
        CalculateLineSegment(vertice[0], vertice[1], center, area);
    }
    else if (3 == n)
    {
        CalculateTriangle(vertice[0], vertice[1], vertice[2], center, area);
    }
    else
    {
        Vector xG(0, 0, 0);
        for (int i = 0; i < n; ++i)
        {
            xG = xG + vertice[i];
        }
        xG = xG / Scalar(n);

        Vector numerator(0, 0, 0);
        Scalar dominator = 0;
        area = Vector(0, 0, 0);
        for (int i = 0; i < n; ++i)
        {
            Vector triangleCenter, triangleArea;
            CalculateTriangle(vertice[i % n], vertice[(i + 1) % n], xG, triangleCenter, triangleArea);
            area += triangleArea;
            numerator += triangleArea.Mag() * triangleCenter;
            dominator += triangleArea.Mag();
        }
        //center = (dominator > 10 * SMALL) ? (numerator / dominator) : xG;
        center = xG;
    }
}

//Calculating center point and volume of a phyramid.
void GetCenterAndVolume
(
    const Vector& apex,
    const std::vector<Vector >& base,
    Vector& center,
    Scalar& volume
)
{
    Vector baseCenter, baseArea;
    GetCenterAndArea(base, baseCenter, baseArea);
    center = 0.75*baseCenter + 0.25*apex;
    Vector norm = baseArea;
    norm.Normalize();
    Scalar height = fabs(norm & (apex - baseCenter));
    volume = height*baseArea.Mag() / 3.0;
}

//Calculating center point and volume of a polyhedron (from vertices).
void GetCenterAndVolume
(
    const std::vector<std::vector<Vector > >& polygonVertice,
    Vector& center,
    Scalar& volume
)
{
    int n = (int)polygonVertice.size();

    Vector xG(0, 0, 0);
    for (int i = 0; i < n; ++i)
    {
        Vector faceCenter, faceArea;
        GetCenterAndArea(polygonVertice[i], faceCenter, faceArea);
        xG = xG + faceCenter;
    }
    xG = xG / Scalar(n);

    Vector numerator(0, 0, 0);
    Scalar dominator = 0;
    volume = 0;
    for (int i = 0; i < n; ++i)
    {
        Vector pyramidCenter;
        Scalar pyramidVolume;
        GetCenterAndVolume(xG, polygonVertice[i], pyramidCenter, pyramidVolume);
        numerator += pyramidVolume*pyramidCenter;
        dominator += pyramidVolume;
    }
    
    //center = numerator / dominator;
    center = xG;
    volume = dominator;
}

//Calculating center point and volume of a polyhedron 
//(from center points and areas of its surrounding faces)
void GetCenterAndVolume
(
    const std::vector<Vector >& faceCenter,
    const std::vector<Vector >& faceArea,
    Vector& center,
    Scalar& volume
)
{
    int n = (int)faceCenter.size();
    if (n != faceArea.size())
    {
        FatalError("GetCenterAndVolume: face numbers are not consistent!");
    }

    Vector xG(0, 0, 0);
    for (int i = 0; i < n; ++i)
    {
        xG = xG + faceCenter[i];
    }
    xG = xG / Scalar(n);

    Vector numerator(0, 0, 0);
    Scalar dominator = 0;

    for (int i = 0; i < n; ++i)
    {
        Vector pyramidCenter = 0.75*faceCenter[i] + 0.25*xG;
        Vector norm = faceArea[i];
        norm.Normalize();
        Scalar height = fabs(norm & (xG - faceCenter[i]));
        Scalar pyramidVolume = height*faceArea[i].Mag() / 3.0;
        numerator += pyramidVolume*pyramidCenter;
        dominator += pyramidVolume;
    }

    //center = numerator / dominator;
    center = xG;
    volume = dominator;
}

//rotate a given point around a given axis at a given angle 
//https://en.wikipedia.org/wiki/Rotation_matrix#cite_note-5
Vector RotatePoint
(
    const Vector& GivenPoint,
    const Vector& AxisPoint,
    Vector axis,
    const Scalar& theeta
)
{
    axis.Normalize();
    Scalar alpha = (GivenPoint - AxisPoint) & axis;
    Vector Pedal = AxisPoint + alpha*axis;
    Vector PointRelative = GivenPoint - Pedal;

    Tensor rotation;
    rotation.SetXX(cos(theeta) + pow(axis.X(), 2)*(1 - cos(theeta)));
    rotation.SetXY((axis.X())*(axis.Y())*(1 - cos(theeta)) - (axis.Z())*sin(theeta));
    rotation.SetXZ((axis.X())*(axis.Z())*(1 - cos(theeta)) + (axis.Y())*sin(theeta));

    rotation.SetYX((axis.X())*(axis.Y())*(1 - cos(theeta)) + (axis.Z())*sin(theeta));
    rotation.SetYY(cos(theeta) + pow(axis.Y(), 2)*(1 - cos(theeta)));
    rotation.SetYZ((axis.Y())*(axis.Z())*(1 - cos(theeta)) - (axis.X())*sin(theeta));

    rotation.SetZX((axis.X())*(axis.Z())*(1 - cos(theeta)) - (axis.Y())*sin(theeta));
    rotation.SetZY((axis.Y())*(axis.Z())*(1 - cos(theeta)) + (axis.X())*sin(theeta));
    rotation.SetZZ(cos(theeta) + pow(axis.Z(), 2)*(1 - cos(theeta)));

    return Pedal + rotation*PointRelative;
}
