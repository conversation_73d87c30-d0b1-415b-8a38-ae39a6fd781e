﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Smoother.h
//! <AUTHOR>
//! @brief 光顺类（用于光顺各种场）
//! @date 2022-03-11
//
//------------------------------修改日志----------------------------------------
// 2022-03-11 乔龙、李艳亮
//    说明：建立并添加注释
//------------------------------------------------------------------------------

#ifndef _basic_CFD_smoother_Smoother_
#define _basic_CFD_smoother_Smoother_

#include "basic/field/ElementField.h"
#include "basic/configure/Configure.h"
#include "basic/configure/ConfigureMacro.h"

/**
 * @brief 残值光顺命名空间
 * 
 */
namespace Smoother
{

/**
 * @brief 残值光顺类
 * 
 */
class FieldSmoother
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] smoothType_ 光顺方法
     * @param[in] smoothWeight_ 光顺权重
     * @param[in] nodeCenter_ 格点标识
     * @param[in] symmetryPatchID_ 对称边界标识
     */
    FieldSmoother(Mesh *mesh_, const Scheme &smoothType_, const Scalar &smoothWeight_, const bool &nodeCenter_ = true, const std::vector<int> symmetryPatchID_ = std::vector<int>{});
    
    /**
     * @brief 析构函数
     * 
     */
    ~FieldSmoother();
    
    /**
     * @brief 光顺函数
     * 
     * @tparam Type 物理场类型，包括标量和矢量
     * @param[in, out] field 物理场
     * @param fieldOld 物理场旧值临时保存所需空间
     * @param fieldNew 物理场新值临时保存所需空间
     * @param smoothSteps 光顺次数
     */
    template<class Type>
    void Smooth(ElementField<Type> &field,
                ElementField<Type> *fieldOld_,
                ElementField<Type> *fieldNew_,
                const int &smoothSteps);
    
private:
    /// 计算面权重、单元权重和光顺系数
    void CalculateFaceAndElementWeight();

    /// 常系数权重
    const Scalar GetFaceWeightConstant(const int &faceID) const { return 1.0; }

    /// 距离加权权重
    const Scalar GetFaceWeightDistance(const int &faceID) const { return this->faceWeight[faceID]; }

private:
	Mesh *mesh; ///< 当前物理场所对应的网格
    const Scheme smoothType; ///< 光顺方法
	const Scalar smoothWeight; ///< 光顺权重
    const bool nodeCenter; ///< 格点标识
    const std::vector<int> symmetryPatchID; ///< 对称边界标识

	Scalar epsilon; ///< 权重系数0.25 * (smoothWeight * smoothWeight - 1.0);
    ElementField<Scalar> coefficentField; ///< 光顺系数场
    
    std::vector<Scalar> faceWeight; ///< faceArea / (leftCenter - rightCenter).Mag()
    std::vector<Scalar> elementWeight; ///< faceNumber / (Sum(faceArea_j * faceWeight_j), j=1,faceNumber)

	/// 获得面权重
	const Scalar (FieldSmoother::*GetFaceWeight)(const int &faceID) const;
    
};

}
#endif