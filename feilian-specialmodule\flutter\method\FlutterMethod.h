#ifndef FLUTTER_METHOD_H
#define FLUTTER_METHOD_H

#include "basic/common/ConfigUtility.h"
#include "basic/field/ElementField.h"
#include "sourceFlow/package/FlowPackage.h"

class FlutterMethod
{
public:
    FlutterMethod(const Package::FlowPackage *data);
    ~FlutterMethod();

    // 初始化颤振分析
    void Initialize();

    // 预测步：基于历史状态预测当前时刻模态响应
    void PredictModalResponse();

    // 校正步：基于当前气动力校正模态响应
    void CorrectModalResponse();

    // 更新整个气动网格坐标
    void UpdateAerodynamicMeshCoordinates();

    // 更新网格几何属性（体积、面积、法矢等）
    void UpdateMeshGeometricProperties();

    // 计算网格速度（用于几何守恒律）
    void ComputeMeshVelocities();

    // 结构动力学时间积分方法（数值积分算法层面）
    void NewmarkBetaIntegration();          // Newmark-β方法
    void CentralDifferenceIntegration();    // 中心差分法
    void RungeKuttaIntegration();           // 4阶Runge-Kutta方法

    // 预估校正方法（流固耦合迭代策略层面）
    // 注意：预估校正是耦合策略，时间积分是数值方法
    // PredictModalResponse() 和 CorrectModalResponse() 都会调用时间积分方法

private:

    // 读取模态数据和振型场
    void ReadModalDataAndShapeFields();

    // 初始化流固耦合界面信息
    void InitializeFluidStructureInterface();

    // 获取面的模态振型位移
    Vector GetFaceModalShapeDisplacement(int faceID, int modeIndex);

    // 状态转移矩阵相关方法
    void InitializeStateTransitionMatrices();
    void SetupGeneralizedMassMatrix();
    void ComputeStateTransitionMatrix();
    void ComputeIntegratedStateMatrix();

    // 广义气动力计算（基于摄动场方法）
    void ComputeGeneralizedAerodynamicForces();

    // 基于模态振型场计算所有节点的位移
    void ComputeNodalDisplacementsFromModalShapes();

    // 历史状态管理
    void UpdateHistoryStates();
    void InitializeHistoryStates();

private:
    const Package::FlowPackage *data;
    Mesh *mesh;

    // 基本参数
    int numberOfModes;                      // 模态数量
    Scalar physicalTimeStep;                // 物理时间步长
    Scalar referenceLength;                 // 参考长度
    Scalar freeStreamVelocity;              // 来流速度
    Scalar dynamicPressure;                 // 动压
    Scalar soundSpeed;                      // 声速

    // 模态参数
    std::vector<Scalar> modalMasses;        // 模态质量
    std::vector<Scalar> modalFrequencies;   // 模态频率
    std::vector<Scalar> modalDampingRatios; // 模态阻尼比

    // 模态坐标历史 (用于时间积分)
    std::vector<Scalar> modalDisplacements_n;      // t^n 时刻模态位移
    std::vector<Scalar> modalDisplacements_n1;     // t^{n-1} 时刻模态位移
    std::vector<Scalar> modalDisplacements_n2;     // t^{n-2} 时刻模态位移 (用于高阶方法)

    std::vector<Scalar> modalVelocities_n;         // t^n 时刻模态速度
    std::vector<Scalar> modalVelocities_n1;        // t^{n-1} 时刻模态速度
    std::vector<Scalar> modalVelocities_n2;        // t^{n-2} 时刻模态速度

    std::vector<Scalar> modalAccelerations_n;      // t^n 时刻模态加速度
    std::vector<Scalar> modalAccelerations_n1;     // t^{n-1} 时刻模态加速度

    // 广义力历史
    std::vector<Scalar> generalizedForces_n;       // t^n 时刻广义力
    std::vector<Scalar> generalizedForces_n1;      // t^{n-1} 时刻广义力
    std::vector<Scalar> generalizedForces_n2;      // t^{n-2} 时刻广义力

    // 兼容性：保持原有接口
    std::vector<Scalar> currentModalStates;        // 当前时刻模态状态 [q1, q̇1, q2, q̇2, ...]
    std::vector<Scalar> previousModalStates;       // 上一时刻模态状态
    std::vector<Scalar> currentGeneralizedForces;  // 当前时刻广义力
    std::vector<Scalar> previousGeneralizedForces; // 上一时刻广义力
    std::vector<Scalar> correctedGeneralizedForces;// 校正后广义力

    // 状态转移矩阵
    std::vector<std::vector<Scalar>> stateTransitionMatrix;     // 状态转移矩阵
    std::vector<std::vector<Scalar>> integratedStateMatrix;     // 积分状态转移矩阵
    std::vector<std::vector<Scalar>> generalizedMassMatrix;     // 广义质量矩阵

    // 摄动场数据
    std::vector<std::vector<Vector>> modalShapeFields;     // [模态索引][节点ID]模态振型位移
    std::vector<Vector> currentNodalDisplacements;         // 当前时刻节点位移
    std::vector<Vector> previousNodalDisplacements;        // 上一时刻节点位移

    // 几何守恒律相关数据
    std::vector<Vector> nodeVelocities;                    // 节点网格速度
    std::vector<Vector> faceVelocities;                    // 面中心网格速度
    std::vector<Scalar> previousVolumes;                   // 上一时刻单元体积

    // 流固耦合界面信息
    std::vector<int> fluidStructureInterfaceFaceIDs;       // 耦合界面面编号
    std::vector<Vector> interfaceFaceNormals;              // 耦合界面法矢量
    std::vector<Scalar> interfaceFaceAreas;                // 耦合界面面积
};

#endif