﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceTraverse.h
//! <AUTHOR>
//! @brief 壁面距离计算类（穷举）
//! @date  2022-11-07
//
//------------------------------修改日志----------------------------------------
//
// 2022-11-07 李艳亮、乔龙
// 说明：建立并规范化
//
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_WallDistanceTraverse_
#define _meshProcess_wallDistance_WallDistanceTraverse_

#include "meshProcess/wallDistance/WallDistanceBase.h"

/**
 * @brief 近壁面距离类（穷举法）
 * 
 */
class WallDistanceTraverse : public WallDistanceBase
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in, out] mesh 网格指针
	* @param[in] wallBoundaryFace_ 物面面元容器（包含面元信息和构成面的点坐标）
	*/
    WallDistanceTraverse(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_);
    
	/**
	* @brief 析构函数
	*
	*/
    ~WallDistanceTraverse(){}

	/**
	* @brief 计算壁面距离
	*
	*/
    void Calculate();
};

#endif
