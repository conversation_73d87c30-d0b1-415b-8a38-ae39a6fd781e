﻿#ifndef _basic_geometry_PlaneWall_
#define _basic_geometry_PlaneWall_

#include "basic/geometry/Line.h"

/**
 * @brief 几何命名空间
 * 
 */
namespace Geometry
{

class PlaneWall
{
public:
    /**
     * @brief 构造函数
     * 
     */
    PlaneWall()
    {
        moving    = false;
        rotating  = false;
        translationVelocity = Vector0;
        rotationVelocity   = 0.0;
    }

    bool CreateWall(const std::vector<Vector> &nodes_);
    
    /**
     * @brief 设置壁面属性
     * 
     * @param[in] wall_id_ 壁面编号
     * @param[in] prop_type_ 壁面属性
     * @param[in] both_ 是否为双面
     */
    void SetWallProperty(const int &wall_id_, const int &prop_type_, const bool &both_);

    /**
     * @brief 判断球与平面是否接触
     * 
     * @param[in] pos 球心坐标
     * @param[in] diam 球直径
     * @return 1 接触
     * @return 0 未接触
     * @return -1 穿透
     */
    int IsInContact(const Vector &pos, const Scalar &diam) const;

    /**
     * @brief 判断点是否在平面内
     * 
     * @param[in] p 坐标点
     * @return true 在平面内
     * @return false 不在平面内
     */
    bool IsInPlane(const Vector &p) const;

    /**
     * @brief 判断点是否在平面边界上，更新法向矢量、投影点坐标、点到线段的距离等
     * 
     * @param[in] pos 坐标点
     * @param[in] diam 直径
     * @param[out] nv 法向矢量
     * @param[out] cp 投影点坐标
     * @param[out] dist 点到边界的距离
     * @return true 在边界内
     * @return false 不在边界内
     */
    bool IsOnLines(const Vector &pos, const Scalar &diam, Vector &nv, Vector &cp, Scalar &dist) const;
    
    /**
     * @brief 空间点到面的法向距离
     * 
     * @param p 空间点
     * @return Scalar 距离
     */
    Scalar ObtainNormalDistance(const Vector &p)const;
    
    /**
     * @brief 获得投影点
     * 
     * @param[in] p 空间坐标
     * @return Vector 投影点坐标
     */
    Vector ObtainProjectionPoint(const Vector &p)const;

    /**
     * @brief 得到平面的端点
     * 
     * @param[in] n 端点索引
     * @return Vector 端点坐标
     */
    const Vector GetPoint(const int &n)const {return this->nodes.at(n);}

    /**
     * @brief 得到平面的端点数
     * 
     * @return int 端点数
     */
    const int GetPointNumber()const {return this->nodes.size();}

    /**
     * @brief 得到面的法向矢量
     * 
     * @return Vector 法向矢量
     */
    const Vector GetNormal()const {return this->normal;}

    /**
     * @brief 设置壁面的编号
     * 
     * @param id 面编号
     */
    void SetWallID(const int &id) { this->wallID = id; }

    /**
     * @brief 壁面反向
     * 
     */
    void Reverse();
    
    /**
     * @brief 设置壁面运动信息
     * 
     * @param[in] t_vel 平移速度
     * @param[in] r_vel 旋转速度
     * @param[in] r_line 旋转轴
     */
    void SetMovingInfo(const Vector &t_vel , const Scalar &r_vel, const Line &r_line);

    /**
     * @brief 获得壁面内一点的运动速度
     * 
     * @param[in] p0 坐标点
     * @return const Vector 运动速度
     */
    const Vector GetVelocity(const Vector& p0);
    
    /**
     * @brief 获得点到面法向单位矢量、点到面的距离、壁面属性、投影点坐标等
     * 
     * @param[in] pos 位置
     * @param[in] diam 直径
     * @param[out] nv 法向单位矢量
     * @param[out] dist 法向距离
     * @param[out] vel 接触点速度
     * @param[out] pt 壁面属性
     */
    void GetPointToWallInfo(const Vector &pos, const Scalar &diam, Vector& nv, Scalar& dist, Vector& vel, int& pt);

public:
    int wallID; ///< 壁面编号
    int propType; ///< 壁面属性编号
    bool bothSide; ///< 检测是否为双侧面
    Scalar distance; ///< 平面到坐标原点的距离，也是面隐式方程中的常数
    Vector normal; ///< 面单位法向矢量
    std::vector<Vector> nodes; ///< 面的端点列表
    std::vector<Line> lines; ///< 面的边界线段( P0 to P1, P1 to P2, P2 to P3, P3 to P0)

    bool moving; ///< 运动标识
    bool rotating; ///< 旋转标识
    Vector translationVelocity; ///< 平移速度
    Scalar rotationVelocity; ///< 旋转速度
    Line rotationLine; ///< 旋转轴

#if defined(_BaseParallelMPI_)
public:
	template<class Archive>
	void serialize(Archive& ar, const unsigned int version)
	{
		//当前类成员序列化
		ar& wallID;
		ar& propType;
		ar& bothSide;
		ar& distance;
		ar& normal;
		ar& nodes;
		ar& lines;
		ar& moving;
		ar& rotating;
		ar& translationVelocity;
		ar& rotationVelocity;
		ar& rotationLine;
	}
#endif

};

} // namespace Geometry

#endif