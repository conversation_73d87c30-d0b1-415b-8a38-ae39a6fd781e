﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file StringTools.h
//! <AUTHOR>
//! @brief 字符串工具.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_StringTools_
#define _basic_common_StringTools_

#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "basic/common/Vector.h"
#include "basic/common/Tensor.h"

/**
 * @brief 从文件流中提取两个特定字符之间的内容
 * @details 该函数会从当前文件指针位置开始查找左字符，然后提取直到右字符之间的内容
 * 
 * @param[in] infile 输入文件流
 * @param[out] outstringstream 输出字符串流，存储提取的内容
 * @param[in] leftChar 起始字符
 * @param[in] rightChar 结束字符
 * @return int 返回状态码：0-成功，1-失败
 * @throw std::invalid_argument 如果输入文件流无效
 * @note 函数会修改文件流指针位置
 */
int GetBySymbol(
    std::ifstream& infile,
    std::stringstream& outstringstream,
    char leftChar,
    char rightChar
);

/**
 * @brief 从字符串流中提取两个特定字符之间的内容
 * @details 该函数会从当前字符串流位置开始查找左字符，然后提取直到右字符之间的内容
 * 
 * @param[in] instringstream 输入字符串流
 * @param[out] outstringstream 输出字符串流，存储提取的内容
 * @param[in] leftChar 起始字符
 * @param[in] rightChar 结束字符
 * @return int 返回状态码：0-成功，1-失败
 * @throw std::invalid_argument 如果输入字符串流无效
 * @note 函数会修改字符串流指针位置
 */
int GetBySymbol(
    std::stringstream& instringstream,
    std::stringstream& outstringstream,
    char leftChar,
    char rightChar
);

/**
 * @brief 从字符串流中读取一行，直到遇到指定分隔符
 * @details 类似标准getline，但可以指定任意分隔符
 * 
 * @param[in] sstream 输入字符串流
 * @param[out] outstring 输出字符串
 * @param[in] symbol 行分隔符
 * @return bool true-成功读取一行，false-读取失败
 * @throw std::invalid_argument 如果输入字符串流无效
 */
bool getline(
    std::stringstream& sstream, 
    std::string& outstring, 
    char symbol
);

/**
 * @brief 从文件读取一行并存入字符串流
 * @details 该函数会从文件读取一行内容，并存入字符串流中
 * 
 * @param[in] inFile 输入文件流
 * @param[out] text_line 输出字符串，存储读取的行内容
 * @param[out] sstream 输出字符串流
 * @throw std::ios_base::failure 如果文件读取失败
 */
void ReadLineToSStream(
    std::ifstream& inFile, 
    std::string& text_line, 
    std::stringstream& sstream
);

/**
 * @brief 将任意类型变量转换为字符串
 * @tparam Type 输入数据类型
 * @param[in] value 输入值
 * @return std::string 转换后的字符串
 * @throw std::bad_cast 如果类型转换失败
 */
template<class Type>
std::string ToString(const Type &value);

/**
 * @brief 将vector容器转换为字符串
 * @tparam Type 容器元素类型
 * @param[in] value 输入vector
 * @return std::string 转换后的字符串，格式为"[elem1, elem2, ...]"
 * @throw std::bad_cast 如果元素类型转换失败
 */
template<class Type>
std::string ToStringVector(const std::vector<Type> &value);

/**
 * @brief 将数组转换为字符串
 * @tparam Type 数组元素类型
 * @param[in] value 输入数组指针
 * @param[in] size 数组大小
 * @return std::string 转换后的字符串，格式为"[elem1, elem2, ...]"
 * @throw std::invalid_argument 如果数组指针为nullptr或size<=0
 * @throw std::bad_cast 如果元素类型转换失败
 */
template<class Type>
std::string ToString(const Type *value, const int &size);

/**
 * @brief 判断字符串是否为空或仅包含空白字符
 * @param[in] str 输入字符串
 * @return bool true-字符串为空或全空白，false-包含非空白字符
 */
bool JudgeBlank(const std::string& str);

/**
 * @brief 将字符串转换为全大写
 * @param[in,out] str 输入输出字符串
 * @return std::string 转换后的大写字符串
 */
std::string TransformToCapital(std::string &str);

/**
 * @brief 将字符串转换为整型
 * @param[in] s 输入字符串
 * @return int 转换后的整数值
 * @throw std::invalid_argument 如果字符串无法转换为整数
 * @throw std::out_of_range 如果转换结果超出int范围
 */
int StringToInt(const std::string &s);

/**
 * @brief 将字符串转换为标量(Scalar)类型
 * @param[in] s 输入字符串
 * @return Scalar 转换后的标量值
 * @throw std::invalid_argument 如果字符串无法转换为标量
 */
Scalar StringToScalar(const std::string &s);

/**
 * @brief 将字符串转换为向量(Vector)类型
 * @param[in] s 输入字符串，格式应为"[x,y,z]"或"x y z"
 * @return Vector 转换后的向量值
 * @throw std::invalid_argument 如果字符串格式不正确
 */
Vector StringToVector(const std::string &s);

/**
 * @brief 将标量转换为指定格式的字符串
 * @param[in] value 输入标量值
 * @param[in] size 输出字符串总长度(不足用空格填充)
 * @param[in] precision 小数精度
 * @param[in] scientific 是否使用科学计数法
 * @return std::string 格式化后的字符串
 */
std::string ScalarToString(
    const Scalar &value, 
    const int &size, 
    const int &precision, 
    const bool &scientific
);

/**
 * @brief 比较两个字符串是否相等(不区分大小写)
 * @param[in] str1 第一个字符串
 * @param[in] str2 第二个字符串
 * @return bool true-字符串相等(不区分大小写)，false-不相等
 */
bool CompareTwoStrings(const std::string &str1, const std::string &str2);

/**
 * @brief 统计字符串UTF8字符数
 * @param[in] stringTmp 输入字符串
 * @return int 字符数
 */
int CountUTF8Characters(const std::string &stringTmp);

/**
 * @brief 生成带装饰的信息标题
 * @details 例如：ObtainInfoTitle("WARNING", 20, '*') 返回 "***** WARNING ******"
 * 
 * @param[in] info 中心信息文本
 * @param[in] symbol 装饰字符
 * @param[in] length 总长度
 * @param[in] pos 对齐方式
 * @return std::string 生成后的标题字符串
 * @throw std::invalid_argument 如果length小于info长度+2
 */
std::string ObtainInfoTitle(
    const std::string info = "",
    const char symbol = '-',
    const int length = 71,
    const int pos = 0
);

/**
 * @brief 判断字符串后缀
 * @param[in] str 字符串
 * @param[in] suffix 后缀
 * @return bool true-以后缀结尾，false-不以后缀结尾
 */
bool EndWithSuffix(const std::string& str, const std::string &suffix);

#endif
