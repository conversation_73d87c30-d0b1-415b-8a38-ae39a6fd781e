#include "meshProcess/meshDeform/OversetMeshDeform.h"
#include "sourceFlow/package/FlowPackage.h"

OversetMeshDeform::OversetMeshDeform(Package::FlowPackage &flowPackage_,
                                    const OversetDeformConfig &config_)
    : flowPackage(flowPackage_), config(config_)
{
    // 获取并行信息
#if defined(_BaseParallelMPI_)
    processorID = GetMPIRank();
    nProcessor = GetMPISize();
#else
    processorID = 0;
    nProcessor = 1;
#endif

    // 初始化
    Initialize();

    if (processorID == 0 && config.verboseOutput)
    {
        Print("重叠网格动网格模块初始化完成");
        Print("子域数量: " + ToString(nZones));
        Print("跳过插值单元: " + ToString(config.skipFringeElements));
    }
}

OversetMeshDeform::~OversetMeshDeform()
{
    // 清理资源
}

void OversetMeshDeform::Initialize()
{
    // 获取基础网格信息
    localMesh = flowPackage.GetMeshStruct().mesh;
    zoneManager = flowPackage.GetZoneManager();

    // 获取子域信息
    nZones = zoneManager->GetZoneNum();
    zoneStartElemID.resize(nZones);
    zoneElemNum.resize(nZones);

    for (int zoneID = 0; zoneID < nZones; zoneID++)
    {
        zoneStartElemID[zoneID] = zoneManager->GetZoneStartElemID(zoneID);
        zoneElemNum[zoneID] = zoneManager->GetZoneElemNum(zoneID);
    }

#if defined(_EnableOverset_)
    // 获取重叠网格对象和单元类型场
    if (config.enableOversetDeform)
    {
        oversetMesh = new OversetMesh(flowPackage);
        elemTypeField = flowPackage.GetField().oversetElemType;
    }
    else
    {
        oversetMesh = nullptr;
        elemTypeField = nullptr;
    }
#endif

    // 初始化统计信息
    stats = DeformStats();
}

void OversetMeshDeform::ProcessForStaticAeroelasticWithOverset(
    const std::vector<Solver::AeroMeshData>& aeroDeformData)
{
    if (processorID == 0)
        Print("开始重叠网格变形处理");

    SystemTime totalTime;

    // 将气动变形数据转换为子域变形数据
    std::map<int, std::vector<Vector>> zoneDeformations;

    for (size_t i = 0; i < aeroDeformData.size(); i++)
    {
        const Solver::AeroMeshData& aeroData = aeroDeformData[i];
        int zoneID = static_cast<int>(i); // 假设按顺序对应子域

        zoneDeformations[zoneID].resize(aeroData.ncfd);
        for (int j = 0; j < aeroData.ncfd; j++)
        {
            zoneDeformations[zoneID][j] = aeroData.v_dxyz[j];
        }
    }

    // 执行基于子域的变形
    ProcessOversetDeformation(zoneDeformations);

    stats.deformationTime = totalTime.GetElapsedTime();

    if (processorID == 0)
    {
        Print("重叠网格变形完成，耗时: " + ToString(stats.deformationTime) + "秒");
        if (config.verboseOutput)
        {
            PrintDeformationStatistics();
        }
    }
}

void OversetMeshDeform::ProcessOversetDeformation(
    const std::map<int, std::vector<Vector>>& zoneDeformations)
{
    if (processorID == 0 && config.verboseOutput)
        Print("执行基于子域的独立变形");

    // 为每个子域准备变形数据并执行变形
    for (std::map<int, std::vector<Vector>>::const_iterator it = zoneDeformations.begin();
         it != zoneDeformations.end(); ++it)
    {
        int zoneID = it->first;
        const std::vector<Vector>& deformVectors = it->second;

        // 检查当前进程是否包含该子域
        if (zoneID >= zoneElemNum.size() || zoneElemNum[zoneID] <= 0)
        {
            if (processorID == 0 && config.verboseOutput)
                Print("当前进程不包含子域 " + ToString(zoneID) + "，跳过");
            continue;
        }

        // 提取该子域的壁面节点
        std::vector<Node> wallNodes;
        ExtractZoneWallNodes(zoneID, wallNodes);

        if (wallNodes.empty())
        {
            if (processorID == 0)
                Print("警告：子域 " + ToString(zoneID) + " 没有壁面节点，跳过变形");
            continue;
        }

        // 应用变形到壁面节点
        std::vector<Node> deformedNodes(wallNodes.size());
        size_t minSize = std::min(wallNodes.size(), deformVectors.size());

        for (size_t i = 0; i < minSize; i++)
        {
            deformedNodes[i] = wallNodes[i] + deformVectors[i];
        }

        // 如果变形向量不够，剩余节点保持不变
        for (size_t i = minSize; i < wallNodes.size(); i++)
        {
            deformedNodes[i] = wallNodes[i];
        }

        // 处理该子域的变形
        ProcessSingleZoneDeformation(zoneID, wallNodes, deformedNodes);
        stats.processedZones++;
    }

    // 更新重叠装配
    if (config.updateOversetAssembly)
    {
        SystemTime assemblyTime;
        UpdateOversetAssembly();
        stats.assemblyUpdateTime = assemblyTime.GetElapsedTime();
    }

    // 质量检查
    if (config.enableQualityCheck)
    {
        CheckMeshQuality();
    }
}

void OversetMeshDeform::ProcessSingleZoneDeformation(int zoneID,
                                                   const std::vector<Node>& wallNodes,
                                                   const std::vector<Node>& deformedNodes)
{
    if (processorID == 0 && config.verboseOutput)
        Print("处理子域 " + ToString(zoneID) + " 的网格变形");

    // 参数检查
    if (wallNodes.size() != deformedNodes.size())
    {
        if (processorID == 0)
            Print("错误：子域 " + ToString(zoneID) + " 壁面节点与变形节点数量不匹配");
        return;
    }

    if (wallNodes.empty())
    {
        if (processorID == 0 && config.verboseOutput)
            Print("子域 " + ToString(zoneID) + " 没有壁面节点，跳过变形");
        return;
    }

    // 创建RBF变形器
    std::vector<Node> wallNodesCopy = wallNodes;
    std::vector<Node> deformedNodesCopy = deformedNodes;
    Scalar rbfRadius = config.rbfRadius;

    MeshDeform* meshDeformer = new MeshDeform(wallNodesCopy, deformedNodesCopy, rbfRadius);

    // 配置智能RBF求解器，根据规模自动选择策略
    RBFSolverConfig rbfConfig;
    if (config.enableSmartSolver)
    {
        // 根据节点数量智能选择求解器类型
        if (wallNodes.size() < config.serialThreshold)
        {
            rbfConfig.solverType = RBFSolverType::TRADITIONAL_GAUSS;  // 小规模使用传统高斯消元
        }
        else if (wallNodes.size() < config.parallelThreshold)
        {
            rbfConfig.solverType = RBFSolverType::MKL_SERIAL;         // 中等规模使用串行LAPACK
        }
        else
        {
            rbfConfig.solverType = RBFSolverType::MKL_PARALLEL;       // 大规模使用分布式并行
        }

        rbfConfig.enableMKL = config.enableMKL;                 // 启用MKL加速
        rbfConfig.enableParallel = config.enableParallel;       // 启用并行计算
        rbfConfig.serialThreshold = config.serialThreshold;     // 串行求解阈值
        rbfConfig.maxNodes = config.maxRBFNodes;                // 最大节点数
        rbfConfig.skipInterval = config.nodeReductionInterval;  // 节点缩减间隔
    }
    else
    {
        // 使用传统求解方式
        rbfConfig.solverType = RBFSolverType::TRADITIONAL_GAUSS;
        rbfConfig.enableMKL = false;
        rbfConfig.enableParallel = false;
    }

    if (processorID == 0 && config.verboseOutput)
    {
        Print("子域 " + ToString(zoneID) + " 使用智能RBF求解，节点数: " + ToString(wallNodes.size()));

        // 输出求解策略信息
        if (config.enableSmartSolver)
        {
            if (wallNodes.size() < config.serialThreshold)
            {
                Print("  -> 使用串行直接求解（节点数 < " + ToString(config.serialThreshold) + "）");
            }
            else if (wallNodes.size() < config.parallelThreshold)
            {
                Print("  -> 使用MKL并行求解（节点数 < " + ToString(config.parallelThreshold) + "）");
            }
            else
            {
                Print("  -> 使用分布式并行求解（节点数 >= " + ToString(config.parallelThreshold) + "）");
                if (config.enableGreedyReduction)
                {
                    Print("  -> 启用贪心算法节点缩减，容差: " + ToString(config.greedyTolerance));
                }
                else
                {
                    Print("  -> 使用固定间隔节点缩减，间隔: " + ToString(config.nodeReductionInterval));
                }
            }
        }
        else
        {
            Print("  -> 使用传统直接求解方式");
        }
    }

    // 执行配置化的智能RBF求解
    meshDeformer->ProcessWithConfig(rbfConfig);

    // 获取权重系数
    std::vector<Vector> weights = meshDeformer->GetV_weight();

    // 更新求解策略统计
    stats.totalRBFNodes += wallNodes.size();
    if (config.enableSmartSolver)
    {
        if (wallNodes.size() < config.serialThreshold)
        {
            stats.serialSolveZones++;
        }
        else if (wallNodes.size() < config.parallelThreshold)
        {
            stats.parallelSolveZones++;
        }
        else
        {
            stats.distributedSolveZones++;
            // 估算缩减后的节点数
            if (config.enableGreedyReduction)
            {
                stats.reducedRBFNodes += wallNodes.size() / 2; // 贪心算法通常能缩减约50%
            }
            else
            {
                stats.reducedRBFNodes += wallNodes.size() / config.nodeReductionInterval;
            }
        }
    }

    // 应用变形到该子域的网格节点
    ApplyZoneDeformation(zoneID, wallNodesCopy, weights, rbfRadius);

    // 清理内存
    delete meshDeformer;

    if (processorID == 0 && config.verboseOutput)
        Print("子域 " + ToString(zoneID) + " 变形处理完成");
}

void OversetMeshDeform::ExtractZoneWallNodes(int zoneID, std::vector<Node>& wallNodes)
{
    wallNodes.clear();

    if (processorID == 0 && config.verboseOutput)
        Print("提取子域 " + ToString(zoneID) + " 的壁面节点");

    // 检查当前进程是否包含该子域
    if (zoneID >= zoneElemNum.size() || zoneElemNum[zoneID] <= 0)
    {
        return;
    }

    // 遍历边界面，提取属于该子域的壁面节点
    const int boundarySize = localMesh->GetBoundarySize();
    std::set<int> wallNodeIDs; // 使用set避免重复节点

    for (int patchID = 0; patchID < boundarySize; patchID++)
    {
        // 检查边界类型是否为壁面
        const Boundary::Type boundaryType = flowPackage.GetLocalBoundaryType(patchID);

        // 只处理壁面边界
        if (boundaryType == Boundary::Type::WALL ||
            boundaryType == Boundary::Type::WALL_ADIABATIC ||
            boundaryType == Boundary::Type::WALL_ISOTHERMAL ||
            boundaryType == Boundary::Type::WALL_MOVING)
        {
            const int faceSize = localMesh->GetBoundaryFaceSize(patchID);

            for (int faceIndex = 0; faceIndex < faceSize; faceIndex++)
            {
                const int faceID = localMesh->GetBoundaryFaceID(patchID, faceIndex);
                const Face& face = localMesh->GetFace(faceID);

                // 检查该面是否属于指定子域
                const int ownerElemID = face.GetOwnerID();
                const int elemZoneID = zoneManager->GetElemZoneID(ownerElemID);

                if (elemZoneID == zoneID)
                {
                    // 添加该面的所有节点
                    const int nodeSize = face.GetNodeSize();
                    for (int nodeIndex = 0; nodeIndex < nodeSize; nodeIndex++)
                    {
                        const int nodeID = face.GetNodeID(nodeIndex);
                        wallNodeIDs.insert(nodeID);
                    }
                }
            }
        }
    }

    // 将节点ID转换为节点坐标
    wallNodes.reserve(wallNodeIDs.size());
    for (std::set<int>::const_iterator it = wallNodeIDs.begin(); it != wallNodeIDs.end(); ++it)
    {
        const int nodeID = *it;
        const Node& node = localMesh->GetNode(nodeID);
        wallNodes.push_back(node);
    }

    if (processorID == 0 && config.verboseOutput)
        Print("子域 " + ToString(zoneID) + " 提取到 " + ToString(wallNodes.size()) + " 个壁面节点");
}

void OversetMeshDeform::ApplyZoneDeformation(int zoneID,
                                           const std::vector<Node>& wallNodes,
                                           const std::vector<Vector>& weights,
                                           Scalar rbfRadius)
{
    if (processorID == 0 && config.verboseOutput)
        Print("应用子域 " + ToString(zoneID) + " 的变形");

    // 参数检查
    if (wallNodes.size() != weights.size())
    {
        if (processorID == 0)
            Print("警告：壁面节点数量与权重数量不匹配");
        return;
    }

    if (wallNodes.empty())
    {
        return;
    }

    // 获取该子域的节点ID列表
    std::vector<int> zoneNodeIDs;
    GetZoneNodeIDs(zoneID, zoneNodeIDs);

    int deformedNodeCount = 0;
    int skippedFringeNodes = 0;

    // 对该子域的节点应用RBF变形
    for (size_t nodeIndex = 0; nodeIndex < zoneNodeIDs.size(); nodeIndex++)
    {
        int nodeID = zoneNodeIDs[nodeIndex];

        // 如果启用了跳过插值单元功能，检查该节点是否属于插值单元
        if (config.skipFringeElements && IsNodeInFringeElement(nodeID))
        {
            skippedFringeNodes++;
            continue;
        }

        Node& currentNode = const_cast<Node&>(localMesh->GetNode(nodeID));
        Vector deformation = Vector(0.0, 0.0, 0.0);

        // 使用RBF插值计算该节点的变形
        for (size_t i = 0; i < wallNodes.size() && i < weights.size(); i++)
        {
            const Node& wallNode = wallNodes[i];
            const Vector& weight = weights[i];

            // 计算距离
            Scalar distance = sqrt(
                pow(currentNode.X() - wallNode.X(), 2) +
                pow(currentNode.Y() - wallNode.Y(), 2) +
                pow(currentNode.Z() - wallNode.Z(), 2)
            );

            // 计算RBF基函数值（Wendland C2函数）
            Scalar yita = distance / rbfRadius;
            Scalar rbfValue = 0.0;

            if (yita <= 1.0)
            {
                rbfValue = pow(1.0 - yita, 4) * (4.0 * yita + 1.0);
            }

            // 累加变形量
            deformation = deformation + weight * rbfValue;
        }

        // 应用变形
        Scalar deformMag = deformation.Mag();
        if (deformMag > 1e-12) // 只有变形量足够大才应用
        {
            currentNode.SetX(currentNode.X() + deformation.X());
            currentNode.SetY(currentNode.Y() + deformation.Y());
            currentNode.SetZ(currentNode.Z() + deformation.Z());
            deformedNodeCount++;

            // 更新最大变形量统计
            if (deformMag > stats.maxDeformation)
            {
                stats.maxDeformation = deformMag;
            }
        }
    }

    // 更新统计信息
    stats.totalDeformedNodes += deformedNodeCount;
    stats.skippedFringeNodes += skippedFringeNodes;

    if (processorID == 0 && config.verboseOutput)
    {
        Print("子域 " + ToString(zoneID) + " 变形了 " + ToString(deformedNodeCount) + " 个节点");
        if (config.skipFringeElements && skippedFringeNodes > 0)
        {
            Print("跳过了 " + ToString(skippedFringeNodes) + " 个插值单元节点");
        }
    }
}

bool OversetMeshDeform::IsNodeInFringeElement(int nodeID)
{
#if defined(_EnableOverset_)
    if (!elemTypeField)
    {
        return false; // 如果没有重叠网格信息，认为不是插值单元
    }

    // 检查包含该节点的所有单元，如果有插值单元则返回true
    const int elemNum = localMesh->GetElementNumberReal();

    for (int elemID = 0; elemID < elemNum; elemID++)
    {
        const Element& elem = localMesh->GetElement(elemID);
        const int elemNodeSize = elem.GetNodeSize();

        // 检查该单元是否包含当前节点
        bool containsNode = false;
        for (int i = 0; i < elemNodeSize; i++)
        {
            if (elem.GetNodeID(i) == nodeID)
            {
                containsNode = true;
                break;
            }
        }

        if (containsNode)
        {
            // 检查该单元是否为插值单元
            int elemType = elemTypeField->GetValue(elemID);
            if (elemType == OversetElemType::ACCEPTOR)
            {
                return true;
            }
        }
    }

    return false;
#else
    // 如果没有启用重叠网格，认为不是插值单元
    (void)nodeID; // 避免未使用变量警告
    return false;
#endif
}

void OversetMeshDeform::GetZoneNodeIDs(int zoneID, std::vector<int>& nodeIDs)
{
    nodeIDs.clear();

    // 检查子域是否存在
    if (zoneID >= zoneElemNum.size() || zoneElemNum[zoneID] <= 0)
    {
        return;
    }

    // 使用set避免重复节点
    std::set<int> uniqueNodeIDs;

    // 遍历该子域的所有单元，收集节点ID
    int startElemID = zoneStartElemID[zoneID];
    int endElemID = startElemID + zoneElemNum[zoneID];

    for (int elemID = startElemID; elemID < endElemID; elemID++)
    {
        if (elemID >= localMesh->GetElementNumberReal()) break;

        const Element& elem = localMesh->GetElement(elemID);
        const int elemNodeSize = elem.GetNodeSize();

        // 收集该单元的所有节点
        for (int nodeIndex = 0; nodeIndex < elemNodeSize; nodeIndex++)
        {
            int nodeID = elem.GetNodeID(nodeIndex);
            uniqueNodeIDs.insert(nodeID);
        }
    }

    // 转换为vector
    nodeIDs.reserve(uniqueNodeIDs.size());
    for (std::set<int>::const_iterator it = uniqueNodeIDs.begin();
         it != uniqueNodeIDs.end(); ++it)
    {
        nodeIDs.push_back(*it);
    }
}

void OversetMeshDeform::UpdateOversetAssembly()
{
#if defined(_EnableOverset_)
    if (!oversetMesh)
    {
        if (processorID == 0)
            Print("警告：重叠网格对象未初始化，跳过装配更新");
        return;
    }

    if (processorID == 0 && config.verboseOutput)
        Print("更新重叠网格装配");

    // 更新重叠网格装配
    oversetMesh->UpdateOGA();

    // 更新重叠网格单元类型场
    oversetMesh->UpdateOversetField();

    if (processorID == 0 && config.verboseOutput)
        Print("重叠网格装配更新完成");
#else
    if (processorID == 0 && config.verboseOutput)
        Print("重叠网格未启用，跳过装配更新");
#endif
}

bool OversetMeshDeform::CheckMeshQuality()
{
    if (processorID == 0 && config.verboseOutput)
        Print("检查网格质量");

    bool qualityOK = true;
    int badElements = 0;

    const int elemNum = localMesh->GetElementNumberReal();

    for (int elemID = 0; elemID < elemNum; elemID++)
    {
        Scalar quality = CalculateElementQuality(elemID);

        if (quality < config.minElementQuality)
        {
            badElements++;
            qualityOK = false;
        }
    }

    stats.qualityIssueElements = badElements;

    if (processorID == 0)
    {
        if (badElements > 0)
        {
            Print("发现 " + ToString(badElements) + " 个质量问题单元");
        }
        else if (config.verboseOutput)
        {
            Print("网格质量检查通过");
        }
    }

    return qualityOK;
}

Scalar OversetMeshDeform::CalculateElementQuality(int elemID)
{
    // 简化的单元质量计算 - 基于单元体积
    if (elemID < 0 || elemID >= localMesh->GetElementNumberReal())
    {
        return 0.0; // 无效单元
    }

    const Element& elem = localMesh->GetElement(elemID);

    // 获取单元体积
    Scalar volume = elem.GetVolume();

    // 简单的质量指标：体积必须为正
    if (volume <= 0.0)
    {
        return 0.0; // 负体积或零体积表示质量极差
    }

    // 简化的质量评估
    if (volume < 1e-15)
    {
        return 0.1;
    }
    else if (volume < 1e-12)
    {
        return 0.5;
    }
    else
    {
        return 1.0; // 体积正常
    }
}

void OversetMeshDeform::PrintDeformationStatistics() const
{
    if (processorID == 0)
    {
        Print("=== 重叠网格变形统计信息 ===");
        Print("处理的子域数: " + ToString(stats.processedZones));
        Print("变形节点总数: " + ToString(stats.totalDeformedNodes));
        Print("跳过的插值节点数: " + ToString(stats.skippedFringeNodes));
        Print("最大变形量: " + ToString(stats.maxDeformation));
        Print("变形时间: " + ToString(stats.deformationTime) + " 秒");
        Print("装配更新时间: " + ToString(stats.assemblyUpdateTime) + " 秒");
        Print("质量问题单元数: " + ToString(stats.qualityIssueElements));

        // 智能求解策略统计
        Print("--- 智能求解策略统计 ---");
        Print("RBF节点总数: " + ToString(stats.totalRBFNodes));
        Print("串行求解子域: " + ToString(stats.serialSolveZones));
        Print("并行求解子域: " + ToString(stats.parallelSolveZones));
        Print("分布式求解子域: " + ToString(stats.distributedSolveZones));
        if (stats.reducedRBFNodes > 0)
        {
            Print("节点缩减数量: " + ToString(stats.reducedRBFNodes));
            Scalar reductionRatio = (Scalar)stats.reducedRBFNodes / stats.totalRBFNodes * 100.0;
            Print("节点缩减比例: " + ToString(reductionRatio) + "%");
        }
        Print("==============================");
    }
}

// 便捷接口函数实现
void ProcessOversetMeshDeformation(Package::FlowPackage& flowPackage,
                                 const std::vector<Solver::AeroMeshData>& aeroMeshData,
                                 Scalar rbfRadius,
                                 bool verboseOutput)
{
    // 配置重叠网格变形参数
    OversetDeformConfig config;
    config.enableOversetDeform = true;
    config.updateOversetAssembly = true;
    config.enableQualityCheck = true;
    config.skipFringeElements = true;
    config.verboseOutput = verboseOutput;
    config.rbfRadius = rbfRadius;
    config.maxRBFNodes = 8000;
    config.minElementQuality = 0.1;
    config.convergenceTolerance = 1e-6;

    // 智能求解配置
    config.enableSmartSolver = true;
    config.enableMKL = true;
    config.enableParallel = true;
    config.serialThreshold = 500;
    config.parallelThreshold = 2000;
    config.nodeReductionInterval = 3;
    config.enableGreedyReduction = true;
    config.greedyTolerance = 1e-6;

    // 创建重叠网格变形器
    OversetMeshDeform deformer(flowPackage, config);

    // 执行变形
    deformer.ProcessForStaticAeroelasticWithOverset(aeroMeshData);

    int processorID = GetMPIRank();
    if (processorID == 0)
    {
        Print("重叠网格变形处理完成");
        if (verboseOutput)
        {
            deformer.PrintDeformationStatistics();
        }
    }
}