﻿#include "sourceFlow/flowSolver/MultigridSolver.h"

MultigridSolver::MultigridSolver(SubMesh *subMesh_,
                                 std::vector<Package::FlowPackage *> &flowPackageVector_,
                                 std::vector<Time::Flow::FlowTimeManager *> &timeSchemeVector_)
                                 :
								 flowPackageVector(flowPackageVector_),
                                 timeSchemeVector(timeSchemeVector_),
                                 flowConfigure(flowPackageVector_[0]->GetFlowConfigure()),
                                 MultigridCycle(subMesh_,
                                                flowPackageVector_[0]->GetFlowConfigure().GetAcceleration().multigridSolver.restrictionOperator,
                                                flowPackageVector_[0]->GetFlowConfigure().GetAcceleration().multigridSolver.prolongationOperator)                                               
{
	nTurbulentVariableCoarseMesh = 0;
	if (flowPackageVector.size() > 1) nTurbulentVariableCoarseMesh = flowPackageVector[1]->GetTurbulentStatus().nVariable;
    
    const Smoother::Scheme &smoothScheme = Smoother::Scheme::DISTANCE_WEIGHT;
    const Scalar smoothWeight = 1.5;
    for (int level = 0; level < flowPackageVector.size() - 1; ++level)
    {
        Mesh *mesh = dynamic_cast<Mesh*>(subMesh_->GetMultiGrid(level));

        std::vector<int> symmetryPatchID;
        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
            if (flowConfigure.JudgeSymmetryLocal(level, patchID)) symmetryPatchID.push_back(patchID);

        smootherVector.push_back(new Smoother::FieldSmoother(mesh, smoothScheme, smoothWeight, flowConfigure.GetPreprocess().dualMeshFlag, symmetryPatchID));
    }

    this->multigridLevel = Min(subMesh_->GetTotalLevel(), flowPackageVector_[0]->GetFlowConfigure().GetAcceleration().multigridSolver.level);
}

MultigridSolver::~MultigridSolver()
{
    for (int level = 0; level < smootherVector.size(); ++level)
    {
        if (smootherVector[level] != nullptr)
        {
            delete smootherVector[level];
            smootherVector[level] = nullptr;
        }
    }
}

void MultigridSolver::Initialize(const int &startLevel_, FlowResultsProcess *resultProcess_, const bool multiGridFlag_)
{
    // 更新多重网格求解的初始网格层级
    startLevel = startLevel_;

    // 获取多重网格循环路径
    if (multiGridFlag_) multigridPath = this->GetMultigridPath(startLevel, flowConfigure.GetAcceleration().multigridSolver.type);

	// 设置时间求解器中的初始网格层级
	for (int level = startLevel; level < multigridLevel; ++level)
		timeSchemeVector[level]->SetFineGridLevel(startLevel);

    // 更新细网格监控
    resultProcess = resultProcess_;
    resultProcess->UpdateTime();
}

void MultigridSolver::CloseMultigrid()
{
    multigridPath.clear();
    multigridPath.push_back(std::pair<MultigridCycle::Operation, int>(MultigridCycle::Operation::ITERATE, 0));
    if (GetMPIRank() == 0) Print("multigridPath.size() = " + ToString(multigridPath.size()));
}

void MultigridSolver::Solve(const int &innerStep)
{
    // 两种情况需保存细网格物理量
    // case1: 首次下行限制时
    // case2: W循环上行后的首次下行限制时
    std::vector<bool> saveOldFlag(multigridLevel - startLevel, true);
	const bool &unsteady = flowPackageVector[0]->GetUnsteadyStatus().unsteadyFlag;
	const bool &dualTime = flowPackageVector[0]->GetUnsteadyStatus().dualTime;
    // 按照多重网格循环路径执行多重网格求解循环
    const int steps = multigridPath.size();
    for (int step = 0; step < steps; ++step)
    {
        // 获取当前多重网格循环步所在网格层级
        const int &level = multigridPath[step].second;

        // 获取当前多重网格循环步的操作类型
        switch (multigridPath[step].first)
        {
        // 求解步
        case MultigridSolver::Operation::ITERATE:
        {
            // 定义当前步的上一步和下一步是否为限制算子的标识
            bool nextRestriction = false, preRestriction = false;
            if (step > 0) preRestriction = (multigridPath[step - 1].first == MultigridSolver::Operation::RESTRICTION);
            if (step < steps - 1) nextRestriction = (multigridPath[step + 1].first == MultigridSolver::Operation::RESTRICTION);

            // 多重网格计算时，最细网格或者上一步和下一步均为限制时，
            // 当前步流场更新后，需要采用新的物理场重新计算残值
            bool recalculateResiduals = (level == startLevel || (preRestriction && nextRestriction) );

            // 当上一步为限制时，当前步需要计算力函数
            bool calculateForcingFunction = preRestriction;

            // 当前网格层下的解变量求解更新
            timeSchemeVector[level]->Iterate(recalculateResiduals, calculateForcingFunction);

            // 输出当前迭代步计算结果
			if (level == startLevel)
			{
				if (!unsteady || dualTime) resultProcess->MonitorPerStep(innerStep);
			}
			
            break;
        }

        // 限制步
        case MultigridSolver::Operation::RESTRICTION:
        {
            // 物理场传递到粗网格
            this->RestrictionPrimary(level);

            // 粗网格第0步计算需要初始化（含边界更新）
            if (innerStep == 0) timeSchemeVector[level + 1]->Initialize(Initialization::Type::NONE_INITIAL);
            
            // 更新CFL数
            const Scalar CFLCoarseRatio = flowConfigure.GetTimeScheme().CFL.coarseMeshRatio;
            const Scalar CFLNumber = flowPackageVector[level]->GetCFLNumber() * CFLCoarseRatio;
            flowPackageVector[level + 1]->SetCFLNumber(CFLNumber);

            // 更新边界
            timeSchemeVector[level + 1]->UpdateBoundaryCondition();

            // 计算梯度和湍流粘性系数
            timeSchemeVector[level + 1]->UpdateGradientAndMuT();

            // 残值场传递到粗网格
            this->RestrictionResidual(level);

            // 保存旧值为延拓做准备，最粗层网格不需要保存
            if (saveOldFlag[level + 1 - startLevel] && level + 1 < multigridLevel)
            {
                // 保存旧值，边界更新需在保存旧值之前
                this->SaveOld(level + 1);

                // 在修正前不需要再保存
                saveOldFlag[level + 1] = false;
            }

            break;
        }

        // 修正步
        case MultigridSolver::Operation::CORRECTION:
        {
            // 修正细网格物理场
            this->Correction(level);
            
            // 更新细网格边界条件
            timeSchemeVector[level - 1]->UpdateBoundaryCondition();

            // 更新细网格梯度和湍流粘性系数
            timeSchemeVector[level - 1]->UpdateGradientAndMuT();

            // 修正后，需要打开旧值保存选项，为下次修正做准备
            saveOldFlag[level] = true;

            break;
        }
        }
    }
}

void MultigridSolver::RestrictionPrimary(const int &fineMeshLevel)
{
    // 由输入细网格层级编号计算粗网格层级编号
	const int coarseMeshLevel = fineMeshLevel + 1;

	// 依据输入不同层级物理场包容器、粗细网格层级编号、待插值标量场在物理场包容器中对应的宏，
	// 分别获取相应的细网格单元场和粗网格单元场
	ElementField<Scalar> &rhoFine = *flowPackageVector[fineMeshLevel]->GetField().density;
	ElementField<Vector> &UFine = *flowPackageVector[fineMeshLevel]->GetField().velocity;
	ElementField<Scalar> &pFine = *flowPackageVector[fineMeshLevel]->GetField().pressure;
	ElementField<Scalar> &rhoCoarse = *flowPackageVector[coarseMeshLevel]->GetField().density;
	ElementField<Vector> &UCoarse = *flowPackageVector[coarseMeshLevel]->GetField().velocity;
	ElementField<Scalar> &pCoarse = *flowPackageVector[coarseMeshLevel]->GetField().pressure;

	// 由细网格场体积加权计算粗网格场
    this->Restrict(fineMeshLevel, rhoFine, rhoCoarse, RestrictType::VolumeWeight);
    this->Restrict(fineMeshLevel, pFine, pCoarse, RestrictType::VolumeWeight);
    this->Restrict(fineMeshLevel, UFine, UCoarse, RestrictType::VolumeWeight);
	
    // 更新其他衍生物理场
    flowPackageVector[coarseMeshLevel]->UpdateExtrasField();
    
	if (nTurbulentVariableCoarseMesh == 0)
    {
        ElementField<Scalar> *muTurbulentCoarse = flowPackageVector[coarseMeshLevel]->GetField().muTurbulent;
        ElementField<Scalar> *muTurbulentFine = flowPackageVector[fineMeshLevel]->GetField().muTurbulent;
        if (muTurbulentFine != nullptr)
        {
            this->Restrict(fineMeshLevel, *muTurbulentFine, *muTurbulentCoarse, RestrictType::VolumeWeight);
            muTurbulentCoarse->SetGhostlValueParallel();
        }
    }
    
	for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
    {
	    ElementField<Scalar> &phiFine = *flowPackageVector[fineMeshLevel]->GetField().turbulence[k];
	    ElementField<Scalar> &phiCoarse = *flowPackageVector[coarseMeshLevel]->GetField().turbulence[k];
		this->Restrict(fineMeshLevel, phiFine, phiCoarse, RestrictType::VolumeWeight);
		
        phiCoarse.SetGhostlValueParallel();
    }
}

void MultigridSolver::RestrictionResidual(const int &fineMeshLevel)
{
	// 由输入细网格层级编号计算粗网格层级编号
	const int coarseMeshLevel = fineMeshLevel + 1;
    auto coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);

	// 分别获取相应的细网格和粗网格残值场
	ElementField<Scalar> &residualMassFine = *flowPackageVector[fineMeshLevel]->GetResidualField().residualMass;
	ElementField<Vector> &residualMomentumFine = *flowPackageVector[fineMeshLevel]->GetResidualField().residualMomentum;
	ElementField<Scalar> &residualEnergyFine = *flowPackageVector[fineMeshLevel]->GetResidualField().residualEnergy;
	ElementField<Scalar> &residualMassCoarse = *flowPackageVector[coarseMeshLevel]->GetResidualField().residualMass;
	ElementField<Vector> &residualMomentumCoarse = *flowPackageVector[coarseMeshLevel]->GetResidualField().residualMomentum;
	ElementField<Scalar> &residualEnergyCoarse = *flowPackageVector[coarseMeshLevel]->GetResidualField().residualEnergy;
    
    // 由细网格残值场计算粗网格残值场
    this->Restrict(fineMeshLevel, residualMassFine, residualMassCoarse, RestrictType::NoWeight);
    this->Restrict(fineMeshLevel, residualMomentumFine, residualMomentumCoarse, RestrictType::NoWeight);
    this->Restrict(fineMeshLevel, residualEnergyFine, residualEnergyCoarse, RestrictType::NoWeight);

    // 计算松弛因子
    ElementField<Scalar> *relaxFactor = &flowPackageVector[coarseMeshLevel]->GetTempElementField("relaxFactor", Scalar0);
    this->CalculateRelaxFactor(coarseMeshLevel, relaxFactor);

    const int elementNumber = coarseMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &coarseID = coarseMesh->GetElementIDInDomain(index);
        residualMassCoarse.MultiplyValue(coarseID, relaxFactor->GetValue(coarseID));
        residualMomentumCoarse.MultiplyValue(coarseID, relaxFactor->GetValue(coarseID));
        residualEnergyCoarse.MultiplyValue(coarseID, relaxFactor->GetValue(coarseID));
    }

	for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
    {
	    ElementField<Scalar> &phiFine = *flowPackageVector[fineMeshLevel]->GetResidualField().residualTurbulence[k];
	    ElementField<Scalar> &phiCoarse = *flowPackageVector[coarseMeshLevel]->GetResidualField().residualTurbulence[k];
        
        this->Restrict(fineMeshLevel, phiFine, phiCoarse, RestrictType::NoWeight);

        const int elementNumber = coarseMesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &coarseID = coarseMesh->GetElementIDInDomain(index);
            phiCoarse.MultiplyValue(coarseID, relaxFactor->GetValue(coarseID));
        }
    }

    flowPackageVector[coarseMeshLevel]->FreeTempField(*relaxFactor);
}

void MultigridSolver::Correction(const int &coarseMeshLevel)
{
	// 由输入粗网格层级编号计算细网格层级编号
	const int fineMeshLevel = coarseMeshLevel - 1;
    auto fineMesh = subMesh->GetMultiGrid(fineMeshLevel);
    auto coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);

    // 计算粗网格修正场
	const ElementField<Scalar> &rhoCoarse = *flowPackageVector[coarseMeshLevel]->GetField().density;
	const ElementField<Vector> &UCoarse = *flowPackageVector[coarseMeshLevel]->GetField().velocity;
	const ElementField<Scalar> &pCoarse = *flowPackageVector[coarseMeshLevel]->GetField().pressure;
	const ElementField<Scalar> &rhoCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().density;
	const ElementField<Vector> &UCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().velocity;
	const ElementField<Scalar> &pCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().pressure;
    ElementField<Scalar> &rhoCorrectCoarse = flowPackageVector[coarseMeshLevel]->GetTempElementField("rhoCorrectCoarse", Scalar0);
    ElementField<Vector> &UCorrectCoarse = flowPackageVector[coarseMeshLevel]->GetTempElementField("UCorrectCoarse", Vector0);
    ElementField<Scalar> &pCorrectCoarse = flowPackageVector[coarseMeshLevel]->GetTempElementField("pCorrectCoarse", Scalar0);
	
    const int elementNumberCoarse = coarseMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumberCoarse; ++index)
    {
        const int &coarseID = coarseMesh->GetElementIDInDomain(index);
		rhoCorrectCoarse.SetValue(coarseID, rhoCoarse.GetValue(coarseID) - rhoCoarse0.GetValue(coarseID));
		UCorrectCoarse.SetValue(coarseID, UCoarse.GetValue(coarseID) - UCoarse0.GetValue(coarseID));
		pCorrectCoarse.SetValue(coarseID, pCoarse.GetValue(coarseID) - pCoarse0.GetValue(coarseID));
    }

	// 远场边界不修正
    const int boundarySize = coarseMesh->GetBoundarySize();
    for (int patchID = 0; patchID < boundarySize; patchID++)
    {
        if (flowPackageVector[coarseMeshLevel]->GetLocalBoundaryType(patchID) != Boundary::Type::FARFIELD) continue;
       
	    const int &faceSize = coarseMesh->GetBoundaryFaceNumberInDomain(patchID);
	    for (int index = 0; index < faceSize; ++index)
	    {
	    	// 得到面相关信息
	    	const int &faceID = coarseMesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = coarseMesh->GetFace(faceID).GetOwnerID();
            rhoCorrectCoarse.SetValue(ownerID, Scalar0);
            UCorrectCoarse.SetValue(ownerID, Vector0);
            pCorrectCoarse.SetValue(ownerID, Scalar0);
        }
    }

    // 根据粗网格修正场插值得到细网格修正场
    ElementField<Scalar> &rhoCorrectFine = flowPackageVector[fineMeshLevel]->GetTempElementField("rhoCorrectFine", Scalar0);
    ElementField<Vector> &UCorrectFine = flowPackageVector[fineMeshLevel]->GetTempElementField("UCorrectFine", Vector0);
    ElementField<Scalar> &pCorrectFine = flowPackageVector[fineMeshLevel]->GetTempElementField("pCorrectFine", Scalar0);
    this->Prolongate(coarseMeshLevel, rhoCorrectCoarse, rhoCorrectFine);
    this->Prolongate(coarseMeshLevel, UCorrectCoarse, UCorrectFine);
    this->Prolongate(coarseMeshLevel, pCorrectCoarse, pCorrectFine);
    
    flowPackageVector[coarseMeshLevel]->FreeTempField(rhoCorrectCoarse);
    flowPackageVector[coarseMeshLevel]->FreeTempField(UCorrectCoarse);
    flowPackageVector[coarseMeshLevel]->FreeTempField(pCorrectCoarse);
    
    // 光顺细网格修正场
    ElementField<Scalar> *scalarFieldOld = &flowPackageVector[fineMeshLevel]->GetTempElementField("scalarFieldOld", Scalar0);
    ElementField<Scalar> *scalarFieldNew = &flowPackageVector[fineMeshLevel]->GetTempElementField("scalarFieldNew", Scalar0);
    ElementField<Vector> *vectorFieldOld = &flowPackageVector[fineMeshLevel]->GetTempElementField("vectorFieldOld", Vector0);
    ElementField<Vector> *vectorFieldNew = &flowPackageVector[fineMeshLevel]->GetTempElementField("vectorFieldNew", Vector0);
    
    smootherVector[fineMeshLevel]->Smooth(rhoCorrectFine, scalarFieldOld, scalarFieldNew, 2);
    smootherVector[fineMeshLevel]->Smooth(UCorrectFine, vectorFieldOld, vectorFieldNew, 2);
    smootherVector[fineMeshLevel]->Smooth(pCorrectFine, scalarFieldOld, scalarFieldNew, 2);
    flowPackageVector[fineMeshLevel]->FreeTempField(*scalarFieldOld);
    flowPackageVector[fineMeshLevel]->FreeTempField(*scalarFieldNew);
    flowPackageVector[fineMeshLevel]->FreeTempField(*vectorFieldOld);
    flowPackageVector[fineMeshLevel]->FreeTempField(*vectorFieldNew);

    // 更新细网格物理场
	ElementField<Scalar> &rhoFine = *flowPackageVector[fineMeshLevel]->GetField().density;
	ElementField<Vector> &UFine = *flowPackageVector[fineMeshLevel]->GetField().velocity;
	ElementField<Scalar> &pFine = *flowPackageVector[fineMeshLevel]->GetField().pressure;
    const int elementNumberFine = fineMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumberFine; ++index)
    {
        const int &fineID = fineMesh->GetElementIDInDomain(index);
        const Scalar tempRho = Min(rhoCorrectFine.GetValue(fineID) / Max(rhoFine.GetValue(fineID), SMALL), 0.0);
        const Scalar tempP   = Min(pCorrectFine.GetValue(fineID) / Max(pFine.GetValue(fineID), SMALL), 0.0);
        const Scalar relaxFactor = 1.0 / (1.0 - 2 * Min(tempRho, tempP));

		rhoFine.AddValue(fineID, rhoCorrectFine.GetValue(fineID) * relaxFactor);
		UFine.AddValue(fineID, UCorrectFine.GetValue(fineID) * relaxFactor);
		pFine.AddValue(fineID, pCorrectFine.GetValue(fineID) * relaxFactor);
    }

    flowPackageVector[fineMeshLevel]->FreeTempField(rhoCorrectFine);
    flowPackageVector[fineMeshLevel]->FreeTempField(UCorrectFine);
    flowPackageVector[fineMeshLevel]->FreeTempField(pCorrectFine);

    flowPackageVector[fineMeshLevel]->UpdateExtrasField();

    if (nTurbulentVariableCoarseMesh > 0) this->CorrectionTurbulence(coarseMeshLevel);
}

void MultigridSolver::SaveOld(const int &coarseMeshLevel)
{
	ElementField<Scalar> &rhoCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().density;
	ElementField<Vector> &UCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().velocity;
	ElementField<Scalar> &pCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().pressure;
    rhoCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField().density;
    UCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField().velocity;
    pCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField().pressure;
    
	for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
    {
	    ElementField<Scalar> &phiCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().turbulence[k];
	    phiCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField().turbulence[k];
    }
}

void MultigridSolver::CorrectionTurbulence(const int &coarseMeshLevel)
{
	// 由输入粗网格层级编号计算细网格层级编号
	const int fineMeshLevel = coarseMeshLevel - 1;
    auto fineMesh = subMesh->GetMultiGrid(fineMeshLevel);
    auto coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);

    std::vector<ElementField<Scalar> *> phiCorrectFineVector;
	for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
        phiCorrectFineVector.push_back(&flowPackageVector[fineMeshLevel]->GetTempElementField("phiCorrectFine"+ToString(k), Scalar0));

	// 计算细网格修正量
	ElementField<Scalar> &phiCorrectCoarse = flowPackageVector[coarseMeshLevel]->GetTempElementField("phiCorrectCoarse", Scalar0);
	ElementField<Scalar> *scalarFieldOld = &flowPackageVector[fineMeshLevel]->GetTempElementField("scalarFieldOld", Scalar0);
	ElementField<Scalar> *scalarFieldNew = &flowPackageVector[fineMeshLevel]->GetTempElementField("scalarFieldNew", Scalar0);
	for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
    {
		// 计算粗网格修正量
	    const ElementField<Scalar> &phiCoarse = *flowPackageVector[coarseMeshLevel]->GetField().turbulence[k];
	    const ElementField<Scalar> &phiCoarse0 = *flowPackageVector[coarseMeshLevel]->GetField0Backup().turbulence[k];
        const int elementNumberCoarse = coarseMesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumberCoarse; ++index)
        {
            const int &coarseID = coarseMesh->GetElementIDInDomain(index);
	    	phiCorrectCoarse.SetValue(coarseID, phiCoarse.GetValue(coarseID) - phiCoarse0.GetValue(coarseID));
        }
        
		// 远场边界不修正
        const int boundarySize = coarseMesh->GetBoundarySize();
        for (int patchID = 0; patchID < boundarySize; patchID++)
        {
            if (flowPackageVector[coarseMeshLevel]->GetLocalBoundaryType(patchID) != Boundary::Type::FARFIELD) continue;
            
	        const int &faceSize = coarseMesh->GetBoundaryFaceNumberInDomain(patchID);
	        for (int index = 0; index < faceSize; ++index)
	        {
	        	// 得到面相关信息
	        	const int &faceID = coarseMesh->GetBoundaryFaceIDInDomain(patchID, index);
                const int &ownerID = coarseMesh->GetFace(faceID).GetOwnerID();
                phiCorrectCoarse.SetValue(ownerID, Scalar0);
            }
        }

		// 粗网格向细网格延拓
        this->Prolongate(coarseMeshLevel, phiCorrectCoarse, *phiCorrectFineVector[k]);
        smootherVector[fineMeshLevel]->Smooth(*phiCorrectFineVector[k], scalarFieldOld, scalarFieldNew, 2);
    }

    flowPackageVector[coarseMeshLevel]->FreeTempField(phiCorrectCoarse);
    flowPackageVector[fineMeshLevel]->FreeTempField(*scalarFieldOld);
    flowPackageVector[fineMeshLevel]->FreeTempField(*scalarFieldNew);

	// 修正细网格湍流量
	const int elementNumberFine = fineMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumberFine; ++index)
    {
        const int &fineID = fineMesh->GetElementIDInDomain(index);
        Scalar relaxFactor = INF;
		for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
        {
	        const Scalar &phiTemp = flowPackageVector[fineMeshLevel]->GetField().turbulence[k]->GetValue(fineID);
	        const Scalar &phiCorrectTemp = phiCorrectFineVector[k]->GetValue(fineID);
            relaxFactor = Min(relaxFactor, phiCorrectTemp / Max(phiTemp, SMALL));
        }
        relaxFactor = 1.0 / (1.0 - 100 * Min(relaxFactor, 0.0));
        
		for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
        {
	        ElementField<Scalar> &phiFine = *flowPackageVector[fineMeshLevel]->GetField().turbulence[k];
            const Scalar temp = phiCorrectFineVector[k]->GetValue(fineID) * relaxFactor;
            phiFine.AddValue(fineID, temp);
        }
    }
    
	for (int k = 0; k < nTurbulentVariableCoarseMesh; ++k)
        flowPackageVector[fineMeshLevel]->FreeTempField(*phiCorrectFineVector[k]);
}

void MultigridSolver::CalculateRelaxFactor(const int &coarseMeshLevel, ElementField<Scalar> *relaxFactor)
{
	// 由输入粗网格层级编号计算细网格层级编号
	const int fineMeshLevel = coarseMeshLevel - 1;
    const auto *coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);

    flowPackageVector[fineMeshLevel]->CalculatePressureSwitch(); //是否重复计算，需要检查前面是否已经计算过？？？
	ElementField<Scalar> *pressureSwitch = flowPackageVector[fineMeshLevel]->GetField().pressureSwitch;
	ElementField<Scalar> *deltaTFine = flowPackageVector[fineMeshLevel]->GetField().deltaT;
	pressureSwitch->SetGhostValueMultigrid();
	deltaTFine->SetGhostValueMultigrid();

	const ElementField<Scalar> *residualMassFine = flowPackageVector[fineMeshLevel]->GetResidualField().residualMass;
	const ElementField<Scalar> *rhoFine = flowPackageVector[fineMeshLevel]->GetField().density;
	const ElementField<Vector> *UFine = flowPackageVector[fineMeshLevel]->GetField().velocity;
	const ElementField<Scalar> *AFine = flowPackageVector[fineMeshLevel]->GetField().soundSpeed;

    relaxFactor->Initialize(1.0);
    const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
    for (int index = 0; index < IDSize; ++index)
    {
        const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
        const int &fineID = IDPair.first;
        const int &coarseID = IDPair.second;

        const Scalar machTemp = sqrt(UFine->GetValue(fineID) & UFine->GetValue(fineID)) / AFine->GetValue(fineID);
        const Scalar damp1 = 5.0 * pressureSwitch->GetValue(fineID) * Max(Min(machTemp-1.0, 1.0), 0.0);
        const Scalar damp2 = 100 * fabs(residualMassFine->GetValue(fineID)) / rhoFine->GetValue(fineID) * deltaTFine->GetValue(fineID);
        const Scalar relaxFactorTemp = Min(relaxFactor->GetValue(coarseID), Max(0.0, 1.0 - Max(damp1, damp2)));
        relaxFactor->SetValue(coarseID, relaxFactorTemp);
    }
}

void MultigridSolver::InitializeFromCoarse(const int &startLevel)
{
	const int &fineMeshLevel = startLevel;
	const int coarseMeshLevel = fineMeshLevel + 1;

	// 依据输入不同层级物理场包容器、粗细网格层级编号、待插值标量场在物理场包容器中对应的宏，
	// 分别获取相应的细网格单元场和粗网格单元场
	ElementField<Scalar> &rhoFine = *flowPackageVector[fineMeshLevel]->GetField().density;
	ElementField<Vector> &UFine = *flowPackageVector[fineMeshLevel]->GetField().velocity;
	ElementField<Scalar> &pFine = *flowPackageVector[fineMeshLevel]->GetField().pressure;
    std::vector<ElementField<Scalar> *> turbulenceFine = flowPackageVector[fineMeshLevel]->GetField().turbulence;
	ElementField<Scalar> &rhoCoarse = *flowPackageVector[coarseMeshLevel]->GetField().density;
	ElementField<Vector> &UCoarse = *flowPackageVector[coarseMeshLevel]->GetField().velocity;
	ElementField<Scalar> &pCoarse = *flowPackageVector[coarseMeshLevel]->GetField().pressure;
    std::vector<ElementField<Scalar> *> turbulenceCoarse = flowPackageVector[coarseMeshLevel]->GetField().turbulence;
	
	// 粗网格解插值传递到细网格
	this->Prolongate(coarseMeshLevel, rhoCoarse, rhoFine);
	this->Prolongate(coarseMeshLevel, UCoarse, UFine);
	this->Prolongate(coarseMeshLevel, pCoarse, pFine);
    for (int k = 0; k < nTurbulentVariableCoarseMesh; k++)
        this->Prolongate(coarseMeshLevel, *turbulenceCoarse[k], *turbulenceFine[k]);
    
    // 更新其他衍生物理场
    flowPackageVector[fineMeshLevel]->UpdateExtrasField();
}

void MultigridSolver::SmoothField(const int &level)
{
    // 依据输入不同层级物理场包容器、粗细网格层级编号、待插值标量场在物理场包容器中对应的宏，
    // 分别获取相应的细网格单元场和粗网格单元场
    ElementField<Scalar> &rhoFine = *flowPackageVector[level]->GetField().density;
    ElementField<Vector> &UFine = *flowPackageVector[level]->GetField().velocity;
    ElementField<Scalar> &pFine = *flowPackageVector[level]->GetField().pressure;
    std::vector<ElementField<Scalar> *> turbulenceFine = flowPackageVector[level]->GetField().turbulence;

    // 获取光顺所需的临时场
    ElementField<Scalar> *scalarFieldOld = &flowPackageVector[level]->GetTempElementField("scalarFieldOld", Scalar0);
    ElementField<Scalar> *scalarFieldNew = &flowPackageVector[level]->GetTempElementField("scalarFieldNew", Scalar0);
    ElementField<Vector> *vectorFieldOld = &flowPackageVector[level]->GetTempElementField("vectorFieldOld", Vector0);
    ElementField<Vector> *vectorFieldNew = &flowPackageVector[level]->GetTempElementField("vectorFieldNew", Vector0);

    // 光顺细网格修正场
    smootherVector[level]->Smooth(rhoFine, scalarFieldOld, scalarFieldNew, 4);
    smootherVector[level]->Smooth(UFine, vectorFieldOld, vectorFieldNew, 4);
    smootherVector[level]->Smooth(pFine, scalarFieldOld, scalarFieldNew, 4);
    for (int k = 0; k < nTurbulentVariableCoarseMesh; k++)
        smootherVector[level]->Smooth(*turbulenceFine[k], scalarFieldOld, scalarFieldNew, 4);

    // 释放临时场
    flowPackageVector[level]->FreeTempField(*scalarFieldOld);
    flowPackageVector[level]->FreeTempField(*scalarFieldNew);
    flowPackageVector[level]->FreeTempField(*vectorFieldOld);
    flowPackageVector[level]->FreeTempField(*vectorFieldNew);

    // 更新细网格上其他物理量，如T，muL
    flowPackageVector[level]->UpdateExtrasField();
}