﻿#include "sourceFlow/timeScheme/BackEuler.h"
#include "meshProcess/meshSorting/MeshSorting.h"
#include <limits>

namespace Time
{
namespace Flow
{
BackEuler::BackEuler(Package::FlowPackage &flowPackage)
	:
    FlowTime(flowPackage)
{
    LUSGSFlag = flowConfigure.GetTimeScheme().innnerLoopType == Time::Scheme::LUSGS;
}

BackEuler::~BackEuler()
{
}

void BackEuler::Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction)
{
	// 保存旧值
    SaveOld();
	
    // 预处理计算
    if (precondition != nullptr) precondition->Calculate();

    // 计算CFL数
    CFLNumber.Calculate();

    //计算谱半径
    fluxNS.CalculateSpectralRadius();

    // 计算当地时间步长
    this->CalculateDeltaT();
	
    // 多重网格计算时，限制前进行两次迭代
    if (LUSGSFlag && recalculateResiduals)
    {
        numberStage = 2;
        hybridFlag = true;
        calculatedFlag = {true, false};
    }
    else
    {
        numberStage = 1;
        hybridFlag = false;
        calculatedFlag = {true};
    }
    
    // 时间推进
    for (int currentStage = 0; currentStage < numberStage; currentStage++)
    {
    	// 初始化Jacobian和solVector
		this->InitializeSolver();

        // 计算本层网格流场残值
        this->CalculateFlowResidual(currentStage, 
                                    calculatedFlag[currentStage],
                                    currentLevel > fineMeshLevel && calculateForcingFunction);
        
    	// 方程求解
    	this->Solve();

    	// 实现流动变量的更新
    	this->Update();

    	// 边界条件更新
    	this->UpdateBoundaryCondition();

    	// 更新梯度和MuT
    	if (currentStage == numberStage - 1) this->UpdateGradientAndMuT();
	}

    // 多重网格计算时，需采用最新的物理量计算本层网格残差，用于传递给粗网格
    if (numberLevel > 1 && currentLevel < numberLevel - 1 && recalculateResiduals)
    {
        // 计算残值
		this->CalculateFlowResidual(numberStage, true, currentLevel > fineMeshLevel && calculateForcingFunction);

        // 边界残值更新（用于对偶网格）
        this->UpdateBoundaryResidual();
    }
}

void BackEuler::ReNumber()
{
    //获取重排方法
    const Preprocessor::RenumberType &type = flowPackage.GetFlowConfigure().GetPreprocess().renumberMethod;
	if (type == Preprocessor::RenumberType::NONE_RENUMBER)
	{
		FatalError(" LUSGS::ReNumber: type is wrong！");
		return;
	}
    
    //初始化大小
    const int &elementNumber = mesh->GetElementNumberInDomain();
    OwnerIDCell.resize(elementNumber);
    NeighborIDCell.resize(elementNumber);
    OwnerIDFace.resize(elementNumber);
    NeighborIDFace.resize(elementNumber);
    elementIDSortMap.resize(elementNumber);    

    //网格重排
    if (GetMPIRank() == 0) Print("\nRenumber elements of level " + ToString(currentLevel) + " mesh ...");
    MeshSorting meshSorting(mesh, type, elementIDSortMap);

    //建立反向关系
    std::vector<int> elementIDOldToNew(elementNumber);
    for (int i = 0; i < elementNumber; ++i) elementIDOldToNew[elementIDSortMap[i]] = i;

	// 内部面循环, 建立前向、后向遍历列表
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const int &ownerIDOld = mesh->GetFace(faceID).GetOwnerID();
        const int &neighIDOld = mesh->GetFace(faceID).GetNeighborID();
        
        // 并行边界面除外
        if (!mesh->JudgeRealElement(neighIDOld)) continue;

        const int &ownerIDNew = elementIDOldToNew[ownerIDOld];
        const int &neighIDNew = elementIDOldToNew[neighIDOld];

        if (ownerIDNew < neighIDNew)
        {
            OwnerIDCell[neighIDOld].push_back(ownerIDOld);
            OwnerIDFace[neighIDOld].push_back(faceID);

            NeighborIDCell[ownerIDOld].push_back(neighIDOld);
            NeighborIDFace[ownerIDOld].push_back(faceID);
        }
        else
        {
            OwnerIDCell[ownerIDOld].push_back(neighIDOld);
            OwnerIDFace[ownerIDOld].push_back(faceID);

            NeighborIDCell[neighIDOld].push_back(ownerIDOld);
            NeighborIDFace[neighIDOld].push_back(faceID);
        }
    }
}

}
}