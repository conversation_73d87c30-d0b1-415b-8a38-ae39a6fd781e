﻿#include "feilian-specialmodule/particle/contact/contactForce/BaseForce.h"

namespace Particle
{
namespace Contact
{

BaseForce::BaseForce(Geometry::Geometry *geom, const Configure::Particle::ContactForceType &CF_Type_,
					 const Configure::Particle::ContactTorqueType &CT_Model_,
					 const Configure::Particle::WallContactType &wallContactType_,
                     const int & max_nPrtcl_, const int &numParticles_,
                     std::vector<Particle *> &particles_, const Scalar &dt_)
					 : geometry(geom), contactForceType(CF_Type_), wallContactType(wallContactType_),
                     contactTorqueType(CT_Model_), numParticlesMax(max_nPrtcl_),
                     numParticles(numParticles_), particles(particles_), dt(dt_)
{
	contactTorqueFlag = (contactTorqueType != Configure::Particle::ContactTorqueType::CTT_NONE);
}

BaseForce::~BaseForce()
{

}

void BaseForce::SetParticleNumber(const int &numParticleNew)
{
    this->numParticles = numParticleNew;
}

void BaseForce::CalculateForceAndTorquePP()
{
    // 接触数量
    const int &contactsNumber = this->b_PP_ContList->GetContactsNumber();
    
    // 没有接触直接返回
    if(contactsNumber <= 0 ) return;

    // 遍历接触容器
    int numProcessed = 0;
    const int &contactsNumberMax = this->b_PP_ContList->GetContactsNumberMax();
    for(int i = 0; i < contactsNumberMax; ++i)
    {
        // 如果存在接触
        if( this->b_PP_ContList->contactStatus[i] > ContactStatus::PROCESSED_CONTACT  )
        {
            // 判断是否是新接触
            bool lnew = this->b_PP_ContList->contactStatus[i] == ContactStatus::NEW_CONTACT; // 新接触
            
            // 计算接触力和力矩
            this->ContactForcePP( i , lnew );
            
            // 更新接触状态
            this->b_PP_ContList->contactStatus[i] = ContactStatus::PROCESSED_CONTACT;   // 接触已处理
            
            // 统计接触处理数量，全部处理时跳出循环
            numProcessed++;
            if( numProcessed >= contactsNumber ) break;
        }
    }
}

void BaseForce::CalculateForceAndTorquePW()
{
    // 接触数量
    const int &contactsNumber = this->b_PW_ContList->GetContactsNumber();

    // 没有接触直接返回
    if(contactsNumber <= 0 ) return;

    // 遍历接触容器
    int numProcessed = 0;
    const int &contactsNumberMax = this->b_PW_ContList->GetContactsNumberMax();
    for(int i = 0; i < contactsNumberMax; ++i)
    {
        // 如果存在接触
        if( this->b_PW_ContList->contactStatus[i] > ContactStatus::PROCESSED_CONTACT  )
        {
            // 判断是否是新接触
            bool lnew = this->b_PW_ContList->contactStatus[i] == ContactStatus::NEW_CONTACT;

			if (wallContactType == Configure::Particle::WallContactType::WCT_GRANULAR)
			{
				// 计算接触力和力矩
				this->ContactForcePW(i, lnew);
			}
			else
			{
				// 反弹边界，不考虑动量损失
				const int &particleID = this->b_PW_ContList->GetItem(i).pari;
				// if (particles[particleID] == nullptr) continue;

				const int &wallID = this->b_PW_ContList->GetItem(i).id_j - this->geometry->GetWallBaseID();
				const Vector &normal = this->geometry->GetWall(wallID).GetNormal();
				const Vector &velocity = this->particles[particleID]->linearVelocity;
				this->particles[particleID]->linearVelocity = velocity - 2.0 * (velocity & normal) * normal;
			}

			// 更新接触状态
			this->b_PW_ContList->contactStatus[i] = ContactStatus::PROCESSED_CONTACT;   // 接触已处理

            // 统计接触处理数量，全部处理时跳出循环
            numProcessed++;
            if( numProcessed >= contactsNumber ) break;
        }
    }
}

void BaseForce::AddForce(const int &pari , const int &parj , const Vector &Fc )
{
    this->particles[pari]->force += Fc;
    this->particles[parj]->force -= Fc;
}

void BaseForce::AddTorque( const int &pari , const int &parj , const Vector &Mij, const Vector &Mji )
{
    this->particles[pari]->torque += Mij;
    this->particles[parj]->torque += Mji;
}

void BaseForce::AddForcePW( const int &pari , const Vector &Fc )
{
    this->particles[pari]->force += Fc;
}

void BaseForce::AddTorquePW( const int &pari , const Vector &Mij )
{
    this->particles[pari]->torque += Mij;
}

} // namespace Contact

} // namespace Particle