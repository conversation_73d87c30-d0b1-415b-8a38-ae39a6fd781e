﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceManager.h
//! <AUTHOR>
//! @brief 计算壁面距离的管理器
//! @date  2021-5-12
//
//------------------------------修改日志----------------------------------------
//
// 2021-04-12 李艳亮，乔龙
// 说明：建立并规范化
//
//------------------------------------------------------------------------------
#ifndef _meshProcess_wallDistance_WallDistanceManager_
#define _meshProcess_wallDistance_WallDistanceManager_

#include "basic/configure/ConfigureMacro.h"
#include "basic/configure/ConfigureMacro.hxx"
#include "basic/mesh/SubMesh.h"
#include "meshProcess/wallDistance/WallDistanceADT.h"
#include "meshProcess/wallDistance/WallDistanceKDT.h"
#include "meshProcess/wallDistance/WallDistanceTraverse.h"

/**
 * @brief 近壁面距离类
 * 
 */
class WallDistanceManager
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in, out] subMesh_ 网格指针
	* @param[in] nLevel_ 网格层数
	* @param[in] type_ 壁面距离计算方法
	* @param[in] dualMeshFlag_ 对偶网格标志
	*/
    WallDistanceManager(SubMesh* subMesh_,
						const int &nLevel_, 
                        const Turbulence::WallDistance &type_ = Turbulence::WallDistance::ADT,
                        const bool &dualMeshFlag_ = false);
    
	/**
	* @brief 析构函数
	*
	*/
    ~WallDistanceManager();

	/**
	* @brief 计算壁面距离
	*
	* @param[in] wallPatchIDList 物面边界编号的容器
	*/
    void Calculate(const std::vector<int> &wallPatchIDList);

	/**
	* @brief 从文件读取壁面面元
	*
	* @param[in] filePathAndName 文件路径与名称
	*/
    void ReadWallFace(const std::string &filePathAndName);

private:
	/**
	* @brief 检查壁面距离
	*
	*/
    void CheckDistance();

	/**
	* @brief 建立壁面距离基类指针
	*
	* @param[in] level 当前网格编号
	*/
    void SetPointer(const int &level);

	/**
	* @brief 从每个进程收集物面面元（采用并发）
	*
	* @param[in] wallPatchIDList 物面边界编号的容器
	*/
    void CollectWallFace(const std::vector<int> &wallPatchIDList);

private:
	SubMesh *subMesh; ///< 网格指针
	const Turbulence::WallDistance &type; ///< 壁面距离计算方法
	WallDistanceBase *wallDistancePointer; ///< 壁面距离基类指针
	std::vector<std::pair<Face, std::vector<Node>>> wallBoundaryFace; ///< 壁面面元信息容器
	const bool &dualMeshFlag; ///< 对偶网格标识
	int nLevel; ///< 计算壁面距离网格总层数
};

#endif
