//////////////////////////////////////////////////////////////////////////////
//
// (C) Copyright Ion Gaztanaga 2010-2012.
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/move for documentation.
//
//////////////////////////////////////////////////////////////////////////////

//! \file

#ifndef BOOST_INTERPROCESS_DETAIL_MOVE_HPP
#define BOOST_INTERPROCESS_DETAIL_MOVE_HPP

#ifndef BOOST_CONFIG_HPP
#  include <boost/config.hpp>
#endif
#
#if defined(BOOST_HAS_PRAGMA_ONCE)
#  pragma once
#endif

#include <boost/move/utility_core.hpp>

namespace boost {
namespace interprocess {

using ::boost::move;
using ::boost::forward;

}  //namespace interprocess {
}  //namespace boost {

#endif //#ifndef BOOST_INTERPROCESS_DETAIL_MOVE_HPP
