// Boost.Units - A C++ library for zero-overhead dimensional analysis and 
// unit/quantity manipulation and conversion
//
// Copyright (C) 2003-2008 <PERSON>
// Copyright (C) 2008 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_UNITS_SI_HPP
#define BOOST_UNITS_SI_HPP

/// \file
/// Includes all the si unit headers

#include <string>

#include <boost/units/quantity.hpp>

#include <boost/units/systems/si/base.hpp>

#include <boost/units/systems/si/absorbed_dose.hpp>
#include <boost/units/systems/si/acceleration.hpp>
#include <boost/units/systems/si/action.hpp>
#include <boost/units/systems/si/activity.hpp>
#include <boost/units/systems/si/amount.hpp>
#include <boost/units/systems/si/angular_acceleration.hpp>
#include <boost/units/systems/si/angular_momentum.hpp>
#include <boost/units/systems/si/angular_velocity.hpp>
#include <boost/units/systems/si/area.hpp>
#include <boost/units/systems/si/capacitance.hpp>
#include <boost/units/systems/si/catalytic_activity.hpp>
#include <boost/units/systems/si/conductance.hpp>
#include <boost/units/systems/si/conductivity.hpp>
#include <boost/units/systems/si/current.hpp>
#include <boost/units/systems/si/dimensionless.hpp>
#include <boost/units/systems/si/dose_equivalent.hpp>
#include <boost/units/systems/si/dynamic_viscosity.hpp>
#include <boost/units/systems/si/electric_charge.hpp>
#include <boost/units/systems/si/electric_potential.hpp>
#include <boost/units/systems/si/energy.hpp>
#include <boost/units/systems/si/force.hpp>
#include <boost/units/systems/si/frequency.hpp>
#include <boost/units/systems/si/illuminance.hpp>
#include <boost/units/systems/si/impedance.hpp>
#include <boost/units/systems/si/inductance.hpp>
#include <boost/units/systems/si/kinematic_viscosity.hpp>
#include <boost/units/systems/si/length.hpp>
#include <boost/units/systems/si/luminous_flux.hpp>
#include <boost/units/systems/si/luminous_intensity.hpp>
#include <boost/units/systems/si/magnetic_field_intensity.hpp>
#include <boost/units/systems/si/magnetic_flux.hpp>
#include <boost/units/systems/si/magnetic_flux_density.hpp>
#include <boost/units/systems/si/mass.hpp>
#include <boost/units/systems/si/mass_density.hpp>
#include <boost/units/systems/si/moment_of_inertia.hpp>
#include <boost/units/systems/si/momentum.hpp>
#include <boost/units/systems/si/permeability.hpp>
#include <boost/units/systems/si/permittivity.hpp>
#include <boost/units/systems/si/plane_angle.hpp>
#include <boost/units/systems/si/power.hpp>
#include <boost/units/systems/si/pressure.hpp>
#include <boost/units/systems/si/reluctance.hpp>
#include <boost/units/systems/si/resistance.hpp>
#include <boost/units/systems/si/resistivity.hpp>
#include <boost/units/systems/si/solid_angle.hpp>
#include <boost/units/systems/si/surface_density.hpp>
#include <boost/units/systems/si/surface_tension.hpp>
#include <boost/units/systems/si/temperature.hpp>
#include <boost/units/systems/si/time.hpp>
#include <boost/units/systems/si/torque.hpp>
#include <boost/units/systems/si/velocity.hpp>
#include <boost/units/systems/si/volume.hpp>
#include <boost/units/systems/si/wavenumber.hpp>

#endif // BOOST_UNITS_SI_HPP
