/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2001-2011 Hartmut <PERSON>
    Copyright (c) 2011 <PERSON>
    http://spirit.sourceforge.net/

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#ifndef BOOST_SPIRIT_INCLUDE_QI_KWD
#define BOOST_SPIRIT_INCLUDE_QI_KWD

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/repository/home/<USER>/directive/kwd.hpp>

#endif
/*=============================================================================
    Copyright (c) 2001-2011 Joel de Guzman
    Copyright (c) 2001-2011 Hartmut Kaiser
    Copyright (c) 2011 Thomas Bernard
    http://spirit.sourceforge.net/

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#ifndef BOOST_SPIRIT_INCLUDE_QI_KWD
#define BOOST_SPIRIT_INCLUDE_QI_KWD

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/repository/home/<USER>/directive/kwd.hpp>

#endif
