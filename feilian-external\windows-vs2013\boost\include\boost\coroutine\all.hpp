
//          Copyright <PERSON> 2009.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_COROUTINES_ALL_H
#define BOOST_COROUTINES_ALL_H

#include <boost/coroutine/attributes.hpp>
#include <boost/coroutine/coroutine.hpp>
#include <boost/coroutine/exceptions.hpp>
#include <boost/coroutine/flags.hpp>
#include <boost/coroutine/protected_stack_allocator.hpp>
#include <boost/coroutine/segmented_stack_allocator.hpp>
#include <boost/coroutine/stack_allocator.hpp>
#include <boost/coroutine/stack_context.hpp>
#include <boost/coroutine/stack_traits.hpp>
#include <boost/coroutine/standard_stack_allocator.hpp>

#endif // BOOST_COROUTINES_ALL_H
