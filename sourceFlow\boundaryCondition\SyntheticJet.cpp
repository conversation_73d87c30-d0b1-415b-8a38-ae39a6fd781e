﻿#include "sourceFlow/boundaryCondition/SyntheticJet.h"

namespace Boundary
{
namespace Flow
{

SyntheticJet::SyntheticJet(const int &boundaryPatchID, Package::FlowPackage &data,
    const Vector &direction_, const Scalar &velocityMean_, const Scalar &velocityAmplitude_,
    const Scalar &frequency_, const Scalar &theta_)
    :
    FlowBoundary(boundaryPatchID, data),
    currentTime(data.GetUnsteadyStatus().currentTime),
	startTime(data.GetUnsteadyStatus().startTime)
{    
    this->direction = direction_;
    this->velocityMean = velocityMean_;
    this->velocityAmplitude = velocityAmplitude_;
    this->frequency = frequency_;
    this->omega = frequency_ * 2.0 * PI;
    this->theta = theta_ * PI / 180.0;

    //检查速度方向
    this->directionNormFlag = false;
    const Scalar mag = direction.Mag();
    if (mag < 0.95) this->directionNormFlag = true;
    else if (mag < 1.05) {}
    else FatalError("SyntheticJet::SyntheticJet: velocity direction error...");
}

void SyntheticJet::Initialize()
{
    this->UpdateBoundaryCondition();
}

void SyntheticJet::UpdateBoundaryCondition()
{
    // 密度、压强、温度等直接取相邻体心值
    this->BoundFromElement(rho);
    this->BoundFromElement(p);
    this->BoundFromElement(T);

    // 更新速度
	const Scalar UbMag = velocityMean + velocityAmplitude * sin(omega * (currentTime + startTime) + theta);
    Vector Ub = UbMag * direction;
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();    

        if (directionNormFlag) Ub = (-UbMag) * mesh->GetFace(faceID).GetNormal(); //速度方向朝向内

        if (nodeCenter)
        {            
            U.SetValue(ownerID, Ub);
            U.SetValue(neighID, Ub);            
        }
        else
        {            
            U.SetValue(neighID, 2.0 * Ub - U.GetValue(ownerID));
        }
    }
}

void SyntheticJet::AddDiffusiveResidual()
{
    if (!nodeCenter) this->AddDiffusiveResidualCellCenter();
    else             this->AddDiffusiveResidualNodeCenter();
}

void SyntheticJet::AddConvectiveResidual()
{
	if (jacobian) this->AddConvectiveResidualReflected();
	else          this->AddConvectiveResidualAverage();
}

void SyntheticJet::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;

    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();

        residualMomentum.SetValue(ownerID, Vector0);
    }
}

}// namespace Flow
}// namespace Boundary
