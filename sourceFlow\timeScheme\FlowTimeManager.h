﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowTimeManager.h
//! <AUTHOR>
//! @brief NS方程时间管理类，用于管理各类时间推进格式
//! @date 2021-04-07
//
//------------------------------修改日志----------------------------------------
// 2021-04-07 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_timeScheme_FlowTimeManager_
#define _sourceFlow_timeScheme_FlowTimeManager_

#include "sourceFlow/timeScheme/FlowTime.h"
#include "sourceFlow/timeScheme/RungeKutta.h"
#include "sourceFlow/timeScheme/LUSGS.h"
#include "sourceFlow/timeScheme/ExactJacobian.h"

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief  流场计算时间管理类
 * 
 */
class  FlowTimeManager
{
public:
    /**
     * @brief 构造函数，创建流场时间管理器对象
     * 
     * @param[in, out] data 当前网格层所对应的流场包
     */
    FlowTimeManager(Package::FlowPackage &data);

    /**
     * @brief 析构函数
     * 
     */
    ~FlowTimeManager();

    /**
     * @brief 初始化
     * 
     * @param[in] initialType 流场初始化类型
     */
    void Initialize(const Initialization::Type &initialType);

    /**
    * @brief 计算残差
    *
    */
    void CalculateResidual();

    /**
     * @brief 时间推进迭代求解一次
     * 
     * @param[in] recalculateResiduals 本层网格重新计算残值标识
     * @param[in] calculateForcingFunction 本层网格计算力源项标识
     */
    void Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction);
    
    /**
     * @brief 更新边界条件
     * 
     */
    void UpdateBoundaryCondition();
    
    /**
     * @brief 设置时间类对象中的细网格层级
     * 
     * @param[in] fineMeshLevel 细网格层级
     */
    void SetFineGridLevel(const int &fineMeshLevel);

    /**
     * @brief 保存上一步物理场
     * 
     */
    void SaveOld();

    /**
    * @brief 更新梯度场和muT
    *
    */
    void UpdateGradientAndMuT();

private:
    /**
     * @brief 确定时间格式指针
     * 
     */
    void SetTimePointer();    

private:
    Package::FlowPackage &flowPackage; ///< 包含流场和残值场的数据包
    Time::Flow::FlowTime *time; ///< 时间格式对象    
};

} // namespace Flow
} // namespace Time

#endif 