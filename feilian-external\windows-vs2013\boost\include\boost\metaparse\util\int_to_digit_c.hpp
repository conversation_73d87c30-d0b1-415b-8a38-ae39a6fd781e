#ifndef BOOST_METAPARSE_UTIL_INT_TO_DIGIT_C_HPP
#define BOOST_METAPARSE_UTIL_INT_TO_DIGIT_C_HPP

// Copyright <PERSON> (<EMAIL>)  2013.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/v1/util/int_to_digit_c.hpp>

namespace boost
{
  namespace metaparse
  {
    namespace util
    {
      using v1::util::int_to_digit_c;
    }
  }
}

#endif

