﻿#include "sourceFlow/resultsProcess/ForceProcess.h"

ForceProcess::ForceProcess(std::vector<Package::FlowPackage *> flowPackageVector_)
    : flowPackageVector(flowPackageVector_)
{
    dimension3D = flowPackageVector_[0]->GetMeshStruct().dim3;

    // 获取合力计算所需参考量
    const auto &flowConfigure = flowPackageVector_[0]->GetFlowConfigure();
    const auto &flowReference = flowConfigure.GetFlowReference();
    const auto &meshReference = flowConfigure.GetMeshParameters().reference;
    alpha = flowReference.alpha * PI / 180.0;
    beta = flowReference.beta * PI / 180.0;
    densityReference = flowReference.density;
    velocityReference = flowReference.velocity;
    pressureReference = flowReference.staticPressure;
    cReference = meshReference.cRef;
    bReference = meshReference.bRef;
    cmReference = meshReference.cmRef;
    SReference = meshReference.SRef;

    // 来流动压计算
    dynamicPressure = 0.5 * densityReference * velocityReference & velocityReference;

    // 转换矩阵
    matrix.Resize(3, 3);
    if (dimension3D)
    {
        matrix.SetValue(0, 0,  cos(alpha) * cos(-beta));
        matrix.SetValue(0, 1,               sin(-beta));
        matrix.SetValue(0, 2,  sin(alpha) * cos(-beta));
        matrix.SetValue(1, 0, -cos(alpha) * sin(-beta));
        matrix.SetValue(1, 1,               cos(-beta));
        matrix.SetValue(1, 2, -sin(alpha) * sin(-beta));
        matrix.SetValue(2, 0, -sin(alpha)            );
        matrix.SetValue(2, 1,  0.0                   );
        matrix.SetValue(2, 2,  cos(alpha)            );
    }
    else
    {
        matrix.Resize(3, 3);
        matrix.SetValue(0, 0,  cos(alpha));
        matrix.SetValue(0, 1,  sin(alpha));
        matrix.SetValue(0, 2,  0.0       );
        matrix.SetValue(1, 0, -sin(alpha));
        matrix.SetValue(1, 1,  cos(alpha));
        matrix.SetValue(1, 2,  0.0       );
        matrix.SetValue(2, 0,  0.0       );
        matrix.SetValue(2, 1,  0.0       );
        matrix.SetValue(2, 2,  0.0       );
    }
    
    // 获得全局网格边界名称容器
    globalBoundaryName = flowConfigure.GetGlobalBoundaryNameList();

    // 确定全局网格每个边界是否为物面，true为物面
    globalWallFlag.resize(globalBoundaryName.size());
	if (!flowConfigure.GetMonitor().forces.boundaryIDList.empty())
	{
		if (flowConfigure.GetMonitor().forces.boundaryIDList.size() > globalBoundaryName.size())
            FatalError("参数文件后处理参考值boundaryIDList参数设置错误！");

		for (int i = 0; i < globalWallFlag.size(); i++)
			for (int j = 0; j < flowConfigure.GetMonitor().forces.boundaryIDList.size(); j++)
			{
				if (flowConfigure.GetMonitor().forces.boundaryIDList[j] > globalBoundaryName.size())
                    FatalError("参数文件后处理参考值boundaryIDList参数中有无效边界ID！");
                
				if (!flowConfigure.JudgeWallGlobal(flowConfigure.GetMonitor().forces.boundaryIDList[j]))
                    FatalError("参数文件后处理参考值boundaryIDList参数中有非物面边界条件！");

				if (i == flowConfigure.GetMonitor().forces.boundaryIDList[j])
				{
					globalWallFlag[i] = true;
				}
				else
				{
					globalWallFlag[i] = false;
				}
			}
	}
	else
	{
		for (int i = 0; i < globalWallFlag.size(); i++)
			globalWallFlag[i] = flowConfigure.JudgeWallGlobal(i);
	}

    positionTotalForce = globalBoundaryName.size();

    // 确定风轴力容器大小
    forceWindAll.resize(positionTotalForce + 1);
    for (int i = 0; i < forceWindAll.size(); i++) forceWindAll[i].resize(FORCE_SUM+1);

    // 确定体轴力容器大小
    forceBodyAll.resize(positionTotalForce + 1);
    for (int i = 0; i < forceBodyAll.size(); i++) forceBodyAll[i].resize(FORCE_SUM+1);

	// 确定无量纲力容器大小
	forceDimensionalAll.resize(positionTotalForce);
	for (int i = 0; i < forceDimensionalAll.size(); i++) forceDimensionalAll[i].resize(Dimensional_AREA_SUM + 1);

    const auto &monitorForce = flowConfigure.GetMonitor().forces;
    
    monitorForceName.push_back("CL");
    monitorForceName.push_back("CD");
    monitorForceName.push_back("CmX");
    monitorForceName.push_back("CmY");
    monitorForceName.push_back("CmZ");

    // 全NS计算标识
    fullNS = flowConfigure.GetFluxScheme(0).viscous == Flux::Flow::Viscous::CENTRAL_FULL;
}

void ForceProcess::Initialize()
{
    monitorForceValues.clear();
}

void ForceProcess::OutputForceCoefficient()
{
    this->CalculateForceCoefficient(0);

    // 0号进程输出，其它进程返回
    if (GetMPIRank() > 0) return;

    // 输出标识打印
    Print("\n");
    PrintTitleInfo(" Forces and Moment ");

    std::ostringstream stringStream;

    // 参考力输出
    stringStream.str("");
    stringStream << "\nReference values: \n";
    stringStream << std::setw(26) << "       alpha (deg)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << alpha * 180.0 / PI << "\n";
    stringStream << std::setw(26) << "        beta (deg)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << beta * 180.0 / PI << "\n";
    stringStream << std::setw(26) << "     density (kg/m^3):" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << densityReference << "\n";
    stringStream << std::setw(26) << "    velocity (m/s)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << velocityReference.Mag() << "\n";
    stringStream << std::setw(26) << "    pressure (Pa)    :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << pressureReference << "\n";
    stringStream << std::setw(26) << "       chord (m)     :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << cReference << "\n";
    stringStream << std::setw(26) << "        span (m)     :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << bReference << "\n";
    stringStream << std::setw(26) << "        area (m^2)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << SReference << "\n";
    stringStream << std::setw(26) << "moment point (m)     :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << cmReference.X()
                                                              << std::setiosflags(std::ios_base::scientific) << std::setw(15) << cmReference.Y()
                                                              << std::setiosflags(std::ios_base::scientific) << std::setw(15) << cmReference.Z();
    Print(stringStream.str());
    
    for (int part = 0; part < forceWindAll.size(); part++)
    {
        if (!globalWallFlag[part] && part != forceWindAll.size() - 1) continue;

        // 部件名称输出
        std::string partName;
        if (part == forceWindAll.size()-1)
        {
            partName = "SUM";
        }
        else
        {
            const int zoneID = flowPackageVector[0]->GetFlowConfigure().GetGlobalBoundary(part).zoneID;
            partName = "Zone" + ToString(zoneID) + "-" + globalBoundaryName[part];
        }
        Print("\n" + partName + ":");

		if (dimension3D)
		{
			// 风轴力系数输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Force (flow-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceWindAll[part][FORCE_SUM].Z() << " (CL)  "
				<< std::setw(16) << forceWindAll[part][FORCE_SUM].X() << " (CD)  "
				<< std::setw(16) << forceWindAll[part][FORCE_SUM].Y() << " (CC)  ";
			Print(stringStream.str());

			// 阻力分量输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Drag (flow-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceWindAll[part][FORCE_PRESSURE].X() << " (CD_P)"
				<< std::setw(16) << forceWindAll[part][FORCE_VISCOUS].X() << " (CD_V)";
			Print(stringStream.str());

			// 体轴力系数输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Force (body-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceBodyAll[part][FORCE_SUM].Z() << " (CN)  "
				<< std::setw(16) << forceBodyAll[part][FORCE_SUM].X() << " (CA)  "
				<< std::setw(16) << forceBodyAll[part][FORCE_SUM].Y() << " (CY)  ";
			Print(stringStream.str());

			// 力矩系数输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Moment (body-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceBodyAll[part][MOMENT].X() << " (CmX) "
				<< std::setw(16) << forceBodyAll[part][MOMENT].Y() << " (CmY) "
				<< std::setw(16) << forceBodyAll[part][MOMENT].Z() << " (CmZ) ";
			Print(stringStream.str());
		}
		else
		{
			// 风轴力系数输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Force (flow-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceWindAll[part][FORCE_SUM].Y() << " (CL)  "
				<< std::setw(16) << forceWindAll[part][FORCE_SUM].X() << " (CD)  ";
			Print(stringStream.str());

			// 阻力分量输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Drag (flow-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceWindAll[part][FORCE_PRESSURE].X() << " (CD_P)"
				<< std::setw(16) << forceWindAll[part][FORCE_VISCOUS].X() << " (CD_V)";
			Print(stringStream.str());

			// 体轴力系数输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Force (body-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceBodyAll[part][FORCE_SUM].Y() << " (CN)  "
				<< std::setw(16) << forceBodyAll[part][FORCE_SUM].X() << " (CA)  ";
			Print(stringStream.str());

			// 力矩系数输出
			stringStream.str("");
			stringStream
				<< std::setw(35) << "Coefficient_Moment (body-axis): "
				<< std::setiosflags(std::ios_base::scientific)
				<< std::setw(16) << forceBodyAll[part][MOMENT].Z() << " (CmZ) ";
			Print(stringStream.str());
		}
    }

    Print("\n" + ObtainInfoTitle());

    return;
}

const std::vector<Scalar> ForceProcess::CalculateMonitorForce(const int &currentLevel)
{
    this->CalculateForceCoefficient(currentLevel);

    // 构造监测力容器
    std::vector<Scalar> monitorForceTotal(monitorForceName.size());
    if (dimension3D) monitorForceTotal[0] = forceWindAll[positionTotalForce][FORCE_SUM].Z();
    else             monitorForceTotal[0] = forceWindAll[positionTotalForce][FORCE_SUM].Y();
    monitorForceTotal[1] = forceWindAll[positionTotalForce][FORCE_SUM].X();
    monitorForceTotal[2] = forceWindAll[positionTotalForce][MOMENT].X();
    monitorForceTotal[3] = forceWindAll[positionTotalForce][MOMENT].Y();
    monitorForceTotal[4] = forceWindAll[positionTotalForce][MOMENT].Z();

    monitorForceValues.clear();
    for (int k=0; k<monitorForceTotal.size(); k++) monitorForceValues.push_back(monitorForceTotal[k]);

    return monitorForceValues;
}

std::vector<std::vector<Vector>> &ForceProcess::CalculateForceCoefficient(const int &currentLevel)
{
    // 力容器置零
    for (int i = 0; i < forceBodyAll.size(); i++)
    {
        for (int j = FORCE_PRESSURE; j <= FORCE_SUM; j++)
        {
            forceBodyAll[i][j] = Vector0;
            forceWindAll[i][j] = Vector0;
        }		
    }
	for (int i = 0; i < forceDimensionalAll.size(); i++)
	{
		for (int j = Dimensional_FORCE_PRESSURE; j <= Dimensional_AREA_SUM; j++)
		{
			forceDimensionalAll[i][j] = Vector0;
		}
	}
    // 所有进程合力计算    
    this->CalculateLocalForce(currentLevel);
    MPIBarrier();

    for (int i = 0; i < positionTotalForce; i++)
    {
        for (int j = FORCE_PRESSURE; j <= MOMENT;j++)
        {
            SumAllProcessor(forceBodyAll[i][j], 0);
        }
    }
	for (int i = 0; i < forceDimensionalAll.size(); i++)
	{
		for (int j = Dimensional_FORCE_PRESSURE; j <= Dimensional_AREA_SUM; j++)
		{
            SumAllProcessor(forceDimensionalAll[i][j], 0);
		}
	}

    // 无量纲化,并转化力到风轴系（力矩是体轴系），并求合力到SUM中
    if (GetMPIRank() == 0)
    {
        // 计算参考力
        referenceForce = dynamicPressure * SReference;
        if (referenceForce <= 0) referenceForce = 1.0;

        // 所有部件循环，计算部件力
        for (int i = 0; i < positionTotalForce; i++)
        {
            // 跳过非壁面边界
            if (!globalWallFlag[i]) continue;

            // 计算风轴和体轴的无量纲力系数
            for (int j = FORCE_PRESSURE; j <= FORCE_VISCOUS; j++)
            {
                // 体轴系无量纲化
                forceBodyAll[i][j] /=  referenceForce;
                
                // 体轴系转风轴系
                Matrix forceBody(3, 1);
                forceBody.SetValue(0, 0, forceBodyAll[i][j].X());
                forceBody.SetValue(1, 0, forceBodyAll[i][j].Y());
                if (dimension3D) forceBody.SetValue(2, 0, forceBodyAll[i][j].Z());

                const Matrix forceWind = matrix * forceBody;

                forceWindAll[i][j].SetX(forceWind.GetValue(0, 0));
                forceWindAll[i][j].SetY(forceWind.GetValue(1, 0));
                if (dimension3D) forceWindAll[i][j].SetZ( forceWind.GetValue(2, 0));
            }

            // 计算压强力和粘性力的合力系数
            forceBodyAll[i][FORCE_SUM] = forceBodyAll[i][FORCE_PRESSURE] + forceBodyAll[i][FORCE_VISCOUS];
            forceWindAll[i][FORCE_SUM] = forceWindAll[i][FORCE_PRESSURE] + forceWindAll[i][FORCE_VISCOUS];
            
            // 计算无量纲化力矩，力矩仅为体轴
            if (dimension3D)
            {
                forceBodyAll[i][MOMENT].SetX(forceBodyAll[i][MOMENT].X() / (referenceForce * bReference));
                forceBodyAll[i][MOMENT].SetY(forceBodyAll[i][MOMENT].Y() / (referenceForce * cReference));
                forceBodyAll[i][MOMENT].SetZ(forceBodyAll[i][MOMENT].Z() / (referenceForce * bReference));
            }
            else
            {
                forceBodyAll[i][MOMENT].SetX(0.0);
                forceBodyAll[i][MOMENT].SetY(0.0);
                forceBodyAll[i][MOMENT].SetZ(forceBodyAll[i][MOMENT].Z() / (referenceForce * cReference));
            }
            forceWindAll[i][MOMENT] = forceBodyAll[i][MOMENT];
        }
        
        // 部件力完成后计算所有部件力的和
        for (int j = FORCE_PRESSURE; j <= FORCE_SUM; j++)
        {
            forceWindAll[positionTotalForce][j] = Vector0;
            forceBodyAll[positionTotalForce][j] = Vector0;
            for (int i = 0; i < positionTotalForce; i++)
            {
                forceWindAll[positionTotalForce][j] += forceWindAll[i][j];
                forceBodyAll[positionTotalForce][j] += forceBodyAll[i][j];
            }
        }
    }

    MPIBarrier();
    
    return forceWindAll;
}

void ForceProcess::CalculateLocalForce(const int &currentLevel)
{
    auto *flowPackage = flowPackageVector[currentLevel];
    const auto &flowConfigure = flowPackage->GetFlowConfigure();
    Mesh *currentMesh = flowPackage->GetMeshStruct().mesh;
    const bool &viscousFlag = flowPackage->GetTurbulentStatus().viscousFlag;
    const bool &nodeCenter = flowPackage->GetFlowConfigure().GetPreprocess().dualMeshFlag;

    ElementField<Scalar> *p = flowPackage->GetField().pressure;
    ElementField<Vector> *U = flowPackage->GetField().velocity;
    ElementField<Scalar> *muLaminar = flowPackage->GetField().muLaminar;
    ElementField<Scalar> *muTurbulence = flowPackage->GetField().muTurbulent;
    ElementField<Tensor> *gradientU = fullNS ? flowPackage->GetGradientField().gradientU : nullptr;

    // 本进程边界和全局边界的对应关系
    // 如 k=globalIndexOfLocalBoundary[i]表示本进程的i号边界对应全局网格的k号边界
    std::vector<int> globalIndexOfLocalBoundary(currentMesh->GetBoundarySize());
    for (int i = 0; i < globalIndexOfLocalBoundary.size(); i++)
        globalIndexOfLocalBoundary[i] = flowConfigure.GetLocalBoundary(currentLevel, i).globalID;

    // 确定本进程每个边界是否为物面，true为物面
    const int boundarySize = currentMesh->GetBoundarySize();
    std::vector<bool> localWallFlag;
    localWallFlag.resize(boundarySize);
    for (int patchID = 0; patchID < boundarySize; ++patchID)
        localWallFlag[patchID] = flowConfigure.JudgeWallLocal(currentLevel, patchID);

    // 参考面积为0时，重新计算
    if(SReference < SMALL)
    {
        SReference = 0.0;
        for (int patchID = 0; patchID < boundarySize; ++patchID)
        {
            if (!localWallFlag[patchID]) continue;
            
            // 遍历面进行累加
		    const int &faceSize = currentMesh->GetBoundaryFaceNumberInDomain(patchID);
		    for (int index = 0; index < faceSize; ++index)
		    {
		    	// 得到面相关信息
		    	const int &faceID = currentMesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = currentMesh->GetFace(faceID);
                const Vector faceArea = face.GetArea() * face.GetNormal();
                if (dimension3D) SReference += std::fabs(faceArea.Z());
                else SReference += std::fabs(faceArea.Y());
            }
        }
        SumAllProcessor(SReference, 0);
        MPIBroadcast(SReference, 0);
        SReference /= 2.0;
    }

    const Scalar oneThird = 1.0 / 3.0;

    // 判定流动是否无粘
    for (int patchID = 0; patchID < boundarySize; ++patchID)
    {
        if (!localWallFlag[patchID]) continue;

        // 该物理面在全局网格中的编号
        const int &globalPatchID = globalIndexOfLocalBoundary[patchID];

        // 遍历面进行累加
		const int &faceSize = currentMesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = currentMesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = currentMesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighborID = face.GetNeighborID();
            const Vector &faceNormal = face.GetNormal();
            const Vector faceArea = faceNormal * face.GetArea();

            // 某面的压强力
            const Scalar pFace = 0.5 * (p->GetValue(ownerID) + p->GetValue(neighborID));
            Vector forcePressure = (pFace - pressureReference) * faceArea;
            
            // 计算粘性力
            Vector forceViscous = Vector0;
            if (viscousFlag)
            {
                const Vector UFace = 0.5 * (U->GetValue(ownerID) + U->GetValue(neighborID));

                const int ID1 = nodeCenter ? ownerID : neighborID;
                const int ID2 = nodeCenter ? currentMesh->GetInnerElementIDForBoundaryElement(patchID, index) : ownerID;

                Scalar muF = 0.5 * (muLaminar->GetValue(ID1) + muLaminar->GetValue(ID2));
                if (muTurbulence != nullptr) muF += 0.5 * (muTurbulence->GetValue(ID1) + muTurbulence->GetValue(ID2));

                if (fullNS)
                {
                    const Tensor gradUFace = 0.5 * (gradientU->GetValue(ID1) + gradientU->GetValue(ID2));
                    const Tensor tauFace = flowPackage->CalculateTau(muF, gradUFace);
                    forceViscous = -faceArea * tauFace;
                }
                else
                {
                    const Vector dU = U->GetValue(ID2) - UFace;
                    const Scalar distanceInv = 1.0 / ((currentMesh->GetElement(ID2).GetCenter() - face.GetCenter()).Mag() + SMALL);
                    forceViscous = face.GetArea() * muF * dU * distanceInv;
                }

                forceViscous -= (forceViscous & faceNormal) * faceNormal;
            }
            
            // 累加
            forceBodyAll[globalPatchID][FORCE_PRESSURE] += forcePressure;
            forceBodyAll[globalPatchID][FORCE_VISCOUS] += forceViscous;
            const Vector distance = face.GetCenter() - cmReference;
            forceBodyAll[globalPatchID][MOMENT] += distance ^ (forcePressure + forceViscous);

			forceDimensionalAll[globalPatchID][Dimensional_FORCE_PRESSURE] += forcePressure;
			forceDimensionalAll[globalPatchID][Dimensional_FORCE_VISCOUS] += forceViscous;
			forceDimensionalAll[globalPatchID][Dimensional_MOMENT_PRESSURE] += distance ^ forcePressure;
			forceDimensionalAll[globalPatchID][Dimensional_MOMENT_VISCOUS] += distance ^ forceViscous;
			forceDimensionalAll[globalPatchID][Dimensional_AREA_SUM] += faceArea;
        }
    }

    return;
}

void ForceProcess::OutputForceDimensional(std::fstream &file, int &currentStep)
{
	std::ostringstream stringStream;

	for (int patchID = 0; patchID < forceDimensionalAll.size(); patchID++)
	{
        if (!globalWallFlag[patchID]) continue;

		stringStream.str("");
		stringStream
			<< std::setw(16) << currentStep
			<< std::setw(15) << patchID
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_FORCE_PRESSURE].X()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_FORCE_PRESSURE].Y()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_FORCE_PRESSURE].Z()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_FORCE_VISCOUS].X()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_FORCE_VISCOUS].Y()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_FORCE_VISCOUS].Z()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_MOMENT_PRESSURE].X()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_MOMENT_PRESSURE].Y()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_MOMENT_PRESSURE].Z()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_MOMENT_VISCOUS].X()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_MOMENT_VISCOUS].Y()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_MOMENT_VISCOUS].Z()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_AREA_SUM].X()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_AREA_SUM].Y()
			<< std::setw(15) << std::scientific << forceDimensionalAll[patchID][Dimensional_AREA_SUM].Z();
		file << stringStream.str() << std::endl;
	}
}