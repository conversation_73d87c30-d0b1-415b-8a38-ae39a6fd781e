// Copyright <PERSON> 2003.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
#ifndef DEF_HELPER_FWD_DWA2003810_HPP
# define DEF_HELPER_FWD_DWA2003810_HPP

# include <boost/python/detail/not_specified.hpp>

namespace boost { namespace python { namespace detail { 

template <class T1, class T2 = not_specified, class T3 = not_specified, class T4 = not_specified>
struct def_helper;

}}} // namespace boost::python::detail

#endif // DEF_HELPER_FWD_DWA2003810_HPP
