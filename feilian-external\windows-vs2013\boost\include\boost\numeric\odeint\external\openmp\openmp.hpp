/*
 [auto_generated]
 boost/numeric/odeint/external/openmp/openmp.hpp

 [begin_description]
 Wrappers for OpenMP.
 [end_description]

 Copyright 2013 <PERSON><PERSON>
 Copyright 2013 <PERSON>
 Copyright 2013 Pascal Germroth

 Distributed under the Boost Software License, Version 1.0.
 (See accompanying file LICENSE_1_0.txt or
 copy at http://www.boost.org/LICENSE_1_0.txt)
 */


#ifndef BOOST_NUMERIC_ODEINT_EXTERNAL_OPENMP_OPENMP_HPP_INCLUDED
#define BOOST_NUMERIC_ODEINT_EXTERNAL_OPENMP_OPENMP_HPP_INCLUDED

// level 1: parallel iteration over random access container
#include <boost/numeric/odeint/external/openmp/openmp_range_algebra.hpp>

// level 2: split range state
#include <boost/numeric/odeint/external/openmp/openmp_state.hpp>

// level 3: process a random access container of sub-states in parallel
#include <boost/numeric/odeint/external/openmp/openmp_nested_algebra.hpp>

#endif
