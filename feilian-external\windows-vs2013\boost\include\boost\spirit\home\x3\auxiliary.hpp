/*=============================================================================
    Copyright (c) 2001-2014 <PERSON>
    Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_X3_AUXILIARY_FEBRUARY_03_2007_0355PM)
#define BOOST_SPIRIT_X3_AUXILIARY_FEBRUARY_03_2007_0355PM

#include <boost/spirit/home/<USER>/auxiliary/any_parser.hpp>
#include <boost/spirit/home/<USER>/auxiliary/eps.hpp>
#include <boost/spirit/home/<USER>/auxiliary/guard.hpp>
#include <boost/spirit/home/<USER>/auxiliary/eol.hpp>
#include <boost/spirit/home/<USER>/auxiliary/eoi.hpp>
#include <boost/spirit/home/<USER>/auxiliary/attr.hpp>

#endif
