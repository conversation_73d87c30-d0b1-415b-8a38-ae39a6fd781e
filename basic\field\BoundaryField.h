﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BoundaryField.h
//! <AUTHOR>
//! @brief 边界场类
//! @date 2020-07-23
//
//------------------------------修改日志----------------------------------------
//
// 2022-02-11 李艳亮、乔龙（气动院）
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_field_BoundaryField_
#define _basic_field_BoundaryField_

#include "basic/mesh/Mesh.h"

/**
 * @brief 边界场
 * 
 * @tparam Type 
 */
template<class Type>
class BoundaryField
{
public:
    /**
     * @brief 枚举类型：场的状态
     * 
     */
    enum FieldStatus
    {
        fsNotExist, ///< 不存在
        fsCreated, ///< 创建
        fsAssigned ///< 分配
    };

public:    
    /**
     * @brief 构造函数
     * 
     * @param[in] UGB 网格指针
     * @param[in] name 场名称
     */
    BoundaryField(Mesh* UGB, const std::string name = "NO_NAME");

    /**
     * @brief 构造函数
     * 
     * @param[in] UGB 网格指针
     * @param[in] value 初始值
     * @param[in] name 场名称
     */
    BoundaryField(Mesh* UGB, const Type &value, const std::string name = "NO_NAME");

    /**
     * @brief 获取网格
     * 
     * @return Mesh* 
     */
    Mesh *GetMesh()const { return this->p_blockMesh; }

    /**
     * @brief 获得给定位置的场值
     * 
     * @param[in] patchID 边界编号
     * @param[in] index 边界面序列号
     * @return const Type& 
     */
    const Type &GetValue(const int &patchID, const int &index)const { return this->v_value[patchID][index]; }

    /**
     * @brief 修改给定位置的场值
     * 
     * @param[in] patchID 边界编号
     * @param[in] index 边界面序列号
     * @param[in] value 新值
     */
    void SetValue(const int &patchID, const int &index, const Type &value) { this->v_value[patchID][index] = value; }
    
    /**
     * @brief 获得场的名称
     * 
     * @return const std::string& 
     */
    const std::string &GetName()const { return this->st_name; }

    /**
     * @brief 修改场的名称
     * 
     * @param[in] name 新名称
     */
    void SetName(const std::string &name) { this->st_name = name; }

    /**
     * @brief 开辟场的存储空间，并初始化为零
     * 
     */
    void Initialize();

    /**
     * @brief 开辟场的存储空间，并用initalValue初始化
     * 
     * @param[in] initalValue 初始值
     */
    void Initialize(Type initalValue);
    
    /**
     * @brief 创建边界场
     * 
     */
    void Create();    

	/**
	* @brief 检查场是否存在
	*
	* @return true 存在
	* @return false 不存在
	*/
	bool Existence() const { return fsNotExist != this->fs_status; }

	/**
	* @brief 检查场空间是否已经分配
	*
	* @return true 分配
	* @return false 未分配
	*/
	bool Assignment() const { return fsAssigned == this->fs_status; }

	/**
	* @brief 释放场
	* 修改场的状态为fsCreated，场存储空间保留，值没有意义
	*
	*/
	void Free() { this->fs_status = fsCreated; }

	/**
	* @brief 销毁场存储空间
	*
	*/
	void Destroy() { this->fs_status = fsNotExist; std::vector<std::vector<Type>>().swap(this->v_value); }

    /**
     * @brief 从文件初始化边界场
     * 
     * @param[in] fileName 文件名
     * @param[in] binary 二进制标识，true为二进制
     */
    void ReadFile(const std::string fileName, const bool binary = true);
    
    /**
     * @brief 边界场写到文件
     * 
     * @param[in] fileName 文件名
     * @param[in] binary 二进制标识，true为二进制
     */
    void WriteFile(const std::string fileName, const bool binary = true);

    /**
     * @brief 运算符=的重载
     * 
     * @param[in] rhs 等号右端边界场
     * @return BoundaryField<Type>& 
     */
    BoundaryField<Type>& operator = (const BoundaryField<Type>& rhs);

    /**
     * @brief 运算符=的重载
     * 
     * @param[in] rhs 等号右端值
     * @return BoundaryField<Type>& 
     */
    BoundaryField<Type>& operator = (const Type &rhs);

private:
    Mesh* p_blockMesh; ///< 网格指针    
    std::string st_name; ///< 场的名称    
    FieldStatus fs_status; ///< 场的状态
    std::vector<std::vector<Type>> v_value; ///< 场的存储值
};

#endif
