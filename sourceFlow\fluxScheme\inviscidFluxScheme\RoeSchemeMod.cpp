﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/RoeSchemeMod.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

RoeSchemeMod::RoeSchemeMod(Package::FlowPackage &data,
                     Limiter::Limiter *limiter,
                     Flux::Flow::Precondition::Precondition *precondition)
    :
    UpwindScheme(data, limiter, precondition)
{
    const auto &flowConfigure = data.GetFlowConfigure();
    const int &currentLevel = data.GetMeshStruct().level;

    secondOrderFlag = (flowConfigure.GetFluxScheme(currentLevel).reconstructOrder == Flux::ReconstructionOrder::SECOND);

	if (precondition != nullptr)
	{
		FatalError("RoeSchemeMod::RoeSchemeMod: precondition暂时不能用");
		return;
	}
    
    dim3 = data.GetMeshStruct().dim3;
}

NSFaceFlux RoeSchemeMod::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    //得到面信息
    const Face &face = mesh->GetFace(faceID);
    const int &ownerID = face.GetOwnerID();
    const int &neighID = face.GetNeighborID();
    const Vector &faceNormal = face.GetNormal();
    const Scalar &faceAreaMag = face.GetArea();

    //获取左右面值(无梯度修正)
    const Scalar &rhoLeft = rho.GetValue(ownerID);
    const Vector &ULeft = U.GetValue(ownerID);
    const Scalar &pLeft = p.GetValue(ownerID);
    const Scalar &TLeft = T.GetValue(ownerID);
    const Scalar hLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);
    const Scalar &cLeft = A.GetValue(ownerID);

    const Scalar &rhoRight = rho.GetValue(neighID);
    const Vector &URight = U.GetValue(neighID);
    const Scalar &pRight = p.GetValue(neighID);
    const Scalar &TRight = T.GetValue(neighID);
    const Scalar hRight = Cp * TRight + 0.5 * (URight & URight);
    const Scalar &cRight = A.GetValue(neighID);

    // 采用Roe平均计算面心处值
    const Scalar rhoRatio = sqrt(rhoRight / rhoLeft);
    const Scalar weightLeft = 1.0 / (1.0 + rhoRatio);
    const Scalar weightRight = rhoRatio / (1.0 + rhoRatio);
    const Scalar rhoMean = sqrt(rhoLeft * rhoRight);
    const Vector UMean = ULeft*weightLeft + URight*weightRight;
    const Scalar hMean = hLeft*weightLeft + hRight*weightRight;
    const Scalar UMean2Half = 0.5 * (UMean & UMean);
    const Scalar cMean2 = fabs(gamma1*(hMean - UMean2Half));
    const Scalar cMean = sqrt(cMean2);

    if (precondition != nullptr)
    {
        FatalError("RoeSchemeMod::FaceFluxCalculate: precondition is wrong!");
		return NSFaceFlux();
    }
    
    // 计算特征值
    const Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
    const Scalar UDistMean = UMean & distance.GetNormal();
    Scalar eigenvalue1 = fabs(UDistMean);
    Scalar eigenvalue4 = fabs(UDistMean + cMean);
    Scalar eigenvalue5 = fabs(UDistMean - cMean);

    // 熵修正
    if (entropyFixFlag)
    {
        const Scalar lambdaMean = fabs(UDistMean) + cMean;
        Scalar delta = Max(eigenvalue1, 0.2 * lambdaMean);
        eigenvalue1 = 0.5 * (eigenvalue1 * eigenvalue1 + delta * delta) / (delta + SMALL);
        delta = Max(eigenvalue4, 0.05 * lambdaMean);
        eigenvalue4 = 0.5 * (eigenvalue4*eigenvalue4 + delta * delta) / (delta + SMALL);
        delta = Max(eigenvalue5, 0.05 * lambdaMean);
        eigenvalue5 = 0.5 * (eigenvalue5 * eigenvalue5 + delta * delta) / (delta + SMALL);
    }
    
    // 计算相关差量值
    const Scalar deltaRho = rhoRight - rhoLeft;
    const Vector deltaU = URight - ULeft;
    const Scalar deltaP = pRight - pLeft;

    // 计算dWF
    Scalar deltaRhoCFace, deltaPCFace;
    Vector deltaUCFace;
    CalculateCharacteristics(deltaRhoCFace, deltaUCFace, deltaPCFace,
                             rhoMean, cMean, faceNormal,
                             deltaRho, deltaU, deltaP);

    // 利用梯度计算左右的差量值并修正
    if (secondOrderFlag)
    {
        const Scalar deltaRhoLeft = distance & rhoGradient->GetValue(ownerID);
        const Vector deltaULeft = distance * UGradient->GetValue(ownerID);
        const Scalar deltaPLeft = distance & pGradient->GetValue(ownerID);
        const Scalar deltaRhoRight = distance & rhoGradient->GetValue(neighID);
        const Vector deltaURight = distance * UGradient->GetValue(neighID);
        const Scalar deltaPRight = distance & pGradient->GetValue(neighID);

        Scalar deltaRhoCLeft, deltaPCLeft;
        Vector deltaUCLeft;
        CalculateCharacteristics(deltaRhoCLeft, deltaUCLeft, deltaPCLeft,
                                 rhoLeft, cLeft, faceNormal,
                                 deltaRhoLeft, deltaULeft, deltaPLeft);

        Scalar deltaRhoCRight, deltaPCRight;
        Vector deltaUCRight;
        CalculateCharacteristics(deltaRhoCRight, deltaUCRight, deltaPCRight,
            rhoRight, cRight, faceNormal,
            deltaRhoRight, deltaURight, deltaPRight);

        //综合计算有效的delta量(按照TVD方法计算的)
        const Scalar deltaRhoStar = CalculateDelta(deltaRhoCLeft, deltaRhoCRight, deltaRhoCFace);
        const Vector deltaUStar( CalculateDelta(deltaUCLeft.X(), deltaUCRight.X(), deltaUCFace.X()),
                                 CalculateDelta(deltaUCLeft.Y(), deltaUCRight.Y(), deltaUCFace.Y()),
                                 CalculateDelta(deltaUCLeft.Z(), deltaUCRight.Z(), deltaUCFace.Z()) );
        const Scalar deltaPStar = CalculateDelta(deltaPCLeft, deltaPCRight, deltaPCFace);

        // 计算限制器
        const Scalar limiterRho = MinMod(deltaRhoStar, deltaRhoCFace);
        const Vector limiterU( MinMod(deltaUStar.X(), deltaUCFace.X()),
                               MinMod(deltaUStar.Y(), deltaUCFace.Y()),
                               MinMod(deltaUStar.Z(), deltaUCFace.Z()) );
        const Scalar limiterP = MinMod(deltaPStar, deltaPCFace);

        //修正面心处值
        deltaRhoCFace -= limiterRho;
        deltaUCFace -= limiterU;
        deltaPCFace -= limiterP;
    }

    // 乘特征值
    deltaUCFace = deltaUCFace * eigenvalue1;
    deltaRhoCFace = deltaRhoCFace * eigenvalue4;
    deltaPCFace = deltaPCFace * eigenvalue5;
    if (!dim3) deltaUCFace.SetZ(Scalar0);

    // 计算通量
    const Scalar UNormMean = UMean & faceNormal;
    const Scalar deltaUCFaceNorm = faceNormal & deltaUCFace;
    const Scalar massFlux = deltaUCFaceNorm + deltaRhoCFace + deltaPCFace;
    const Vector momentumFlux = UMean * deltaUCFaceNorm + (faceNormal ^ deltaUCFace) * cMean
                              + (UMean + cMean * faceNormal) * deltaRhoCFace
                              + (UMean - cMean * faceNormal) * deltaPCFace;
    const Scalar energyFlux = UMean2Half * deltaUCFaceNorm + ((UMean ^ faceNormal) & deltaUCFace) * cMean
                            + (hMean + cMean * UNormMean) * deltaRhoCFace
                            + (hMean - cMean * UNormMean) * deltaPCFace;

    // 系数：0.5、面积、负号放在这里了
    const Scalar coefficient = -0.5 * faceAreaMag / cMean2;
    faceFlux.massFlux = massFlux * coefficient;
    faceFlux.momentumFlux = momentumFlux * coefficient;
    faceFlux.energyFlux = energyFlux * coefficient;

    // 平均项计算时的通量部分
    NSFaceFlux faceFlux1;
    this->CalculateFaceFluxAverage(faceID, faceFlux1);
    faceFlux.massFlux += faceFlux1.massFlux;
    faceFlux.momentumFlux += faceFlux1.momentumFlux;
    faceFlux.energyFlux += faceFlux1.energyFlux;

    return faceFlux;
}

void RoeSchemeMod::CalculateCharacteristics(Scalar &cRho, Vector &cU, Scalar &cP,
    const Scalar &density, const Scalar &sound, const Vector &normal,
    const Scalar &deltaRho, const Vector &deltaU, const Scalar &deltaP)
{
    const Scalar sound2 = sound * sound;
    const Scalar rhoSound = sound * density;
    cRho = 0.5 * (rhoSound * (deltaU & normal) + deltaP);
    cU = (deltaRho * sound2 - deltaP) * normal + (deltaU ^ normal) * rhoSound;
    cP = deltaP - cRho;
    
    return;
}

Scalar RoeSchemeMod::CalculateDelta(const Scalar &left, const Scalar &right, const Scalar &mean)
{
    Scalar signLeft = 1.0;
    Scalar signRight = 1.0;
    Scalar signMean = 1.0;
    if (left < 0.0) signLeft = -1.0;
    if (right < 0.0) signRight = -1.0;
    if (mean < 0.0) signMean = -1.0;

    return 0.5 * (1. + Min(signLeft * signMean, signMean * signRight))
               * signLeft * Min(signLeft * left, signRight * right);
}

Scalar RoeSchemeMod::MinMod(const Scalar &delta, const Scalar &mean)
{
    Scalar signDelta = 1.0;
    Scalar signMean = 1.0;
    if (delta < 0.0) signDelta = -1.0;
    if (mean < 0.0) signMean = -1.0;
    return signMean * Min(signMean * mean, signDelta * delta);
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux