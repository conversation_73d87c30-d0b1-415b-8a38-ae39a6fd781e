﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MultigridSolver.h
//! <AUTHOR>
//! @brief 多重网格计算类.
//! @date 2021-05-19
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释，并对函数参数名称及参数顺序进行调整
//
// 2021-03-13 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _multigridSolver_MultigridSolver_
#define _multigridSolver_MultigridSolver_

#include "basic/CFD/multigridCycle/MultigridCycle.h"
#include "basic/CFD/smoother/Smoother.h"
#include "sourceFlow/package/FlowPackage.h"
#include "sourceFlow/resultsProcess/ResultsProcess.h"
#include "sourceFlow/timeScheme/FlowTimeManager.h"

/**
 * @brief 流场计算多重网格求解类
 * 
 */
class MultigridSolver : public MultigridCycle
{
public:
    /**
     * @brief 多重网格构造函数
     * 
     * @param[in] subMesh_ 当地网格，含细网格和所有粗网格
     * @param[in, out] flowPackageVector_ 流场包对象容器
     * @param[in, out] timeSchemeVector_ 时间推进对象容器
     */
    MultigridSolver(SubMesh *subMesh_,
					std::vector<Package::FlowPackage *> &flowPackageVector_,
					std::vector<Time::Flow::FlowTimeManager *> &timeSchemeVector_);
    
    /**
     * @brief 析构函数
     * 
     */
    ~MultigridSolver();

    /**
     * @brief 多重网格求解器初始化
     * 
     * @param[in] startLevel_ 多重网格求解器起始网格层级
     * @param[in, out] resultProcess_ 起始网格所对应的流场结果处理器
     */
    void Initialize(const int &startLevel_, FlowResultsProcess *resultProcess_, const bool multiGridFlag_ = true);

    /**
     * @brief 多重网格求解
     * 实现内循环中一个迭代步的流场解变量更新
     * 
     * @param[in] innerStep 当前内迭代步
     */
    void Solve(const int &innerStep);

    /**
     * @brief 细网格上原始变量场插值到粗网格
     * 
     * @param[in] fineMeshLevel 细网格所在层级编号
     */
    void RestrictionPrimary(const int &fineMeshLevel);

    /**
     * @brief 细网格上残值场插值到粗网格
     * 
     * @param[in] fineMeshLevel 细网格所在层级编号
     */
    void RestrictionResidual(const int &fineMeshLevel);

    /**
     * @brief 粗网格上的标量物理场对其所对应的细网格上的物理场进行修正
     * 
     * @param[in] coarseMeshLevel 粗网格所在层级编号
     */
    void Correction(const int &coarseMeshLevel);

    /**
    * @brief 采用粗网格上求解后的流场对细网格进行初始化
    *
    * @param[in] currentSequence 当前网格序列号
    */
    void InitializeFromCoarse(const int &currentSequence);

    /**
    * @brief 光顺流场
    *
    * @param[in] level 当前网格层级
    */
    void SmoothField(const int &level);

    /**
    * @brief 设置网格层数
    *
    */
    void CloseMultigrid();

private:
    /**
     * @brief 保存粗网格物理量旧值
     * 
     * @param[in] coarseMeshLevel 粗网格所在层级编号
     */
    void SaveOld(const int &coarseMeshLevel);
    
    /**
     * @brief 粗网格上的湍流场对其所对应的细网格上的湍流场进行修正
     * 
     * @param[in] coarseMeshLevel 粗网格所在层级编号
     */
    void CorrectionTurbulence(const int &coarseMeshLevel);

    /**
     * @brief 计算残值松弛因子
     * 
     * @param[in] coarseMeshLevel 粗网格所在层级编号
     * @param[out] relaxFactor 松弛因子
     */
    void CalculateRelaxFactor(const int &coarseMeshLevel, ElementField<Scalar> *relaxFactor);

private:
    /// 由细网格和所有粗网格上的物理场包所构成的容器
    std::vector<Package::FlowPackage *> flowPackageVector;

    /// 对于不同层级的网格，流场求解时采用的具体时间推进对象所构成的容器, 容器大小为总网格层数
    std::vector<Time::Flow::FlowTimeManager *> timeSchemeVector;

    /// 流场光顺函数
    std::vector<Smoother::FieldSmoother *> smootherVector;

    /// 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
    const Configure::Flow::FlowConfigure &flowConfigure;

    /// 细网格流场计算结果输出对象
    FlowResultsProcess *resultProcess; 
    
    /// 当前网格序列的多重网格路径
    std::vector<std::pair<MultigridCycle::Operation, int>> multigridPath;

    /// 当前网格序列细网格层级
    int startLevel;

    /// 粗网格上湍流方程的数量
    int nTurbulentVariableCoarseMesh;
};

#endif
