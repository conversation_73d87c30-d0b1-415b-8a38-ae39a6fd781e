 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <limits>
#include <vector>
#include <boost/make_shared.hpp>
#include <boost/shared_ptr.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "MASTER.h"
#include "GLOBAL.h"
#include "CodeContract.h"
#include "fileio.h"
struct ___1362 { static double const GHOST_VALUE; typedef boost::shared_ptr<___1362> Ptr; FieldDataType_e ___2459; size_t ___2668; ValueLocation_e ___2669; ___1362(); virtual ~___1362(); virtual void ___3504(size_t index, double ___4314) = 0; virtual double ___1780(size_t index) = 0; virtual void appendValue(double ___4314) = 0; virtual void reserveValues(size_t count) = 0; virtual void assignValues(size_t count, double ___4314) = 0; virtual void* getRawPointer() = 0; virtual size_t storedValueCount() = 0; void ___1759(double& minVal, double& maxVal) const; void ___3499(double minVal, double maxVal); virtual void writeToFile(tecplot::___3933::FileWriterInterface& outputFile, bool ___4480) const = 0; virtual uint64_t sizeInFile(bool ___4480) const = 0; virtual void readFromFile(tecplot::___3933::___1399& inputFile, bool readASCII) = 0; static Ptr makePtr(tecplot::___3933::___1399& inputFile, bool readASCII); protected: void updateMinMax(double ___4314); double m_minValue; double m_maxValue; }; namespace tecplot { namespace tecioszl { template <typename T> class TypedFieldData : public ___1362 { public: TypedFieldData(); void ___3504(size_t index, double ___4314); double ___1780(size_t index); void appendValue(double ___4314); void reserveValues(size_t count); void assignValues(size_t count, double ___4314); void* getRawPointer(); size_t storedValueCount(); virtual void writeToFile(tecplot::___3933::FileWriterInterface& outputFile, bool ___4480) const; virtual uint64_t sizeInFile(bool ___4480) const; virtual void readFromFile(tecplot::___3933::___1399& inputFile, bool readASCII); private: std::vector<T> ___2671; }; class TypedFieldDataFactory { public: boost::shared_ptr<___1362> make(FieldDataType_e type); }; }} inline ___1362::___1362() : ___2459(FieldDataType_Float) , ___2668(0) , ___2669(___4330) , m_minValue(std::numeric_limits<double>::max()) , m_maxValue(-std::numeric_limits<double>::max()) {} inline ___1362::~___1362() {} inline void ___1362::___1759(double& minVal, double& maxVal) const { minVal = m_minValue; maxVal = m_maxValue; if (maxVal < minVal) { minVal = 0.0; maxVal = 0.0; } } inline void ___1362::___3499(double minVal, double maxVal) { m_minValue = minVal; m_maxValue = maxVal; } inline ___1362::Ptr ___1362::makePtr(tecplot::___3933::___1399& inputFile, bool readASCII) { FieldDataType_e ___1363; READ_ENUM(___1363, FieldDataType_e, inputFile, readASCII); Ptr ___1351 = tecplot::tecioszl::TypedFieldDataFactory().make(___1363); ___1351->readFromFile(inputFile, readASCII); return ___1351; } inline void ___1362::updateMinMax(double ___4314) { if (___4314 == ___4314) { m_minValue = std::min(m_minValue, ___4314); m_maxValue = std::max(m_maxValue, ___4314); } } namespace tecplot { namespace tecioszl { template <typename T> inline TypedFieldData<T>::TypedFieldData() {} template <typename T> inline void TypedFieldData<T>::___3504(size_t index, double ___4314) { ___2671[index] = static_cast<T>(___4314); updateMinMax(___4314); } template <typename T> inline double TypedFieldData<T>::___1780(size_t index) { return static_cast<double>(___2671[index]); } template <typename T> inline void TypedFieldData<T>::appendValue(double ___4314) { ___2671.push_back(static_cast<T>(___4314)); updateMinMax(___4314); } template <typename T> inline void TypedFieldData<T>::reserveValues(size_t count) { ___2671.reserve(count); } template <typename T> inline void TypedFieldData<T>::assignValues(size_t count, double ___4314) { ___2671.assign(count, static_cast<T>(___4314)); updateMinMax(___4314); } template <typename T> inline void* TypedFieldData<T>::getRawPointer() { if (___2671.empty()) return NULL; return (reinterpret_cast<void*>(&___2671[0])); } template <typename T> inline size_t TypedFieldData<T>::storedValueCount() { return ___2671.size(); } inline boost::shared_ptr<___1362> TypedFieldDataFactory::make(FieldDataType_e type) { boost::shared_ptr<___1362> ___3358; switch(type) { case FieldDataType_Float: ___3358 = boost::make_shared<TypedFieldData<float> >(); break; case FieldDataType_Double: ___3358 = boost::make_shared<TypedFieldData<double> >(); break; case FieldDataType_Int32: ___3358 = boost::make_shared<TypedFieldData<int32_t> >(); break; case FieldDataType_Int16: ___3358 = boost::make_shared<TypedFieldData<int16_t> >(); break; case FieldDataType_Byte: case ___1365: ___3358 = boost::make_shared<TypedFieldData<uint8_t> >(); break; default: ___478(___1305); ___3358 = boost::make_shared<TypedFieldData<float> >(); break; } ___3358->___2459 = type; return ___3358; } template <typename T> inline void TypedFieldData<T>::writeToFile(tecplot::___3933::FileWriterInterface& outputFile, bool ___4480) const { tecplot::tecioszl::writeScalar(outputFile, (uint32_t)___2459, ___4480); tecplot::tecioszl::writeScalar(outputFile, (uint64_t)___2668, ___4480);
tecplot::tecioszl::writeScalar(outputFile, (uint32_t)___2669, ___4480); tecplot::tecioszl::writeScalar(outputFile, m_minValue, ___4480); tecplot::tecioszl::writeScalar(outputFile, m_maxValue, ___4480); tecplot::tecioszl::writeVector(outputFile, ___2671, ___4480); } template <typename T> uint64_t TypedFieldData<T>::sizeInFile(bool ___4480) const { return tecplot::tecioszl::scalarSizeInFile((uint32_t)___2459, ___4480) + tecplot::tecioszl::scalarSizeInFile((uint64_t)___2668, ___4480) + tecplot::tecioszl::scalarSizeInFile((uint32_t)___2669, ___4480) + tecplot::tecioszl::scalarSizeInFile(m_minValue, ___4480) + tecplot::tecioszl::scalarSizeInFile(m_maxValue, ___4480) + tecplot::tecioszl::vectorSizeInFile(___2671, ___4480); } template <typename T> inline void TypedFieldData<T>::readFromFile(tecplot::___3933::___1399& inputFile, bool readASCII) { tecplot::tecioszl::readScalar(inputFile, ___2668, readASCII); uint32_t tempValueLocation; tecplot::tecioszl::readScalar(inputFile, tempValueLocation, readASCII); ___2669 = static_cast<ValueLocation_e>(tempValueLocation); tecplot::tecioszl::readScalar(inputFile, m_minValue, readASCII); tecplot::tecioszl::readScalar(inputFile, m_maxValue, readASCII); tecplot::tecioszl::readVector(inputFile, ___2671, readASCII); } }}
