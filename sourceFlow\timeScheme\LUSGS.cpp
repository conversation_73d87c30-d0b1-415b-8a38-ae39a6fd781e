﻿#include "sourceFlow/timeScheme/LUSGS.h"
#include "meshProcess/meshSorting/MeshSorting.h"

namespace Time
{
namespace Flow
{
LUSGS::LUSGS(Package::FlowPackage &flowPackage)
    :
    BackEuler(flowPackage),
    deltaR(mesh, Scalar0, "deltaR"),
    deltaRU(mesh, Vector0, "deltaRU"),
    deltaRE(mesh, Scalar0, "deltaRE"),
    jacobianTurbulence(flowPackage.GetField().jacobianTurbulence)
{
    deltaRTur.resize(nTurbulence);
    for (int m = 0; m < nTurbulence; ++m)    
        deltaRTur[m] = new ElementField<Scalar>(mesh, Scalar0, "deltaRTur" + ToString(m));

    // 网格重排
    if (LUSGSFlag) this->ReNumber();

    // 松弛系数
    omega = flowConfigure.GetTimeScheme().LUSGS.underRelax;

    if (LUSGSFlag)
    {
	    dissipativeResidualMass.Initialize();
	    dissipativeResidualMomentum.Initialize();
	    dissipativeResidualEnergy.Initialize();
        for (int k = 0; k < nTurbulence; k++)
            dissipativeResidualTurbulence[k]->Initialize();
    }
}

LUSGS::~LUSGS()
{
}

void LUSGS::Initialize(const Initialization::Type &initialType)
{
    this->InitializeBase(initialType);
}

void LUSGS::InitializeSolver()
{
}

void LUSGS::Solve()
{
    if (LUSGSFlag) MarchLUSGS();
    else           MarchDPLUR();
}

void LUSGS::Update()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        
        // 获得单元旧值
        const Scalar &rhoTemp0 = rho0->GetValue(elementID);
        const Vector &UTemp0 = U0->GetValue(elementID);
        const Scalar &pTemp0 = p0->GetValue(elementID);

        // 当前守恒流动变量的推进
        const Scalar temp1 = Min(deltaR.GetValue(elementID) / (rhoTemp0 + SMALL), 0.0);
        const Scalar rhoTemp = rhoTemp0 + deltaR.GetValue(elementID) / (1.0 - 1.1 * temp1);
        const Vector rhoUTemp = rhoTemp0 * UTemp0 + deltaRU.GetValue(elementID);
    
        rho->SetValue(elementID, rhoTemp);
        U->SetValue(elementID, rhoUTemp *(1.0 / rhoTemp));
    
        // 更新湍流量
        Scalar temp2 = INF;
		for (int k = 0; k < nTurbulence; k++)
        {
            const Scalar rhoPhi0 = rhoTemp0 * turbulence0[k]->GetValue(elementID);
            temp2 = Min(temp2, deltaRTur[k]->GetValue(elementID) / Max(rhoPhi0, SMALL));
			temp2 = Min(temp2, jacobianTurbulence[k]->GetValue(elementID) * deltaT->GetValue(elementID));
        }
        for (int k = 0; k < nTurbulence; k++)
        {
            const Scalar rhoPhi0 = rhoTemp0 * turbulence0[k]->GetValue(elementID);
            const Scalar rhoPhi = rhoPhi0 + deltaRTur[k]->GetValue(elementID) / (1.0 - 1.1 * temp2);
            turbulence[k]->SetValue(elementID, Max(rhoPhi / rhoTemp, SMALL));
        }
    
        Scalar rhoETemp0 = pTemp0 * gamma1Inv + 0.5 * rhoTemp0 * (UTemp0 & UTemp0);
        const Scalar temp3 = Min(deltaRE.GetValue(elementID) / (rhoETemp0 + SMALL), 0.0);
        const Scalar rhoETemp = rhoETemp0 + deltaRE.GetValue(elementID) / (1.0 - 1.1 * temp3);

        Scalar pTemp = rhoETemp - 0.5 / rhoTemp * (rhoUTemp & rhoUTemp);
        pTemp *= gamma1;
        pTemp = Max(pTemp, SMALL);

        Scalar deltaP = (pTemp0 - pTemp);
        const Scalar temp4 = Min(-deltaP / (p0->GetValue(elementID) + SMALL), 0.0);
        pTemp = pTemp0 - deltaP / (1.0 - 1.1 * temp4);
    
        p->SetValue(elementID, pTemp);
	}
    
    flowPackage.UpdateExtrasField();

    // 限制更新量
    CheckAndLimit();

	return;
}
void LUSGS::MarchLUSGS()
{
    Scalar diagFlow; //主流的对角阵系数
    std::vector<Scalar> diagTur(nTurbulence);//湍流的对角阵系数

    //前向推进
    const int elementNumber = elementIDSortMap.size();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = elementIDSortMap[index];  
        if (!mesh->JudgeRealElement(elementID)) continue;
        
        // 1.计算对角阵系数的倒数        
        this->CalculateDiagInverse(elementID, diagFlow, diagTur);

        // 2.计算L*deltaW
        Scalar Dr = Scalar0, DrE = Scalar0;              //rho, rhoE的变化量 
        Vector DrU = Vector0;                            //rhoU的变化量
        std::vector<Scalar> DrTur(nTurbulence, Scalar0); //湍流方程rhoX的变化量
        for (int i = 0; i < OwnerIDCell[elementID].size(); ++i)
        {
            const int &adjacentID = OwnerIDCell[elementID][i];
            const int &faceID = OwnerIDFace[elementID][i];

            this->AddDeltaFlux(adjacentID, faceID, Dr, DrU, DrE, DrTur);
            this->AddrAStar(elementID, adjacentID, faceID, Dr, DrU, DrE, DrTur);
        }

        // 3.加入右端项残值
        Dr  = -0.5 * Dr - residualMass->GetValue(elementID);
        DrU = -0.5 * DrU - residualMomentum->GetValue(elementID);
        DrE = -0.5 * DrE - residualEnergy->GetValue(elementID);
        for (int m = 0; m < nTurbulence; ++m)        
            DrTur[m] = -0.5 * DrTur[m] - residualTurbulence[m]->GetValue(elementID);
        
        // 4.完成前向计算
        deltaR.SetValue(elementID, diagFlow * Dr);
        deltaRU.SetValue(elementID, diagFlow * DrU);
        deltaRE.SetValue(elementID, diagFlow * DrE);
        for (int m = 0; m < nTurbulence; ++m)        
            deltaRTur[m]->SetValue(elementID, diagTur[m] * DrTur[m]);
    }

    //后向推进
    for (int index = elementNumber - 1; index >= 0; --index)
    {
        const int &elementID = elementIDSortMap[index];  
        if (!mesh->JudgeRealElement(elementID)) continue;
        
        // 1.计算对角阵系数的倒数
        this->CalculateDiagInverse(elementID, diagFlow, diagTur);

        // 2.计算L*deltaW
        Scalar Dr = Scalar0, DrE = Scalar0;              //rho, rhoE的变化量 
        Vector DrU = Vector0;                            //rhoU的变化量
        std::vector<Scalar> DrTur(nTurbulence, Scalar0); //湍流方程rhoX的变化量
        for (int i = 0; i < NeighborIDCell[elementID].size(); ++i)
        {
            const int &adjacentID = NeighborIDCell[elementID][i];
            const int &faceID = NeighborIDFace[elementID][i];

            this->AddDeltaFlux(adjacentID, faceID, Dr, DrU, DrE, DrTur);
            this->AddrAStar(elementID, adjacentID, faceID, Dr, DrU, DrE, DrTur);
        }

        // 3.加入其他右端项(前向计算值)完成后向计算
        deltaR.SetValue(elementID, -0.5 * diagFlow * Dr + deltaR.GetValue(elementID));
        deltaRU.SetValue(elementID, -0.5 * diagFlow * DrU + deltaRU.GetValue(elementID));
        deltaRE.SetValue(elementID, -0.5 * diagFlow * DrE + deltaRE.GetValue(elementID));
        for (int m = 0; m < nTurbulence; ++m)
            deltaRTur[m]->SetValue(elementID, -0.5 * diagTur[m] * DrTur[m] + deltaRTur[m]->GetValue(elementID));
    }
}

void LUSGS::MarchDPLUR()
{
    Scalar diagFlow;                          //主流的对角阵系数
    std::vector<Scalar> diagTur(nTurbulence); //湍流的对角阵系数

    const int &elementNumber = mesh->GetElementNumberInDomain();
    int k_max = 5;
 
    // 1. delta_W(0)=D(^-1)*R(W(^n))
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        // 计算对角阵系数的倒数  
              
        this->CalculateDiagInverse(elementID, diagFlow, diagTur);

        deltaR.SetValue(elementID,-diagFlow*residualMass->GetValue(elementID));
        deltaRU.SetValue(elementID,-diagFlow*residualMomentum->GetValue(elementID));
        deltaRE.SetValue(elementID,-diagFlow*residualEnergy->GetValue(elementID)); 
        for (int m = 0; m < nTurbulence; ++m)
            deltaRTur[m]->SetValue(elementID, -diagTur[m]*residualTurbulence[m]->GetValue(elementID));
    }

    //2. delta_W(k)=D(^-1)[R(W(^n))-(L+U)*delta_W(k-1)]
    for(int k = 0; k < k_max; k++)
    {
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            Scalar Dr = Scalar0;
            Vector DrU = Vector0;
            Scalar DrE = Scalar0;
            std::vector<Scalar> DrTur(nTurbulence, Scalar0);
            // 计算对角阵系数的倒数      

            this->CalculateDiagInverse(elementID, diagFlow, diagTur);
            //搜寻单元相邻单元，不同于LUSGS，无需区分上下三角矩阵
            for (int i = 0; i < mesh->GetElement(elementID).GetFaceSize(); ++i)
            {
                const int &faceID = mesh->GetElement(elementID).GetFaceID(i);
                const int &ownerID=mesh->GetFace(faceID).GetOwnerID();
                const int &neighID=mesh->GetFace(faceID).GetNeighborID();
                //选择相邻单元进行deltaF和rA的求解
                if (ownerID == elementID)
                {
                    this->AddDeltaFlux(neighID, faceID, Dr, DrU, DrE, DrTur);
                    this->AddrAStar(elementID, neighID, faceID, Dr, DrU, DrE, DrTur);
                }
                else
                {
                    this->AddDeltaFlux(ownerID, faceID, Dr, DrU, DrE, DrTur);
                    this->AddrAStar(elementID, ownerID, faceID, Dr, DrU, DrE, DrTur);
                }

            }
            deltaR.SetValue(elementID, diagFlow*(-residualMass->GetValue(elementID)-0.5*Dr));
            deltaRU.SetValue(elementID, diagFlow*(-residualMomentum->GetValue(elementID)-0.5*DrU));
            deltaRE.SetValue(elementID, diagFlow*(-residualEnergy->GetValue(elementID)-0.5*DrE));
            for (int m = 0; m < nTurbulence; ++m)
                deltaRTur[m]->SetValue(elementID, diagTur[m]*(-residualTurbulence[m]->GetValue(elementID)-0.5*DrTur[m]));
        }
    }
}

void LUSGS::CalculateDiagInverse(const int &elementID, Scalar &diagFlow, std::vector<Scalar> &diagTur)
{
    Scalar rdeltaT = 1.0 / deltaT->GetValue(elementID); // deltaT不含体积
    if(unsteadyFlag && dualTime) rdeltaT +=  1.5 * beta * mesh->GetElement(elementID).GetVolume() / physicalDeltaTime;
    
    // 主流的对角阵系数
    diagFlow = rdeltaT + 0.5 * omega * lambdaConvective->GetValue(elementID);
    if (lambdaViscous != nullptr) diagFlow += lambdaViscous->GetValue(elementID);
    diagFlow = 1.0 / diagFlow;

    //湍流的对角阵系数
    for (int m = 0; m < nTurbulence; ++m)
    {
        diagTur[m] = 1.0 / (rdeltaT + 0.5 * omega * (lambdaConvective->GetValue(elementID)
                                    + lambdaViscous->GetValue(elementID))  //用主流粘性谱半径替代湍流的
                                    - jacobianTurbulence[m]->GetValue(elementID)); //源项的贡献
    }
}

void LUSGS::AddDeltaFlux(const int &adjacentID, const int &faceID,
                         Scalar &Dr, Vector &DrU, Scalar &DrE, std::vector<Scalar> &DrTur)
{
    //面信息
    const Vector &faceNorm = mesh->GetFace(faceID).GetNormal();    
    Scalar faceArea = mesh->GetFace(faceID).GetArea();
   if (mesh->GetFace(faceID).GetOwnerID() == adjacentID) faceArea *= -1.0;

    //取得原始量旧值
    const Scalar &rhoOld = rho->GetValue(adjacentID);
    const Vector &UOld = U->GetValue(adjacentID);
    const Scalar &pOld = p->GetValue(adjacentID);
    const Scalar &TOld = T->GetValue(adjacentID);
    const Scalar HOld = material.Enthalpy(TOld) + 0.5 * (UOld & UOld);    

    //计算对流通量旧值
    const Scalar fluxOldR = rhoOld * (UOld & faceNorm);
    const Vector fluxOldRU = fluxOldR * UOld + pOld * faceNorm;
    const Scalar fluxOldRE = fluxOldR * HOld;
    
    //预估原始量新值
    const Scalar rhoNew = rhoOld + deltaR.GetValue(adjacentID);
    const Vector rhoUNew = rhoOld * UOld + deltaRU.GetValue(adjacentID);
    const Scalar rhoENew = (rhoOld * HOld - pOld) + deltaRE.GetValue(adjacentID);
    const Vector UNew = rhoUNew / rhoNew;
    const Scalar pNew = (rhoENew - 0.5 * rhoNew * (UNew & UNew)) * gamma1;
    const Scalar HNew = (rhoENew + pNew) / rhoNew;

    //计算对流通量新值
    const Scalar fluxNewR = rhoUNew & faceNorm;
    Vector fluxNewRU = fluxNewR * UNew + pNew * faceNorm;
    Scalar fluxNewRE = fluxNewR * HNew;

    //累加差量
    Dr  += (fluxNewR - fluxOldR) * faceArea;
    DrU += (fluxNewRU - fluxOldRU) * faceArea;
    DrE += (fluxNewRE - fluxOldRE) * faceArea;

    //处理湍流部分
    for (int m = 0; m < nTurbulence; ++m)
    {
        const Scalar &turOld = turbulence[m]->GetValue(adjacentID);
        const Scalar fluxOldRTur = fluxOldR * turOld;

        const Scalar rhoTurNew = rhoOld * turOld + deltaRTur[m]->GetValue(adjacentID);
        const Scalar fluxNewRTur = fluxNewR * (rhoTurNew / rhoNew);

        DrTur[m] += (fluxNewRTur - fluxOldRTur) * faceArea;
    }    
}

void LUSGS::AddrAStar(const int &elementID, const int &adjacentID, const int &faceID,
                      Scalar &Dr, Vector &DrU, Scalar &DrE, std::vector<Scalar> &DrTur)
{
    //面信息
    const Vector &faceNorm = mesh->GetFace(faceID).GetNormal();
    const Scalar &faceArea = mesh->GetFace(faceID).GetArea();
 
    const Vector UMean = 0.5 * (U->GetValue(elementID) + U->GetValue(adjacentID));
    const Scalar soundMean = 0.5 * (A->GetValue(elementID) + A->GetValue(adjacentID));
    Scalar lambda = (fabs(UMean & faceNorm) + soundMean) * faceArea;

    const auto *muLaminar = flowPackage.GetField().muLaminar;
    const auto *muTurbulent = flowPackage.GetField().muTurbulent;
    if (muTurbulent != nullptr)
    {
        const Scalar mue_lam = 0.5 * (muLaminar->GetValue(elementID) + muLaminar->GetValue(adjacentID));
        const Scalar eddyv1 = 0.5 * (muTurbulent->GetValue(elementID) + muTurbulent->GetValue(adjacentID));
        const Scalar PrandtlLaminar = flowPackage.GetMaterialNumber().PrandtlLaminar;
        const Scalar PrandtlTurbulent = flowPackage.GetMaterialNumber().PrandtlTurbulent;
        const Scalar Cv = flowPackage.GetMaterialNumber().Cv;
        const Scalar Cp = flowPackage.GetMaterialNumber().Cp;
        const Scalar kappa_lam = Cp * mue_lam / PrandtlLaminar;
        const Scalar rhoMean = 0.5 * (rho->GetValue(elementID) + rho->GetValue(adjacentID));
        const Scalar volMean = 0.5 * (mesh->GetElement(elementID).GetVolume() + mesh->GetElement(adjacentID).GetVolume());
        const Scalar lambda_v1 = (mue_lam + eddyv1) * 4.0 / 3.0;
        const Scalar lambda_v2 = (1. + PrandtlLaminar / mue_lam * eddyv1 / PrandtlTurbulent) * kappa_lam / Cv;
        const Scalar lambdaVis = faceArea * faceArea / volMean * (lambda_v1 + lambda_v2) / rhoMean;

        lambda += lambdaVis;
    }
    else if (muLaminar != nullptr)
    {
        const Scalar mue_lam = 0.5 * (muLaminar->GetValue(elementID) + muLaminar->GetValue(adjacentID));
        const Scalar PrandtlLaminar = flowPackage.GetMaterialNumber().PrandtlLaminar;
        const Scalar Cv = flowPackage.GetMaterialNumber().Cv;
        const Scalar Cp = flowPackage.GetMaterialNumber().Cp;
        const Scalar kappa_lam = Cp * mue_lam / PrandtlLaminar;
        const Scalar rhoMean = 0.5 * (rho->GetValue(elementID) + rho->GetValue(adjacentID));
        const Scalar volMean = 0.5 * (mesh->GetElement(elementID).GetVolume() + mesh->GetElement(adjacentID).GetVolume());
        const Scalar lambda_v1 = mue_lam * 4.0 / 3.0;
        const Scalar lambda_v2 = kappa_lam / Cv;
        const Scalar lambdaVis = faceArea * faceArea / volMean * Max(lambda_v1, lambda_v2) / rhoMean;

        lambda += lambdaVis;
    }

    Scalar rA = omega * lambda;
    Dr -= rA * deltaR.GetValue(adjacentID);
    DrU -= rA * deltaRU.GetValue(adjacentID);
    DrE -= rA * deltaRE.GetValue(adjacentID);
    for (int m = 0; m < nTurbulence; ++m)
        DrTur[m] -= rA * deltaRTur[m]->GetValue(adjacentID);
}

}
}