﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ZoneManager.h
//! <AUTHOR>
//! @brief 多域信息管理器
//! @date 2022-03-07
//
//------------------------------修改日志----------------------------------------
// 2022-03-07 曾凯
//    说明：建立
// 2023-03-07 曾凯
//    说明：将zoneManger数据的构造，从原来的前处理构造并保存成文件，修改为多域计算开始前
//         从各进程网格信息构造
//------------------------------------------------------------------------------
#ifndef _meshProcess_zone_ZoneManager_
#define _meshProcess_zone_ZoneManager_

#include "basic/common/BoostLib.h"
#include "basic/mesh/Mesh.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif


class ZoneManager
{
//  template<class T>
//	using List = std::vector<T>;

public:
    ZoneManager(const Mesh * srcMesh);
    ~ZoneManager();

private:
    int nZones;
    std::vector<std::string> zoneNames;
    // 多域并行信息
    std::vector<int> zoneMpiStartRank;
    std::vector<int> zoneMpiEndRank;

    // 当前进程的各子域网格信息
	std::vector<int> zoneStartElemID; //子域网格单元起始编号，子域在该进程无单元时记为-1
	std::vector<int> zoneElemNum; // 子域网格单元数，子域在该进程无单元时记为-1；
    std::vector<int> zoneElemNumAll; // 子域网格带虚单元的单元数量，子域在该进程无单元时记为-1；
	std::vector<std::vector<int>> zoneBoundaryID;

    boost::property_tree::ptree zoneInfoXml;

#if defined(_BaseParallelMPI_)
    mpi::communicator mpi_world;
#endif

public:
	// 获取总子域信息接口
	const int & GetZoneNum(){ return nZones; }
	const int & GetZoneStartRank(int zoneID){ return zoneMpiStartRank[zoneID]; }
	const int & GetZoneEndRank(int zoneID){ return zoneMpiEndRank[zoneID]; }
    const int & GetZoneStartElemID(int zoneID){ return zoneStartElemID[zoneID]; }
	const int & GetZoneElemNum(int zoneID) { return zoneElemNum[zoneID]; }
	const int GetBoundaryZoneID(const int &boundaryID);
	const int GetElemZoneID(const int &elemID);

    // 将传入的Mesh对象列表合并为一个Mesh对象
    //Mesh* MergeMeshes(std::vector<Mesh*> &srcMesh);

    // 根据各子域网格单元数分配并行核数
    void DistributeZones(const int &gPart, const std::vector<Mesh*> &srcMesh);

    // 输出多域信息至文件
    void WriteZoneInfoToXML();

    // 从文件读取多域信息
    void ReadZoneInfoFromXML();
    void ReadZoneNumber();   //读取子域个数
    void ReadZoneNames();    //读取子域名城
    void ReadZoneElemNum();  //读取子域网格单元数量
    void ReadZoneMpiRank();  //读取子域并行分区信息

    // 打印多域信息
    void PrintZoneInfo(int processorID);

#if defined(_BaseParallelMPI_)
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & nZones;
        ar & zoneElemNum;
        ar & zoneMpiStartRank;
        ar & zoneMpiEndRank;
    }
#endif

private:


};

#endif    
