﻿#ifndef _particle_PureProperty_
#define _particle_PureProperty_

#include "basic/common/ConfigUtility.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

class PureProperty
{
private:
    Scalar density;
    Scalar YoungModulus;
    Scalar shearModulus;
    Scalar yeildStress;
    Scalar poissonsRatio;
    Scalar kappa;
    
    Scalar radius;
    Scalar volume;
    Scalar mass;
    Scalar inertia;

	int dimension;

public:
    PureProperty();
    
    PureProperty(const Scalar &density_, const Scalar &YoungModulus_,
                 const Scalar &poissonsRatio_, const Scalar &yeildStress_);
    
	PureProperty(const Configure::Particle::ParticleConfigure &options, const int &dim = 3);

    void Calculate();

    void SetDensity(const Scalar &v) { this->density = v; }
    void SetYoung(const Scalar &v) { this->YoungModulus = v; }
    void SetShear(const Scalar &v) { this->shearModulus = v; }
    void SetPratio(const Scalar &v) { this->poissonsRatio = v; }
    void SetYeild(const Scalar &v) { this->yeildStress = v; }
    void SetKappa(const Scalar &v) { this->kappa = v; }
    
    void SetRad(const Scalar &v) { this->radius = v; }
    void SetVol(const Scalar &v) { this->volume = v; }
    void SetMass(const Scalar &v) { this->mass = v; }
    void SetInertia(const Scalar &v) { this->inertia = v; }

    inline const Scalar &GetDensity() const { return this->density; }
    inline const Scalar &GetYoungMod() const { return this->YoungModulus; }
    inline const Scalar &GetShearMod() const { return this->shearModulus; }
    inline const Scalar &GetYeildStress() const { return this->yeildStress; }
    inline const Scalar &GetPoissonsRatio() const { return this->poissonsRatio; }
    inline const Scalar &GetKappa() const { return this->kappa; }
    
    inline const Scalar &GetRad() const { return this->radius; }
    inline const Scalar &GetVol() const { return this->volume; }
    inline const Scalar &GetMass() const { return this->mass; }
    inline const Scalar &GetInertia() const { return this->inertia; }
};

} // namespace Particle

#endif