﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MenterSSTGammaRe.h
//! <AUTHOR>
//! @brief 转捩计算类
//! @date 2024-05-13
//
//------------------------------修改日志----------------------------------------
// 2024-05-13 钱琛庚、李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_MenterSSTGammaRe_
#define _sourceFlow_turbulence_MenterSSTGammaRe_

#include "sourceFlow/turbulence/MenterSST.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 转捩预测MenterSSTGammaRe类
 * 
 */
class  MenterSSTGammaRe : public MenterSST
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
    MenterSSTGammaRe(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
    ~MenterSSTGammaRe();    
    
    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();
    
    /**
     * @brief 湍流量限制
     * 
     * @return int 
     */
    int CheckAndLimit();    

protected:
    /**
     * @brief 计算扩散项面心处扩散系数
     * 
     * @param[in] faceID 面编号
     * @return std::vector<std::pair<Scalar, Scalar>> 
     */
    std::vector<std::pair<Scalar, Scalar>> CalculateGammaFace(const int &faceID);

private:
    /**
     * @brief SST模型的调和函数F1
     * 
     * @param[in] rhoI 密度
     * @param[in] muL 层流粘性系数
     * @param[in] kI 湍动能
     * @param[in] UI 速度
     * @param[in] Ug 速度的梯度
     * @return Scalar 
     */
	Scalar ReThetaT(const Scalar &rhoI, const Scalar &muL, const Scalar &kI, const Vector &UI, const Tensor &Ug);


	Scalar F_lambdaTheta(const Scalar &lambda, const Scalar &Tu);

	Scalar F_diff(const Scalar &lambda, const Scalar &Tu);

	Scalar ReThetaCorrelation(const Scalar &ReI);

	Scalar CalculateFlenght(const Scalar &ReI, const Scalar &Rew2);
	 
private:
	ElementField<Scalar> &Ga; ///< 间歇因子
    ElementField<Scalar> &Re; ///< 当地转捩动量厚度雷诺数 
	ElementField<Vector> *gradientGa; ///< Ga的梯度
    ElementField<Vector> *gradientRe; ///< Re的梯度
	ElementField<Scalar> &residualGa; ///< Ga的残值
    ElementField<Scalar> &residualRe; ///< Re的残值

	const Scalar sigmaGamma; ///< Ga方程扩散项模型参数
	const Scalar Ce1; ///< Ga方程源项的模型参数
	const Scalar Ca1; ///< Ga方程源项的模型参数
	const Scalar Ca2; ///< Ga方程源项的模型参数

	const Scalar sigmaThetaT; ///< Re方程扩散项模型参数    
	const Scalar CThetaT; ///< Re方程源项的模型参数
    const Scalar Ce2; ///< Re方程源项的模型参数    
	
	const Scalar &cRef; ///< 模型的参考长度
    
    Scalar omegaFree; ///< 远场比耗散率
    Scalar kFree; ///< 远场湍动能
    Scalar ReFree; ///< 远场当地转捩动量厚度雷诺数
    Scalar GaFree; ///< 远场间歇因子
};

} // namespace Turbulence
#endif 
