﻿#if defined(_EnablePETSC_)

#include "basic/CFD/linearSystemSolver/LinearSystemSolverPetsc.h"

LinearSystemSolverPetsc::LinearSystemSolverPetsc(Mesh *mesh_, const int nVariable_)
	:
	LinearSystemSolver(mesh_, nVariable_)
{
}

LinearSystemSolverPetsc::~LinearSystemSolverPetsc()
{
	KSPDestroy(&ksp);
}

void LinearSystemSolverPetsc::Initialize(const Time::Scheme &scheme,
                                         const Configure::Flow::ExactJacobianStruct &paramters,
					                     BlockSparseMatrix *CSRMatrix_,
					                     LinearSystemVector *resVector_,
					                     LinearSystemVector *solVector_)
{
	A = &(((BlockSparseMatrixPetsc *)CSRMatrix_)->GetMat());
	b = &(((LinearSystemVectorPetsc *)resVector_)->GetVector());
	x = &(((LinearSystemVectorPetsc *)solVector_)->GetVector());

	// 求解器创建
	KSPCreate(PETSC_COMM_WORLD, &ksp);

	// 求解器类型设置
	if (scheme == Time::Scheme::GMRES)
	{
		// 求解器设置
		KSPSetType(ksp, KSPGMRES);

		// 设置重启动
		if (paramters.restartFlag) KSPGMRESSetRestart(ksp, paramters.restartStep);
	}
	else if (scheme == Time::Scheme::CG)
	{
		// 求解器设置
		KSPSetType(ksp, KSPCG);
	}
	else if (scheme == Time::Scheme::BICG)
	{
		// 求解器设置
		KSPSetType(ksp, KSPBICG);
	}

	// 计算收敛设置
	KSPSetTolerances(ksp,
					 paramters.linearError,	 // 相对误差
					 PETSC_DEFAULT,			 // 绝对误差
					 PETSC_DEFAULT,			 // 发散误差
					 paramters.maxIterStep); // 最大迭代步数

	// 预处理设置
	KSPGetPC(ksp, &pc);
	PCSetType(pc, PCASM);
	PetscOptionsSetValue(NULL,"-pc_asm_overlap","1");
	if (paramters.preconditionerType == Time::LinearSolverPreconditionerType::JACOBI)
	{
		PetscOptionsSetValue(NULL,"-sub_pc_type","bjacobi");
	}
	else if (paramters.preconditionerType == Time::LinearSolverPreconditionerType::ILU)
	{
		PetscOptionsSetValue(NULL,"-sub_pc_type","ilu");
	}

	// 将矩阵A设置为KSP解算器的主运算符和预条件矩阵
	KSPSetOperators(ksp, *A, *A);

	// KSP解算器从PETSc选项中读取参数
	KSPSetFromOptions(ksp);
}

void LinearSystemSolverPetsc::Solve()
{
	// 矩阵组装
	MatAssemblyBegin(*A, MAT_FINAL_ASSEMBLY);
    MatAssemblyEnd(*A, MAT_FINAL_ASSEMBLY);

	// b和x组装
	VecAssemblyBegin(*b);
	VecAssemblyEnd(*b);
	VecAssemblyBegin(*x);
	VecAssemblyEnd(*x);
	
	// 求解
	KSPSolve(ksp, *b, *x);
}

#endif