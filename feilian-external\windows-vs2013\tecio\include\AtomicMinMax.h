 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <limits>
#include <boost/atomic.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "CodeContract.h"
#include "MinMax.h"
namespace tecplot { namespace ___3933 { template <typename T> struct ___225 { boost::atomic<T> m_min; boost::atomic<T> m_max; ___225(void) { invalidate(); } ___225(double newMin, double newMax) { m_min = static_cast<T>(newMin); m_max = static_cast<T>(newMax); } ___225(double ___4298) { m_min = static_cast<T>(___4298); m_max = static_cast<T>(___4298); } ___225(___225<T> const& ___2888) : m_min(___2888.m_min.load()) , m_max(___2888.m_max.load()) {} inline double minValue(void) const { return m_min; }; inline double maxValue(void) const { return m_max; }; inline void ___3499(double newMin, double newMax) { REQUIRE(newMin<=newMax); m_min = static_cast<T>(newMin); m_max = static_cast<T>(newMax); }; inline void ___3499(___225<T> const& ___2888) { m_min = ___2888.m_min.load(); m_max = ___2888.m_max.load(); }; inline void include(double ___4298) { T const tVal = static_cast<T>(___4298); T curMin = m_min; while(tVal < curMin && !m_min.compare_exchange_weak(curMin, tVal)) ; T curMax = m_max; while(tVal > curMax && !m_max.compare_exchange_weak(curMax, tVal)) ; } inline void include(___225<T> const& minMax) { T testMin = minMax.m_min; T curMin = m_min; while(testMin < curMin && !m_min.compare_exchange_weak(curMin, testMin)) ; T testMax = minMax.m_max; T curMax = m_max; while(testMax > curMax && !m_max.compare_exchange_weak(curMax, testMax)) ; } inline void include(___2479 const& minMax) { T testMin = static_cast<T>(minMax.minValue()); T curMin = m_min; while(testMin < curMin && !m_min.compare_exchange_weak(curMin, testMin)) ; T testMax = static_cast<T>(minMax.maxValue()); T curMax = m_max; while(testMax > curMax && !m_max.compare_exchange_weak(curMax, testMax)) ; } inline bool containsValue(double ___4298) const { return ( m_min <= ___4298 && ___4298 <= m_max ); } inline void invalidate(void) { m_min =  std::numeric_limits<T>::max(); m_max = -std::numeric_limits<T>::max(); } inline bool ___2067(void) const { return m_min.load() <= m_max.load(); } ___225<T>& operator=(___225<T> const& ___3392) { m_min = ___3392.m_min.load(); m_max = ___3392.m_max.load(); return *this; } }; }}
