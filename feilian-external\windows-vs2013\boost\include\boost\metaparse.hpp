#ifndef BOOST_METAPARSE_HPP
#define BOOST_METAPARSE_HPP

// Copyright Abel <PERSON> (<EMAIL>) 2013.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/accept.hpp>
#include <boost/metaparse/accept_tag.hpp>
#include <boost/metaparse/accept_when.hpp>
#include <boost/metaparse/alphanum.hpp>
#include <boost/metaparse/always_c.hpp>
#include <boost/metaparse/always.hpp>
#include <boost/metaparse/repeated.hpp>
#include <boost/metaparse/repeated1.hpp>
#include <boost/metaparse/repeated_reject_incomplete1.hpp>
#include <boost/metaparse/repeated_reject_incomplete.hpp>
#include <boost/metaparse/repeated_one_of1.hpp>
#include <boost/metaparse/repeated_one_of.hpp>
#include <boost/metaparse/build_parser.hpp>
#include <boost/metaparse/change_error_message.hpp>
#include <boost/metaparse/config.hpp>
#include <boost/metaparse/debug_parsing_error.hpp>
#include <boost/metaparse/define_error.hpp>
#include <boost/metaparse/digit.hpp>
#include <boost/metaparse/digit_val.hpp>
#include <boost/metaparse/empty.hpp>
#include <boost/metaparse/entire_input.hpp>
#include <boost/metaparse/error/digit_expected.hpp>
#include <boost/metaparse/error/end_of_input_expected.hpp>
#include <boost/metaparse/error/index_out_of_range.hpp>
#include <boost/metaparse/error/letter_expected.hpp>
#include <boost/metaparse/error/literal_expected.hpp>
#include <boost/metaparse/error/none_of_the_expected_cases_found.hpp>
#include <boost/metaparse/error/unexpected_character.hpp>
#include <boost/metaparse/error/unexpected_end_of_input.hpp>
#include <boost/metaparse/error/unpaired.hpp>
#include <boost/metaparse/error/whitespace_expected.hpp>
#include <boost/metaparse/except.hpp>
#include <boost/metaparse/fail_at_first_char_expected.hpp>
#include <boost/metaparse/fail.hpp>
#include <boost/metaparse/fail_tag.hpp>
#include <boost/metaparse/first_of.hpp>
#include <boost/metaparse/foldl1.hpp>
#include <boost/metaparse/foldl_reject_incomplete.hpp>
#include <boost/metaparse/foldl_reject_incomplete1.hpp>
#include <boost/metaparse/foldl_reject_incomplete_start_with_parser.hpp>
#include <boost/metaparse/foldl.hpp>
#include <boost/metaparse/foldl_start_with_parser.hpp>
#include <boost/metaparse/foldr1.hpp>
#include <boost/metaparse/foldr_reject_incomplete.hpp>
#include <boost/metaparse/foldr_reject_incomplete1.hpp>
#include <boost/metaparse/foldr.hpp>
#include <boost/metaparse/foldr_start_with_parser.hpp>
#include <boost/metaparse/get_col.hpp>
#include <boost/metaparse/get_line.hpp>
#include <boost/metaparse/get_message.hpp>
#include <boost/metaparse/get_position.hpp>
#include <boost/metaparse/get_prev_char.hpp>
#include <boost/metaparse/get_remaining.hpp>
#include <boost/metaparse/get_result.hpp>
#include <boost/metaparse/grammar.hpp>
#include <boost/metaparse/if_.hpp>
#include <boost/metaparse/int_.hpp>
#include <boost/metaparse/is_error.hpp>
#include <boost/metaparse/iterate_c.hpp>
#include <boost/metaparse/iterate.hpp>
#include <boost/metaparse/keyword.hpp>
#include <boost/metaparse/last_of.hpp>
#include <boost/metaparse/letter.hpp>
#include <boost/metaparse/limit_one_char_except_size.hpp>
#include <boost/metaparse/limit_one_of_size.hpp>
#include <boost/metaparse/limit_sequence_size.hpp>
#include <boost/metaparse/limit_string_size.hpp>
#include <boost/metaparse/lit_c.hpp>
#include <boost/metaparse/lit.hpp>
#include <boost/metaparse/look_ahead.hpp>
#include <boost/metaparse/middle_of.hpp>
#include <boost/metaparse/next_char.hpp>
#include <boost/metaparse/next_line.hpp>
#include <boost/metaparse/nth_of_c.hpp>
#include <boost/metaparse/nth_of.hpp>
#include <boost/metaparse/one_char_except_c.hpp>
#include <boost/metaparse/one_char_except.hpp>
#include <boost/metaparse/one_char.hpp>
#include <boost/metaparse/one_of_c.hpp>
#include <boost/metaparse/one_of.hpp>
#include <boost/metaparse/optional.hpp>
#include <boost/metaparse/range_c.hpp>
#include <boost/metaparse/range.hpp>
#include <boost/metaparse/reject.hpp>
#include <boost/metaparse/return_.hpp>
#include <boost/metaparse/sequence.hpp>
#include <boost/metaparse/sequence_apply.hpp>
#include <boost/metaparse/source_position.hpp>
#include <boost/metaparse/source_position_tag.hpp>
#include <boost/metaparse/space.hpp>
#include <boost/metaparse/spaces.hpp>
#include <boost/metaparse/start.hpp>
#include <boost/metaparse/string.hpp>
#include <boost/metaparse/string_tag.hpp>
#include <boost/metaparse/token.hpp>
#include <boost/metaparse/transform_error.hpp>
#include <boost/metaparse/transform_error_message.hpp>
#include <boost/metaparse/transform.hpp>
#include <boost/metaparse/unless_error.hpp>
#include <boost/metaparse/util/digit_to_int_c.hpp>
#include <boost/metaparse/util/digit_to_int.hpp>
#include <boost/metaparse/util/in_range_c.hpp>
#include <boost/metaparse/util/in_range.hpp>
#include <boost/metaparse/util/int_to_digit_c.hpp>
#include <boost/metaparse/util/int_to_digit.hpp>
#include <boost/metaparse/util/is_digit.hpp>
#include <boost/metaparse/util/is_lcase_letter.hpp>
#include <boost/metaparse/util/is_letter.hpp>
#include <boost/metaparse/util/is_ucase_letter.hpp>
#include <boost/metaparse/util/is_whitespace_c.hpp>
#include <boost/metaparse/util/is_whitespace.hpp>
#include <boost/metaparse/version.hpp>

#endif

