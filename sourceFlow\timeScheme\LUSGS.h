﻿////////////////////////////////////////////////////////////////////////////////////
////---------------------------------------ARI-CFD-----------------------------////
////------------------------中国航空工业空气动力研究院-------------------------////
///////////////////////////////////////////////////////////////////////////////////
//! @file      LUSGS.h
//! @brief     时间求解的隐式方法：LUSGS类
//! <AUTHOR>
//! @date      2022-02-14
//
//------------------------------修改日志----------------------------------------
//
//  2022-02-14 李艳亮
//    说明：建立。
//------------------------------------------------------------------------------
# ifndef _sourceFlow_timeScheme_LUSGS_
# define _sourceFlow_timeScheme_LUSGS_

#include "sourceFlow/timeScheme/BackEuler.h"

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场LUSGS时间推进类
 * 由基础时间类进行派生
 * 
 */
class LUSGS : public BackEuler
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    LUSGS(Package::FlowPackage &flowPackage_);

    /**
    * @brief 析构函数
    */
    ~LUSGS();

    /**
     * @brief 初始化
     * 
     * @param[in] initialType 流场初始化类型
     */
    void Initialize(const Initialization::Type &initialType);    

protected:
	/**
	* @brief 初始化求解器
	*
	*/
    void InitializeSolver();

    /**
     * @brief 求解函数
     * 
     */
    void Solve();

private:
    /**
     * @brief 流动变量更新
     * 
     */
    void Update();  

    /**
     * @brief LUSGS推进
     * 
     */
    void MarchLUSGS();    

    /**
     * @brief DPLUR推进
     * 
     */
    void MarchDPLUR();

    /**
    * @brief 计算对角阵的倒数
    *
    * @param[in] elementID 单元编号
    * @param[out] diagFlow 主流方程的对角阵倒数
    * @param[out] diagTur 湍流方程的对角阵倒数
    */
    void CalculateDiagInverse(const int &elementID, Scalar &diagFlow, std::vector<Scalar> &diagTur);

    /**
     * @brief 累加对流项差值（替代Jacobian矩阵系数）
     * 
     * @param[in] adjacentID 相邻单元编号
     * @param[in] faceID 面编号
     * @param[out] Dr    质量方程的对流项差值
     * @param[out] DrU   动量方程的对流项差值
     * @param[out] DrE   能量方程的对流项差值
     * @param[out] DrTur 湍流方程的对流项差值
     */
    void AddDeltaFlux(const int &adjacentID, const int &faceID,
        Scalar &Dr, Vector &DrU, Scalar &DrE, std::vector<Scalar> &DrTur);

    /**
    * @brief 累加rA项（替代Jacobian矩阵系数）
    *
    * @param[in] elementID 单元编号
    * @param[in] adjacentID 相邻单元编号
    * @param[in] faceID 面编号
    * @param[out] Dr    质量方程的对流项差值
    * @param[out] DrU   动量方程的对流项差值
    * @param[out] DrE   能量方程的对流项差值
    * @param[out] DrTur 湍流方程的对流项差值
    */
    void AddrAStar(const int &elementID, const int &adjacentID, const int &faceID, 
        Scalar &Dr, Vector &DrU, Scalar &DrE, std::vector<Scalar> &DrTur);

private:
    Scalar omega; ///< 对流项谱半径的松弛系数（1~2）
    std::vector<ElementField<Scalar>*> jacobianTurbulence; ///< 湍流的对角阵D中源项贡献部分
    
    ElementField<Scalar> deltaR; ///< 守恒量rho的差值场
    ElementField<Vector> deltaRU; ///< 守恒量rhoU的差值场
    ElementField<Scalar> deltaRE; ///< 守恒量rhoE的差值场
    std::vector<ElementField<Scalar>*> deltaRTur; ///< 湍流守恒量rhoX的差值场
};

} // namespace Flow
} // namespace Time

# endif