/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_UTREE_NOV_30_2010_1246PM)
#define BOOST_SPIRIT_UTREE_NOV_30_2010_1246PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/utree/utree_traits_fwd.hpp>
#include <boost/spirit/home/<USER>/utree/utree.hpp>
#include <boost/spirit/home/<USER>/utree/operators.hpp>
#include <boost/spirit/home/<USER>/utree/detail/utree_detail2.hpp>
#include <boost/spirit/home/<USER>/utree/utree_traits.hpp>

#endif
