///////////////////////////////////////////////////////////////////////////////
/// \file context.hpp
/// Includes all the context classes in the context/ sub-directory.
//
//  Copyright 2008 <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_PROTO_CONTEXT_HPP_EAN_06_23_2007
#define BOOST_PROTO_CONTEXT_HPP_EAN_06_23_2007

#include <boost/proto/context/null.hpp>
#include <boost/proto/context/default.hpp>
#include <boost/proto/context/callable.hpp>

#endif
