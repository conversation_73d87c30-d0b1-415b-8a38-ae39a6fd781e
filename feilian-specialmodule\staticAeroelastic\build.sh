#!/bin/bash
#

module load gcc/gcc-4.8.5
export PATH=/home/<USER>/guochengpeng/gcc-rpm/cmake-3.16.6-Linux-x86_64/bin:$PATH
export LD_LIBRARY_PATH=/home/<USER>/gcc-4.8.5/lib:/home/<USER>/gcc-4.8.5/lib64:$LD_LIBRARY_PATH


# cd ../../feilian-external/
# 
# ln -s linux-gcc4.4.7 linux-gcc4.8.5
# cd packages
# tinyxml2_path=$(pwd)/../tinyxml2
# tar -zxvf tinyxml2-10.0.0.tar.gz
# cd tinyxml2-10.0.0
# sed -i "s#/usr/local#$tinyxml2_path#" Makefile
# sed -i "s/AR = ar/AR = \/usr\/bin\/ar/g" Makefile
# sed -i "s/RANLIB = ranlib/RANLIB = \/usr\/bin\/ranlib/g" Makefile
# make &&  make install
# cd .. && rm -rf tinyxml2-10.0.0
# cd ../linux-gcc4.8.5
# ln -s ../tinyxml2 .
# ln -s ../linux-intel2021/mkl . 


cd ../../
# 修改配置适应气弹
sed -i "/option(ARI_ENABLE_PETSc/s/ON/OFF/g" CMakeLists.txt
sed -i "/option(ARI_ENABLE_TECIO/s/ON/OFF/g" CMakeLists.txt
sed -i "/option(ARI_ENABLE_AEROSTATIC/s/OFF/ON/g" CMakeLists.txt
sed -i "/option(ARI_ENABLE_MESHDEFORM/s/OFF/ON/g" CMakeLists.txt
sed -i "/option(ARI_ENABLE_MKL/s/OFF/ON/g" CMakeLists.txt
sed -i "/set(EXECUTABLE_OUTPUT_PATH/s/bin)/bin_gcc4.8.5)/g" CMakeLists.txt

rm -rf build_gcc4.8.5
mkdir build_gcc4.8.5
cd build_gcc4.8.5
cmake ..

for name in `find .|xargs grep -ri "gcc-4.8.5" -l`;do
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/ar/\/usr\/bin\/ar/g" $name
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/gcc-ar/\/usr\/bin\/gcc-ar/g" $name
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/ranlib/\/usr\/bin\/ranlib/g" $name
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/gcc-ranlib/\/usr\/bin\/gcc-ranlib/g" $name
done
make -j40

# 回复配置
sed -i "/option(ARI_ENABLE_PETSc/s/OFF/ON/g" ../CMakeLists.txt
sed -i "/option(ARI_ENABLE_TECIO/s/OFF/ON/g" ../CMakeLists.txt
sed -i "/option(ARI_ENABLE_AEROSTATIC/s/ON/OFF/g" ../CMakeLists.txt
sed -i "/option(ARI_ENABLE_MESHDEFORM/s/ON/OFF/g" ../CMakeLists.txt
sed -i "/option(ARI_ENABLE_MKL/s/ON/OFF/g" ../CMakeLists.txt
sed -i "/set(EXECUTABLE_OUTPUT_PATH/s/bin_gcc4.8.5)/bin)/g" ../CMakeLists.txt

CURRENT=$PWD

cd $CURRENT/../testCases/m6
# 设置MKL库环境变量
export LD_LIBRARY_PATH=$CURRENT/../feilian-external/linux-gcc4.8.5/mkl/lib:$LD_LIBRARY_PATH
# 运行测试
/home/<USER>/intel/oneapi/mpi/2021.1.1/bin/mpirun -np 20  $CURRENT/../bin_gcc4.8.5/mainAeroStatic default.xml
