// Copyright <PERSON> 2002.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

// DEPRECATED HEADER (2006 Jan 12)
// Provided only for backward compatibility.
// The boost::python::len() function is now defined in object.hpp.

#ifndef BOOST_PYTHON_API_PLACE_HOLDER_HPP
#define BOOST_PYTHON_API_PLACE_HOLDER_HPP

#include <boost/python/object.hpp>

#endif // BOOST_PYTHON_API_PLACE_HOLDER_HPP
