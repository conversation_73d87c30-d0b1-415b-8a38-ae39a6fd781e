// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CSTRING_HEADER
#define __CSTRING_HEADER

#include <string.h>

namespace std {
  using ::size_t;
  using ::memchr;
  using ::strcat;
  using ::strcspn;
  using ::strncpy;
  using ::strtok;
  using ::memcmp;
  using ::strchr;
  using ::strerror;
  using ::strpbrk;
  using ::strxfrm;
  using ::memcpy;
  using ::strcmp;
  using ::strlen;
  using ::strrchr;
  using ::memmove;
  using ::strcoll;
  using ::strncat;
  using ::strspn;
  using ::memset;
  using ::strcpy;
  using ::strncmp;
  using ::strstr;
}

#endif // CSTRING_HEADER
