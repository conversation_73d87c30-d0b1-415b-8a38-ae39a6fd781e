//  (C) Copyright <PERSON> 2005.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)


#ifndef BOOST_TR1_RANDOM_INCLUDED
#  define BOOST_TR1_RANDOM_INCLUDED
#  include <boost/tr1/detail/config_all.hpp>

#  ifdef BOOST_HAS_CPP_0X
#     if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#        include_next <random>
#     else
#        include BOOST_TR1_STD_HEADER(random)
#     endif
#  endif

#  if !defined(BOOST_TR1_NO_RECURSION)
#  define BOOST_TR1_NO_RECURSION
#  ifdef BOOST_HAS_TR1_RANDOM
#     if defined(BOOST_HAS_INCLUDE_NEXT) && !defined(BOOST_TR1_DISABLE_INCLUDE_NEXT)
#        include_next BOOST_TR1_HEADER(random)
#     else
#        include BOOST_TR1_STD_HEADER(BOOST_TR1_PATH(random))
#     endif
#  else
#     include <boost/tr1/random.hpp>
#  endif
#  undef BOOST_TR1_NO_RECURSION
#  endif
#endif


