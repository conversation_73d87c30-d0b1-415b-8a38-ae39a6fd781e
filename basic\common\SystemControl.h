﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SystemControl.h
//! <AUTHOR>
//! @brief 程序控制及打印信息.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_SystemControl_
#define _basic_common_SystemControl_

#include <string>
#include <fstream>

/**
 * @namespace Info
 * @brief 日志信息相关配置和状态
 */
namespace Info
{
    static std::ofstream infoFile;   ///< 日志文件输出流
    static int status;               ///< 系统状态码
    static int fileLevel = 1;        ///< 文件日志级别(1-5)
    static int screenLevel = 1;      ///< 屏幕输出级别(1-5)
}

/**
 * @brief 致命错误处理
 * @param info 错误信息
 * @details 打印错误信息并终止程序运行
 * @note 此函数不会返回，程序将直接退出
 */
void FatalError(const std::string &info);

/**
 * @brief 警告信息处理(暂停)
 * @param info 警告信息
 * @details 打印警告信息并暂停程序运行，等待用户确认
 */
void WarningPause(const std::string &info);

/**
 * @brief 警告信息处理(继续)
 * @param info 警告信息 
 * @details 打印警告信息但程序继续运行
 */
void WarningContinue(const std::string &info);

/**
 * @brief 设置日志文件路径
 * @param s 日志文件路径
 * @return 无
 * @note 如果文件已打开，会先关闭当前文件再打开新文件
 */
void SetInfoFile(const std::string &s);

/**
 * @brief 设置日志输出级别
 * @param fileLevel 文件日志级别(1-5)
 * @param screenLevel 屏幕输出级别(1-5)
 * @details 级别越高输出的信息越详细
 * @note 默认级别为1(仅输出关键信息)
 */
void SetInfoLevel(const int &fileLevel = 0, const int &screenLevel = 0);

/**
 * @brief 关闭日志文件
 * @return 无
 */
void CloseInfoFile();

/**
 * @brief 通用信息输出
 * @param info 输出信息
 * @param level 信息级别
 * @details 根据设置的级别决定是否输出到文件和屏幕
 */
void Print(const std::string &info, const int &level = 0);

/**
 * @brief 可变参数模板打印
 * @tparam Type1 第一个参数类型
 * @tparam Types 剩余参数类型包
 * @param value1 第一个参数值
 * @param rest 剩余参数值包
 * @details 递归打印所有参数到日志
 */
template<class Type1, class ... Types>
void PrintAll(Type1 value1, Types ... rest)
{
    Print(ToString(value1));
    PrintAll(rest...);
}

/**
 * @brief 屏幕输出
 * @param info 输出信息
 * @details 直接输出到屏幕，不受日志级别限制
 */
void PrintScreen(const std::string &info);

/**
 * @brief 文件输出 
 * @param info 输出信息
 * @details 直接输出到日志文件，不受日志级别限制
 */
void PrintFile(const std::string &info);

/**
 * @brief 打印标题信息 
 * @param info 输出信息
 * @details 打印标题信息
 */
void PrintTitleInfo(const std::string &info);

/**
 * @brief 打印进度信息
 * @param processDescription 进度描述
 * @param currentStep 当前步数
 * @param totalStep 总步数
 * @details 格式化为百分比进度显示
 */
void PrintProgressRate(const std::string &processDescription, 
                       const int &currentStep, 
                       const int &totalStep);

/**
 * @brief 创建目录
 * @param path 目录路径
 * @return 无
 * @throw 如果创建失败抛出异常
 */
void MakeDirectory(const std::string &path);

/**
 * @brief 检查状态码
 * @param codeID 状态码
 * @return 无
 * @throw 如果状态码非零抛出异常
 */
void CheckStatus(const int &codeID);

/**
 * @brief 检查消息有效性
 * @param message 待检查消息
 * @return true-消息有效 false-消息无效
 */
bool CheckMessage(const std::string &message);

/**
 * @brief 删除目录及其内容
 * @param directoryPath 目录路径
 * @return 无
 * @throw 如果删除失败抛出异常
 * @warning 此操作不可逆，会递归删除目录下所有内容
 */
void DeleteDirectory(const std::string& directoryPath);

#endif // _basic_common_SystemControl_
