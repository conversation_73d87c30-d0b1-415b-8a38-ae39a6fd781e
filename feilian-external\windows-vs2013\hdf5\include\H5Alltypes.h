// C++ informative line for the emacs editor: -*- C++ -*-
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * Copyright by the Board of Trustees of the University of Illinois.         *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://support.hdfgroup.org/ftp/HDF5/releases.  *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

// This header file simply serves as a container to hold the
// header files of all datatypes.  It simplifies the header
// file including in the code.

#include "H5DataType.h"
#include "H5AtomType.h"
#include "H5EnumType.h"
#include "H5IntType.h"
#include "H5FloatType.h"
#include "H5StrType.h"
#include "H5CompType.h"
#include "H5ArrayType.h"
#include "H5VarLenType.h"
