﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//

//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_SpalartAllmarasDDES_
#define _sourceFlow_turbulence_SpalartAllmarasDDES_

#include "sourceFlow/turbulence/SpalartAllmaras.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流SA-DES类(DES97)
 * 
 */
class  SpalartAllmarasDDES : public SpalartAllmaras
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
	SpalartAllmarasDDES(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
	~SpalartAllmarasDDES();

	/**
	* @brief 累加源项通量残差
	*
	*/
	void AddSourceResidual();


private:

	ElementField<Scalar> *ldOverlr;     ///< l_des / l_rans
	ElementField<Scalar> *shieldingFunction;     ///< 延迟函数

        // DES常数
        Scalar Cdes; //SA-DES模型中网格尺度的缩放系数
	Scalar Cdt; //延迟函数参数，值越大对边界层的保护越大
	const Scalar rKappa2; //SA-DES模型中网格尺度的缩放系数


};

} // namespace Turbulence
#endif 
