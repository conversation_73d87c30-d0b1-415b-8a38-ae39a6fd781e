﻿#include "sourceFlow/boundaryCondition/Periodic.h"

namespace Boundary
{
namespace Flow
{

Periodic::Periodic(const int &boundaryPatchID, Package::FlowPackage &data, const std::vector<Scalar> &value)
    :
    FlowBoundary(boundaryPatchID, data)
{    
    if (value.size() != 11)
    {
        FatalError("Periodic::Periodic: patchID = " + ToString(boundaryPatchID) + ", need 11 datas!");
    }    

    this->couplePatchIDGlobal = int(value[0]);
    this->translation = Vector(value[1], value[2], value[3]);
    this->rotationAxis.first = Vector(value[4], value[5], value[6]);
    this->rotationAxis.second = Vector(value[7], value[8], value[9]);
    this->rotationAngle = value[10] * PI / 180.0;

    //形成标志    
    if (translation.Mag() < SMALL) this->translationFlag = false;
    else                           this->translationFlag = true;

    if (rotationAngle < SMALL) this->rotationFlag = false;
    else                       this->rotationFlag = true;
}

void Periodic::Initialize()
{
    this->UpdateBoundaryCondition();
}

void Periodic::UpdateBoundaryCondition()
{
    FatalError("Periodic::UpdateBoundaryCondition: need to check and modify");
}

void Periodic::AddDiffusiveResidual()
{
    if(!nodeCenter) this->AddDiffusiveResidualCellCenter();
    else            FatalError("Periodic::UpdateBoundaryResidual: need to check and modify");
}

void Periodic::AddConvectiveResidual()
{
	if (jacobian) this->AddConvectiveResidualReflected();
	else          this->AddConvectiveResidualAverage();
}

void Periodic::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;
    
    FatalError("Periodic::UpdateBoundaryResidual: need to check and modify");
}

}// namespace Flow
}// namespace Boundary
