﻿#include "meshProcess/motion/TranslationRotation.h"

TranslationRotation::TranslationRotation(const std::vector<Package::FlowPackage *> flowPackageVector_,
	const Configure::Flow::MotionStruct &motionStruct_)
	:Motion(flowPackageVector_),
	translationVelocity(motionStruct_.translationRotation.velocity),
	rotationAxisOrigin(motionStruct_.translationRotation.axisOrigin),
	rotationAxisDirection(motionStruct_.translationRotation.axisDirection),
	rotationRate(motionStruct_.translationRotation.rotationRate)
{
	
}

TranslationRotation::~TranslationRotation(){

}

void TranslationRotation::MeshUpdate()
{
	Print("Update mesh motion......");

	deltaDistance = translationVelocity * unsteadyTimeStep;
	deltaAngle = rotationRate * unsteadyTimeStep;
	rotationAxisOrigin += deltaDistance;
	
	mesh->Translate(deltaDistance);
	mesh->Rotate(rotationAxisOrigin, rotationAxisDirection, deltaAngle);

	this->FieldUpdate();
}

void TranslationRotation::FieldUpdate()
{
	Mesh *localMesh = flowPackageVector[0]->GetMeshStruct().mesh;
	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		Vector elemCenter = localMesh->GetElement(elemID).GetCenter();
		Vector lineVelocity = this->CaculateLineVelocityAroundAxis(
											elemCenter,
											rotationAxisDirection,
											rotationAxisDirection.Normalize(),
											rotationRate);
		
		Vector meshVelocity = translationVelocity + lineVelocity;
		flowPackageVector[0]->GetField().meshVelocity->SetValue(elemID, meshVelocity);
	}
}