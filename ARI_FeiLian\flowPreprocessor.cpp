﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file flowPreprocessor.cpp
//! <AUTHOR>
//! @brief 前处理功能主流程.
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-31 李艳亮、乔龙
//     说明：建立并规范化
//
//------------------------------------------------------------------------------

#include "meshProcess/meshProcess/MeshProcess.h"

int main(int argc, char **argv)
{
	if (argc <= 1)
	{
		FatalError("参数错误");
		return -1;
	}

    // 输出软件相关信息
    SetInfoFile("AriCFD_Preprocessor.info");
    PrintTitleInfo(" ARI-FlowPreprocessor ");
    PrintSystemTime();

    // 定义参数文件对象
    Configure::Configure configure;

    // 读取参数文件
    configure.ReadCaseXml(argv[1]);

    // 打印参数信息
    configure.PrintInformation();
    
    // 设置前处理的线程数
    configure.SetOpenMPThread();

    // 定义前处理对象
    MeshProcess meshProcess(configure);
    CheckStatus(1000);

    // 开始前处理
    meshProcess.PreProcess();

    // 前处理完成
    CloseInfoFile();

    return 0;
}
