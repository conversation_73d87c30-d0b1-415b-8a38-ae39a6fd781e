 #if defined EXTERN
 #undef EXTERN
 #endif
 #if defined ___905
 #define EXTERN
 #else
 #define EXTERN extern
 #endif
typedef struct ___4622 { ___3501 ___4672; struct ___4622 *___2701; } ___4623; template <typename T> void ___1923(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___3318 >= 1); ++(___2850->___3318); } template <typename T> void ___944(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___3318 > 0); --(___2850->___3318); } template <typename T> ___372 ___2061(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___3318 >= 0); return ___2850->___3318 > 1; } template <typename T> ___372 ___2060(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___3318 >= 0); return ___2850->___3318 > 0; } template <> void ___1923(___1361 ___2850); template <> void ___944(___1361 ___2850); template <> ___372 ___2061(___1361 ___2850); template <> ___372 ___2060(___1361 ___2850); template <typename T> void ___1924(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___4371 >= 0); REQUIRE(___2850->___3318 > ___2850->___4371); ++(___2850->___4371); } template <typename T> void ___945(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___4371 >= 1); REQUIRE(___2850->___3318 >= ___2850->___4371); --(___2850->___4371); } template <typename T> ___372 ___2079(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___4371 >= 0); REQUIRE(___2850->___3318 >= ___2850->___4371); return ___2850->___4371 > 1; } template <typename T> ___372 ___2078(T* ___2850) { REQUIRE(VALID_REF(___2850)); REQUIRE(___2850->___4371 >= 0); REQUIRE(___2850->___3318 >= ___2850->___4371); return ___2850->___4371 > 0; } bool isSharedVarBaseZone( ___902 const& ___881, ___3501           ___4671, ___1172       refZone, ___1172       refVar); EXTERN ___3501 ___1772( ___902 const* ___883, ___1172       ___4600, ___1172       ___4337, ___3501           ZonesToExamine); EXTERN ___1172 ___3181( ___902 const* ___881, ___3501           ___4684, ___1172       zone, ___1172       ___4336); EXTERN ___3501 ___1804(___902  *___883, ___1172  ___4600, ___3501      ZonesToExamine); EXTERN ___2227 ___1798(___902 *___883, ___1172 ___4600, ___1172 ___4337); EXTERN ___372 ___415( ___902* ___883, ___1172 ___4600, ___1172 ___4337, ___372  CopySharedData); EXTERN ___2227 ___1662( ___902 const* ___883, ___1172       ___4600); EXTERN ___372 ___412(___902 *___883, ___1172 ___4600); EXTERN ___372 ___413(___1521    *___1483, ___1172  ___4600); EXTERN ___372 ___414(___1521    *___1483, ___1172  ___4600); EXTERN ___372 ZoneStructuresAreNativeVarCopyCompatible( ValueLocation_e   sourceVarValueLocation, ValueLocation_e   targetVarValueLocation, ___4683 const* sourceZoneSpec, ___4683 const* targetZoneSpec); EXTERN ___372 ___4688( ValueLocation_e   sourceVarValueLocation, ValueLocation_e   targetVarValueLocation, ZoneData_s const* sourceZoneData, ___4683 const* sourceZoneSpec, ___4683 const* targetZoneSpec, ___372&        ___2039); EXTERN ___372 ___2870( ___902 const*  ___881, ___1172        sourceZone, ___4683 const* targetZoneSpec, ValueLocation_e   targetVarValueLocation, ___1172        ___4336, ___372&        ___2039); EXTERN ___372 ___4687( ZoneData_s const* sourceZoneData, ___4683 const* sourceZoneSpec, ___4683 const* targetZoneSpec, ___372&        ___2039); EXTERN ___372 ___2869( ___902 const*  ___881, ___1172        sourceZone, ___4683 const* targetZoneSpec, ___372&        ___2039); EXTERN ___372 ___2077(___902 const* ___883, ___1172       ___4600, ___1172       ___4337); EXTERN ___372 ___4354(___902 const* ___883, ___1172       ___4601, ___1172       ___4602, ___1172       ___718); EXTERN ___372 ___2726(___902 const* ___883, ___1172       ___4601, ___1172       ___4602); EXTERN ___372 ___1261(___902 const* ___883, ___1172       ___4601, ___1172       ___4602); EXTERN ___372 ___2737(___902 const* ___883, ___1172       ___4601, ___1172       ___4602); EXTERN ___372 ElemToFaceMapIsSharedBetweenZones(___902 const* ___883, ___1172       ___4601, ___1172       ___4602); EXTERN ___372 ___546(___902 const* ___883, ___1172       ___4601, ___1172       ___4602); EXTERN ___1172 ___3180( ___902 const* ___881, ___3501           ___4684, ___1172       zone); EXTERN ___372 ___4625(___902 const* ___883, ___1172       ___4600); EXTERN ___372 ___4627(___902 const* ___883,
___1172       ___4600, ___3501           ___4370); EXTERN ___372 ___4626(___902 const* ___883, ___1172       ___4600, ___3501           ___4370); EXTERN ___372 ___892(___902 const* ___883, ___3501           ___4672); EXTERN ___372 ___894(___902 const* ___883, ___3501           ___4672, ___3501           ___4370); EXTERN ___372 ___893(___902 const* ___883, ___3501           ___4672, ___3501           ___4370); EXTERN ___372 ___80(void); EXTERN ___372 ___1799( ___902 const*       ___883, ___3501                 ___1419, ValueLocation_e const* ___2231, ___3501                 ___4655, ___4623*&      ___4624); EXTERN void ___3553( ___902 const* ___883, ___1172       ___3648, ___1172       ___3952, ___1172       ___4346); EXTERN void ___680(const ___4683* ___3649, ___4683*       ___3953); EXTERN void ___3548( ___902 const* ___883, ___1172       ___3648, ___1172       ___3952); EXTERN void ___3554( ___902 const*       ___883, ___4623 const* ___4624, ___1172             ___4340); EXTERN void ___940(___4623 **___4621);
