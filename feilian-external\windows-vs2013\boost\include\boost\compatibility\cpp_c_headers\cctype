// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CCTYPE_HEADER
#define __CCTYPE_HEADER

#include <ctype.h>

namespace std {
  using ::isalnum;
  using ::isdigit;
  using ::isprint;
  using ::isupper;
  using ::tolower;
  using ::isalpha;
  using ::isgraph;
  using ::ispunct;
  using ::isxdigit;
  using ::toupper;
  using ::iscntrl;
  using ::islower;
  using ::isspace;
}

#endif // CCTYPE_HEADER
