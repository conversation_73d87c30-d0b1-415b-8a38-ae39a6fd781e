﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceKDT.h
//! <AUTHOR>
//! @brief 计算壁面距离KDT方法
//! @date  2023-02-10
//
//------------------------------修改日志----------------------------------------
//
// 2023-02-10 曾凯
// 说明：添加KDT搜索计算壁面距离的方法
//
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_WallDistanceKDT_
#define _meshProcess_wallDistance_WallDistanceKDT_

#include "meshProcess/wallDistance/WallDistanceBase.h"
#include "meshProcess/wallDistance/KDT_utilities.h"

/**
 * @brief 采用KDT计算近壁面距离类
 * 
 */
class WallDistanceKDT : public WallDistanceBase
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in, out] mesh 网格指针
	* @param[in] wallBoundaryFace_ 物面面元容器（包含面元信息和构成面的点坐标）
	*/
    WallDistanceKDT(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_);
    
	/**
	* @brief 析构函数
	*
	*/
	~WallDistanceKDT();

	/**
	* @brief 计算壁面距离
	*
	*/
	void Calculate();

private:
	/**
	* @brief 建立KDT树结构
	*
	* @param[in] ndim 网格维度，2或3
	*/
    void BuildUnstructuredSurfaceKDT(const int &ndim);
    
private:
	typedef DataStruct_KdtTree<std::vector<Scalar>, int> KdtTree; ///< 重命名
	KdtTree * KdtTrees; ///< 壁面KDT树的指针
};

#include "meshProcess/wallDistance/KDT_utilities.hxx"

#endif
