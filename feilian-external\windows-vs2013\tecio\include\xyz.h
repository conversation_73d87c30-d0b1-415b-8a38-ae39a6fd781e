 #pragma once
#include "ThirdPartyHeadersBegin.h"
#  include <algorithm>
#  include <cfloat>
#  include <cmath>
#  include <vector>
#include "ThirdPartyHeadersEnd.h"
#include "basicTypes.h"
#include "CodeContract.h"
#include "ClassMacros.h"
namespace tecplot { namespace ___3933 { template<typename T> inline void clearAndDeallocVector(std::vector<T>& vectorToClear) { std::vector<T>().swap(vectorToClear); } class UVW { private: double m_uValue; double m_vValue; double m_wValue; public: UVW(double uu, double vv, double ww) : m_uValue(uu), m_vValue(vv), m_wValue(ww) {} UVW() : m_uValue(DBL_MAX), m_vValue(DBL_MAX), m_wValue(DBL_MAX) {} inline double u(void) const { return m_uValue; } inline double ___4291(void) const { return m_vValue; } inline double w(void) const { return m_wValue; } inline void setU(double uu) { m_uValue = uu; } inline void setV(double vv) { m_vValue = vv; } inline void setW(double ww) { m_wValue = ww; } void invalidate(void) { m_uValue=DBL_MAX; m_vValue=DBL_MAX; m_wValue=DBL_MAX; } bool ___2067(void) const { return m_uValue!=DBL_MAX && m_vValue!= DBL_MAX && m_wValue!=DBL_MAX; } inline bool operator==(UVW const& uvw) { return m_uValue==uvw.m_uValue && m_vValue==uvw.m_vValue && m_wValue==uvw.m_wValue; } inline void operator/=(double const scalar) { m_uValue /= scalar; m_vValue /= scalar; m_wValue /= scalar; } inline void operator+=(UVW const& uvw) { m_uValue += uvw.m_uValue; m_vValue += uvw.m_vValue; m_wValue += uvw.m_wValue; } inline UVW operator+(UVW const& uvw) const { return UVW(m_uValue+uvw.m_uValue, m_vValue+uvw.m_vValue, m_wValue+uvw.m_wValue); } inline UVW operator-(UVW const& uvw) const { return UVW(m_uValue-uvw.m_uValue, m_vValue-uvw.m_vValue, m_wValue-uvw.m_wValue); } inline UVW operator*(double const scalar) const { return UVW(m_uValue*scalar, m_vValue*scalar, m_wValue*scalar); } inline double normL1(void) const { return std::abs(m_uValue) + std::abs(m_vValue) + std::abs(m_wValue); } }; class ___4580 { private: double m_xValue; double m_yValue; double m_zValue; public: ___4580(double xx, double yy, double zz) : m_xValue(xx), m_yValue(yy), m_zValue(zz) {} ___4580() : m_xValue(DBL_MAX), m_yValue(DBL_MAX), m_zValue(DBL_MAX) {} inline double x(void) const { return m_xValue; } inline double ___4583(void) const { return m_yValue; } inline double z(void) const { return m_zValue; } inline void setX(double xx) { m_xValue = xx; } inline void setY(double yy) { m_yValue = yy; } inline void setZ(double zz) { m_zValue = zz; } void invalidate(void) { m_xValue=DBL_MAX; m_yValue=DBL_MAX; m_zValue=DBL_MAX; } bool ___2067(void) const { return m_xValue!=DBL_MAX && m_yValue!= DBL_MAX && m_zValue!=DBL_MAX; } inline void operator*=(double const scalar) { m_xValue *= scalar; m_yValue *= scalar; m_zValue *= scalar; } inline void operator/=(double const scalar) { m_xValue /= scalar; m_yValue /= scalar; m_zValue /= scalar; } inline void operator+=(___4580 const& xyz) { m_xValue += xyz.x(); m_yValue += xyz.___4583(); m_zValue += xyz.z(); } inline ___4580 operator+(___4580 const& xyz) const { return ___4580(m_xValue+xyz.x(), m_yValue+xyz.___4583(), m_zValue+xyz.z()); } inline ___4580 operator-(___4580 const& xyz) const { return ___4580(m_xValue-xyz.x(), m_yValue-xyz.___4583(), m_zValue-xyz.z()); } inline ___4580 operator*(double const scalar) const { return ___4580(m_xValue*scalar, m_yValue*scalar, m_zValue*scalar); } inline bool operator>(double const scalar) const { return m_xValue > scalar && m_yValue > scalar && m_zValue > scalar; } inline bool operator<(double const scalar) const { return m_xValue < scalar && m_yValue < scalar && m_zValue < scalar; } inline bool operator<=(___4580 const& xyz) const { return m_xValue <= xyz.x() && m_yValue <= xyz.___4583() && m_zValue <= xyz.z(); } inline bool operator==(___4580 const& xyz) const { return float(m_xValue) == float(xyz.x()) && float(m_yValue) == float(xyz.___4583()) && float(m_zValue) == float(xyz.z()); } inline double normL1(void) const { return std::abs(m_xValue) + std::abs(m_yValue) + std::abs(m_zValue); } }; inline ___4580 abs(___4580 const& xyz) { return ___4580(std::abs(xyz.x()), std::abs(xyz.___4583()), std::abs(xyz.___4583())); } class XYZC { private: ___4580 m_xyz; double m_cValue; public: XYZC(___4580 const& xyz, double cc) : m_xyz(xyz), m_cValue(cc) {} XYZC() : m_xyz(DBL_MAX, DBL_MAX, DBL_MAX), m_cValue(DBL_MAX) {} void invalidate(void) { m_xyz.invalidate(); m_cValue = DBL_MAX; } bool ___2067(void) const { return m_xyz.___2067() && m_cValue!= DBL_MAX; } inline ___4580 const& xyz(void) const { return m_xyz; } inline double const& c(void) const { return m_cValue; } inline XYZC operator+(XYZC const& xyzc) const { return XYZC(m_xyz+xyzc.xyz(), m_cValue+xyzc.c()); } inline XYZC operator-(XYZC const& xyzc) const { return XYZC(m_xyz-xyzc.xyz(), m_cValue-xyzc.c()); } inline XYZC operator*(double const scalar) const { return XYZC(m_xyz*scalar, m_cValue*scalar); } inline void operator+=(XYZC const& xyzc) { m_xyz+=xyzc.xyz(); m_cValue+=xyzc.c(); } inline void operator*=(double const ___1304) { m_xyz*=___1304; m_cValue*=___1304; } inline void operator/=(double const ___1304) { m_xyz/=___1304; m_cValue/=___1304; } inline void setX(double xx) { m_xyz.setX(xx); } inline void setY(double yy) { m_xyz.setY(yy); } inline void setZ(double zz) { m_xyz.setZ(zz); } inline void setC(double cc) { m_cValue = cc; } inline double norm(void) { return m_xyz.normL1() + std::abs(m_cValue); }
}; template<typename T> class ThreeValues { private: T m_v1; T m_v2; T m_v3; public: ThreeValues(T v1, T v2, T v3) : m_v1(v1), m_v2(v2), m_v3(v3) {} ThreeValues() {} T const& v1() const { return m_v1; } T const& v2() const { return m_v2; } T const& v3() const { return m_v3; } T& v1() { return m_v1; } T& v2() { return m_v2; } T& v3() { return m_v3; } bool operator>=(T const& scalar) const { return m_v1>=scalar && m_v2>=scalar && m_v3>=scalar; } bool operator<=(T const& scalar) const { return m_v1<=scalar && m_v2<=scalar && m_v3<=scalar; } bool operator<(T const& scalar) const { return m_v1<scalar && m_v2<scalar && m_v3<scalar; } bool operator>(T const& scalar) const { return m_v1>scalar && m_v2>scalar && m_v3>scalar; } bool anyComponentEquals(T const& scalar) const { return m_v1==scalar || m_v2==scalar || m_v3==scalar; } }; class XYZVars : public ThreeValues<___4352> { public: XYZVars(___4352 xVar, ___4352 yVar, ___4352 zVar) : ThreeValues<___4352>(xVar, yVar, zVar) {} ___4352 xVar() const { return v1(); } ___4352 yVar() const { return v2(); } ___4352 zVar() const { return v3(); } }; class UVWVars : public ThreeValues<___4352> { public: UVWVars(___4352 uVar, ___4352 vVar, ___4352 wVar) : ThreeValues<___4352>(uVar, vVar, wVar) {} ___4352 uVar() const { return v1(); } ___4352 vVar() const { return v2(); } ___4352 wVar() const { return v3(); } }; template<typename T> class FourValues { private: T m_v1; T m_v2; T m_v3; T m_v4; public: FourValues(T v1, T v2, T v3, T v4) : m_v1(v1), m_v2(v2), m_v3(v3), m_v4(v4) {} FourValues() {} T const& v1() const { return m_v1; } T const& v2() const { return m_v2; } T const& v3() const { return m_v3; } T const& v4() const { return m_v4; } T& v1() { return m_v1; } T& v2() { return m_v2; } T& v3() { return m_v3; } T& v4() { return m_v4; } bool operator>=(T const& scalar) const { return m_v1>=scalar && m_v2>=scalar && m_v3>=scalar && m_v4>=scalar; } bool operator<=(T const& scalar) const { return m_v1<=scalar && m_v2<=scalar && m_v3<=scalar && m_v4<=scalar; } }; class ThreeValueArray : public ThreeValues< std::vector<float> > { UNCOPYABLE_CLASS(ThreeValueArray); public: ThreeValueArray() {} size_t size() { ___478(v1().size()==v2().size() && v2().size()==v3().size()); return v1().size(); } void clearAndDealloc(void) { clearAndDeallocVector(v1()); clearAndDeallocVector(v2()); clearAndDeallocVector(v3()); } void repeatLastPoint() { size_t lastPoint = size()-1; v1().push_back(v1()[lastPoint]); v2().push_back(v2()[lastPoint]); v3().push_back(v3()[lastPoint]); } void switchValues(size_t ___1841, size_t ___2113) { REQUIRE(___1841<size()); REQUIRE(___2113<size()); float ___4179 = v1()[___1841]; v1()[___1841] = v1()[___2113]; v1()[___2113] = ___4179; ___4179 = v2()[___1841]; v2()[___1841] = v2()[___2113]; v2()[___2113] = ___4179; ___4179 = v3()[___1841]; v3()[___1841] = v3()[___2113]; v3()[___2113] = ___4179; } }; class XYZArray : public ThreeValueArray { UNCOPYABLE_CLASS(XYZArray); public: XYZArray() {} void push_back(___4580 const& xyz) { REQUIRE(xyz.___2067()); v1().push_back(float(xyz.x())); v2().push_back(float(xyz.___4583())); v3().push_back(float(xyz.z())); } ___4580 const operator[](size_t pos) const { return ___4580(v1()[pos], v2()[pos], v3()[pos]); } }; class StreamUVWArray : public ThreeValueArray { UNCOPYABLE_CLASS(StreamUVWArray); public: StreamUVWArray() {} void push_back(UVW const& uvw) { REQUIRE(uvw.___2067()); v1().push_back(float(uvw.u())); v2().push_back(float(uvw.___4291())); v3().push_back(float(uvw.w())); } UVW const operator[](size_t pos) const { return UVW(v1()[pos], v2()[pos], v3()[pos]); } }; }}
