//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_CONTAINER_STRING_HPP
#define BOOST_COMPUTE_CONTAINER_STRING_HPP

#include <boost/compute/types/fundamental.hpp>
#include <boost/compute/container/basic_string.hpp>

namespace boost {
namespace compute {

typedef basic_string<char_> string;

} // end compute namespace
} // end boost namespace

#endif // BOOST_COMPUTE_CONTAINER_STRING_HPP
