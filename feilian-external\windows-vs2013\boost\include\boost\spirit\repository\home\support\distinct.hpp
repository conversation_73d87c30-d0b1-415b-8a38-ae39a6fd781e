//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_REPOSITORY_SUPPORT_DISTINCT_APR_28_2009_0110PM)
#define BOOST_SPIRIT_REPOSITORY_SUPPORT_DISTINCT_APR_28_2009_0110PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/terminal.hpp>

namespace boost { namespace spirit { namespace repository
{
    // The distinct extended terminal
    BOOST_SPIRIT_DEFINE_TERMINALS_NAME_EX(( distinct, distinct_type ))

}}}

#endif
