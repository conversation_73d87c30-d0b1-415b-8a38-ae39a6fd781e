﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file NodeField.h
//! <AUTHOR> 张帅（西交大/数峰科技）
//! @brief 点场类
//! @date 2020-07-23
//
//------------------------------修改日志----------------------------------------
// 2022-05-27 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-23 凌空 张帅
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_field_NodeField_
#define _basic_field_NodeField_

#include "basic/field/BaseField.h"

/**
 * @brief 格点场类
 * 
 * @tparam Type 节点数值类型
 */
template<class Type>
class NodeField : public BaseField<Type>
{
public:
    /**
     * @brief 构造函数，创建节点场对象
     * 
     * @param[in] UGB 网格指针
     * @param[in] name 场名称
     */
    NodeField(Mesh* UGB, const std::string name = "NO_NAME") :BaseField<Type>(UGB, name){};

    /**
     * @brief 构造函数，创建节点场对象并赋初值
     * 
     * @param[in] UGB 网格指针
     * @param[in] value 初始值
     * @param[in] name 场名称
     */
    NodeField(Mesh* UGB, const Type &value, const std::string name = "NO_NAME") :BaseField<Type>(UGB, name) { this->Initialize(value);}

    /**
     * @brief 构造函数，基于输入节点场创建节点场对象
     * 
     * @param[in] field 输入节点场
     * @param[in] name 场名称
     */
    NodeField(const NodeField<Type> &field, const std::string name = "NO_NAME");

    /**
     * @brief 开辟场的存储空间，并初始化为零
     * 
     */
    void Initialize();
    
    /**
     * @brief 创建节点场
     * 
     */
    void Create();

    /**
     * @brief 开辟场的存储空间，并均匀初始化
     * 
     * @param[in] initalValue 初始值
     */
    void Initialize(Type initalValue);

    /**
     * @brief 开辟场的存储空间，并非均匀初始化
     * 
     * @param[in] valueList 初始值列表
     */
    void Initialize(const std::vector<Type>& valueList);
    
    /**
     * @brief 开辟场的存储空间，并用自定义函数初始化
     * 
     * @param[in] udf 自定义函数
     */
    void Initialize(Type(udf)(Scalar, Scalar, Scalar));
    
    /**
     * @brief 检查场物理量是否超过最大最小值
     * 
     * @param[in] minValue 最小值
     * @param[in] maxValue 最大值
     * @return int 
     */
    int CheckAndLimit(const Scalar &minValue, const Scalar &maxValue);

    /**
     * @brief 从文件初始化节点场
     * 
     * @param[in] fileName 文件名称
     * @param[in] binary 二进制标识，true为二进制
     */
    void ReadFile(const std::string fileName, const bool binary = true);
    
    /**
     * @brief 节点场写到文件
     * 
     * @param[in] fileName 文件名称
     * @param[in] binary 二进制标识，true为二进制
     */
    void WriteFile(const std::string fileName, const bool binary = true);
    
    // 以下函数后续版本调整时拟舍弃
    // *****************************************************************************************
public:
    /**
     * @brief 运算符=的重载(采用场赋值)
     * 
     * @param[in] rhs 运算符右端物理场
     * @return ElementField<Type>& 
     */
    NodeField<Type>& operator = (const NodeField<Type>& rhs);

    /**
     * @brief 运算符=的重载（采用常数赋值）
     * 
     * @param[in] rhs 运算符右端常数
     * @return ElementField<Type>& 
     */
    NodeField<Type>& operator = (const Type &rhs);

    // *****************************************************************************************
};

#endif
