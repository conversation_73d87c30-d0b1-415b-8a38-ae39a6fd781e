// Copyright <PERSON> 2002.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
#ifndef HANDLE_FWD_DWA2002615_HPP
# define HANDLE_FWD_DWA2002615_HPP

# include <boost/python/detail/prefix.hpp>

namespace boost { namespace python { 

template <class T = PyObject> class handle;

}} // namespace boost::python

#endif // HANDLE_FWD_DWA2002615_HPP
