/*!
@file
Forward declares the @ref group-core module.

@copyright <PERSON> 2013-2016
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_HANA_FWD_CORE_HPP
#define BOOST_HANA_FWD_CORE_HPP

#include <boost/hana/fwd/core/common.hpp>
#include <boost/hana/fwd/core/to.hpp>
#include <boost/hana/fwd/core/default.hpp>
#include <boost/hana/fwd/core/is_a.hpp>
#include <boost/hana/fwd/core/make.hpp>
#include <boost/hana/fwd/core/tag_of.hpp>
#include <boost/hana/fwd/core/when.hpp>

#endif // !BOOST_HANA_FWD_CORE_HPP
