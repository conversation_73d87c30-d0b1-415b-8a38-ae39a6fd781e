﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file InviscidFluxScheme.h
//! <AUTHOR>
//! @brief 进行NS方程无粘项通量求解的基类，用于派生具体通量类。
//! @date 2021-03-29
//
//------------------------------修改日志----------------------------------------
// 2021-03-29 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_InviscidFluxScheme_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_InviscidFluxScheme_

#include "sourceFlow/fluxScheme/FlowFluxScheme.h"
#include "sourceFlow/fluxScheme/limiter/Limiter.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief NS方程无粘项通量基类
 * 用于派生具体通量格式，不能直接实例化
 * 
 */
class  InviscidFluxScheme : public FlowFluxScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
    InviscidFluxScheme(Package::FlowPackage &data,
                       Limiter::Limiter *limiter_,
                       Flux::Flow::Precondition::Precondition *precondition_);

    /**
     * @brief 析构函数
     * 
     */
    virtual ~InviscidFluxScheme();

    /**
     * @brief 对流项平均残差累加
     * 纯虚函数，在具体格式中实现
     * 
     */
    virtual void AddAverageResidual() = 0;

    /**
     * @brief 对流项耗散残差累加
     * 纯虚函数，在具体格式中实现
     * 
     */
    virtual void AddDissipationResidual() = 0;

    /**
     * @brief 计算面通量
     * 纯虚函数，在具体格式中实现
     * 
     * @param[in] face 当前面
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
    virtual NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue) = 0;

    /**
     * @brief 计算对流项谱半径
     * 
     */
    void CalculateSpectralRadius();

    /**
     * @brief 根据精度，重构内部面的左右值
     * 
     * @param[in] faceID 面编号
     */
    NSFaceValue GetFaceLeftRightValue(const int &faceID);

    /**
     * @brief 计算面Jacobian矩阵
     * 
     * @param[in] U 速度
     * @param[in] E 能量
     * @param[in] normal 面法矢系数
     * @param[in, out] jacobian0 Jacobian矩阵
     */
    void GetInviscidProjJac(const Vector &U, const Scalar &E, const Vector &normal, const Scalar &scale, Matrix &jacobian0);

    /**
     * @brief 采用一阶格式计算平均面通量
     * 
     * @param[in] faceID 当前面编号
     * @param[out] faceFlux 当前面的通量
     */
    virtual void CalculateFaceFluxAverage(const int &faceID, NSFaceFlux &faceFlux) = 0;

protected:
    /**
     * @brief 计算面的谱半径，先平均后计算，不考虑谱半径
     * 
     * @param[in] faceID 面编号
     * @return const Scalar
     */
    const Scalar CalculateSpectralRadiusAverageNonPre(const int &faceID);

    /**
     * @brief 计算面的谱半径，先平均后计算，考虑谱半径
     * 
     * @param[in] faceID 面编号
     * @return const Scalar
     */
    const Scalar CalculateSpectralRadiusAveragePre(const int &faceID);

    /**
     * @brief 获取左右面的基本物理量
     * 
     * @param[in] faceID 面编号
     * @param[in, out] faceValue 该面的左右值
     */
    void GetFaceValueFirstOrder(const int &faceID, NSFaceValue &faceValue);

    /**
     * @brief 获取左右面重构后的基本物理量
     * 
     * @param[in] faceID 面编号
     * @param[in, out] faceValue 该面的左右值
     */
    void GetFaceValueSecondOrder(const int &faceID, NSFaceValue &faceValue);

protected:
    Limiter::Limiter *limiter; ///< 通量限制器指针
    Flux::Flow::Precondition::Precondition *precondition; ///< 预处理

    ElementField<Vector> *rhoGradient; ///< 密度梯度
    ElementField<Tensor> *UGradient; ///< 速度梯度
    ElementField<Vector> *pGradient; ///< 压强梯度
#if defined(_EnableMultiSpecies_)
	const std::vector<ElementField<Vector> *> &massFractionGradient; ///< 组分梯度
#endif
    
    ElementField<Scalar> &lambdaConvective; ///< 对流谱半径
    
	/// 计算面谱半径的函数指针，先平均后计算
	const Scalar (InviscidFluxScheme::*CalculateSpectralRadiusAverage)(const int &faceID);
    
	/// 获取左右面基本物理量的函数指针
	void (InviscidFluxScheme::*GetFaceValue)(const int &faceID, NSFaceValue &faceValue);
    
    const Scalar &gamma; ///< 比热比\gamma
    const Scalar &gamma1; ///< \gamma - 1.0   
    const Scalar gamma1Inv; ///< 1.0 / (\gamma - 1.0); 
    const Scalar &Cp; ///< 定压比热容

    const bool &entropyFixFlag; ///< 迎风格式特征值熵修正标识
    
};

} // namespace Inviscid
} // namespace Flow
} // namespace Flux
#endif 