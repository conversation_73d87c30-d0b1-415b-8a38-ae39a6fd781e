///////////////////////////////////////////////////////////////////////////////
/// \file range.hpp
/// Proto callables for things found in the boost range library
//
//  Copyright 2012 Eric <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_PROTO_FUNCTIONAL_RANGE_HPP_EAN_27_08_2012
#define BOOST_PROTO_FUNCTIONAL_RANGE_HPP_EAN_27_08_2012

#include <boost/proto/functional/range/begin.hpp>
#include <boost/proto/functional/range/empty.hpp>
#include <boost/proto/functional/range/end.hpp>
#include <boost/proto/functional/range/rbegin.hpp>
#include <boost/proto/functional/range/rend.hpp>
#include <boost/proto/functional/range/size.hpp>

#endif
