////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlutterSolver.cpp
//! <AUTHOR>
//! @brief 颤振解算器主流程.
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2025-07-26 郭承鹏
//     说明：添加注释
//------------------------------------------------------------------------------
//#include "feilian-specialmodule/flutter/solver/FlutterSolver.h"
#include "feilian-specialmodule/flutter/solver/FlutterSolver.h"


FlutterSolver::FlutterSolver(SubMesh *subMesh_, Configure::Flow::FlowConfigure &flowConfigure_)
    : subMesh(subMesh_), flowConfigure(flowConfigure_)
{

}

FlutterSolver::~FlutterSolver()
{
}

void FlutterSolver::Initialize()
{
    MPIBarrier();

    if (GetMPIRank() == 0) Print("\nInitialize flutter analysis ...");

    // 读全局网格
    SubMesh *globalMesh = new SubMesh();

    if (GetMPIRank() == 0)
    {
        std::string temp;

        const auto &meshDim = flowConfigure.GetMeshParameters().dimension;
        const auto &meshPath = flowConfigure.GetStaticAero().CFDParameter.meshPath[0];
        const auto &fileName = flowConfigure.GetStaticAero().CFDParameter.fileName[0];
        const auto &meshType = flowConfigure.GetStaticAero().CFDParameter.meshType[0];
        const auto &meshTransform = flowConfigure.GetStaticAero().CFDParameter.meshTransform;

        MeshConvertManager meshConvertManager(meshPath+fileName, meshType, meshDim, globalMesh, meshTransform);
        meshConvertManager.ReadMesh(true);
        meshConvertManager.BuildTopology();
    }

    //**创建流场解算器对象及初始化
    outerLoop = new OuterLoop(subMesh, flowConfigure);
    outerLoop->Initialize();
    const Package::FlowPackage *data = outerLoop->GetFlowPackage();

    //**创建Flutter方法类
    flutter_Method = new FlutterMethod(data);

    //**将颤振方法设置给外循环求解器
    outerLoop->SetFlutterMethod(flutter_Method);

}

void FlutterSolver::Process()
{
    if (GetMPIRank() == 0) Print("\nBegin to solve flutter analysis ...");
    SystemTime timeTotal;
    timeTotal.UpdateTime();

    // 直接调用外循环求解器，颤振分析已经集成在其中
    outerLoop->Solve();

    if (GetMPIRank() == 0) Print("颤振分析计算总耗时：" + ToString(timeTotal.GetElapsedTime()));
}


