#include "meshProcess/meshDeform/cgnsTocgns.h"
cgnsTocgns::cgnsTocgns(const std::string &SourceFile_,const std::string &Newfile_)
:SourceFile(SourceFile_),Newfile(Newfile_)
{
	processorID = GetMPIRank();
	npart = GetMPISize();
}
cgnsTocgns::~cgnsTocgns()
{
}


void cgnsTocgns::cgns2cgns(std::vector<Node> &v_globalNode) 
{

	int fileID,baseID=1,cellDim;

	if (cg_open(Newfile.c_str(), CG_MODE_MODIFY, &fileID)) cg_error_exit();
	
	char basename [33];
	int physDim;
	if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim)) cg_error_exit();

	cgsize_t isize[3][1];
	char zonename[33];
	int zoneID=1;
	if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0])) cg_error_exit();
	int nodeSize = isize[0][0];
  std::fstream file;
	 Scalar temp; 

	cgsize_t irmin = 1, irmax = nodeSize;
	Scalar *coor = new Scalar[nodeSize];
	Scalar *coor_x = new Scalar[nodeSize];
	Scalar *coor_y = new Scalar[nodeSize];
	Scalar *coor_z = new Scalar[nodeSize];
	char coordname_x[33],coordname_y[33],coordname_z[33];
	DataType_t dataType;
	int coodID;

	if (cg_coord_info(fileID, baseID, zoneID, 1, &dataType, coordname_x)) cg_error_exit();
	if (cg_coord_read(fileID, baseID, zoneID, coordname_x, dataType, &irmin, &irmax, coor)) cg_error_exit();

    for(int j = 0; j < v_globalNode.size(); j++)
    {         	 
     	coor_x[j] = v_globalNode[j].X();
     	coor_y[j] = v_globalNode[j].Y();
     	if (physDim == 3) {coor_z[j] = v_globalNode[j].Z(); }          	  
    } 	
	if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_x, coor_x,&coodID)) cg_error_exit();
		  

	if (cg_coord_info(fileID, baseID, zoneID, 2, &dataType, coordname_y)) cg_error_exit();
	if (cg_coord_read(fileID, baseID, zoneID, coordname_y, dataType, &irmin, &irmax, coor)) cg_error_exit();
  if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_y, coor_y,&coodID)) cg_error_exit();
        		    	
        	
	
	if (physDim == 3)
	{
		if (cg_coord_info(fileID, baseID, zoneID, 3, &dataType, coordname_z)) cg_error_exit();
		if (cg_coord_read(fileID, baseID, zoneID, coordname_z, dataType, &irmin, &irmax, coor)) cg_error_exit();
    if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_z, coor_z,&coodID)) cg_error_exit();    	       		 		
	}
	if (cg_close(fileID)) cg_error_exit();
}


int cgnsTocgns::CopyFile() 
{

  	std::ifstream in;
	  std::ofstream out;
	try{
		in.open(SourceFile,std::ios::binary);
		if(in.fail()){
			std::cout<<"Fail to open the source file;"<<std::endl;
			in.close();
			out.close();
			return 0;
		}
		out.open(Newfile,std::ios::binary);
		if(out.fail()){
			std::cout<<"Fail to open the new file;"<<std::endl;
                        in.close();
                        out.close();
                        return 0;
		}
		out<<in.rdbuf();
		out.close();
		in.close();
		return 1;
	}
	catch(std::exception e){
	}
}


std::vector<Node> cgnsTocgns::cgns2cgns_send() 
{

	int fileID,baseID=1,cellDim;
	if (cg_open(SourceFile.c_str(), CG_MODE_MODIFY, &fileID)) cg_error_exit();
	
	char basename [33];
	int physDim;
	if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim)) cg_error_exit();

	cgsize_t isize[3][1];
	char zonename[33];
	int zoneID=1;
	if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0])) cg_error_exit();
	int nodeSize = isize[0][0];
  std::fstream file;
	 Scalar temp; 

	cgsize_t irmin = 1, irmax = nodeSize;
	Scalar *coor_x = new Scalar[nodeSize];
	Scalar *coor_y = new Scalar[nodeSize];
	Scalar *coor_z = new Scalar[nodeSize];
	char coordname_x[33],coordname_y[33],coordname_z[33];
	DataType_t dataType;
	int coodID;
	
	if (cg_coord_info(fileID, baseID, zoneID, 1, &dataType, coordname_x)) cg_error_exit();
	if (cg_coord_read(fileID, baseID, zoneID, coordname_x, dataType, &irmin, &irmax, coor_x)) cg_error_exit();


	if (cg_coord_info(fileID, baseID, zoneID, 2, &dataType, coordname_y)) cg_error_exit();
	if (cg_coord_read(fileID, baseID, zoneID, coordname_y, dataType, &irmin, &irmax, coor_y)) cg_error_exit();

        	
	if (cg_coord_info(fileID, baseID, zoneID, 3, &dataType, coordname_z)) cg_error_exit();
	if (cg_coord_read(fileID, baseID, zoneID, coordname_z, dataType, &irmin, &irmax, coor_z)) cg_error_exit();  		 		
	
	
	if (cg_close(fileID)) cg_error_exit();

    std::vector<Node> v_globalNode;
	v_globalNode.resize(nodeSize);
	for(int j = 0; j < nodeSize; j++)
    {         	 
     	v_globalNode[j].SetX(coor_x[j]);
     	v_globalNode[j].SetY(coor_y[j]);
     	v_globalNode[j].SetZ(coor_z[j]);          	  
    } 
	return v_globalNode;
}



void cgnsTocgns::RebuildOriginalGlobalNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode, Scalar &R, Scalar &scale)
{


  std::vector<Node> v_globalNode = this->cgns2cgns_send();
   
  std::vector<Node> v_Node;
  int number;
  const int n = v_globalNode.size();
  if (processorID == 0)
  {  
    number = n / npart;
    int n_mod = n % npart;
    Print("number:" + ToString(number) + " n " + ToString(n) + " n_mod " + ToString(n_mod));
    v_Node.resize(number);
    for (int i = 0; i < number; i++)
      v_Node[i] = v_globalNode[i]*scale;

    for (int i = 1; i < npart; i++)
    {
      std::vector<Node> v_Node_temp;
      if (i != npart - 1)
      {       
        v_Node_temp.resize(number);
        for (int j = number * i; j < number * (i + 1); j++)
          v_Node_temp[j - number * i] = v_globalNode[j]*scale;
      }
      else
      {
        v_Node_temp.resize(n_mod + number);
        for (int j = number * i; j < n; j++)
          v_Node_temp[j - number * i] = v_globalNode[j]*scale;
      }

      mpi_world.send(i, 7, v_Node_temp);
      v_Node_temp.clear();
    }
  }
  else
  {
    mpi_world.recv(0, 7, v_Node);
  }
mpi_world.barrier(); 

  std::vector<Node> v_RebuildNode = this->RebuildNode(v_weight, v_wallNode, v_Node,R);
  if (processorID == 0) Print("\tRebuildNode各进程求解完成");
 
  if (processorID == 0)
  {
	std::vector<Node> v_globalNode_deform;
    v_globalNode_deform.resize(n);
    for (int i = 0; i < v_RebuildNode.size(); i++)
      v_globalNode_deform[i] = v_RebuildNode[i];

    for (int i = 1; i < npart; i++)
    {
      std::vector<Node> v_tempdNode;
      mpi_world.recv(i, 8, v_tempdNode);
      for (int j = 0; j < v_tempdNode.size(); j++)
        v_globalNode_deform[j + number * i] = v_tempdNode[j];    
    }	
    Print("\t全局网格合并完成");

    this->cgns2cgns(v_globalNode_deform);
	Print("\t全局网格输出完成");
  }
  else
  {
    mpi_world.send(0, 8, v_RebuildNode);
  }

}



std::vector<Vector> cgnsTocgns::RebuildNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode,std::vector<Vector> v_Node, Scalar &R_deform)
{
	std::vector<Node> Delta;

	Node Delta_xyz;
	Scalar yita;
	//Scalar temp;
	std::fstream file;
	int n = v_Node.size();
	int n1 = v_wallNode.size();
  Delta.resize(n1);

  std::vector<Node> v_ReturnNode(n);

  std::vector<Scalar> fai;
  int j, k;

    for (int i = 0; i < n; i++)
    {
		  Delta_xyz =Vector0;
		  yita = 0.0;
		  const Node nodeTemp1 = v_Node[i]; //*支撑半径;//*scale;		
		  Node node_deform =	Vector0;
		  fai.resize(n1);
		  for ( j = 0; j < v_wallNode.size(); j++)
		  {
			  const Node nodeTemp2 = v_wallNode[j];  
                                  
			  yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2)
				  + pow((nodeTemp2.Y() - nodeTemp1.Y()), 2)
			  	+ pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R_deform;

			  if (yita > 1.0)
			  {
				  fai[j] = 0.0;				
			  }
			  else
			  {
				  fai[j] = pow((1 - yita), 4)*(4 * yita + 1);		
			  }

		  }

		  for ( k = 0; k < v_wallNode.size(); k++)
		  {
			  Delta_xyz.SetX( Delta_xyz.X() + v_weight[k].X()*fai[k] );
			  Delta_xyz.SetY( Delta_xyz.Y() + v_weight[k].Y()*fai[k] );
			  Delta_xyz.SetZ( Delta_xyz.Z() + v_weight[k].Z()*fai[k] );
		  }
		  
		  node_deform.SetX(nodeTemp1.X() + Delta_xyz.X());
		  node_deform.SetY(nodeTemp1.Y() + Delta_xyz.Y());
		  node_deform.SetZ(nodeTemp1.Z() + Delta_xyz.Z());

      fai.clear();    
      v_ReturnNode[i]  = node_deform;
    
    }	
      return v_ReturnNode;
}

