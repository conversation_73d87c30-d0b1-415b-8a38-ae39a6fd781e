﻿#include "basic/common/Matrix.h"
#include <cmath>
#include <iostream>

Matrix::Matrix(const int row, const int col)
	:rowSize(row), colSize(col)
{
	matrixValue.resize(rowSize * colSize);
}

Matrix::~Matrix()
{
}

void Matrix::Resize(const int &row, const int &col)
{
	rowSize = row;
	colSize = col;
	matrixValue.resize(rowSize * colSize);
}

void Matrix::SetIdentity()
{
	const size_t valueSize = rowSize * colSize;
	for (size_t i = 0; i < valueSize; ++i) matrixValue[i] = 0.0;

	for (int i = 0; i < rowSize; i++) (*this)(i, i) = 1.0;
}

void Matrix::SetZero()
{
	const size_t valueSize = rowSize * colSize;
	for (size_t i = 0; i < valueSize; ++i) matrixValue[i] = 0.0;
}

Matrix Matrix::Inverse()
{
	if (rowSize != colSize) FatalError("Matrix::Inverse: matrix must be square!");

	const int &matSize = rowSize;

	// 构造增广矩阵
	Matrix extendedMatrix(matSize, 2 * matSize);
	for (int i = 0; i < matSize; i++)
	{
		for (int j = 0; j < matSize; j++)
		{
			extendedMatrix(i, j) = (*this)(i,j);
			extendedMatrix(i, j + matSize) = (i == j) ? 1 : 0;
		}
	}

	// 消元
	for (int i = 0; i < matSize; i++)
	{
		// 主元为零，交换行
		if (extendedMatrix(i, i) == 0)
		{
			int j;
			for (j = i + 1; j < matSize; j++)
			{
				if (extendedMatrix(j, i) != 0)
				{
					Scalar temp;
					for (int k = 0; k < colSize; ++k)
					{
						temp = extendedMatrix(i, k);
						extendedMatrix(i, k) = extendedMatrix(j, k);
						extendedMatrix(j, k) = temp;
					}
					break;
				}
			}
			if (j == matSize) FatalError("Matrix::Inverse: Inverse does not exist!");
		}

		const Scalar temp = extendedMatrix(i, i);
		for (int j = 0; j < 2 * matSize; j++) extendedMatrix(i, j) /= temp;

		for (int j = 0; j < matSize; j++)
		{
			if (j == i) continue;

			const Scalar temp = extendedMatrix(j, i);
			for (int k = 0; k < 2 * matSize; k++)
				extendedMatrix(j, k) -= temp * extendedMatrix(i, k);
		}
	}

	// 提取逆矩阵
	Matrix inverse(matSize, matSize);
	for (int i = 0; i < matSize; i++)
	{
		for (int j = 0; j < matSize; j++)
			inverse(i, j) = extendedMatrix(i, j + matSize);
	}

	return inverse;
}

Matrix Matrix::DiagInverse()
{
    Matrix inverse(this->rowSize, this->colSize);

    for (unsigned i = 0; i < this->rowSize; ++i)
	{
	    inverse.matrixValue[i * colSize + i] = 1.0 / this->matrixValue[i * colSize + i];
	}

	return inverse;
}

Scalar Matrix::CalculateDeterminant()
{
	if (rowSize != colSize) FatalError("CalculateDeterminant: input is wrong!");

	const int matSize = rowSize;

	if (matSize == 1)
	{
		return this->matrixValue[0];
	}
	else
	{
		Scalar det = 0;
		for (int i = 0; i < matSize; i++)
		{
			Matrix sub_matrix(matSize - 1, matSize - 1);
			for (int j = 1; j < matSize; j++)
			{
				int index = 0;
				for (int k = 0; k < matSize; k++)
				{
					if (k != i) sub_matrix.SetValue(j - 1, index++, this->GetValue(j, k));
				}
			}
			det += pow(-1, i) * this->GetValue(0, i) * sub_matrix.CalculateDeterminant();
		}

		return det;
	}
}

std::ostream& operator << (std::ostream& os, const Matrix& matrix)
{
	for (int i = 0; i < matrix.SizeRow(); ++i)
	{
		os << "| ";
		for (int j = 0; j < matrix.SizeCol(); ++j) os << "\t" << matrix.GetValue(i, j);
		os << "| \n";
	}
	
	return os;
}

Matrix Matrix::operator=(const Matrix &rhs)
{
	if (this->rowSize != rhs.rowSize || this->colSize != rhs.colSize)
	{
		this->Resize(rhs.rowSize, rhs.colSize);
	}

	const size_t valueSize = rowSize * colSize;
	for (int i = 0; i < valueSize; ++i) matrixValue[i] = rhs.GetValue(i);

	return *this;
}

Matrix Matrix::operator*(const Matrix &rhs)
{
	const int rowSize2 = rhs.SizeRow();
	const int colSize2 = rhs.SizeCol();

	if (colSize != rowSize2) FatalError("Matrix::operator*(Matrix): input is wrong!");

	Matrix result(rowSize, colSize2);
	result.SetZero();
	for (int i = 0; i < rowSize; ++i)
	{
		for (int j = 0; j < colSize2; ++j)
		{
			for (int k = 0; k < colSize; ++k)
			{
				result(i, j) += (*this)(i, k) * rhs.GetValue(k, j);
			}
		}
	}

	return result;
}

Matrix Matrix::operator*(const Scalar &value)
{
	Matrix result(rowSize, colSize);

	const size_t valueSize = rowSize * colSize;
	for (int i = 0; i < valueSize; ++i) result(i) = matrixValue[i] * value;

	return result;
}

Matrix operator*(const Scalar value, const Matrix &rhs)
{	
	int rowSize1 = rhs.SizeRow();
	int colSize1 = rhs.SizeCol();
	Matrix result(rowSize1, colSize1);

	const size_t valueSize = rowSize1 * colSize1;
	for (int i = 0; i < valueSize; ++i) result(i) = rhs.GetValue(i) * value;

	return result;
}

Matrix Matrix::operator-() const
{
	Matrix result(rowSize, colSize);
	const size_t valueSize = rowSize * colSize;
	for (size_t i = 0; i < valueSize; ++i) result.matrixValue[i] = -this->matrixValue[i];
	return result;
}

Matrix Matrix::operator-(const Matrix &rhs)
{
	Matrix result = (*this);
	result -= rhs;
	return result;
}

Matrix Matrix::operator+(const Matrix &rhs)
{
	Matrix result = (*this);
	result += rhs;
	return result;
}

void Matrix::operator+=(const Matrix &rhs)
{
	const int rowSize2 = rhs.SizeRow();
	const int colSize2 = rhs.SizeCol();

	if (rowSize != rowSize2 || colSize != colSize2)
		FatalError("Matrix::operator+=(Matrix): input is wrong!");

	const size_t valueSize = rowSize * colSize;
	for (int i = 0; i < valueSize; ++i) matrixValue[i] += rhs.GetValue(i);
}

void Matrix::operator-=(const Matrix &rhs)
{
	const int rowSize2 = rhs.SizeRow();
	const int colSize2 = rhs.SizeCol();

	if (rowSize != rowSize2 || colSize != colSize2)
		FatalError("Matrix::operator-=(Matrix): input is wrong!");

	const size_t valueSize = rowSize * colSize;
	for (int i = 0; i < valueSize; ++i) matrixValue[i] -= rhs.GetValue(i);
}