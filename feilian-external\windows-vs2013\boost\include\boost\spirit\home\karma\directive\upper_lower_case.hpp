//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
//  Copyright (c) 2001-2011 <PERSON>
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(SPIRIT_UPPER_LOWER_CASE_JANUARY_19_2009_1142AM)
#define SPIRIT_UPPER_LOWER_CASE_JANUARY_19_2009_1142AM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/common_terminals.hpp>
#include <boost/spirit/home/<USER>/modify.hpp>
#include <boost/spirit/home/<USER>/domain.hpp>
#include <boost/spirit/home/<USER>/meta_compiler.hpp>

namespace boost { namespace spirit
{
    ///////////////////////////////////////////////////////////////////////////
    // Enablers
    ///////////////////////////////////////////////////////////////////////////
    template <typename CharEncoding>
    struct use_directive<
        karma::domain, tag::char_code<tag::upper, CharEncoding> > // enables upper
      : mpl::true_ {};

    template <typename CharEncoding>
    struct use_directive<
        karma::domain, tag::char_code<tag::lower, CharEncoding> > // enables lower
      : mpl::true_ {};

    ///////////////////////////////////////////////////////////////////////////
    template <typename CharEncoding>
    struct is_modifier_directive<karma::domain
        , tag::char_code<tag::upper, CharEncoding> >
      : mpl::true_ {};

    template <typename CharEncoding>
    struct is_modifier_directive<karma::domain
        , tag::char_code<tag::lower, CharEncoding> >
      : mpl::true_ {};

    ///////////////////////////////////////////////////////////////////////////
    // Don't add tag::upper or tag::lower if there is already one of those in
    // the modifier list
    template <typename Current, typename CharEncoding>
    struct compound_modifier<
            Current
          , tag::char_code<tag::upper, CharEncoding>
          , typename enable_if<
                has_modifier<Current, tag::char_code<tag::lower, CharEncoding> > 
            >::type
          >
      : Current
    {
        compound_modifier()
          : Current() {}

        compound_modifier(Current const& current, 
                tag::char_code<tag::upper, CharEncoding> const&)
          : Current(current) {}
    };

    template <typename Current, typename CharEncoding>
    struct compound_modifier<
            Current
          , tag::char_code<tag::lower, CharEncoding>
          , typename enable_if<
                has_modifier<Current, tag::char_code<tag::upper, CharEncoding> > 
            >::type
          >
      : Current
    {
        compound_modifier()
          : Current() {}

        compound_modifier(Current const& current, 
                tag::char_code<tag::lower, CharEncoding> const&)
          : Current(current) {}
    };
}}

#endif
