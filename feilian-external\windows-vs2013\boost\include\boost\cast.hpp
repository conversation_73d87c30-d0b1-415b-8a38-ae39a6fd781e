//  boost cast.hpp header file
//
//  (C) Copyright <PERSON> 2014.
//
//  Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
//  See http://www.boost.org/libs/conversion for Documentation.

// This is a DEPRECATED header file!
// Use <boost/polymorphic_cast.hpp> or <boost/numeric/conversion/cast.hpp> instead

#ifndef BOOST_CAST_HPP
#define BOOST_CAST_HPP

# include <boost/polymorphic_cast.hpp>
# include <boost/numeric/conversion/cast.hpp>

#endif  // BOOST_CAST_HPP
