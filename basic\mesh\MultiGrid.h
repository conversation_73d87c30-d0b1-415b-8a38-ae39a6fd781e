﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MultiGrid.h
//! <AUTHOR>
//! @brief 基本网格类（多重网格类）
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_MultiGrid_
#define _basic_mesh_MultiGrid_

#include "basic/mesh/Mesh.h"

// 用于友元类的声明（暂时）
class AgglomerateManager;
class DecomposeManager;
class MeshProcess;
class SubMesh;
class DlgMesh;

/**
 * @class MultiGrid
 * @brief 多重网格类，继承自基础网格类Mesh
 * 
 * 该类实现了多级网格的管理功能，包括粗网格与细网格之间的映射关系、
 * 网格数据的读写操作以及序列化支持(当启用并行MPI时)
 */
class MultiGrid : public Mesh
{
public:
    /**
     * @brief 默认构造函数
     */
    MultiGrid();

    /**
     * @brief 通过网格文件构造多重网格对象
     * @param MshFileName 网格文件名
     */
    explicit MultiGrid(const std::string& MshFileName);

    /**
     * @brief 获取粗网格单元对应的细网格单元ID
     * @param coarseID 粗网格单元ID
     * @param index 细网格单元索引
     * @return 对应的细网格单元ID
     */
    const int& GetFineID(const int& coarseID, const int& index) const 
    { 
        return this->v_elemMap[coarseID][index]; 
    }

    /**
     * @brief 获取粗网格单元对应的细网格单元数量
     * @param coarseID 粗网格单元ID
     * @return 细网格单元数量
     */
    const int GetFineIDSize(const int& coarseID) const 
    { 
        return static_cast<int>(this->v_elemMap[coarseID].size()); 
    }

    /**
     * @brief 获取细网格单元对应的粗网格单元ID对
     * @param fineID 细网格单元ID
     * @return 包含(细网格ID, 粗网格ID)的pair
     */
    const std::pair<int, int>& GetFineToCoarseIDPair(const int& fineID) const 
    { 
        return this->v_fineToCoarseIDPair[fineID]; 
    }

    /**
     * @brief 获取细网格到粗网格映射表的大小
     * @return 映射表大小
     */
    const int GetFineToCoarseIDPairSize() const 
    { 
        return static_cast<int>(this->v_fineToCoarseIDPair.size()); 
    }
    
    /**
     * @brief 写入多重网格数据到文件
     * @param file 文件流
     * @param level 当前网格级别
     * @param nLevel 总网格级别数
     * @param binary 是否以二进制格式写入(默认为true)
     */
    void WriteMultiGrid(std::fstream& file, const int& level, const int& nLevel, 
                       const bool& binary = true) const;

    /**
     * @brief 从文件读取多重网格数据
     * @param file 文件流
     * @param level 当前网格级别
     * @param nLevel 总网格级别数
     * @param binary 是否从二进制格式读取(默认为true)
     */
    void ReadMultiGrid(std::fstream& file, const int& level, const int& nLevel, 
                      const bool& binary = true);

    /**
     * @brief 写入多重网格的幽灵单元数据
     * @param file 文件流
     * @param binary 是否以二进制格式写入(默认为true)
     */
    void WriteMultiGridGhostElement(std::fstream& file, const bool& binary = true) const;

    /**
     * @brief 读取多重网格的幽灵单元数据
     * @param file 文件流
     * @param binary 是否从二进制格式读取(默认为true)
     */
    void ReadMultiGridGhostElement(std::fstream& file, const bool& binary = true);

    /**
     * @brief 清除网格数据
     */
    void ClearMesh();
    
    /**
     * @brief 计算细网格到粗网格的映射关系
     */
    void CalculatefineToCoarseMap();
    
#if defined(_BaseParallelMPI_)
public:
    /**
     * @brief 序列化函数(用于MPI并行)
     * @tparam Archive 序列化存档类型
     * @param ar 序列化存档对象
     * @param version 版本号
     */
    template<class Archive>
    void serialize(Archive& ar, const unsigned int version)
    {
        // 基类成员序列化
        ar & boost::serialization::base_object<Mesh>(*this);
        
        // 当前类成员序列化
        ar & v_elemMap;
        ar & v_fineToCoarseIDPair;
    }
#endif

protected:
    /// 粗网格到细网格的映射表，每个粗网格单元存储对应的细网格单元ID列表
    std::vector<std::vector<int>> v_elemMap;

    /// 细网格到粗网格的反向映射表，存储(细网格ID, 粗网格ID)对
    std::vector<std::pair<int, int>> v_fineToCoarseIDPair;

    // 友元类声明
    friend class AgglomerateManager;  ///< 聚合管理器
    friend class DecomposeManager;    ///< 分解管理器
    friend class MeshProcess;         ///< 网格处理器
    friend class SubMesh;             ///< 子网格类
    friend class DlgMesh;             ///< DLG网格类
};

#endif // _basic_mesh_MultiGrid_
