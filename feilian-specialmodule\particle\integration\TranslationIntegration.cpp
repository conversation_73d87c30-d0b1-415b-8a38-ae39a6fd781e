﻿#include "feilian-specialmodule/particle/integration/TranslationIntegration.h"

namespace Particle
{

TranslationIntegration::TranslationIntegration(const int &max_nPrtcl_, const int &numParticles_,
											   std::vector<Particle *> &particles_,
											   const Configure::Particle::WallContactType &wallContactType_,
                                               const Scalar &dt_,
                                               const Configure::Particle::MotionIntegrationScheme &Method_)
											   :numParticles(numParticles_), numParticlesMax(max_nPrtcl_), wallContactType(wallContactType_),
											   Int_Method(Method_), dt(dt_), particles(particles_)
{
	switch (this->Int_Method)
	{
	case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
		this->m_AB3AM4 = new AB3AM4();
		break;

	default:
		FatalError("平移运动不支持该积分方法！");
	}
}

void TranslationIntegration::Predict()
{
    switch (this->Int_Method)
    {
    case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
        for (int i = 0; i < this->numParticles; ++i)
        {
			if (this->particles[i] != nullptr)
			{
				auto &particle = this->particles[i];
				this->m_AB3AM4->Predict(this->dt, particle->position0, particle->linearVelocity0, particle->linearAcceleration0,
					particle->position, particle->linearVelocity);
            }
        }
		break;

    default:
        FatalError("平移运动不支持该积分方法！");
    }
}

void TranslationIntegration::Correct()
{
    switch (this->Int_Method)
    {
    case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
        for (int i = 0; i < this->numParticles; ++i)
        {
			if (this->particles[i] != nullptr)
			{
				auto &particle = this->particles[i];

				const bool flag = (particle->GetContactWallIndex() > -1) &&
					              (wallContactType == Configure::Particle::WallContactType::WCT_PLANE);

				this->m_AB3AM4->Correct(particle->linearAcceleration, this->dt, particle->position, particle->linearVelocity,
					particle->position0, particle->linearVelocity0, particle->linearAcceleration0, flag);
            }
        }
		break;

    default:
        FatalError("平移运动不支持该积分方法！");
    }
}

void TranslationIntegration::SetParticleNumber(const int &numParticleNew)
{
    switch (this->Int_Method)
    {
    case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
		for (int i = numParticles; i < numParticleNew; ++i)
		{
			if (this->particles[i] != nullptr)
			{
				this->particles[i]->linearVelocity0.resize(3, Vector0);
				this->particles[i]->linearAcceleration0.resize(3, Vector0);
				this->particles[i]->position0 = this->particles[i]->position;
				this->particles[i]->linearVelocity0[0] = this->particles[i]->linearVelocity;
			}
		}
		break;

    default:
        FatalError("平移运动不支持该积分方法！");
    }
    
    this->numParticles = numParticleNew;
}

} // namespace Particle