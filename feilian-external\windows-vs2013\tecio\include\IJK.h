 #pragma once
#include "basicTypes.h"
#include "TASSERT.h"
#include "LightweightVector.h"
namespace tecplot { namespace ___3933 { class ___1844 { private: static ___81 const BAD_VALUE = BAD_ANY_INDEX; union { ___81 m_iValue; ___81 m_numFENodes; }; union { ___81 m_jValue; ___81 m_numFECells; }; union { ___81 m_kValue; ___81 m_numFECorners; }; ___81 m_ijValue; public: ___1844(___81 ___1841, ___81 ___2113, ___81 ___2158) : m_iValue(___1841), m_jValue(___2113), m_kValue(___2158), m_ijValue(___1841 * ___2113) { } ___1844(___1844 const& ___1843) : m_iValue(___1843.m_iValue), m_jValue(___1843.m_jValue), m_kValue(___1843.m_kValue), m_ijValue(___1843.m_ijValue) { } ___1844() : m_iValue(BAD_VALUE), m_jValue(BAD_VALUE), m_kValue(BAD_VALUE) {} bool ___2067(void) const { REQUIRE(IMPLICATION(m_iValue == BAD_VALUE, *this==BAD_VALUE)); REQUIRE(IMPLICATION(m_iValue != BAD_VALUE, m_iValue*m_jValue == m_ijValue)); return m_iValue != BAD_VALUE; } void invalidate(void) { m_iValue = m_jValue = m_kValue = m_ijValue = BAD_VALUE; } ___81 i(void) const { return m_iValue; } ___81 ___2105(void) const { return m_jValue; } ___81 ___2134(void) const { return m_kValue; } void setI(___81 ___1841) { m_iValue   = ___1841; m_ijValue = m_jValue*m_iValue; } void setJ(___81 ___2113) { m_jValue   = ___2113; m_ijValue = m_jValue*m_iValue; } void ___3497(___81 ___2158) { m_kValue = ___2158; } ___465 ___1668(void) const { REQUIRE(m_numFECells <= MAX_NUM_CELLS); return ___465(m_numFECells); } ___682 ___1669(void) const { REQUIRE(m_numFECorners <= MAX_NUM_CELL_CORNERS); return ___682(m_numFECorners); } ___2718 ___1670(void) const { REQUIRE(m_numFENodes <= MAX_NUM_NODES); return ___2718(m_numFENodes); } void setFENumCells(___81 ___2781) { m_numFECells = ___2781; } void ___3485(___682 ___2789) { m_numFECorners = ___2789; } void setFENumNodes(___81 ___2821) { m_numFENodes = ___2821; } void setIJK(___1844 const& ___1843) { REQUIRE(___1843>=0); m_iValue=___1843.i(); m_jValue=___1843.___2105(); m_kValue=___1843.___2134(); } void operator=(___81 const ___4298) { m_iValue=___4298; m_jValue=___4298; m_kValue=___4298; m_ijValue=___4298*___4298; } bool operator==(___81 const ___4298) const { return m_iValue==___4298 && m_jValue==___4298 && m_kValue==___4298; } bool operator==(___1844 const& ___1843) const { return m_iValue==___1843.m_iValue && m_jValue==___1843.m_jValue && m_kValue==___1843.m_kValue; } bool operator!=(___81 const ___4298) const { return m_iValue!=___4298 || m_jValue!=___4298 || m_kValue!=___4298; } bool operator!=(___1844 const& ___1843) const { return m_iValue!=___1843.m_iValue || m_jValue!=___1843.m_jValue || m_kValue!=___1843.m_kValue; } bool operator>(___81 const ___4298) const { return m_iValue>___4298 && m_jValue>___4298 && m_kValue>___4298; } bool operator>(___1844 const& ___1843) const { return m_iValue>___1843.m_iValue && m_jValue>___1843.m_jValue && m_kValue>___1843.m_kValue; } bool operator>=(___81 const ___4298) const { return m_iValue>=___4298 && m_jValue>=___4298 && m_kValue>=___4298; } bool operator>=(___1844 const& ___1843) const { return m_iValue>=___1843.m_iValue && m_jValue>=___1843.m_jValue && m_kValue>=___1843.m_kValue; } bool operator<(___81 const ___4298) const { return m_iValue<___4298 && m_jValue<___4298 && m_kValue<___4298; } bool operator<(___1844 const& ___1843) const { return m_iValue<___1843.m_iValue && m_jValue<___1843.m_jValue && m_kValue<___1843.m_kValue; } bool operator<=(___81 const ___4298) const { return m_iValue<=___4298 && m_jValue<=___4298 && m_kValue<=___4298; } bool operator<=(___1844 const& ___1843) const { return m_iValue<=___1843.m_iValue && m_jValue<=___1843.m_jValue && m_kValue<=___1843.m_kValue; } ___1844 operator+(___81 const ___4298) const { return ___1844(m_iValue+___4298, m_jValue+___4298, m_kValue+___4298); } ___1844 operator+(___1844 const& ___1843) const { return ___1844(m_iValue+___1843.m_iValue, m_jValue+___1843.m_jValue, m_kValue+___1843.m_kValue); } ___1844 operator-(___81 const ___4298) const { ___1844 ___3358(m_iValue-___4298, m_jValue-___4298, m_kValue-___4298); ENSURE(___3358<=*this); return ___3358; } ___1844 operator-(___1844 const& ___1843) const { ___1844 ___3358(m_iValue-___1843.m_iValue, m_jValue-___1843.m_jValue, m_kValue-___1843.m_kValue); ENSURE(___3358<=*this); return ___3358; } ___1844 operator*(___81 const ___4298) const { return ___1844(m_iValue*___4298, m_jValue*___4298, m_kValue*___4298); } ___1844 operator*(___1844 const& ___1843) const { return ___1844(m_iValue*___1843.m_iValue, m_jValue*___1843.m_jValue, m_kValue*___1843.m_kValue); } ___1844 operator/(___81 const ___4298) const { return ___1844(m_iValue/___4298, m_jValue/___4298, m_kValue/___4298); } ___1844 operator/(___1844 const& ___1843) const { return ___1844(m_iValue/___1843.m_iValue, m_jValue/___1843.m_jValue, m_kValue/___1843.m_kValue); } ___1844 operator%(___81 const ___4298) const { return ___1844(m_iValue%___4298, m_jValue%___4298, m_kValue%___4298); } ___1844 operator%(___1844 const& ___1843) const { return ___1844(m_iValue%___1843.m_iValue, m_jValue%___1843.m_jValue, m_kValue%___1843.m_kValue); } ___1844& operator+=(___1844 const& ___1843) { m_iValue += ___1843.m_iValue; m_jValue += ___1843.m_jValue; m_kValue += ___1843.m_kValue; m_ijValue = m_iValue*m_jValue; return *this; } ___1844& operator-=(___1844 const& ___1843) { m_iValue -= ___1843.m_iValue; m_jValue -= ___1843.m_jValue; m_kValue -= ___1843.m_kValue; m_ijValue = m_iValue*m_jValue; return *this; } ___1844 minOp(___81 const ___4298) const { return ___1844(std::min(m_iValue, ___4298), std::min(m_jValue, ___4298), std::min(m_kValue, ___4298)); }
___1844 minOp(___1844 const& ___1843) const { return ___1844(std::min(m_iValue, ___1843.m_iValue), std::min(m_jValue, ___1843.m_jValue), std::min(m_kValue, ___1843.m_kValue)); } ___1844 maxOp(___81 const ___4298) const { return ___1844(std::max(m_iValue, ___4298), std::max(m_jValue, ___4298), std::max(m_kValue, ___4298)); } ___1844 maxOp(___1844 const& ___1843) const { return ___1844(std::max(m_iValue, ___1843.m_iValue), std::max(m_jValue, ___1843.m_jValue), std::max(m_kValue, ___1843.m_kValue)); } ___1844 getCellIJK(void) const { ___1844 const ___3358 = maxOp(2)-1; return ___3358; } ___81 ___461(void) const { return std::max(m_iValue,___81(2))-1; } ___81 ___466(void) const { return std::max(m_jValue,___81(2))-1; } ___81 ___467(void) const { return std::max(m_kValue,___81(2))-1; } ___81 blockSize(void) const { return m_ijValue*m_kValue; } ___2718 ___1768(void) const { return ___2718(blockSize()); } ___465 getNumContiguousCells(void) const { return ___465(getCellIJK().blockSize()); } ___465 getNumPaddedCells(void) const { return ___465(i()*___2105()*___467()); } inline ___81 iFromOffset(___81 itemOffset) const { REQUIRE(itemOffset < blockSize()); return itemOffset % m_iValue; } inline ___81 jFromOffset(___81 itemOffset) const { REQUIRE(itemOffset < blockSize()); return ( itemOffset / m_iValue ) % m_jValue; } inline ___81 kFromOffset(___81 itemOffset) const { REQUIRE(itemOffset < blockSize()); return ( itemOffset / m_iValue ) / m_jValue; } ___1844 ijkAtOffset(___81 itemOffset) const { REQUIRE(itemOffset<blockSize()); if (itemOffset < ___2182 && m_ijValue < ___2182) { int32_t const ijValue32 = static_cast<int32_t>(m_ijValue); int32_t const iValue32  = static_cast<int32_t>(m_iValue); int32_t itemOffset32    = static_cast<int32_t>(itemOffset); int32_t const kk32 = itemOffset32 / ijValue32; itemOffset32 -= kk32 * ijValue32; int32_t const jj32 = itemOffset32 / iValue32; ___81 const ___2113 = jj32; ___81 const ___1841 = itemOffset32 - jj32 * iValue32; ___81 const ___2158 = kk32; return ___1844(___1841, ___2113, ___2158); } else { ___81 const ___1841 = iFromOffset(itemOffset); ___81 const ___2113 = jFromOffset(itemOffset); ___81 const ___2158 = kFromOffset(itemOffset); return ___1844(___1841, ___2113, ___2158); } } ___81 offsetAtIJK(___1844 const& ___1843) const { REQUIRE(___1843>=0 && ___1843<*this); ___81 ___2865 = ___1843.m_iValue + m_iValue*(___1843.m_jValue + m_jValue*___1843.m_kValue); ENSURE(___2865<blockSize()); return ___2865; } }; inline bool operator!=(___81 const ___4298, ___1844 const& ___1843) { return ___1843 != ___4298; } inline bool operator==(___81 const ___4298, ___1844 const& ___1843) { return ___1843 == ___4298; } inline bool operator> (___81 const ___4298, ___1844 const& ___1843) { return ___1843 <  ___4298; } inline bool operator>=(___81 const ___4298, ___1844 const& ___1843) { return ___1843 <= ___4298; } inline bool operator< (___81 const ___4298, ___1844 const& ___1843) { return ___1843 >  ___4298; } inline bool operator<=(___81 const ___4298, ___1844 const& ___1843) { return ___1843 >= ___4298; } inline ___81 ___1877(___1844 const& ___1880, ___1844 const& ___1852) { REQUIRE(0<=___1880 && ___1880<=___1852); ___1844 ijkSpan = ___1852-___1880+1; return ijkSpan.___1768(); } inline ___81 maxComponent(___1844 const& ___1843) { ___81 maxComp = std::max(___1843.i(), ___1843.___2105()); if ( maxComp < ___1843.___2134() ) maxComp = ___1843.___2134(); return maxComp; }
 #ifdef LWV_SPECIALIZE_PLAIN_DATA_VECTORS
template<> class ___2240<___1844> : public ___3094<___1844> {};
 #endif
}}
