//  (C) Copyright Gennadiy Rozental 2001.
//  Distributed under the Boost Software License, Version 1.0.
//  (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/test for the library home page.
//
//  File        : $RCSfile$
//
//  Version     : $Revision$
//
//  Description : included (vs. linked ) version of Program Execution Monitor
// ***************************************************************************

#ifndef BOOST_INCLUDED_PRG_EXEC_MONITOR_HPP_071894GER
#define BOOST_INCLUDED_PRG_EXEC_MONITOR_HPP_071894GER

#include <boost/test/impl/execution_monitor.ipp>
#include <boost/test/impl/debug.ipp>
#include <boost/test/impl/cpp_main.ipp>

#define BOOST_TEST_INCLUDED
#include <boost/test/prg_exec_monitor.hpp>

#endif // BOOST_INCLUDED_PRG_EXEC_MONITOR_HPP_071894GER
