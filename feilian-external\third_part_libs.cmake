
# 根据编译环境系统选择外部库
if (CMAKE_SYSTEM_NAME MATCHES "Linux")
	if (CMAKE_CXX_COMPILER_ID MATCHES "GNU" AND CMAKE_CXX_COMPILER_VERSION MATCHES "7.3.0")
		set(EXTERNAL_PATH_CURRENT ${EXTERNAL_PATH}/linux-gcc7.3.0)
	elseif (CMAKE_CXX_COMPILER_ID MATCHES "GNU" AND CMAKE_CXX_COMPILER_VERSION MATCHES "4.4.7")
		set(EXTERNAL_PATH_CURRENT ${EXTERNAL_PATH}/linux-gcc4.4.7)
	elseif (CMAKE_CXX_COMPILER_ID MATCHES "GNU" AND CMAKE_CXX_COMPILER_VERSION MATCHES "4.8.5")
		set(EXTERNAL_PATH_CURRENT ${EXTERNAL_PATH}/linux-gcc4.8.5)
	elseif (CMAKE_CXX_COMPILER_ID MATCHES "GNU" AND CMAKE_CXX_COMPILER_VERSION MATCHES "8.5.0")
		set(EXTERNAL_PATH_CURRENT ${EXTERNAL_PATH}/linux-gcc8.5.0)
	elseif (CMAKE_CXX_COMPILER_ID MATCHES "Intel")
		string(SUBSTRING "20.2.1.20201112" 0 4 STRING0)
		string(SUBSTRING ${CMAKE_CXX_COMPILER_VERSION} 0 4 STRING1)
		if (STRING0 EQUAL STRING1)
			set(EXTERNAL_PATH_CURRENT ${EXTERNAL_PATH}/linux-intel2021)
		else ()
			message(STATUS "In current system CMAKE_CXX_COMPILER is ${CMAKE_CXX_COMPILER_ID}")
			message(STATUS "In current system CMAKE_CXX_COMPILER_VERSION is ${CMAKE_CXX_COMPILER_VERSION}")
			message(WARNING "External libraries is mismatched with current system environment!")
		endif ()
	else()
		message(WARNING "External libraries is mismatched with current system environment!")
		message(WARNING "In current system CMAKE_CXX_COMPILER is ${CMAKE_CXX_COMPILER_ID}")
		message(WARNING "In current system CMAKE_CXX_COMPILER_VERSION is ${CMAKE_CXX_COMPILER_VERSION}")
	endif ()
elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
	string(SUBSTRING "18.0.40629.0" 0 4 STRING0)
	string(SUBSTRING ${CMAKE_CXX_COMPILER_VERSION} 0 4 STRING1)
	if ((CMAKE_CXX_COMPILER_ID MATCHES "MSVC") AND (STRING0 EQUAL STRING1))
		set(EXTERNAL_PATH_CURRENT ${EXTERNAL_PATH}/windows-vs2013)
	else ()
		message(STATUS "In current system CMAKE_CXX_COMPILER is ${CMAKE_CXX_COMPILER_ID}")
		message(STATUS "In current system CMAKE_CXX_COMPILER_VERSION is ${CMAKE_CXX_COMPILER_VERSION}")
		message(WARNING "External libraries is mismatched with current system environment!")
	endif ()
else ()
    message(WARNING "Does not support current system platform ${CMAKE_SYSTEM_NAME}")
endif ()
message(STATUS "EXTERNAL_PATH_CURRENT: ${EXTERNAL_PATH_CURRENT}")

# MPI配置
if(ARI_ENABLE_MPI)
	if (CMAKE_SYSTEM_NAME MATCHES "Linux")
		# 优先使用OpenMPI
		set(OPENMPI_ROOT /usr/lib64/openmpi)
		set(OPENMPI_INCLUDE /usr/include/openmpi-x86_64)
		if (EXISTS ${OPENMPI_ROOT} AND EXISTS ${OPENMPI_INCLUDE})
			include_directories(${OPENMPI_INCLUDE})
			link_libraries(${OPENMPI_ROOT}/lib/libmpi.so)
			link_libraries(${OPENMPI_ROOT}/lib/libmpi_cxx.so)
			add_definitions(-D_BaseParallelMPI_)
			message(STATUS "OpenMPI is enable")
		else ()
			# 备选：使用Intel MPI
			set(MPI_ROOT /home/<USER>/intel/compilers_and_libraries_2020.4.304/linux/mpi/intel64)
			if (EXISTS ${MPI_ROOT})
				include_directories(${MPI_ROOT}/include)
				link_libraries(${MPI_ROOT}/lib/release/libmpi.so)
				add_definitions(-D_BaseParallelMPI_)
				message(STATUS "Intel MPI is enable")
			else ()
				find_package(MPI REQUIRED)
				if (MPI_FOUND)
					include_directories(${MPI_CXX_INCLUDE_PATH})
					link_libraries(${MPI_CXX_LIBRARIES})
					add_definitions(-D_BaseParallelMPI_)
					message(STATUS "MPI is enable")
				else ()
					message(WARNING "MPI was requested but support was not found")
				endif ()
			endif ()
		endif ()
	elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
		find_package(MPI REQUIRED)
		if (MPI_FOUND)
			include_directories(${MPI_CXX_INCLUDE_PATH})
			link_libraries(${MPI_CXX_LIBRARIES})
			add_definitions(-D_BaseParallelMPI_)
			message(STATUS "MPI is enable")
		else ()
			message(WARNING "MPI was requested but support was not found")
		endif ()
	endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
endif(ARI_ENABLE_MPI)

# OpenMP配置
if(ARI_ENABLE_OPENMP)
    FIND_PACKAGE(OpenMP REQUIRED)
    if(OPENMP_FOUND)
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
        set(CMAKE_SHARE_LINKER_FLAGS "${CMAKE_SHARE_LINKER_FLAGS} ${OpenMP_SHARE_LINKER_FLAGS}")
        add_definitions(-D_BaseParallelOpenMP_)
    else ()
        message(WARNING "OpenMP was requested but support was not found")
    endif(OPENMP_FOUND)
endif(ARI_ENABLE_OPENMP)

# Boost库配置
set(BOOST_ROOT ${EXTERNAL_PATH_CURRENT}/boost)
if (EXISTS ${BOOST_ROOT})
	include_directories(${BOOST_ROOT}/include)
	if (CMAKE_SYSTEM_NAME MATCHES "Linux")
		link_libraries(${BOOST_ROOT}/lib/libboost_serialization.a)
		if(ARI_ENABLE_MPI)
			link_libraries(${BOOST_ROOT}/lib/libboost_mpi.a)
			message(STATUS "boost is enable")
		endif(ARI_ENABLE_MPI)
	elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
		link_libraries(${BOOST_ROOT}/lib/libboost_serialization-vc120-mt-1_62.lib)
		if(ARI_ENABLE_MPI)
			link_libraries(${BOOST_ROOT}/lib/libboost_mpi-vc120-mt-1_62.lib)
		endif(ARI_ENABLE_MPI)
	endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
else ()
	message(WARNING "BOOST was requested but support was not found ${EXTERNAL_PATH_CURRENT}")
endif ()

# CGNS库配置
if(ARI_ENABLE_CGNS)
	set(CGNS_ROOT ${EXTERNAL_PATH_CURRENT}/cgns)
	if (EXISTS ${CGNS_ROOT})
		include_directories(${CGNS_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${CGNS_ROOT}/lib/libcgns.a)
			message(STATUS "cgns is enable")
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			link_libraries(${CGNS_ROOT}/lib/cgns.lib)
			message(STATUS "cgns is enable")
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnableCGNS_)
	elseif ()
		message(WARNING "CGNS was requested but support was not found")
	endif ()
endif(ARI_ENABLE_CGNS)

# HDF5库配置
if(ARI_ENABLE_HDF5)
	set(HDF5_ROOT ${EXTERNAL_PATH_CURRENT}/hdf5)
	if (EXISTS ${HDF5_ROOT})
		include_directories(${HDF5_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${HDF5_ROOT}/lib/libhdf5.so)
			message(STATUS "hdf5 is enable")
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			link_libraries(${HDF5_ROOT}/lib/hdf5.lib)
			message(STATUS "hdf5 is enable")
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnableHDF5_)

		# 设置可执行文件的安装路径
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			install(FILES ${HDF5_ROOT}/lib/libhdf5.so DESTINATION ${ARI_INSTALL_PREFIX}/bin)
			install(FILES ${HDF5_ROOT}/lib/libhdf5.so.8 DESTINATION ${ARI_INSTALL_PREFIX}/bin)
			install(FILES ${HDF5_ROOT}/lib/libhdf5.so.8.0.2 DESTINATION ${ARI_INSTALL_PREFIX}/bin)
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			install(FILES ${HDF5_ROOT}/bin/hdf5.dll DESTINATION ${ARI_INSTALL_PREFIX}/bin)
			install(FILES ${HDF5_ROOT}/bin/szip.dll DESTINATION ${ARI_INSTALL_PREFIX}/bin)
			install(FILES ${HDF5_ROOT}/bin/zlib.dll DESTINATION ${ARI_INSTALL_PREFIX}/bin)
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
	else ()
		message(WARNING "HDF5 was requested but support was not found")
	endif ()
endif(ARI_ENABLE_HDF5)

# Metis库配置
if(ARI_ENABLE_METIS)
	set(METIS_ROOT ${EXTERNAL_PATH_CURRENT}/metis)
	if (EXISTS ${METIS_ROOT})
		include_directories(${METIS_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${METIS_ROOT}/lib/libmetis.a)
			message(STATUS "metis is enable")
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			link_libraries(${METIS_ROOT}/lib/metis.lib)
			message(STATUS "metis is enable")
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnableMetis_)
	elseif ()
		message(WARNING "METIS was requested but support was not found")
	endif ()
endif(ARI_ENABLE_METIS)

# Eigen库配置
if(ARI_ENABLE_EIGEN)
	set(EIGEN_ROOT ${EXTERNAL_PATH_CURRENT}/eigen)
	if (EXISTS ${EIGEN_ROOT})
		include_directories(${EIGEN_ROOT})
		add_definitions(-D_EnableEigen_)
		message(STATUS "eigen is enable")
	else ()
		message(WARNING "EIGEN was requested but support was not found")
	endif ()
endif(ARI_ENABLE_EIGEN)

# TinyXML2库配置
if(ARI_ENABLE_TINYXML2)
	set(TINYXML2_ROOT ${EXTERNAL_PATH_CURRENT}/tinyxml2)
	if (EXISTS ${TINYXML2_ROOT})
		include_directories(${TINYXML2_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${TINYXML2_ROOT}/lib/libtinyxml2.a)
			message(STATUS "tinyxml2 is enable")
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			link_libraries(${TINYXML2_ROOT}/lib/tinyxml2.lib)
			message(STATUS "tinyxml2 is enable")
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnableTinyXML2_)
	else ()
		message(WARNING "tinyxml2 was requested but support was not found")
	endif ()
endif(ARI_ENABLE_TINYXML2)

# Tecio库配置
if(ARI_ENABLE_TECIO)
	set(TECIO_ROOT ${EXTERNAL_PATH_CURRENT}/tecio)
	if (EXISTS ${TECIO_ROOT})
		include_directories(${TECIO_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${TECIO_ROOT}/lib/libtecio.a)
			message(STATUS "tecio is enable")
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			link_libraries(${TECIO_ROOT}/lib/tecio.lib)
			message(STATUS "tecio is enable")
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnableTecio_)
	else ()
		message(WARNING "TECIO was requested but support was not found")
	endif ()
endif(ARI_ENABLE_TECIO)

# ParMGridGen库配置
if(ARI_ENABLE_PARMGRIDGEN)
	set(PARMGRIDGEN_ROOT ${EXTERNAL_PATH_CURRENT}/parmgridgen)
	if (EXISTS ${PARMGRIDGEN_ROOT})
		include_directories(${PARMGRIDGEN_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${PARMGRIDGEN_ROOT}/lib/libIMlib.a)
			link_libraries(${PARMGRIDGEN_ROOT}/lib/libmgrid.a)
			message(STATUS "ParMGridGen is enable")
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			link_libraries(${PARMGRIDGEN_ROOT}/lib/IMlib.lib)
			link_libraries(${PARMGRIDGEN_ROOT}/lib/mgrid.lib)
			message(STATUS "ParMGridGen is enable")
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnablePARMGRIDGEN_)
	else ()
		message(WARNING "PARMGRIDGEN was requested but support was not found")
	endif ()
endif(ARI_ENABLE_PARMGRIDGEN)

# PETSc库配置
if(ARI_ENABLE_PETSc AND NOT("$ENV{PETSC_DIR}" STREQUAL "") AND NOT("$ENV{PETSC_ARCH}" STREQUAL ""))
	set(PETSC $ENV{PETSC_DIR}/$ENV{PETSC_ARCH})
	set(ENV{PKG_CONFIG_PATH} ${PETSC}/lib/pkgconfig)
	execute_process ( COMMAND pkg-config PETSc --variable=ccompiler COMMAND tr -d '\n' OUTPUT_VARIABLE C_COMPILER)
	SET(CMAKE_C_COMPILER ${C_COMPILER})
	execute_process ( COMMAND pkg-config PETSc --variable=cxxcompiler COMMAND tr -d '\n' OUTPUT_VARIABLE CXX_COMPILER)
	if (CXX_COMPILER)
  		SET(CMAKE_CXX_COMPILER ${CXX_COMPILER})
	endif (CXX_COMPILER)
	find_package(PkgConfig REQUIRED)
	pkg_search_module(PETSC REQUIRED IMPORTED_TARGET PETSc)
	link_libraries(PkgConfig::PETSC)

	add_definitions(-D_EnablePETSC_)
	message(STATUS "PETSc is enable")

endif()

# Triangle库配置
if(ARI_ENABLE_TRIANGLE)
	set(TRIANGLE_ROOT ${EXTERNAL_PATH_CURRENT}/triangle)
	if (EXISTS ${TRIANGLE_ROOT})
		include_directories(${TRIANGLE_ROOT}/include)
		if (CMAKE_SYSTEM_NAME MATCHES "Linux")
			link_libraries(${TRIANGLE_ROOT}/lib/libtriangle.a)
		elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
			#link_libraries(${TRIANGLE_ROOT}/lib/triangle.lib)
			link_libraries(${TRIANGLE_ROOT}/lib/triangle.obj)
		endif (CMAKE_SYSTEM_NAME MATCHES "Linux")
		add_definitions(-D_EnableTRIANGLE_)
		add_definitions(-DANSI_DECLARATORS)
		message(WARNING "TRIANGLE was found")
	else ()
		message(WARNING "TRIANGLE was requested but support was not found")
	endif ()
endif()


if (ARI_ENABLE_MKL)
	set(MKL_ROOT ${EXTERNAL_PATH_CURRENT}/mkl)
	include_directories(${MKL_ROOT}/include)

	# 设置MKL接口层为LP64（32位整数）
	add_definitions(-DMKL_ILP64=0)
	add_definitions(-D_EnableMKL_)

	if (CMAKE_SYSTEM_NAME MATCHES "Linux")
		link_libraries(${MKL_ROOT}/lib/libmkl_intel_lp64.so)
		link_libraries(${MKL_ROOT}/lib/libmkl_intel_thread.so)
		link_libraries(${MKL_ROOT}/lib/libmkl_scalapack_lp64.so)
		link_libraries(${MKL_ROOT}/lib/libmkl_blacs_intelmpi_lp64.so)
		link_libraries(${MKL_ROOT}/lib/libmkl_core.so)
		link_libraries(${MKL_ROOT}/lib/libmkl_def.so.1)
		link_libraries(${MKL_ROOT}/lib/libiomp5.so)
		message(STATUS "MKL Linux LP64 libraries enabled")
	elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
		# 使用LP64接口的MKL库（32位整数）
		if(CMAKE_BUILD_TYPE STREQUAL "Debug")
			# Debug版本库
			link_libraries(${MKL_ROOT}/lib/mkl_intel_lp64_dll.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_intel_thread_dll.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_core_dll.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_scalapack_lp64_dll.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_blacs_intelmpi_lp64_dll.lib)
		else()
			# Release版本库
			link_libraries(${MKL_ROOT}/lib/mkl_intel_lp64.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_intel_thread.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_core.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_scalapack_lp64.lib)
			link_libraries(${MKL_ROOT}/lib/mkl_blacs_intelmpi_lp64.lib)
		endif()
		# 添加Intel OpenMP运行时库 - 使用可用的库文件
		# link_libraries(${MKL_ROOT}/lib/libiomp5md.lib)  # 文件不存在，注释掉
		message(STATUS "MKL Windows LP64 libraries enabled for ${CMAKE_BUILD_TYPE}")
	endif (CMAKE_SYSTEM_NAME MATCHES "Linux")

	message(STATUS "Intel MKL enabled with LP64 interface (32-bit integers)")
endif()
