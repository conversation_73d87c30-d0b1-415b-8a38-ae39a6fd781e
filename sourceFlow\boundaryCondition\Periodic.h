﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Periodic.h
//! <AUTHOR>
//! @brief 空间周期性边界条件
//! @date  2023-2-6
//
//------------------------------修改日志----------------------------------------
//
// 2023-2-6 李艳亮、乔龙
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_Periodic_
#define _sourceFlow_boundaryCondition_Periodic_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 空间周期性边界条件类
 * 
 */
class Periodic: public FlowBoundary
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] value 给定11个数据(对应另一边界编号1，平移向量3，旋转轴6，旋转角度1)
     */
    Periodic(const int &boundaryPatchID, Package::FlowPackage &data, const std::vector<Scalar> &value);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 边界条件更新
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();
    
    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();
    
private:
    int couplePatchIDGlobal; ///< 对应的另一个边界编号

    Vector translation; ///< 平移向量
    bool translationFlag; ///< 平移标志

    std::pair<Vector, Vector> rotationAxis; ///< 旋转轴
    Scalar rotationAngle; ///< 旋转角度（输入角度，程序转化为弧度）
    bool rotationFlag; ///< 旋转标志
};

} // namespace Flow
} // namespace Boundary


#endif
