// Boost.Units - A C++ library for zero-overhead dimensional analysis and 
// unit/quantity manipulation and conversion
//
// Copyright (C) 2014 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_UNITS_INFORMATION_HPP
#define BOOST_UNITS_INFORMATION_HPP

#include <boost/units/systems/information/byte.hpp>
#include <boost/units/systems/information/bit.hpp>
#include <boost/units/systems/information/nat.hpp>
#include <boost/units/systems/information/hartley.hpp>
#include <boost/units/systems/information/shannon.hpp>
#include <boost/units/systems/information/prefixes.hpp>

#endif
