// This file is automatically generated. Do not edit.
// ['../../libs/compatibility/generate_cpp_c_headers.py']
// Wed Jul 23 12:11:19 2003 ('GMTST', 'GMTST')

#ifndef __CWCTYPE_HEADER
#define __CWCTYPE_HEADER

#include <wctype.h>

namespace std {
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wctrans_t;
#endif
  using ::wctype_t;
  using ::wint_t;
  using ::iswalnum;
  using ::iswctype;
  using ::iswlower;
  using ::iswspace;
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::towctrans;
#endif
#if !(defined(__sgi) && defined(_COMPILER_VERSION) && _COMPILER_VERSION <= 740)
  using ::wctrans;
#endif
  using ::iswalpha;
  using ::iswdigit;
  using ::iswprint;
  using ::iswupper;
  using ::towlower;
  using ::wctype;
  using ::iswcntrl;
  using ::iswgraph;
  using ::iswpunct;
  using ::iswxdigit;
  using ::towupper;
}

#endif // CWCTYPE_HEADER
