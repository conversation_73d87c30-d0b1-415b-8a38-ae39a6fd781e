 #pragma once
#include "basicTypes.h"
#include "fileStuff.h"
namespace tecplot { namespace ___3933 { class ___37; class FileWriterInterface; class ItemSetIterator; class ZoneHeaderWriterAbstract { public: ZoneHeaderWriterAbstract( ItemSetIterator&   varIter, ___4636        zone, ___4636        ___341, ___37&        ___36, std::string const& zoneMarkerLabel = "zoneMarker*", uint32_t           zoneMarker = SZPLT_ZONE_MARKER, std::string const& zoneNumberLabel = SZPLT_ZONE_NUM_DESCRIPTION, std::string const& zoneNumberSuffix = "ForZone"); virtual ~ZoneHeaderWriterAbstract(); void setMarkerAndLabels( std::string const& zoneMarkerLabel, uint32_t zoneMarker, std::string const& zoneNumberLabel, std::string const& zoneNumberSuffix); virtual uint64_t sizeInFile(bool ___2002) const = 0; virtual ___372 write(FileWriterInterface& fileWriter) const = 0; protected: ___372 ___4565( FileWriterInterface& file, ___3945 const&   ___3944) const; uint64_t zoneHeaderTagsSizeInFile(uint16_t numTags, bool ___2002) const; std::string appendZoneSuffix(std::string const& str) const; ItemSetIterator&  m_varIter; ___4636 const ___2677; ___4636 const m_baseZone; ___37 const& ___2337; std::string       m_zoneMarkerLabel; uint32_t          m_zoneMarker; std::string       m_zoneNumberLabel; std::string       m_zoneNumberSuffix; }; }}
