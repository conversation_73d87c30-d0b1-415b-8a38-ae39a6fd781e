﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ViscousFluxScheme.h
//! <AUTHOR>
//! @brief 进行NS方程粘性项通量求解的基类，用于派生具体通量类。
//! @date 2021-04-01
//
//------------------------------修改日志----------------------------------------
// 2021-04-01 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_viscousFluxScheme_ViscousFluxScheme_
#define _sourceFlow_fluxScheme_viscousFluxScheme_ViscousFluxScheme_

#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/configure/FlowGlobalDataMacro.h"
#include "sourceFlow/fluxScheme/FlowFluxScheme.h"
#include "basic/mesh/Mesh.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 粘性项通量命名空间
 * 
 */
namespace Viscous
{
/**
 * @brief 流场粘性项通量类
 * 用于派生具体通量格式，不能直接实例化
 * 
 */
class  ViscousFluxScheme : public FlowFluxScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     */
    ViscousFluxScheme(Package::FlowPackage &data);
    
    /**
     * @brief 通量累加
     * 
     */
    void AddResidual();

    /**
     * @brief 计算粘性项谱半径
     * 
     */
    void CalculateSpectralRadius();

	/**
	* @brief 面通量计算(距离方式)
	*
	*/
	void CalculateInnerFaceFluxDistance();

	/**
	* @brief 面通量计算(完全计算方式)
	*
	*/
	void CalculateInnerFaceFluxFull();

	/**
	* @brief 计算粘性通量Jacobian(完全计算方式)
	*
	*/
    void CalculateViscousJacobian(const int &faceID, const Scalar &muFace, const Scalar &kappaFace,
                                  const Vector &UFace, const NSFaceFlux &faceFlux);

protected:
    ElementField<Tensor> *gradientU; ///< 速度的梯度场
    ElementField<Vector> *gradientT; ///< 温度的梯度场
#if defined(_EnableMultiSpecies_)
    std::vector<ElementField<Vector> *> gradientMassFraction; ///< 组分场梯度
#endif

    ElementField<Scalar> *lambdaViscous; ///< 粘性谱半径

	const std::vector<Scalar> &distance; ///< 当地网格体心连线距离
    const std::vector<Vector> &distanceNormal; ///< 体心连线的单位化（owner指向neigher）
    Flux::Flow::Viscous::Scheme viscousScheme; ///< 粘性计算方法
	void (ViscousFluxScheme::*CalculateFaceFluxViscous)(); ///< 计算面通量的函数指针
    
    Matrix Jacobian_i_temp; ///< Owner单元三维Jacobian矩阵
    Matrix Jacobian_j_temp; ///< Neigh单元三维Jacobian矩阵
    Scalar nVarSqr3D; ///< 三维变量数量的平方

};

} // namespace Viscous
} // namespace Flow
} // namespace Flux
#endif 