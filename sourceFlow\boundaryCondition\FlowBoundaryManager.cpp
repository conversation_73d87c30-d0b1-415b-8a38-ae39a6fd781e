﻿#include "sourceFlow/boundaryCondition/FlowBoundaryManager.h"

namespace Boundary
{
namespace Flow
{

FlowBoundaryManager::FlowBoundaryManager(Package::FlowPackage &data)
    :
    flowPackage(data), flowConfigure(data.GetFlowConfigure()),
    level(data.GetMeshStruct().level), parallelB(data)
{
    // 得到边界patch的数目
    flowBoundarySize = data.GetMeshStruct().mesh->GetBoundarySize();

    // 初始化边界条件类容器的大小
    this->flowBoundary.resize(flowBoundarySize);

    // 得到各边界patch的边界类型名称
    for (int i = 0; i < flowBoundarySize; ++i)
        flowBoundaryType.push_back(flowConfigure.GetLocalBoundary(level, i).type);

    //短舱入口、短舱出口、质量流量入口、质量流量出口初始化
	nacelleInlet.resize(flowConfigure.GetNacelleInletGlobalIdVector().size());
	nacelleOutlet.resize(flowConfigure.GetNacelleOutletGlobalIdVector().size());
	massFlowInlet.resize(flowConfigure.GetMassFlowInletGlobalIdVector().size());
	massFlowOutlet.resize(flowConfigure.GetMassFlowOutletGlobalIdVector().size());
	hasNacelleInlet = nacelleInlet.size() > 0;
	hasNacelleExhaust = nacelleOutlet.size() > 0;
	hasMassFlowInlet = massFlowInlet.size() > 0;
	hasMassFlowOutlet = massFlowOutlet.size() > 0;

    //启动重叠模块时，创建重叠边界对象
	if (flowConfigure.JudgeEnableOversetMesh()) oversetB = new OversetBoundary(data);
}

FlowBoundaryManager::~FlowBoundaryManager()
{
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundary[patchID] != nullptr)
        {
            delete flowBoundary[patchID];
            flowBoundary[patchID] = nullptr;
        }
    }

	if (flowConfigure.JudgeEnableOversetMesh())
	{
		delete oversetB;
    }
}

const std::vector<Boundary::Type> &FlowBoundaryManager::GetBoundaryVector()
{
    return this->flowBoundaryType;
}

void FlowBoundaryManager::Initialize()
{
    this->CalculateNacelleInfo(true);
	this->CalculateMassflow();

    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET)
        {
            flowBoundary[patchID]->Initialize();
            flowBoundary[patchID]->UpdateExtras(); 
        }
    }
    parallelB.UpdateBoundaryCondition();

    if (flowConfigure.JudgeEnableOversetMesh())
    {
        oversetB->Initialize(); //单独进行边界初始化，以避免没有重叠边界的分区在更新边界条件时会跳过，导致并行通信错误
    }
}

void FlowBoundaryManager::UpdateBoundaryCondition()
{
    this->CalculateNacelleInfo(false);
	this->CalculateMassflow();

    // 非壁面边界
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] > Boundary::Type::WALL) continue;
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET)   
        {
            flowBoundary[patchID]->UpdateBoundaryCondition();
            flowBoundary[patchID]->UpdateExtras(); 
        }
    }
    
    // 壁面边界
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] < Boundary::Type::WALL) continue;
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET)   
        {
            flowBoundary[patchID]->UpdateBoundaryCondition();
            flowBoundary[patchID]->UpdateExtras(); 
        }
    }

    // 并行边界
    parallelB.UpdateBoundaryCondition();
    
    // 重叠边界
    if (flowConfigure.JudgeEnableOversetMesh())
    {
        oversetB->UpdateBoundaryCondition(); //单独进行边界更新，以避免没有重叠边界的分区在更新边界条件时会跳过重叠更新，导致并行通信错误
    }
}

void FlowBoundaryManager::AddDiffusiveResidual()
{
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET) 
        {
            flowBoundary[patchID]->AddDiffusiveResidual();
#if defined(_EnableMultiSpecies_)
            flowBoundary[patchID]->AddDiffusiveResidualMultiSpecies();
#endif
        }
    }
}

void FlowBoundaryManager::AddConvectiveResidual()
{
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET)
        {
            flowBoundary[patchID]->AddConvectiveResidual();
#if defined(_EnableMultiSpecies_)
            flowBoundary[patchID]->AddConvectiveResidualMultiSpecies();
#endif
        }
    }
}

void FlowBoundaryManager::UpdateBoundaryResidual()
{
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET)
        {
            flowBoundary[patchID]->UpdateBoundaryResidual();
        }
    }
}

void FlowBoundaryManager::SetBoundaryCondition(Flux::Flow::Precondition::Precondition *precondition)
{
    // 根据各边界patch的边界类型名称，创建具体的边界条件类
	int processedNacelleInlet = 0;
	int processedMassInFlow = 0;
	int processedMassOutFlow = 0;
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        flowBoundary[patchID] = nullptr;
        auto &pb = flowBoundary[patchID];
        switch (flowBoundaryType[patchID])
        {
        // 对称面边界条件
        case Boundary::Type::SYMMETRY:
        {
            pb = new Flow::Symmetry(patchID, flowPackage);
            break;
        }
        // 绝热壁面边界条件
        case Boundary::Type::WALL_ADIABATIC:
        {
            pb = new Flow::WallAdiabatic(patchID, flowPackage);
            break;
        }
        // 等温壁面边界条件
        case Boundary::Type::WALL_ISOTHERMAL:
        {
            Scalar wallTemperature = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);        
            pb = new Flow::WallIsothermal(patchID, flowPackage, wallTemperature);
            break;
        }
		// 热通量插值壁面边界条件
		case Boundary::Type::WALL_HEATFLUX_FILE:
		{
			std::string fileName = flowConfigure.GetLocalBoundary(level, patchID).value[0];
			pb = new Flow::WallGivenHeatFlux(patchID, flowPackage, fileName);
			break;
		}
		// 温度插值壁面边界条件
		case Boundary::Type::Wall_TEMPERATURE_FILE:
		{
			std::string fileName = flowConfigure.GetLocalBoundary(level, patchID).value[0];
			pb = new Flow::WallGivenTemperature(patchID, flowPackage, fileName);
			break;
		}
        // 远场边界条件
        case Boundary::Type::FARFIELD:
        {
            const Scalar &density = flowConfigure.GetFlowReference().density;
            const Vector &velocity = flowConfigure.GetFlowReference().velocity;
            const Scalar &pressure = flowConfigure.GetFlowReference().staticPressure;

            pb = new Flow::FarField(patchID, flowPackage, density, velocity, pressure, precondition);
            break;
        }
        // 周期性边界条件
        case Boundary::Type::PERIODIC:
        {
            // const auto &value = flowConfigure.GetLocalBoundary(level, patchID).value;
            // pb = new Flow::Periodic(patchID, flowPackage, value);
            break;
        }
        // 重叠网格边界条件
        case Boundary::Type::OVERSET:
        {
            //pb = new Flow::OversetBoundary(patchID, flowPackage);
            break;
        }
        // 指定原始变量边界条件
        case Boundary::Type::INFLOW_SPECIFY:
        {
            const Scalar density = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            const Vector velocity = StringToVector(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
            const Scalar pressure = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[2]);
            pb = new Flow::InflowSpecify(patchID, flowPackage, density, velocity, pressure);
            break;
        }
        // 外插边界
        case Boundary::Type::EXTRAPOLATION:
        {
            pb = new Flow::Extrapolation(patchID, flowPackage);
            break;
        }
        // 给定出口反压的边界
        case Boundary::Type::OUTFLOW_PRESSURE:
        {
            const Scalar pOut = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            pb = new Flow::OutflowPressure(patchID, flowPackage, pOut);
            break;
        }
        // 给定总条件的入口边界
        case Boundary::Type::INFLOW_TOTAL_CONDITION:
        {
            const Scalar totalPressure = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            const Scalar totalTemperature = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
            pb = new Flow::NacelleExhaust(patchID, flowPackage, totalPressure, totalTemperature);
            break;
        }
        // 给定总条件的入口边界
        case Boundary::Type::INFLOW_TOTAL_CONDITION_MACH:
        {
            const Scalar mach = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            const Scalar totalPressure = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
            const Scalar totalTemperature = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[2]);
            const Scalar alpha = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[3]);
            const Scalar beta = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[4]);
            pb = new Flow::InflowTotalCondition(patchID, flowPackage, mach, totalPressure, totalTemperature, alpha, beta, precondition);
            break;
        }
        // 短舱入口边界条件
        case Boundary::Type::NACELLE_INLET:
        {
            Scalar eps = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            nacelleInlet[processedNacelleInlet].outMassFlow = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
            int couplePatchIDGlobal = StringToInt(flowConfigure.GetLocalBoundary(level, patchID).value[2]);
			nacelleInlet[processedNacelleInlet].updateFlag = (couplePatchIDGlobal >= 0);

			pb = new Flow::NacelleInlet(patchID, flowPackage, eps, nacelleInlet[processedNacelleInlet].outMassFlow, couplePatchIDGlobal, nacelleInlet[processedNacelleInlet].inArea);
			processedNacelleInlet += 1;
            break;
        }
        // 短舱出口边界条件
        case Boundary::Type::NACELLE_EXHAUST:
        {
            Scalar totalPressure = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            Scalar totalTemperature = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
            pb = new Flow::NacelleExhaust(patchID, flowPackage, totalPressure, totalTemperature);
            break;
        }
		// 质量流率入流边界条件
		case Boundary::Type::MASSFLOW_INLET:
		{
			Scalar massFlowRateTarget = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
			pb = new Flow::MassFlow(patchID, flowPackage, massFlowRateTarget, massFlowInlet[processedMassInFlow].massFlowRate, precondition);
			processedMassInFlow += 1;
			break;
		}
		// 质量流率出流边界条件
		case Boundary::Type::MASSFLOW_OUTLET:
		{
			Scalar massFlowRateTarget = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
			pb = new Flow::MassFlow(patchID, flowPackage, massFlowRateTarget, massFlowOutlet[processedMassOutFlow].massFlowRate, precondition);
			processedMassOutFlow += 1;
            break;
        }
        // 风洞壁压边界条件
        case Boundary::Type::WIND_TUNNEL_WALL_PRESSURE:
        {
            std::string fileName = flowConfigure.GetLocalBoundary(level, patchID).value[0];
            pb = new Flow::WindTunnelWallPressure(patchID, flowPackage, fileName, precondition);
            break;
        }
        // 给定壁面速度的运动边界
        case Boundary::Type::WALL_MOVING:
        {
            Vector velocity = StringToVector(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            pb = new Flow::WallMoving(patchID, flowPackage, velocity);
            break;
        }
        //给定旋转速度的边界
		case Boundary::Type::WALL_ROTATE:
		{
			Vector Omega = StringToVector(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
			Vector origin = StringToVector(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
			pb = new Flow::WallRotate(patchID, flowPackage, Omega, origin);
			break;
		}
        // 滑移壁面边界条件
        case Boundary::Type::WALL_SLIPPING:
        {
            pb = new Flow::WallSlipping(patchID, flowPackage);
            break;
        }
        // 给定微型凹槽壁面几何
		case Boundary::Type::WALL_RIBLETS:
		{
			Scalar s = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
			pb = new Flow::WallRiblets(patchID, flowPackage, s);
			break;
		}
        // 合成射流边界条件
        case Boundary::Type::SYNTHETIC_JET:
        {
            Vector direction = StringToVector(flowConfigure.GetLocalBoundary(level, patchID).value[0]);
            Scalar velocityMean = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[1]);
            Scalar velocityAmplitude = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[2]);
            Scalar frequency = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[3]);
            Scalar theta = StringToScalar(flowConfigure.GetLocalBoundary(level, patchID).value[4]);

            pb = new Flow::SyntheticJet(patchID, flowPackage, direction, velocityMean, velocityAmplitude, frequency, theta);
            break;
        }
        default:
        {
            std::string error = "Bounday patch( " + ToString(patchID) + ") bcType is unkown!";
            FatalError("FlowBoundaryManager::SetBoundaryCondition: " + error);
			return;
        }
        }
    }
}

void FlowBoundaryManager::CalculateNacelleInfo(const bool &initializeFlag)
{
	// 计算面积及流量动态更新标识
    if (initializeFlag)
	{
		// 计算发动机入口面积及动态更新标识
		for (int i = 0; i < nacelleInlet.size(); i++)
		{
			SumAllProcessor(nacelleInlet[i].inArea, 0); // 收集到0号进程
			MPIBroadcast(nacelleInlet[i].inArea, 0); // 从0号进程广播到所有进程

			// 如果有发动机出口，判断是否流量动态更新
			if (hasNacelleExhaust)
			{
				int updateFlag = (int)nacelleInlet[i].updateFlag;
				SumAllProcessor(updateFlag, 0); // 收集到0号进程
				MPIBroadcast(updateFlag, 0); // 从0号进程广播到所有进程
				nacelleInlet[i].updateFlag = updateFlag > 0;
			}
		}
    }
    
    if(hasNacelleExhaust)
    {
        // 计算发动机出口流量
		for (int i = 0; i < nacelleInlet.size(); i++)
		{
			if (nacelleInlet[i].updateFlag) nacelleInlet[i].outMassFlow = 0.0;
			const int couplePatchIDGlobalForNacInleti = ((Flow::NacelleInlet*)flowBoundary[flowConfigure.GetNacelleInletGlobalIdVector()[i]])->GetcouplePatchIDGlobal();
			for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
			{
				if ((flowBoundaryType[patchID] == Boundary::Type::NACELLE_EXHAUST)&(flowPackage.GetMeshStruct().mesh->GetBoundaryIDGlobal(patchID) == couplePatchIDGlobalForNacInleti))
				{
					nacelleInlet[i].outMassFlow = ((Flow::NacelleExhaust*)flowBoundary[patchID])->CalculateMassFlow();
				}
			}

			if (nacelleInlet[i].updateFlag)
			{
				SumAllProcessor(nacelleInlet[i].outMassFlow, 0); // 收集到0号进程
				MPIBroadcast(nacelleInlet[i].outMassFlow, 0); // 从0号进程广播到所有进程
			}
		}
    }
}

void FlowBoundaryManager::CalculateMassflow()
{
	for (int i = 0; i < massFlowInlet.size(); i++)
	{
        massFlowInlet[i].massFlowRate = Scalar0;
		for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
        {
			if ((flowBoundaryType[patchID] == Boundary::Type::MASSFLOW_INLET) & (flowPackage.GetMeshStruct().mesh->GetBoundaryIDGlobal(patchID)==flowConfigure.GetMassFlowInletGlobalIdVector()[i]))
				massFlowInlet[i].massFlowRate = ((Flow::MassFlow*)flowBoundary[patchID])->CalculateMassFlowRate();
        }
		SumAllProcessor(massFlowInlet[i].massFlowRate, 0); // 收集到0号进程
		MPIBroadcast(massFlowInlet[i].massFlowRate, 0); // 从0号进程广播到所有进程
	}

    for (int i = 0; i < massFlowOutlet.size(); i++)
    {
        massFlowOutlet[i].massFlowRate = Scalar0;
		for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
        {
            if ((flowBoundaryType[patchID] == Boundary::Type::MASSFLOW_OUTLET) & (flowPackage.GetMeshStruct().mesh->GetBoundaryIDGlobal(patchID) == flowConfigure.GetMassFlowOutletGlobalIdVector()[i]))
				massFlowOutlet[i].massFlowRate = ((Flow::MassFlow *)flowBoundary[patchID])->CalculateMassFlowRate();
        }
        SumAllProcessor(massFlowOutlet[i].massFlowRate, 0); // 收集到0号进程
        MPIBroadcast(massFlowOutlet[i].massFlowRate, 0); // 从0号进程广播到所有进程
    }
}

void FlowBoundaryManager::SetFluxScheme(Flux::Flow::Inviscid::InviscidFluxScheme *InviscidFluxScheme_, Flux::Flow::Viscous::ViscousFluxScheme *viscousFluxScheme_)
{
    for (int patchID = 0; patchID < flowBoundarySize; ++patchID)
    {
        if (flowBoundaryType[patchID] != Boundary::Type::OVERSET)
        {
            this->flowBoundary[patchID]->SetFluxScheme(InviscidFluxScheme_, viscousFluxScheme_);
        }
    }
}

} //namespace Flow
} //namespace Boundary