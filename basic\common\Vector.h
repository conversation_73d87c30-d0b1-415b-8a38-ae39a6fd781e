﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Vector.h
//! <AUTHOR>
//! @brief 矢量类
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-09-22 李艳亮、乔龙
//    说明：改写与规范化
//
// 2020-07-22 数峰科技/西交大
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_common_Vector_
#define _basic_common_Vector_

#include "basic/common/BoostLib.h"
#include "basic/common/Configuration.h"

#include <iostream>
#include <string>
#include <fstream>
#include <math.h>

class Tensor;

/**
 * @brief 3D向量类，提供基本的向量运算和操作
 *
 * 该类实现了三维向量的各种数学运算，包括加减乘除、点积、叉积等，
 * 并支持序列化和并行计算(MPI)
 */
class Vector
{
public:
    /// @brief 默认构造函数，初始化为零向量
    Vector() { x = Scalar0; y = Scalar0; z = Scalar0; }
    
    /**
     * @brief 带参数的构造函数
     * @param X x分量值
     * @param Y y分量值
     * @param Z z分量值
     */
    Vector(const Scalar &X, const Scalar &Y, const Scalar &Z) { x = X; y = Y; z = Z; }

public:
    /**
     * @brief 获取x分量
     * @return x分量的常量引用
     */
    const Scalar &X() const { return x; }
    
    /**
     * @brief 获取y分量
     * @return y分量的常量引用
     */
    const Scalar &Y() const { return y; }
    
    /**
     * @brief 获取z分量
     * @return z分量的常量引用
     */
    const Scalar &Z() const { return z; }

    /**
     * @brief 设置x分量值
     * @param value 要设置的标量值
     */
    void SetX(const Scalar &value) { x = value; }
    
    /**
     * @brief 设置y分量值
     * @param value 要设置的标量值
     */
    void SetY(const Scalar &value) { y = value; }
    
    /**
     * @brief 设置z分量值
     * @param value 要设置的标量值
     */
    void SetZ(const Scalar &value) { z = value; }

public:
    /**
     * @brief 计算向量模长
     * @return 向量的模（标量值）
     */
    Scalar Mag() const
    {
        return sqrt(x * x + y * y + z * z);
    }

    /**
     * @brief 归一化当前向量（原地操作）
     * @return 归一化后的向量引用
     * @throw std::runtime_error 如果向量长度为零
     */
    Vector &Normalize()
    {
        const Scalar mag = this->Mag();
#if defined(_DevelopMode_)
        if (mag <= std::numeric_limits<Scalar>::epsilon())
        {
            throw std::runtime_error("Cannot normalize zero vector");
        }
#endif
        (*this) *= (1.0 / mag);
        return *this;
    }

    /**
     * @brief 获取归一化后的向量（不修改原向量）
     * @return 归一化后的新向量
     * @throw std::runtime_error 如果向量长度为零
     */
    Vector GetNormal() const
    {
        const Scalar mag = this->Mag();
#if defined(_DevelopMode_)
        if (mag <= std::numeric_limits<Scalar>::epsilon())
        {
            throw std::runtime_error("Cannot normalize zero vector");
        }
#endif
        return (*this) * (1.0 / mag);
    }

    /**
     * @brief 计算当前向量在指定方向上的投影（修改当前向量）
     * @param direct 投影方向向量
     */
    void Projection(const Vector &direct)
    {
        const Vector norm = direct.GetNormal();
        *this -= (*this & norm) * norm;
    }

public:
    /**
     * @brief 获取向量元素
     * @param index 向量元素编号
     * @return 向量元素值
     */
    Scalar operator[](const int &index) const
    {
        switch (index)
        {
        case 0:
            return x;
            break;
        case 1:
            return y;
            break;
        case 2:
            return z;
            break;
        default:
            return Scalar0; 
            break;
        }
    }

    /**
     * @brief 向量加法运算符
     * @param rhs 右操作数向量
     * @return 新向量（当前向量与rhs的和）
     */
    Vector operator+(const Vector &rhs) const
    {
        return Vector(x + rhs.x, y + rhs.y, z + rhs.z);
    }

    /**
     * @brief 向量减法运算符
     * @param rhs 右操作数向量
     * @return 新向量（当前向量与rhs的差）
     */
    Vector operator-(const Vector &rhs) const
    {
        return Vector(x - rhs.x, y - rhs.y, z - rhs.z);
    }

    /**
     * @brief 向量分量除法运算符
     * @param rhs 右操作数向量
     * @return 新向量（当前向量与rhs的对应分量相除）
     */
    Vector operator/(const Vector &rhs) const
    {
        return Vector(x / rhs.x, y / rhs.y, z / rhs.z);
    }

    /**
     * @brief 向量取负运算符
     * @return 新向量（当前向量的相反数）
     */
    Vector operator-() const
    {
        return Vector(-x, -y, -z);
    }

    /**
     * @brief 向量分量乘法（逐元素相乘）
     * @param rhs 右操作数向量
     * @return 新向量（当前向量与rhs的对应分量相乘）
     */
    Vector Multiply(const Vector &rhs) const
    {
        return Vector(x * rhs.x, y * rhs.y, z * rhs.z);
    }

    /**
     * @brief 向量加法赋值运算符
     * @param rhs 右操作数向量
     */
    void operator+=(const Vector &rhs)
    {
        x += rhs.x;
        y += rhs.y;
        z += rhs.z;
    }

    /**
     * @brief 向量减法赋值运算符
     * @param rhs 右操作数向量
     */
    void operator-=(const Vector &rhs)
    {
        x -= rhs.x;
        y -= rhs.y;
        z -= rhs.z;
    }

    /**
     * @brief 标量乘法赋值运算符
     * @param rhs 标量乘数
     */
    void operator*=(const Scalar &rhs)
    {
        x *= rhs;
        y *= rhs;
        z *= rhs;
    }

    /**
     * @brief 标量除法赋值运算符
     * @param rhs 标量除数
     */
    void operator/=(const Scalar &rhs)
    {
        *this *= (1.0 / rhs);
    }

    /**
     * @brief 向量相等比较运算符
     * @param rhs 要比较的向量
     * @return 如果所有分量差值小于SMALL则返回true
     */
    bool operator==(const Vector &rhs) const
    {
        return (std::abs(x - rhs.x) < SMALL) &&
               (std::abs(y - rhs.y) < SMALL) &&
               (std::abs(z - rhs.z) < SMALL);
    }
    
    /**
     * @brief 从文件读取向量数据
     * @param file 输入文件流
     * @param binary 是否为二进制模式
     * @throw std::runtime_error 如果读取失败
     */
    void Read(std::fstream &file, const bool binary)
    {
        try
        {
            if (binary)
            {
                file.read((char *)&x, sizeof(Scalar));
                file.read((char *)&y, sizeof(Scalar));
                file.read((char *)&z, sizeof(Scalar));
            }
            else
            {
                file >> x >> y >> z;
            }

            if (file.fail())
            {
                throw std::runtime_error("Vector read failed");
            }
        }
        catch (...)
        {
            throw std::runtime_error("Vector read operation failed");
        }
    }

    /**
     * @brief 将向量数据写入文件
     * @param file 输出文件流
     * @param binary 是否为二进制模式
     * @throw std::runtime_error 如果写入失败
     */
    void Write(std::fstream &file, const bool binary) const
    {
        try
        {
            if (binary)
            {
                file.write((char *)&x, sizeof(Scalar));
                file.write((char *)&y, sizeof(Scalar));
                file.write((char *)&z, sizeof(Scalar));
            }
            else
            {
                file << x << " " << y << " " << z << std::endl;
            }

            if (file.fail())
            {
                throw std::runtime_error("Vector write failed");
            }
        }
        catch (...)
        {
            throw std::runtime_error("Vector write operation failed");
        }
    }

    /**
     * @brief 点积计算函数
     * @param lhs 左操作数向量
     * @param rhs 右操作数向量
     * @return 点积结果（标量）
     */
    friend inline Scalar Dot(const Vector &lhs, const Vector &rhs)
    {
        return lhs.x * rhs.x + lhs.y * rhs.y + lhs.z * rhs.z;
    }

    /**
     * @brief 输出流运算符重载
     * @param out 输出流对象
     * @param rhs 要输出的向量
     * @return 输出流引用
     */
    friend inline std::ostream &operator<<(std::ostream &out, const Vector &rhs)
    {
        out << "(" << rhs.x << ", " << rhs.y << ", " << rhs.z << ")";
        return out;
    }

    /**
     * @brief 标量乘以向量运算符重载（标量左乘）
     * @param d 标量值
     * @param p 向量
     * @return 缩放后的新向量
     */
    friend inline Vector operator*(const Scalar &d, const Vector &p)
    {
        return Vector(d * p.x, d * p.y, d * p.z);
    }

    /**
     * @brief 标量乘以向量运算符重载（标量右乘）
     * @param p 向量
     * @param d 标量值
     * @return 缩放后的新向量
     */
    friend inline Vector operator*(const Vector &p, const Scalar &d)
    {
        return Vector(p.x * d, p.y * d, p.z * d);
    }

    /**
     * @brief 向量叉积运算符
     * @param v1 左操作数向量
     * @param v2 右操作数向量
     * @return 叉积结果（新向量）
     */
    friend inline Vector operator^(const Vector &v1, const Vector &v2)
    {
        return Vector((v1.y * v2.z - v1.z * v2.y),
                      (v1.z * v2.x - v1.x * v2.z),
                      (v1.x * v2.y - v1.y * v2.x));
    }

    /**
     * @brief 向量除以标量运算符
     * @param p 向量
     * @param d 标量除数
     * @return 除法结果（新向量）
     */
    friend inline Vector operator/(const Vector &p, const Scalar &d)
    {
        return Vector(p.x / d, p.y / d, p.z / d);
    }

    /**
     * @brief 点积运算符（等价于Dot函数）
     * @param lhs 左操作数向量
     * @param rhs 右操作数向量
     * @return 点积结果（标量）
     */
    friend inline Scalar operator&(const Vector &lhs, const Vector &rhs)
    {
        return lhs.x * rhs.x + lhs.y * rhs.y + lhs.z * rhs.z;
    }

    // 矢量用于张量的计算
    friend class Tensor;
    friend Vector operator*(const Vector &v, const Tensor &t);
    friend inline Tensor operator*(const Vector &p1, const Vector &p2);
    friend inline Vector Dot(const Tensor &t, const Vector &v);
    friend inline Vector Dot(const Vector &v, const Tensor &t);

private:
    Scalar x, y, z; ///< 存储向量分量的数组

#if defined(_BaseParallelMPI_)
public:
    /**
     * @brief 序列化函数（用于MPI通信）
     * @tparam Archive 序列化类型
     * @param ar 序列化对象
     * @param version 版本号
     */
    template <class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        ar & x;
        ar & y;
        ar & z;
    }
#endif
};

/// @def 零向量常量
#define Vector0 (Vector(0.0, 0.0, 0.0))

#endif