﻿#include "feilian-specialmodule/particle/solver/DEMSolver.h"

#include "TECIO.h"
#include <string.h>

#ifndef NULL
#define NULL 0
#endif

namespace Particle
{

DEMSolver::DEMSolver(const Configure::Particle::ParticleConfigure &configure_,
                     Geometry::Geometry *geometry_,
                     const std::vector<Boundary::Type> &boundaryTypeList_,
                     SubMesh *localMesh_,
                     const Package::FlowPackage *flowPackage_)
                     : configure(configure_), geometry(geometry_),
                     boundaryTypeList(boundaryTypeList_),
                     localMesh(localMesh_), flowPackage(flowPackage_),
					 backgroundGrid(nullptr), volumeFraction(nullptr),
					 contactforce(nullptr), contactListPP(nullptr), contactListPW(nullptr),
					 contactSearchPP(nullptr), contactSearchPW(nullptr), integrationTranslation(nullptr),
					 integrationRotation(nullptr), externalForce(nullptr)
{
	this->numParticleCurrent = 0;
    this->currentStep = 0;
	this->currentStepOutloop = 0;
    this->deltaT = configure.control.timeStep;
	this->currentTime = Scalar0;
	this->deltaTFromFlow = INF;
	this->stopTime = configure.control.timeStep * configure.control.totalSteps;
    this->numParticleMaximum = configure.distribution.numParticles;
    this->particles.resize(this->numParticleMaximum);
	this->contactFlagPP = configure.contact.particlesContactFlag;
	this->dim2 = (configure.reference.minDomain.Z() - configure.reference.maxDomain.Z()) > SMALL;

	if (localMesh != nullptr) volumeFraction = new ElementField<Scalar>(localMesh, Scalar0, "FRACTION");

	if (GetMPIRank() == 0) Print("定义颗粒和壁面的物理属性... ", 2);
    this->CreateProperty();

	if (GetMPIRank() == 0) Print("定义颗粒注入对象... ", 2);
	this->CreateInsertion();

	if (GetMPIRank() == 0) Print("定义接触力计算对象... ", 2);
    this->CreateContactForce();
    
	if (GetMPIRank() == 0) Print("定义接触搜索对象... ", 2);
    this->CreateContactSearch();
    
	if (GetMPIRank() == 0) Print("定义定义时间积分对象... ", 2);
    this->CreateIntegration();

	if (localMesh_ != nullptr)
	{
		if (GetMPIRank() == 0) Print("创建背景网格信息... ", 2);
		this->CreateBackGroundInfo();
	}

	if (GetMPIRank() == 0) Print("定义外部力对象... ", 2);
    this->CreateExternalForce();
}

DEMSolver::~DEMSolver()
{
    if (geometry != nullptr) {delete geometry; geometry = nullptr;}

    if (distributionProperty != nullptr) {delete distributionProperty; distributionProperty = nullptr;}
    if (physicalProperty != nullptr) {delete physicalProperty; physicalProperty = nullptr;}

    if (insertion != nullptr) {delete insertion; insertion = nullptr;}
   
    if (contactListPP != nullptr) {delete contactListPP; contactListPP = nullptr;}
    if (contactListPW != nullptr) {delete contactListPW; contactListPW = nullptr;}

    if (contactforce != nullptr) {delete contactforce; contactforce = nullptr;}

    if (contactSearchPP != nullptr) {delete contactSearchPP; contactSearchPP = nullptr;}
    if (contactSearchPW != nullptr) {delete contactSearchPW; contactSearchPW = nullptr;}
    
    if (integrationTranslation != nullptr) {delete integrationTranslation; integrationTranslation = nullptr;}
    if (integrationRotation != nullptr) {delete integrationRotation; integrationRotation = nullptr;}

	if (backgroundGrid != nullptr) { delete backgroundGrid; backgroundGrid = nullptr; }
	if (externalForce != nullptr) { delete externalForce; externalForce = nullptr; }
}

void DEMSolver::Initialize()
{
    // 初始化接触搜索信息
	if (contactFlagPP) this->contactSearchPP->Initialize();
    this->contactSearchPW->Initialize();

	// 更新时间
	time.UpdateTime();
}

void DEMSolver::Solve()
{
	currentStepOutloop++;

    const int &numIter = this->configure.control.totalSteps;
    for(int i = 0; i < numIter; ++i)
	{
		if (this->stopTime - this->currentTime < SMALL) break;

		Print("计算前准备工作", 2);
		this->PreCalculate();

		Print("基于上一时间步信息预估颗粒位置和速度（平移和旋转）", 2);
        this->integrationTranslation->Predict();
		if (contactTorqueFlag) this->integrationRotation->Predict();

		if (contactFlagPP)
		{
			Print("查找颗粒与颗粒接触信息", 2);
			this->contactSearchPP->FindContacts();

			Print("计算颗粒与颗粒间的接触力和力矩", 2);
			this->contactforce->CalculateForceAndTorquePP();
		}

		Print("查找颗粒与壁面接触信息", 2);
		this->contactSearchPW->FindContacts();

		Print("计算颗粒与壁面间的接触力和力矩", 2);
		this->contactforce->CalculateForceAndTorquePW();

		Print("计算加速度", 2);
		this->CalculateAcceleration(i);
        
		Print("修正颗粒位置和速度（平移）", 2);
		this->integrationTranslation->Correct();
        
		Print("修正颗粒位置和速度（旋转）", 2);
		if (contactTorqueFlag) this->integrationRotation->Correct();

		Print("更新颗粒体积分数", 2);
		this->UpdateVolumeFraction();
		
        // 更新当前步数
        this->currentStep += 1;
		this->currentTime += this->deltaT;

		Print("更新当前在计算域的颗粒数量", 2);
		this->CountParticlesInDomain();
		Print("numParticlesInDomainTotal = " + ToString(numParticlesInDomainTotal), 3);

		Print("输出颗粒信息", 2);
        if( this->currentStep == 1 || this->currentStep % this->configure.control.saveInterval == 0 )
		{
			this->OutputSolution();
        }
		
		Print("打印颗粒注入及接触信息", 2);
        if( this->currentStep == 1 || this->currentStep % this->configure.control.monitorInterval == 0 )
		{
			if (numParticlesInDomainTotal > 0)
			{
				this->MonitorParticle();
				if (GetMPIRank() == 0) this->PrintInfomation();
			}
        }
    }
}

void DEMSolver::SetTimeStep(const Scalar &timeStep_, const Scalar &currentTime_)
{
	this->deltaTFromFlow = timeStep_;
	this->currentTime = currentTime_;
	this->startTime = currentTime_;
	this->stopTime = currentTime_ + timeStep_;
}

void DEMSolver::CreateProperty()
{
    // 生成颗粒分布属性（含颗粒物质属性）
	const int dimension = (localMesh != nullptr) ? (int)localMesh->GetMeshDimension() : 3;
	distributionProperty = new DistributionProperty(Distribution(configure), PureProperty(configure, dimension));
    
    // 生成物理属性
    this->physicalProperty = new PhysicalProperty;

    // 颗粒分布
    physicalProperty->SetParticleProperty( distributionProperty );

    // 边界属性
    const int boundarySize = boundaryTypeList.size();
    physicalProperty->SetWallTypeNumber(boundarySize);
    for (int i = 0; i < boundarySize; i++)
    {
        if (boundaryTypeList[i] >= Boundary::Type::WALL)
            physicalProperty->SetWallProperty(i, PureProperty(configure) );
        else
            physicalProperty->SetNonWallProperty(i, PureProperty(configure) );
    }

    // 颗粒颗粒碰撞属性
    physicalProperty->SetBinaryPropertyPP(configure);

    // 颗粒壁面碰撞属性
    physicalProperty->SetBinaryPropertyPW(configure);
    
    // 打印物理属性中颗粒类型和壁面类型
	if (GetMPIRank() == 0) Print("系统包含"
         + ToString(this->physicalProperty->GetParticleTypeNumber()) + "种颗粒属性类型和"
         + ToString(this->physicalProperty->GetWallTypeNumber()  ) + "种壁面属性类型", 3);
}

void DEMSolver::CreateInsertion()
{
	// 生成注入对象
	this->insertion = nullptr;
	if (GetMPIRank() == 0) this->insertion = new Insertion(configure, particles, distributionProperty);
	if (GetMPIRank() == 0) Print("系统最大包含的颗粒数为：" + ToString(this->numParticleMaximum), 3);
}

void DEMSolver::CreateContactForce()
{
	if (GetMPIRank() == 0) Print("初始化颗粒-颗粒和颗粒-壁面接触列表...", 3);
	if (contactFlagPP) this->contactListPP = new Contact::ContactList(5 * this->numParticleMaximum + 1, this->numParticleMaximum);
    this->contactListPW = new Contact::ContactList( 3 * this->numParticleMaximum + 1, this->numParticleMaximum );
    
	if (GetMPIRank() == 0) Print("初始化颗粒-颗粒和颗粒-壁面接触力对象...", 3);
	const auto &wallContactType = this->configure.contact.wallContactType;
    const auto &contactForceType  = this->configure.contact.contactForceType;
    const auto &contactTorqueType = this->configure.contact.contactTorqueType;
    if( contactForceType == Configure::Particle::ContactForceType::LINEAR_NOLIMITED ||
		contactForceType == Configure::Particle::ContactForceType::LINEAR_LIMITED )
    {
		this->contactforce
			= new Contact::LinearForce(this->geometry, contactForceType, contactTorqueType, wallContactType, 
                                   this->numParticleMaximum, this->numParticleCurrent,
                                   this->particles, this->deltaT, this->contactListPP, this->contactListPW,
                                   this->physicalProperty);
    }
    else
    {
		this->contactforce
			= new Contact::NonlinearForce(this->geometry, contactForceType, contactTorqueType, wallContactType,
                                      this->numParticleMaximum, this->numParticleCurrent,
                                      this->particles, this->deltaT, this->contactListPP, this->contactListPW,
                                      this->physicalProperty);
    }
    
	contactTorqueFlag = (contactTorqueType != Configure::Particle::ContactTorqueType::CTT_NONE);

    // 打印接触力类型
    std::string stringTemp = Configure::Particle::contactForceTypeReverseMap.find(contactForceType)->second;
	if (GetMPIRank() == 0) Print("接触力计算采用的模型：" + stringTemp, 3);
}

void DEMSolver::CreateContactSearch()
{
    // 初始化颗粒颗粒接触搜索
	if (contactFlagPP) this->contactSearchPP
		= new Contact::SearchParticleParticle(
		this->configure, this->numParticleMaximum, this->numParticleCurrent,
		this->particles, this->contactListPP, this->volumeFraction);
    
    // 初始化颗粒壁面接触搜索
    this->contactSearchPW
    = new Contact::SearchParticleWall(this->numParticleCurrent, this->particles,
                                      this->deltaT, this->currentStep,
                                      this->contactListPW, this->geometry );
}

void DEMSolver::CreateIntegration()
{
	const Configure::Particle::WallContactType &wallContactType = this->configure.contact.wallContactType;

    // 初始平移运动积分
    this->integrationTranslation =
		new TranslationIntegration( this->numParticleMaximum, this->numParticleCurrent,
		this->particles, wallContactType, this->deltaT, this->configure.control.translationIntegrationScheme);
    
	if (contactTorqueFlag)
	{
		// 初始旋转运动积分
		this->integrationRotation
			= new RotationIntegration(this->numParticleMaximum, this->numParticleCurrent,
			this->particles, this->deltaT, this->configure.control.rotationIntegrationScheme);
	}
}

void DEMSolver::CreateBackGroundInfo()
{
	this->backgroundGrid = new BackgroundGrid(localMesh);
	this->backgroundID.resize(numParticleMaximum);
}

void DEMSolver::CreateExternalForce()
{
    this->externalForce = new ExternalForce(configure, particles, physicalProperty, backgroundID, flowPackage);
}

void DEMSolver::PreCalculate()
{
	Print("检查是否还需要注入颗粒", 3);
	int inserted = 0;
	if (GetMPIRank() == 0) inserted = (int)this->insertion->InsertParticle(this->numParticleCurrent, this->currentStep);

	mpi::broadcast(MPI::mpiWorld, inserted, 0);
	mpi::broadcast(MPI::mpiWorld, numParticleCurrent, 0);

	// 无论是否注入颗粒均需初始化
	this->newInserted.resize(numParticleCurrent, false);

	Print("注入后相关设置", 3);
	if (inserted == 1)
	{
		time.UpdateTime();
		Print("检查注入的颗粒是否在流体计算域外", 3);
		if (localMesh != nullptr) this->CheckInsertedParticles();

		Print("壁面相邻颗粒接触搜索信息重置", 3);
		this->contactSearchPW->ResetSearch();

		// 更新颗粒数量，同时为设置新注入颗粒位置及速度
		this->integrationTranslation->SetParticleNumber(this->numParticleCurrent);
		if (contactTorqueFlag) this->integrationRotation->SetParticleNumber(this->numParticleCurrent);

		// 更新颗粒颗粒接触搜索中颗粒的数量
		if (contactFlagPP) this->contactSearchPP->SetParticleNumber(this->numParticleCurrent);

		// 更新接触力计算中颗粒的数量
		this->contactforce->SetParticleNumber(this->numParticleCurrent);

		// 更新外部力计算中的颗粒数量
		this->externalForce->SetParticleNumber(this->numParticleCurrent);
	}

	Print("检查所有颗粒是否在计算域内，并更新颗粒状态及背景网格信息", 3);
	this->UpdateParticleFlag();

	Print("查找壁面相邻颗粒", 3);
	this->contactSearchPW->FindNearWallParticle();

	Print("颗粒受力置零", 3);
	for (int i = 0; i < this->numParticleCurrent; ++i)
	{
		if (this->particles[i] != nullptr)
		{
			this->particles[i]->force = Vector0;
			this->particles[i]->torque = Vector0;
		}
	}

	Print("接触信息置零", 3);
	if (contactFlagPP) this->contactListPP->SetContactsNumber(0);
	this->contactListPW->SetContactsNumber(0);

	Print("计算最小时间步长", 3);
	this->CalculateDeltaT();
}

void DEMSolver::CheckInsertedParticles()
{
	const int mpiSize = GetMPISize();
	const int mpiRank = GetMPIRank();

	std::vector<int> insertedIDs;
	int insertedNum = 0;
	if (mpiRank == 0)
	{
		insertedIDs = this->insertion->GetInsertedIDs();
		insertedNum = insertedIDs.size();
	}
#if defined(_BaseParallelMPI_)

	mpi::broadcast(MPI::mpiWorld, insertedNum, 0);

	std::vector<std::pair<int, std::pair<Vector, Vector>>> insParticlesList(insertedNum);
	if (mpiRank == 0)
	{
		for (int i = 0; i < insertedNum; ++i)
		{
			const int &particleID = insertedIDs[i];
			insParticlesList[i].first = particleID;
			insParticlesList[i].second.first = this->particles[particleID]->position;
			insParticlesList[i].second.second = this->particles[particleID]->linearVelocity;

			this->newInserted[particleID] = true;
		}
	}
	mpi::broadcast(MPI::mpiWorld, insParticlesList, 0);
	mpi::broadcast(MPI::mpiWorld, newInserted, 0);

	if (mpiRank == 0)
	{
		for (int i = 0; i < insertedNum; ++i)
		{
			const int &particleID = insertedIDs[i];
			delete this->particles[particleID];
			this->particles[particleID] = nullptr;
		}
	}

	Print("每个进程处理自己进程的颗粒", 4);
	for (int n = 0; n < insertedNum; ++n)
	{
		std::pair<int, std::pair<Vector, Vector>> *currentParticle = &insParticlesList[n];

		const int adjElemID = backgroundGrid->GetAdjElementID(currentParticle->second.first, true);
		if (adjElemID >= 0 && localMesh->JudgeRealElement(adjElemID))
		{
			const int insertPos = currentParticle->first;
			this->particles[insertPos] = new Particle();

			this->particles[insertPos]->ID = this->distributionProperty->GetParticleID(insertPos);
			this->particles[insertPos]->type = this->distributionProperty->GetParticlePropertyID(insertPos);
			this->particles[insertPos]->diameter = this->distributionProperty->GetParticleDiameter(insertPos);

			this->particles[insertPos]->position = currentParticle->second.first;
			this->particles[insertPos]->linearVelocity = currentParticle->second.second;
			this->particles[insertPos]->flag = Particle::ParticleFlag::IN_DOMAIN;

			this->backgroundID[insertPos] = adjElemID;
		}
	}
#else
	for (int i = 0; i < insertedNum; ++i)
	{
		const int particleID = insertedIDs[i];
		const Vector &pos = this->particles[particleID]->position;

		this->backgroundID[particleID] = backgroundGrid->GetAdjElementID(pos, true);
		if (this->backgroundID[particleID] < 0)
		{
			delete this->particles[particleID];
			this->particles[particleID] = nullptr;
		}
		else
		{
			this->numParticleCurrent++;
		}
	}
#endif

}

void DEMSolver::UpdateParticleFlag()
{
	// 获得最小最大坐标点
	const Vector &minDomain = configure.reference.minDomain;
	const Vector &maxDomain = configure.reference.maxDomain;

	// 二维标识
	bool dim2 = fabs(maxDomain.Z() - minDomain.Z()) < SMALL;

	Print("遍历所有颗粒，删除搜索范围外的颗粒", 4);
	for (int n = 0; n < numParticleCurrent; ++n)
	{
		if (this->particles[n] != nullptr)
		{
			const Vector &pos = this->particles[n]->position;
			if (pos.X() < minDomain.X() || pos.X() > maxDomain.X() ||
				pos.Y() < minDomain.Y() || pos.Y() > maxDomain.Y() ||
				(!dim2 && (pos.Y() < minDomain.Y() || pos.Y() > maxDomain.Y()))
				)
			{
				delete this->particles[n];
				this->particles[n] = nullptr;
			}
		}
	}

	if (localMesh != nullptr)
	{
		this->numParticlesInGhost = 0;
		MPIBarrier();
		const int mpiSize = GetMPISize();
		const int mpiRank = GetMPIRank();

		Print("更新背景网格索引信息及颗粒状态", 4);
		int numParticlesInGhostAll = 0;
		for (int i = 0; i < this->numParticleCurrent; ++i)
		{
			if (this->newInserted[i]) continue;
			if (this->particles[i] != nullptr)
			{
				const Vector &pos = this->particles[i]->position;
				this->backgroundID[i] = backgroundGrid->GetAdjElementID(pos, false, this->backgroundID[i]);
				if (this->backgroundID[i] < 0) numParticlesInGhostAll++;
			}
		}

		std::vector<std::vector<int>> particleIDList(mpiSize);
		for (int i = 0; i < mpiSize; i++)
		{
			particleIDList[i].reserve(numParticlesInGhostAll);
		}

		Print("更新背景网格索引信息及颗粒状态", 4);
		for (int i = 0; i < this->numParticleCurrent; ++i)
		{
			if (this->newInserted[i]) continue;

			int processIDGhost = -1;
			Vector pos;
			if (this->particles[i] != nullptr)
			{
				pos = this->particles[i]->position;
				if (this->backgroundID[i] < 0) processIDGhost = GetMPIRank();
			}

			int processIDGhostGlobal = -1;
			mpi::all_reduce(MPI::mpiWorld, processIDGhost, processIDGhostGlobal, mpi::maximum<int>());

			if (processIDGhostGlobal > -1)
			{
				mpi::broadcast(MPI::mpiWorld, pos, processIDGhostGlobal);

				int processIDReal = -1;
				if (backgroundGrid->GetAdjElementID(pos) >= 0) processIDReal = GetMPIRank();

				int processIDRealGlobal = -1;
				mpi::all_reduce(MPI::mpiWorld, processIDReal, processIDRealGlobal, mpi::maximum<int>());

				if (processIDRealGlobal == -1)
				{
					if (GetMPIRank() == processIDGhostGlobal)
					{
						delete this->particles[i];
						this->particles[i] = nullptr;
					}
				}
				else
				{
					if (GetMPIRank() == processIDGhostGlobal)
					{
						particleIDList[processIDRealGlobal].push_back(i);
						this->numParticlesInGhost++;
					}
				}
			}
		}

		Print("发送颗粒到其他的进程", 4);
#if defined(_BaseParallelMPI_)
		const auto &vv_ghostElement = localMesh->GetGhostElementsParallel();
		const int ghostBoundarySize = vv_ghostElement.size();

		std::vector<mpi::request> sendRequests;
		std::vector<mpi::request> recvRequests;

		std::vector<std::vector<std::pair<int, Particle>>> recvParticles(ghostBoundarySize);
		for (int i = 0; i < ghostBoundarySize; i++)
		{
			const auto &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
			recvRequests.push_back(MPI::mpiWorld.irecv(procPair.second, procPair.second, recvParticles[i]));
		}
		for (int i = 0; i < ghostBoundarySize; i++)
		{
			const auto &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
			const int count = particleIDList[procPair.second].size();
			std::vector<std::pair<int, Particle>> sendParticles(count);
			for (int j = 0; j < count; j++)
			{
				const int &particleID = particleIDList[procPair.second][j];
				sendParticles[j].first = particleID;
				sendParticles[j].second = (*this->particles[particleID]);
			}
			sendRequests.push_back(MPI::mpiWorld.isend(procPair.second, procPair.first, sendParticles));
		}
		MPIWaitAll(recvRequests);
		MPIWaitAll(sendRequests);

		for (int i = 0; i < ghostBoundarySize; i++)
		{
			const auto &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
			const int count = particleIDList[procPair.second].size();
			for (int j = 0; j < count; j++)
			{
				const int &particleID = particleIDList[procPair.second][j];
				delete this->particles[particleID];
				this->particles[particleID] = nullptr;
				this->backgroundID[particleID] = -1;
			}
		}

		Print("处理接收到的颗粒", 4);
		for (int i = 0; i < ghostBoundarySize; i++)
		{
			const int recvParticleSize = recvParticles[i].size();
			for (int j = 0; j < recvParticleSize; j++)
			{
				Particle *currentParticle = &recvParticles[i][j].second;

				const int adjElemID = backgroundGrid->GetAdjElementID(currentParticle->position, true);
				if (adjElemID >= 0 && localMesh->JudgeRealElement(adjElemID))
				{
					const int &particleID = recvParticles[i][j].first;
					this->particles[particleID] = new Particle();
					(*this->particles[particleID]) = (*currentParticle);
					this->particles[particleID]->flag = Particle::ParticleFlag::IN_DOMAIN;
					this->backgroundID[particleID] = adjElemID;
					break;
				}
				else
				{
					FatalError("DEMSolver::UpdateParticleFlag: received particle info is wrong!");
				}
			}
		}
#endif
	}
}

void DEMSolver::CalculateAcceleration(const int &numIter)
{
	if (numIter == 0) this->externalForce->UpdateFlowVariables();
    this->externalForce->CalculateExternalForce();
    
    // 计算颗粒加速度
    for(int i = 0; i < this->numParticleCurrent; ++i)
    {
		if (this->particles[i] != nullptr)
		{
			if (dim2) this->particles[i]->force.SetZ(Scalar0);
			const Scalar &mass = this->physicalProperty->GetMass(this->particles[i]->type);
			const Scalar &inertia = this->physicalProperty->GetInertia(this->particles[i]->type);
			this->particles[i]->linearAcceleration = this->particles[i]->force / mass;
			this->particles[i]->angularAcceleration = this->particles[i]->torque / inertia;

			// 检查加速度
			if (std::isinf(this->particles[i]->linearAcceleration.X()) ||
				std::isinf(this->particles[i]->linearAcceleration.Y()) ||
				std::isinf(this->particles[i]->linearAcceleration.Z()) ||
				std::isnan(this->particles[i]->linearAcceleration.X()) ||
				std::isnan(this->particles[i]->linearAcceleration.Y()) ||
				std::isnan(this->particles[i]->linearAcceleration.Z()))
			{
				if (GetMPIRank() == 0) Print("acceleration is wrong!");
			}
        }
    }
}

void DEMSolver::CountParticlesInDomain()
{
	this->numParticlesInDomain = 0;
	for (int i = 0; i < this->numParticleCurrent; ++i) if (this->particles[i] != nullptr) this->numParticlesInDomain++;
	mpi::all_reduce(MPI::mpiWorld, numParticlesInDomain, numParticlesInDomainTotal, std::plus<int>());

	this->numParticlesInGhostTotal = 0;
	mpi::all_reduce(MPI::mpiWorld, numParticlesInGhost, numParticlesInGhostTotal, std::plus<int>());

	this->numContactSearchPPTotal = 0;
	int numContactSearchPP = contactFlagPP ? this->contactSearchPP->GetContactNumber()[0] : 0;
	mpi::all_reduce(MPI::mpiWorld, numContactSearchPP, numContactSearchPPTotal, std::plus<int>());

	this->numContactPPTotal = 0;
	int numContactPP = contactFlagPP ? this->contactListPP->GetContactsNumber() : 0;
	mpi::all_reduce(MPI::mpiWorld, numContactPP, numContactPPTotal, std::plus<int>());

	this->numContactPWTotal = 0;
	int numContactPW = this->contactListPW->GetContactsNumber();
	mpi::all_reduce(MPI::mpiWorld, numContactPW, numContactPWTotal, std::plus<int>());
}

void DEMSolver::UpdateVolumeFraction()
{
	if (localMesh == nullptr || !contactFlagPP) return;

	volumeFraction->Initialize();
	for (int i = 0; i < this->numParticleCurrent; ++i)
	{
		if (this->particles[i] != nullptr)
		{
			const int &elemID = this->backgroundID[i];
			const Scalar rad = 0.5 * this->particles[i]->diameter;
			const Scalar volume = dim2 ? PI * rad * rad : 4.0 / 3.0 * rad * rad * rad;
			volumeFraction->AddValue(elemID, volume);
		}
	}

	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		const Scalar temp = 1.0 / localMesh->GetElement(elemID).GetVolume();
		volumeFraction->MultiplyValue(elemID, temp);
	}
}

void DEMSolver::CalculateDeltaT()
{
	this->deltaT = Min(configure.control.timeStep, this->deltaTFromFlow);
	this->deltaT = Min(this->deltaT, this->stopTime - this->currentTime);

	if (configure.control.variableTimeStep)
	{
		Scalar minTimeStepGlobal = INF;
		if (backgroundGrid != nullptr)
		{
			Scalar minTimeStep = INF;
			for (int i = 0; i < this->numParticleCurrent; ++i)
			{
				if (this->particles[i] != nullptr)
				{
					minTimeStep = Min(minTimeStep,
						this->backgroundGrid->GetMinTime(this->particles[i]->position,
						this->particles[i]->linearVelocity, this->backgroundID[i]));
				}
			}

			mpi::all_reduce(MPI::mpiWorld, minTimeStep, minTimeStepGlobal, mpi::minimum<Scalar>());
		}
		this->deltaT = Min(this->deltaT, minTimeStepGlobal);
	}
}

void DEMSolver::OutputSolution()
{
	if (numParticlesInDomainTotal > 0)
	{
		Print("收集颗粒信息", 3);
		std::vector<int> recvParticleIDList;
		this->GatherParticles(recvParticleIDList);

		if (GetMPIRank() == 0)
		{
			Print("输出颗粒和几何信息到Tecplot文件", 3);
			this->WriteGeometryAndParticlesTecplot();

			Print("输出颗粒信息到解文件", 3);
			this->WriteParticleFields();
		}

		Print("删除从其他进程收集的颗粒", 3);
		const int listSize = recvParticleIDList.size();
		for (int i = 0; i < listSize; ++i)
		{
			const int &particleID = recvParticleIDList[i];
			delete this->particles[particleID];
			this->particles[particleID] = nullptr;
		}

		MPIBarrier();
	}
}

void DEMSolver::GatherParticles(std::vector<int> &recvParticleIDList)
{
	const int mpiRank = GetMPIRank();
	const int mpiSize = GetMPISize();

	std::vector<mpi::request> sendRequests;
	std::vector<mpi::request> recvRequests;

	std::vector<std::vector<std::pair<int, Particle>>> recvParticlesList;
	if (mpiRank > 0)
	{
		const int count = this->numParticlesInDomain;
		std::vector<std::pair<int, Particle>> sendParticlesList(count);
		for (int j = 0, index = 0; j < this->numParticleCurrent; j++)
		{
			if (this->particles[j] != nullptr)
			{
				sendParticlesList[index].first = j;
				sendParticlesList[index].second = *(this->particles[j]);
				index++;
				if (index == count) break;
			}
		}
		sendRequests.push_back(MPI::mpiWorld.isend(0, 0, sendParticlesList));
	}
	else
	{
		recvParticlesList.resize(mpiSize);
		for (int procID = 1; procID < mpiSize; procID++)
		{
			recvRequests.push_back(MPI::mpiWorld.irecv(procID, 0, recvParticlesList[procID]));
		}
	}
	MPIWaitAll(recvRequests);
	MPIWaitAll(sendRequests);

	if (mpiRank == 0)
	{
		recvParticleIDList.clear();
		recvParticleIDList.reserve(this->numParticlesInDomainTotal);
		for (int procID = 1; procID < mpiSize; procID++)
		{
			const auto &particlesList = recvParticlesList[procID];
			const int listSize = particlesList.size();
			for (int i = 0; i < listSize; i++)
			{
				const int &particleID = particlesList[i].first;
				this->particles[particleID] = new Particle;
				(*this->particles[particlesList[i].first]) = particlesList[i].second;
				recvParticleIDList.push_back(particleID);
			}
		}
	}
}

void WriteStringASCII(const std::string &currentString, std::fstream &file)
{
	int value = 0;
	int stringSize = currentString.size();
	for (int len = 0; len < stringSize; len++)
	{
		value = int(currentString[len]);
		IO::Write(file, value, true);
	}
	value = 0;
	IO::Write(file, value, true);
}

void DEMSolver::WriteGeometryAndParticlesTecplot()
{
	// 输出路径
	MakeDirectory(this->configure.control.resultsPath);
	std::string tecplotPath = this->configure.control.resultsPath + "tecplot/";
	MakeDirectory(tecplotPath);

	const int &num = numParticlesInDomainTotal;
	std::vector<int> IDList(num);
	for (int i = 0, index = 0; i < this->numParticleCurrent; ++i)
	{
		if (this->particles[i] != nullptr) IDList[index++] = i;
	}

	std::string timeString = ToString(this->currentTime);

	const std::string fileName = tecplotPath + this->configure.caseName + "_" + timeString + ".plt";
	const bool &binary = this->configure.control.outputFileBinary;
	if (!binary)
	{
		// 输出变量
		std::fstream file(fileName, (this->currentStep == 1) ? std::ios::out : std::ios::app);
		if (this->currentStep > 1) file << std::endl;
		if (this->currentStep == 1) file << "Variables = X, Y, Z, VX, VY, VZ, FX, FY, FZ, D" << std::endl;

		// 输出颗粒
		std::string zoneName1 = "Particles: t = " + timeString;
		file << "ZONE T = " << zoneName1 << ", I = " << num << ", SOLUTIONTIME = " << currentTime << ", STRANDID = " << 1 << std::endl;
		for (int i = 0; i < num; ++i)
		{
			file
				<< this->particles[IDList[i]]->position.X() << " "
				<< this->particles[IDList[i]]->position.Y() << " "
				<< this->particles[IDList[i]]->position.Z() << " "
				<< this->particles[IDList[i]]->linearVelocity.X() << " "
				<< this->particles[IDList[i]]->linearVelocity.Y() << " "
				<< this->particles[IDList[i]]->linearVelocity.Z() << " "
				<< this->particles[IDList[i]]->force.X() << " "
				<< this->particles[IDList[i]]->force.Y() << " "
				<< this->particles[IDList[i]]->force.Z() << " "
				<< this->particles[IDList[i]]->diameter << std::endl;
		}

		// 输出几何
		std::vector<Vector> points;
		const int wallNumber = geometry->GetPlaneWallNumber();
		for (int i = 0; i < wallNumber; ++i)
		{
			const Geometry::PlaneWall &wall = geometry->GetWall(i);
			const int nNode = wall.GetPointNumber();
			for (int i = 0; i < nNode; i++) points.push_back(wall.GetPoint(i));
		}
		const int nPoints = points.size();
		const int nFaces = wallNumber;

		std::string zoneType;
		const int faceNodeSize = geometry->GetWall(0).GetPointNumber();
		if (faceNodeSize == 2) zoneType = "FELINESEG";
		else if (faceNodeSize == 3) zoneType = "FETRIANGLE";
		else if (faceNodeSize == 4) zoneType = "FEQUADRILATERAL";
		else FatalError("Geometry::OutputTecplot: zoneType isnot supported!");

		std::string zoneName2 = "Geometry: t = " + timeString;
		file << "ZONE T = " << zoneName2 << ", NODES = " << nPoints << ", ELEMENTS = " << nFaces << ", ZONETYPE = " << zoneType << ", DATAPACKING = POINT"
			<< ", SOLUTIONTIME = " << currentTime << ", STRANDID = " << 2 << std::endl;

		for (int i = 0; i < nPoints; ++i)
		{
			file << points[i].X() << " " << points[i].Y() << " " << points[i].Z() << " "
				<< Scalar0 << " " << Scalar0 << " " << Scalar0 << " "
				<< Scalar0 << " " << Scalar0 << " " << Scalar0 << " "
				<< Scalar0 << std::endl;
		}

		for (int i = 0, index = 0; i < nFaces; ++i)
		{
			const Geometry::PlaneWall &wall = geometry->GetWall(i);
			for (int j = 0; j < faceNodeSize; ++j)
			{
				file << index;
				if (j < faceNodeSize - 1) file << " ";
				index++;
			}
			if (i < nFaces - 1) file << std::endl;
		}

		file.close();
	}
	else
	{
		INTEGER4 fileFormat = 0; // 0 == PLT, 1 == SZPLT
		INTEGER4 FileType = 0; //  FULL = 0, GRID = 1, SOLUTION = 2
		INTEGER4 Debug = 0, VIsDouble = 1;

		// 标题及变量
		TECINI142((char*)"ARI-CFDDEM", (char*)"X Y Z VX VY VZ FX FY FZ D",
			fileName.c_str(), (char*)".", &fileFormat, &FileType, &Debug, &VIsDouble);

		// 输出颗粒信息
		INTEGER4 IMax = numParticlesInDomainTotal, JMax = 1, KMax = 1;
		INTEGER4 ZoneType = 0, StrandID = 1, ParentZn = 0, IsBlock = 1;
		INTEGER4 ICellMax = 0, JCellMax = 0, KCellMax = 0;
		INTEGER4 NFConns = 0, FNMode = 0, ShrConn = 0, DIsDouble = 1;
		double SolTime = currentTime;

		std::string zoneName1 = "Particles: t = " + timeString;
		TECZNE142(zoneName1.c_str(), &ZoneType, &IMax, &JMax, &KMax, &ICellMax, &JCellMax, &KCellMax,
			&SolTime, &StrandID, &ParentZn, &IsBlock, &NFConns, &FNMode, 0, 0, 0, NULL, NULL, NULL, &ShrConn);

		std::vector<Scalar> scalarList(numParticlesInDomainTotal);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->position.X();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->position.Y();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->position.Z();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->linearVelocity.X();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->linearVelocity.Y();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->linearVelocity.Z();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->force.X();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->force.Y();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->force.Z();
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);
		for (int i = 0; i < num; ++i) scalarList[i] = this->particles[IDList[i]]->diameter;
		TECDAT142(&IMax, &scalarList[0], &DIsDouble);

		// 输出几何信息
		std::vector<Vector> points;
		const int wallNumber = geometry->GetPlaneWallNumber();
		for (int i = 0; i < wallNumber; ++i)
		{
			const Geometry::PlaneWall &wall = geometry->GetWall(i);
			const int nNode = wall.GetPointNumber();
			for (int i = 0; i < nNode; i++) points.push_back(wall.GetPoint(i));
		}
		
		StrandID = 2;
		INTEGER4 &nNodes = IMax;
		INTEGER4 &nCells = JMax;
		INTEGER4 &nFaces = KMax;
		nNodes = points.size(), nCells = wallNumber, nFaces = 1;

		// ZoneType: 0=ORDERED, 1=FELINESEG, 2=FETRIANGLE, 3=FEQUADRILATERAL, 4=FETETRAHEDRON, 5=FEBRICK, 6=FEPOLYGON, 7=FEPOLYHEDRON
		const int faceNodeSize = geometry->GetWall(0).GetPointNumber();
		if (faceNodeSize == 2) ZoneType = 1;
		else if (faceNodeSize == 3) ZoneType = 2;
		else if (faceNodeSize == 4) ZoneType = 3;
		else FatalError("Geometry::OutputTecplot: zoneType isnot supported!");

		std::string zoneName2 = "Geometry: t = " + timeString;
		TECZNE142(zoneName2.c_str(), &ZoneType, &nNodes, &nCells, &nFaces, &ICellMax, &JCellMax, &KCellMax,
			&SolTime, &StrandID, &ParentZn, &IsBlock, &NFConns, &FNMode, 0, 0, 0, NULL, NULL, NULL, &ShrConn);

		std::vector<Scalar> list(nNodes);
		for (int i = 0; i < nNodes; i++) list[i] = points[i].X();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		for (int i = 0; i < nNodes; i++) list[i] = points[i].Y();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		for (int i = 0; i < nNodes; i++) list[i] = points[i].Z();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		
		list.resize(nNodes, Scalar0);
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		TECDAT142(&nNodes, &list[0], &DIsDouble);

		int connectivityCount = nCells * faceNodeSize;
		std::vector<int> connectivity(connectivityCount);
		for (int i = 0, index = 0; i < nCells; i++)
		{
			for (int j = 0; j < faceNodeSize; j++)
			{
				connectivity[index] = index + 1;
				index++;
			}
		}
		TECNODE142(&connectivityCount, &connectivity[0]);

		// 结束输出
		TECEND142();
	}
}

void DEMSolver::WriteParticleFields()
{
	// 输出路径
	std::string DEMPath = this->configure.control.resultsPath + "DEM/";
	MakeDirectory(DEMPath);

	// 颗粒信息文件
	bool binary = true;
	std::fstream solutionFile;
	const std::string solutionFileName = DEMPath + ToString(currentStep) + ".PARTICLE";
	if (binary) solutionFile.open(solutionFileName, std::ios::out | std::ios::binary);
	else        solutionFile.open(solutionFileName, std::ios::out);

	IO::Write(solutionFile, std::string("#particle number : "), binary);
	IO::Write(solutionFile, numParticlesInDomainTotal, binary);
	IO::Write(solutionFile, std::string("#Variables = x, y, z, d, V, Fc"), binary);
	std::vector<Vector> vectorList(numParticlesInDomainTotal);
	std::vector<Scalar> scalarList(numParticlesInDomainTotal);
	std::vector<int> intList(numParticlesInDomainTotal);
	for (int i = 0, index = 0; i < this->numParticleCurrent; ++i)
	{
		if (this->particles[i] != nullptr)
		{
			vectorList[index] = this->particles[i]->position;
			scalarList[index] = this->particles[i]->diameter;
			intList[index] = this->particles[i]->ID;
			index++;
		}
	}
	IO::Write(solutionFile, vectorList, binary);
	IO::Write(solutionFile, scalarList, binary);
	IO::Write(solutionFile, intList, binary);

	for (int i = 0, index = 0; i < this->numParticleCurrent; ++i)
	{
		if (this->particles[i] != nullptr)
		{
			vectorList[index] = this->particles[i]->linearVelocity;
			index++;
		}
	}
	IO::Write(solutionFile, vectorList, binary);

	for (int i = 0, index = 0; i < this->numParticleCurrent; ++i)
	{
		if (this->particles[i] != nullptr)
		{
			vectorList[index] = this->particles[i]->force;
			index++;
		}
	}
	IO::Write(solutionFile, vectorList, binary);

	solutionFile.close();
}

void DEMSolver::MonitorParticle()
{
	const int &particleID = this->configure.monitor.particleID;
	if (particleID < 0) return;

	const bool &positionFlag = this->configure.monitor.positionFlag;
	const bool &velocityFlag = this->configure.monitor.velocityFlag;
	const bool &forceFlag = this->configure.monitor.forceFlag;
	if (!positionFlag && !velocityFlag && !forceFlag) return;

	if (this->particles[particleID] == nullptr) return;

	// 输出信息
	const std::string monitorFileName = this->configure.control.resultsPath + this->configure.caseName + "_monitor_" + ".plt";
	std::fstream monitorFile;
	if (this->currentStep == 1) monitorFile.open(monitorFileName, std::ios::out);
	else                        monitorFile.open(monitorFileName, std::ios::app);

	// 输出监测量名称
	if (this->currentStep == 1)
	{
		monitorFile << "# particleID = " << particleID << std::endl;
		monitorFile << std::setw(16) << "variables = time";
		if (positionFlag) monitorFile << std::setw(16) << "x" << std::setw(16) << "y" << std::setw(16) << "z";
		if (velocityFlag) monitorFile << std::setw(16) << "Vx" << std::setw(16) << "Vy" << std::setw(16) << "Vz";
		if (forceFlag) monitorFile << std::setw(16) << "Fx" << std::setw(16) << "Fy" << std::setw(16) << "Fz";
		monitorFile << std::endl;
	}

	monitorFile << ScalarToString(currentTime, 16, 4, 1);
	if (positionFlag)
	{
		const auto &position = this->particles[particleID]->position;
		monitorFile << std::setw(12) << ScalarToString(position.X(), 16, 6, 1)
			<< std::setw(12) << ScalarToString(position.Y(), 16, 6, 1)
			<< std::setw(12) << ScalarToString(position.Z(), 16, 6, 1);
	}
	if (velocityFlag)
	{
		const auto &velocity = this->particles[particleID]->linearVelocity;
		monitorFile << std::setw(12) << ScalarToString(velocity.X(), 16, 6, 1)
			<< std::setw(12) << ScalarToString(velocity.Y(), 16, 6, 1)
			<< std::setw(12) << ScalarToString(velocity.Z(), 16, 6, 1);
	}
	if (forceFlag)
	{
		const auto &force = this->particles[particleID]->force;
		monitorFile << std::setw(12) << ScalarToString(force.X(), 16, 6, 1)
			<< std::setw(12) << ScalarToString(force.Y(), 16, 6, 1)
			<< std::setw(12) << ScalarToString(force.Z(), 16, 6, 1);
	}
	monitorFile << std::endl;

	monitorFile.close();
}

void DEMSolver::PrintInfomation()
{
	std::ostringstream stringStream;

	// 输出监测量名称
	if (this->currentStep == 1 ||
		fabs(this->currentTime - this->deltaT - this->startTime) < SMALL ||
		this->currentStep % (10 * this->configure.control.monitorInterval) == 0)
	{
		if (this->currentStep > 1) stringStream << std::endl;
		stringStream << std::setw(12) << "curStep" << std::setw(12) << "curTime"
			<< std::setw(12) << "nPrtcIns" << std::setw(12) << "nPrtcInDom"
			<< std::setw(12) << "nSearch" << std::setw(12) << "nCntPP" << std::setw(12) << "nCntPW" << std::setw(12) << "cpu_t(s)";
		Print(stringStream.str());
	}

	// 输出监测量
	stringStream.str("");
	stringStream
		<< std::setw(12) << this->currentStep
		<< std::setw(12) << std::scientific << std::setprecision(4) << this->currentTime
		<< std::setw(12) << this->numParticleCurrent
		<< std::setw(12) << this->numParticlesInDomainTotal
		<< std::setw(12) << this->numContactSearchPPTotal
		<< std::setw(12) << this->numContactPPTotal
		<< std::setw(12) << this->numContactPWTotal
		<< std::setw(12) << std::setprecision(6) << std::fixed << time.GetElapsedTime();
	Print(stringStream.str());

	// 更新时间
	time.UpdateTime();
}

} // namespace Particle