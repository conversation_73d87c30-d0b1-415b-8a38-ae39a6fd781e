 #pragma once
#include "ThirdPartyHeadersBegin.h"
#  include <boost/shared_ptr.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "AltTecUtil.h"
namespace tecplot { namespace ___3933 { typedef boost::shared_ptr<class ___2724> ___2729; class ___2724 { public: ___2724() : ___2497(0) , m_rawNodeMapPtr32(0) , m_rawNodeMapPtr64(0) , ___2392(0) , ___2500(0) {} ___2724(___37* tecUtil, ___1172 zone, bool writable = false); bool ___2067() const { return ((((m_rawNodeMapPtr32 != NULL) && (m_rawNodeMapPtr64 == NULL) && (___2497 == NULL)) || ((m_rawNodeMapPtr32 == NULL) && (m_rawNodeMapPtr64 != NULL) && (___2497 == NULL)) || ((m_rawNodeMapPtr32 == NULL) && (m_rawNodeMapPtr64 == NULL) && (___2497 != NULL))) && m_tecUtil != NULL && ___2392 > 0 && 2 <= ___2500 && ___2500 <= 8); } ___2718 ___1763(___2227 ___449, int32_t ___681) const { REQUIRE(0 < ___449 && ___449 <= ___2392); REQUIRE(0 < ___681 && ___681 <= ___2500); if (m_rawNodeMapPtr32 != NULL) return m_rawNodeMapPtr32[(___449 - 1) * ___2500 + ___681 - 1] + 1; else if (m_rawNodeMapPtr64 != NULL) return m_rawNodeMapPtr64[(___449 - 1) * ___2500 + ___681 - 1] + 1; else return m_tecUtil->___865(___2497, ___449, static_cast<___682>(___681)); } ___465 ___1766() const { return ___2392; } int32_t ___1764() const { return ___2500; } void setNode(___465 ___449, int32_t ___681, ___2718 ___2709) { if (m_rawNodeMapPtr32 != NULL) m_rawNodeMapPtr32[(___449 - 1) * ___2500 + ___681 - 1] = static_cast<int32_t>(___2709 - 1); else if (m_rawNodeMapPtr64 != NULL) m_rawNodeMapPtr64[(___449 - 1) * ___2500 + ___681 - 1] = ___2709 - 1; else m_tecUtil->___870(___2497, ___449, static_cast<___682>(___681), ___2709); } private: ___37* m_tecUtil; ___2727  ___2497; int32_t*    m_rawNodeMapPtr32; int64_t*    m_rawNodeMapPtr64; ___465 ___2392; int32_t     ___2500; }; }}
