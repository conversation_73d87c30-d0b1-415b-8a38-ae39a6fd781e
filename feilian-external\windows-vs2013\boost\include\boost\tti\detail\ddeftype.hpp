
//  (C) Copyright <PERSON> 2012
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).

#if !defined(BOOST_TTI_DETAIL_DEFTYPE_HPP)
#define BOOST_TTI_DETAIL_DEFTYPE_HPP

namespace boost
  {
  namespace tti
    {
    namespace detail
      {
      struct deftype
        {
        };
      }
    }
  }
  
#endif // BOOST_TTI_DETAIL_DEFTYPE_HPP
