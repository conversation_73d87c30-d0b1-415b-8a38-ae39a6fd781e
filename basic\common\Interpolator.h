﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Interpolator.h
//! <AUTHOR> 张帅（数峰科技/西交大）
//! @brief 基本插值类.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_INTERPOLATOR_H
#define _basic_common_INTERPOLATOR_H

#include <vector>

/**
 * @brief 插值函数命名空间
 * 
 */
namespace Interpolator
{

/**
 * @brief 插值函数枚举
 * 
 */
enum class FunctionType
{
    Gaussian, ///< f(r) = exp(-(epsilon * r)^2)
    ThinPlateSpline, ///< f(r) = (r^2) * log(r)
    InverseQuadratic, ///< f(r) = (1 + (epsilon * r)^2)^(-1)
    BiharmonicSpline, ///< f(r) = r
    Multiquadrics, ///< f(r) = sqrt(1 + r^2 / epsilon^2)
    Cubic ///< f(r) = r^3
};

/**
 * @brief 插值方法类
 * 
 */
class Interpolator
{
public:
    Interpolator(FunctionType functionType = FunctionType::BiharmonicSpline, const double epsilon = 2.0);

    /// API
    void   reset();
    void   addCenterPoint(const double y, const std::vector<double>& x);
    void   computeWeights(const bool useRegularization = false, const double lambda = 0.1);
    double getInterpolatedValue(const std::vector<double>& x) const;

    /// Getter methods
    const std::vector<double>&              getYs() const { return ys; }
    const std::vector<std::vector<double>>& getXs() const { return xs; }
    const std::vector<double>&              getW()  const { return w; }

private:

    /// Function type
    FunctionType functionType;

    /// A control parameter used in some kernel functions
    double epsilon;

    /// Data points
    std::vector<double>              ys;
    std::vector<std::vector<double>> xs;

    /// Weights
    std::vector<double>              w;

    /// Returns f(r)
    double getRbfValue(const double r) const;

    /// Returns f(||xj - xi||)
    double getRbfValue(const std::vector<double>& xi, const std::vector<double>& xj) const;
};

}

#endif // INTERPOLATOR_H
