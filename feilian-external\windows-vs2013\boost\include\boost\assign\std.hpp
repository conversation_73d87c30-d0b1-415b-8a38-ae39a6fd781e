// Boost.Assign library
//
//  <PERSON> <PERSON><PERSON> 2003-2004. Use, modification and
//  distribution is subject to the Boost Software License, Version
//  1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
// For more information, see http://www.boost.org/libs/assign/
//

#ifndef BOOST_ASSIGN_STD_HPP
#define BOOST_ASSIGN_STD_HPP

#if defined(_MSC_VER)
# pragma once
#endif

#include <boost/assign/std/vector.hpp>
#include <boost/assign/std/deque.hpp>
#include <boost/assign/std/list.hpp>
#include <boost/assign/std/slist.hpp>
#include <boost/assign/std/stack.hpp>
#include <boost/assign/std/queue.hpp>
#include <boost/assign/std/set.hpp>
#include <boost/assign/std/map.hpp>

#endif
