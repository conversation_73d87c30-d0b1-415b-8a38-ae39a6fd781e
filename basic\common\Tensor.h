﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Tensor.h
//! <AUTHOR>
//! @brief 张量类
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-09-22 李艳亮、乔龙
//    说明：改写与规范化
//
// 2020-07-22 数峰科技/西交大
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_common_Tensor_
#define _basic_common_Tensor_

#if defined(_BaseParallelMPI_)
#include "basic/common/BoostLib.h"
#endif
#include "basic/common/SystemControl.h"
#include "basic/common/Vector.h"

#include <vector>
#include <fstream>

/**
 * @class Tensor
 * @brief 三维张量类，支持多种张量类型和基本运算
 *
 * 该类实现了三维张量的表示和运算，支持一般张量、对角张量、对称张量和反对称张量四种类型，
 * 提供转置、行列式计算、伴随矩阵、求逆、对称/反对称分解等操作，并支持序列化和并行计算(MPI)
 */
class Tensor
{
public:
    /// @brief 张量类型枚举
    enum Type
    {
        general = 0, ///< 一般张量（9个独立分量）
        diag,        ///< 对角张量（3个独立分量）
        sym,         ///< 对称张量（6个独立分量）
        antiSym      ///< 反对称张量（3个独立分量）
    };

public:
    /**
     * @brief 默认构造函数
     * @param typ 张量类型（默认为一般张量）
     * @note 初始化为零张量
     */
    explicit Tensor(const Type &typ = Type::general) 
        : type(typ),
          xx(0.0), xy(0.0), xz(0.0),
          yx(0.0), yy(0.0), yz(0.0),
          zx(0.0), zy(0.0), zz(0.0)
    { }

    /**
     * @brief 对角张量构造函数
     * @param xx_ xx分量值
     * @param yy_ yy分量值
     * @param zz_ zz分量值
     * @note 构造一个对角张量，非对角元素初始化为零
     */
    Tensor(const Scalar &xx_, const Scalar &yy_, const Scalar &zz_)
        : type(Type::diag),
          xx(xx_), yy(yy_), zz(zz_),
          xy(0.0), xz(0.0), yz(0.0),
          yx(0.0), zx(0.0), zy(0.0)
    { }

    /**
     * @brief 对称/反对称张量构造函数
     * @param xx_ xx分量值
     * @param yy_ yy分量值
     * @param zz_ zz分量值
     * @param xy_ xy分量值
     * @param xz_ xz分量值
     * @param yz_ yz分量值
     * @param typ 张量类型（默认为对称张量）
     * @throw FatalError 如果类型不是sym或antiSym
     */
    Tensor(const Scalar &xx_, const Scalar &yy_, const Scalar &zz_,
           const Scalar &xy_, const Scalar &xz_, const Scalar &yz_,
           const Type &typ = Type::sym)
    {
        type = typ;

        switch (typ)
        {
        case Type::sym:
            xx = xx_; yy = yy_; zz = zz_;
            xy = xy_; xz = xz_; yz = yz_;
            yx = xy_; zx = xz_; zy = yz_;
            break;
        case Type::antiSym:
            xx = 0.0; yy = 0.0; zz = 0.0;
            xy = xy_; xz = xz_; yz = yz_;
            yx = -xy_; zx = -xz_; zy = -yz_;
            break;
        default:
            FatalError("Tensor::Tensor: type must be sym or antiSym for this constructor");
        }
    }

    /**
     * @brief 一般张量构造函数
     * @param xx_ xx分量值
     * @param xy_ xy分量值
     * @param xz_ xz分量值
     * @param yx_ yx分量值
     * @param yy_ yy分量值
     * @param yz_ yz分量值
     * @param zx_ zx分量值
     * @param zy_ zy分量值
     * @param zz_ zz分量值
     * @note 构造一个一般张量（9个独立分量）
     */
    Tensor(const Scalar &xx_, const Scalar &xy_, const Scalar &xz_,
           const Scalar &yx_, const Scalar &yy_, const Scalar &yz_,
           const Scalar &zx_, const Scalar &zy_, const Scalar &zz_)
        : type(Type::general),
          xx(xx_), xy(xy_), xz(xz_),
          yx(yx_), yy(yy_), yz(yz_),
          zx(zx_), zy(zy_), zz(zz_)
    { }

    /**
     * @brief 拷贝构造函数
     * @param t 源张量对象
     */
    Tensor(const Tensor &t) = default;

public: // 访问器
    /// @brief 获取张量类型
    const Type &GetType() const { return type; }    
    
    ///< @brief 获取xx分量值
    const Scalar &XX() const { return xx; }

    /// @brief 获取yy分量值
    const Scalar &YY() const { return yy; }

    /// @brief 获取zz分量值
    const Scalar &ZZ() const { return zz; }

    /// @brief 获取xy分量值
    const Scalar &XY() const { return xy; }

    /// @brief 获取xz分量值
    const Scalar &XZ() const { return xz; }

    /// @brief 获取yz分量值
    const Scalar &YZ() const { return yz; }

    /// @brief 获取yx分量值
    const Scalar &YX() const { return yx; }

    /// @brief 获取zx分量值
    const Scalar &ZX() const { return zx; }

    /// @brief 获取zy分量值
    const Scalar &ZY() const { return zy; }

public: // 设置器
    /// @brief 设置张量类型
    void SetType(const Type &value) { type = value; } 

    /// @brief 设置xx分量值
    void SetXX(const Scalar &value) { xx = value; }

    /// @brief 设置yy分量值
    void SetYY(const Scalar &value) { yy = value; }

    /// @brief 设置zz分量值
    void SetZZ(const Scalar &value) { zz = value; }

    /// @brief 设置xy分量值
    void SetXY(const Scalar &value) { xy = value; }

    /// @brief 设置xz分量值
    void SetXZ(const Scalar &value) { xz = value; }

    /// @brief 设置yz分量值
    void SetYZ(const Scalar &value) { yz = value; }

    /// @brief 设置yx分量值
    void SetYX(const Scalar &value) { yx = value; }

    /// @brief 设置zx分量值
    void SetZX(const Scalar &value) { zx = value; }
    
    /// @brief 设置zy分量值
    void SetZY(const Scalar &value) { zy = value; }

public: // 张量运算
    /**
     * @brief 计算转置张量
     * @return 当前张量的转置
     */
    Tensor Transpose() const;

    /**
     * @brief 计算行列式值
     * @return 张量的行列式值
     */
    Scalar Det() const;

    /**
     * @brief 计算伴随矩阵
     * @return 当前张量的伴随矩阵
     */
    Tensor Accompany() const;

    /**
     * @brief 求逆矩阵
     * @return 当前张量的逆矩阵
     * @throw std::runtime_error 如果矩阵不可逆
     */
    Tensor Inverse() const;

    /**
     * @brief 提取对称部分
     * @param half 是否使用一半存储（仅存储上三角部分）
     * @return 对称部分张量
     */
    Tensor Symm(const bool &half = true) const;

    /**
     * @brief 提取反对称部分
     * @param half 是否使用一半存储（仅存储上三角部分）
     * @return 反对称部分张量
     */
    Tensor AntiSymm(const bool &half = true) const;
    
    /**
     * @brief 计算迹（对角元素之和）
     * @return 张量的迹
     */
    Scalar Trace() const
    {
        return xx + yy + zz;
    }

    /**
     * @brief 向对角元素添加常数
     * @param diag 要添加的常数值
     */
    void AddDiag(const Scalar &diag)
    {
        xx += diag;
        yy += diag;
        zz += diag;
    }

public: // I/O操作
    /**
     * @brief 从文件读取张量数据
     * @param file 输入文件流
     * @param binary 是否为二进制模式（默认为true）
     * @throw std::runtime_error 如果读取失败
     */
    void Read(std::fstream &file, const bool binary = true);

    /**
     * @brief 将张量数据写入文件
     * @param file 输出文件流
     * @param binary 是否为二进制模式（默认为true）
     * @throw std::runtime_error 如果写入失败
     */
    void Write(std::fstream &file, const bool binary = true) const;

    /**
     * @brief 输出流运算符重载
     * @param os 输出文件流
     * @param t 要输出的张量
     * @return 输出文件流引用
     */
    friend std::fstream& operator<<(std::fstream &os, const Tensor &t);

public: // 运算符重载
    /**
     * @brief 双点积运算符（A:B = A_ij B_ij）
     * @param t 右操作数张量
     * @return 双点积结果（标量）
     */
    Scalar operator&&(const Tensor &t) const;

    /**
     * @brief 张量-向量乘法
     * @param vv 输入向量
     * @return 结果向量
     */
    Vector operator*(const Vector &vv) const;

    /**
     * @brief 向量-张量乘法（左乘）
     * @param v 左乘向量
     * @param t 右乘张量
     * @return 结果向量
     */
    friend Vector operator*(const Vector &v, const Tensor &t);
    
    /**
     * @brief 张量加法赋值运算符
     * @param t 要相加的张量
     * @return 当前张量的引用
     */
    Tensor& operator+=(const Tensor& t)
    {
        xx += t.xx; yy += t.yy; zz += t.zz;
        xy += t.xy; xz += t.xz; yz += t.yz;
        yx += t.yx; zx += t.zx; zy += t.zy;
        return *this;
    }

    /**
     * @brief 标量乘法运算符（标量右乘）
     * @param d 标量乘数
     * @param t 张量
     * @return 缩放后的张量
     */
    friend inline Tensor operator*(const Scalar &d, const Tensor &t)
    {
        return t * d;
    }
    
    /**
     * @brief 张量加法运算符
     * @param t 要相加的张量
     * @return 新的张量（当前张量与t的和）
     */
    Tensor operator+(const Tensor &t) const
    {
        Tensor result(*this);
        result += t;
        return result;
    }

    /**
     * @brief 张量取负运算符
     * @return 新的张量（当前张量的相反数）
     */
    Tensor operator-() const
    {
        Tensor t1;
        t1.type = type;
        t1.xx = -xx; t1.yy = -yy; t1.zz = -zz;
        t1.xy = -xy; t1.xz = -xz; t1.yz = -yz;
        t1.yx = -yx; t1.zx = -zx; t1.zy = -zy;

        return t1;
    }

    /**
     * @brief 张量减法运算符
     * @param t 要减去的张量
     * @return 新的张量（当前张量与t的差）
     */
    Tensor operator-(const Tensor &t) const
    {
        Tensor result(*this);
        result -= t;
        return result;
    }

    /**
     * @brief 张量减法赋值运算符
     * @param t 要减去的张量
     * @return 当前张量的引用
     */
    Tensor& operator-=(const Tensor &t)
    {
        xx -= t.xx; yy -= t.yy; zz -= t.zz;
        xy -= t.xy; xz -= t.xz; yz -= t.yz;
        yx -= t.yx; zx -= t.zx; zy -= t.zy;
        return *this;
    }

    /**
     * @brief 标量乘法运算符（标量右乘）
     * @param s 标量乘数
     * @return 缩放后的张量
     */
    Tensor operator*(const Scalar &s) const
    {
        return Tensor(xx * s, xy * s, xz * s,
                      yx * s, yy * s, yz * s,
                      zx * s, zy * s, zz * s);
    }

    /**
     * @brief 标量乘法赋值运算符
     * @param s 标量乘数
     * @return 当前张量的引用
     */
    Tensor &operator*=(const Scalar &s)
    {
        xx *= s; yy *= s; zz *= s;
        xy *= s; xz *= s; yz *= s;
        yx *= s; zx *= s; zy *= s;
        return *this;
    }

    /**
     * @brief 张量乘法运算符
     * @param t 右乘张量
     * @return 矩阵乘积结果（新张量）
     */
    Tensor operator*(const Tensor& t) const
    {    
        return Tensor(
            xx * t.xx + xy * t.yx + xz * t.zx, 
            xx * t.xy + xy * t.yy + xz * t.zy, 
            xx * t.xz + xy * t.yz + xz * t.zz,
            yx * t.xx + yy * t.yx + yz * t.zx, 
            yx * t.xy + yy * t.yy + yz * t.zy, 
            yx * t.xz + yy * t.yz + yz * t.zz,
            zx * t.xx + zy * t.yx + zz * t.zx, 
            zx * t.xy + zy * t.yy + zz * t.zy, 
            zx * t.xz + zy * t.yz + zz * t.zz
        );
    }

    /**
     * @brief 标量除法运算符
     * @param s 标量除数
     * @return 缩放后的张量
     * @throw std::invalid_argument 如果s接近零
     */
    Tensor operator/(const Scalar &s) const
    {
#if defined(_DevelopMode_)
        if (std::abs(s) < std::numeric_limits<Scalar>::epsilon())
        {
            throw std::invalid_argument("Tensor division by zero");
        }
#endif
        return (*this) * (1.0 / s);
    }

    /**
     * @brief 向量外积（返回二阶张量）
     * @param p1 左操作数向量
     * @param p2 右操作数向量
     * @return 外积结果（二阶张量）
     */
    friend inline Tensor operator*(const Vector &p1, const Vector &p2)
    {
        return Tensor(
            p1.x * p2.x, p1.x * p2.y, p1.x * p2.z,
            p1.y * p2.x, p1.y * p2.y, p1.y * p2.z,
            p1.z * p2.x, p1.z * p2.y, p1.z * p2.z
        );
    }

    /**
     * @brief 张量-向量点积（右乘）
     * @param t 张量
     * @param v 向量
     * @return 点积结果（向量）
     */
    friend inline Vector Dot(const Tensor &t, const Vector &v)
    {
        return t * v;
    }

    /**
     * @brief 向量-张量点积（左乘）
     * @param v 向量
     * @param t 张量
     * @return 点积结果（向量）
     */
    friend inline Vector Dot(const Vector &v, const Tensor &t)
    {
        return v * t;
    }

private:
    /**
     * @brief 张量类型说明
     * - type == general :
     *   | xx  xy  xz |
     *   | yx  yy  yz |
     *   | zx  zy  zz |
     *
     * - type == diag :
     *   | xx   0   0 |
     *   |  0  yy   0 |
     *   |  0   0  zz |
     *
     * - type == sym :
     *   | xx  xy  xz |
     *   | xy  yy  yz |
     *   | xz  yz  zz |
     *
     * - type == antiSym :
     *   |   0  xy  xz |
     *   | -xy   0  yz |
     *   | -xz -yz   0 |
     */
    Type type;

    // 张量分量（按行主序存储）
    Scalar xx;  ///< 第0行第0列分量
    Scalar xy;  ///< 第0行第1列分量
    Scalar xz;  ///< 第0行第2列分量
    Scalar yx;  ///< 第1行第0列分量
    Scalar yy;  ///< 第1行第1列分量
    Scalar yz;  ///< 第1行第2列分量
    Scalar zx;  ///< 第2行第0列分量
    Scalar zy;  ///< 第2行第1列分量
    Scalar zz;  ///< 第2行第2列分量

#if defined(_BaseParallelMPI_)
public:
    /**
     * @brief MPI序列化函数
     * @tparam Archive 序列化类型
     * @param ar 序列化对象
     * @param version 版本号
     */
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & type;
        ar & xx & xy & xz;
        ar & yx & yy & yz;
        ar & zx & zy & zz;
    }
#endif
};

/// 张量零
#define Tensor0 (Tensor(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0))

/// 单位张量
#define Tensor1 (Tensor(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

#endif
