﻿#include "feilian-specialmodule/staticAeroelastic/method/AeroStaticMethod.h"

#if defined(_BasePlatformWinddows_)
#include <direct.h>
#else
#include <unistd.h>
#endif

AeroStaticMethod::AeroStaticMethod(std::vector<struct Solver::AeroMeshData> aeroMeshData_, std::vector<struct Solver::SolidMeshData> solidMeshData_, SubMesh *subMesh_, const Package::FlowPackage *data_, SubMesh *globalMesh_)
    : v_aeroMeshData(aeroMeshData_), v_solidMeshData(solidMeshData_), subMesh(subMesh_), data(data_), globalMesh(globalMesh_)
{

    npart = GetMPISize();
    processorID = GetMPIRank();
    this->BuildMapRelation();
    pressure_ref = data->GetFlowConfigure().GetFlowReference().staticPressure;

    // 初始化松弛因子相关变量
    previousMaxDeformChange = 0.0;
    relaxationIterationCount = 0;
    relaxationHistory.reserve(100); // 预留空间

    // 提取原始子网格中的所有网格点坐标并存储v_subNode（全局变量）
    int n2 = subMesh->GetNodeNumber();
    v_subNode.resize(n2);
    for (int i = 0; i < n2; i++)
    {
        Node nodeTemp = subMesh->GetNode(i);
        v_subNode[i] = nodeTemp;
    }
}

AeroStaticMethod::~AeroStaticMethod()
{
    v_wallboundaryID.clear();
}

void AeroStaticMethod::calculateCouplmat()
{

    if (processorID == 0) //***0进程向其他进程发送全部部件的转换矩阵容器hm_all
    {
        std::cout << "Process" + ToString(GetMPIRank()) + "矩阵计算开始" << std::endl;
    }

    int n_boundary = v_solidMeshData.size(); // 结构部件数目
    hm_all.resize(n_boundary);

    //***串行情况，0进程直接计算
    if (npart == 1)
    {
        for (int i = 0; i < n_boundary; i++)
        {
            int ncsd = v_solidMeshData[i].ncsd;
            std::vector<double> xso, yso, zso; // 结构点坐标
            std::vector<double> xao, yao, zao; // 气动点坐标

            xso.resize(ncsd);
            yso.resize(ncsd);
            zso.resize(ncsd);
            for (int j = 0; j < ncsd; j++)
            {
                xso[j] = v_solidMeshData[i].v_xyz[j].X();
                yso[j] = v_solidMeshData[i].v_xyz[j].Y();
                zso[j] = v_solidMeshData[i].v_xyz[j].Z();
            }
            int id_aero = v_solidMeshData[i].id_aero;
            int ncfd = v_aeroMeshData[id_aero].ncfd;
            xao.resize(ncfd);
            yao.resize(ncfd);
            zao.resize(ncfd);
            for (int j = 0; j < ncfd; j++)
            {
                xao[j] = v_aeroMeshData[id_aero].v_xyz_o[j].X();
                yao[j] = v_aeroMeshData[id_aero].v_xyz_o[j].Y();
                zao[j] = v_aeroMeshData[id_aero].v_xyz_o[j].Z();
            }

            hm_all[i] = this->calculateHm_adv(xso, yso, zso, xao, yao, zao, ncfd, ncsd);
            xso.clear();
            yso.clear();
            zso.clear();
            xao.clear();
            yao.clear();
            zao.clear();
        }
    }

    //***并行情况,部件的数目大于进程的数目
    if (npart != 1)
    {
        if (processorID != 0) //***除去0进程外的其他进程负责计算n_boundary个部件的转换矩阵hm,将n_boundary个部件按顺序分发给n_part个进程，每个进程计算n(n>=1)个部件的转换矩阵hm
        {
            for (int i = 0; i < n_boundary; i++)
            {
                int id = i % (npart - 1);
                if (processorID == id + 1) // 第id+1个进程负责计算第i个部件
                {
                    int ncsd = v_solidMeshData[i].ncsd;
                    std::vector<double> xso, yso, zso; // 结构点坐标
                    std::vector<double> xao, yao, zao; // 气动点坐标

                    xso.resize(ncsd);
                    yso.resize(ncsd);
                    zso.resize(ncsd);
                    for (int j = 0; j < ncsd; j++)
                    {
                        xso[j] = v_solidMeshData[i].v_xyz[j].X();
                        yso[j] = v_solidMeshData[i].v_xyz[j].Y();
                        zso[j] = v_solidMeshData[i].v_xyz[j].Z();
                    }
                    int id_aero = v_solidMeshData[i].id_aero;
                    int ncfd = v_aeroMeshData[id_aero].ncfd;
                    xao.resize(ncfd);
                    yao.resize(ncfd);
                    zao.resize(ncfd);
                    for (int j = 0; j < ncfd; j++)
                    {
                        xao[j] = v_aeroMeshData[id_aero].v_xyz_o[j].X();
                        yao[j] = v_aeroMeshData[id_aero].v_xyz_o[j].Y();
                        zao[j] = v_aeroMeshData[id_aero].v_xyz_o[j].Z();
                    }
                    hm_all[i] = this->calculateHm_adv(xso, yso, zso, xao, yao, zao, ncfd, ncsd);
                    xso.clear();
                    yso.clear();
                    zso.clear();
                    xao.clear();
                    yao.clear();
                    zao.clear();
                }
            }
        }
    }

    mpi_world.barrier();
}

std::vector<std::vector<double>> AeroStaticMethod::calculateHm(std::vector<double> xso, std::vector<double> yso, std::vector<double> zso, std::vector<double> xao, std::vector<double> yao, std::vector<double> zao, int nanodes, int nsnodes)
{
    std::vector<std::vector<double>> hm;
#if defined(_EnableMKL_)
    int i, j, k, info;
    double kx = 1.0, ky = 1.0, kz = 1.0, length, temp;
    std::string basisfun = "WENDLAND C2";
    //***css矩阵
    std::vector<std::vector<double>> css;
    css.resize(nsnodes + 4);
    for (int i = 0; i < nsnodes + 4; i++)
        css[i].resize(nsnodes + 4);
    //***
    std::vector<std::vector<double>> cssinv;
    cssinv.resize(nsnodes + 4);
    for (int i = 0; i < nsnodes + 4; i++)
        cssinv[i].resize(nsnodes + 4);
    //***
    std::vector<std::vector<double>> afs;
    afs.resize(nanodes);
    for (int i = 0; i < nanodes; i++)
        afs[i].resize(nsnodes + 4);
    //***
    std::vector<int> ipiv;
    ipiv.resize(nsnodes + 4);
    //***
    std::vector<std::vector<double>> hmat;
    hmat.resize(nanodes);
    for (int i = 0; i < nanodes; i++)
        hmat[i].resize(nsnodes + 4);
    //***
    hm.resize(nanodes);
    for (int i = 0; i < nanodes; i++)
        hm[i].resize(nsnodes);

    //***构造矩阵css
    for (i = 0; i < 4; ++i)
    {
        for (j = 0; j < 4; ++j)
        {
            css[i][j] = 0.0;
        }
    }
    for (j = 0; j < nsnodes; ++j)
    {
        css[0][j + 4] = 1.0;
        css[1][j + 4] = xso[j];
        css[2][j + 4] = yso[j];
        css[3][j + 4] = zso[j];
        css[j + 4][0] = 1.0;
        css[j + 4][1] = xso[j];
        css[j + 4][2] = yso[j];
        css[j + 4][3] = zso[j];
    }
    Scalar sr = data->GetFlowConfigure().GetStaticAero().CSDParameter.Radiu; //*支撑半径
    for (i = 0; i < nsnodes; ++i)
    {
        for (j = 0; j < nsnodes; ++j)
        {
            length = sqrt(kx * pow(xso[i] - xso[j], 2) + ky * pow(yso[i] - yso[j], 2) + kz * pow(zso[i] - zso[j], 2));
            css[i + 4][j + 4] = calculatecss(basisfun, length, sr);
        }
    }

    //***构造单位矩阵
    for (i = 0; i < nsnodes + 4; ++i)
    {
        for (j = 0; j < nsnodes + 4; ++j)
        {
            cssinv[i][j] = (i == j) ? 1.0 : 0.0;
        }
    }

    /* //***Eigen库求逆，css*x=I(单位矩阵)，x=css的逆
     Eigen::MatrixXd css_eigen(nsnodes + 4, nsnodes + 4);
     for (i = 0; i < nsnodes + 4; ++i) {
         for (j = 0; j < nsnodes + 4; ++j) {
             css_eigen(i, j) = css[i][j];
         }
     }
     Eigen::MatrixXd cssinv_eigen = css_eigen.inverse(); //***求逆操作
     for (i = 0; i < nsnodes + 4; ++i) {
       for (j = 0; j < nsnodes + 4; ++j) {
           cssinv[i][j] = cssinv_eigen(i, j);   //将逆矩阵放入矩阵cssinv中
       }
     }

     for (i = 0; i < nsnodes + 4; ++i)
     {
       std::vector<double> ipi;
       ipi.resize( nsnodes + 4);
       ipi = Gaussin_L(css, cssinv_t[i]);

       for (j = 0; j < nsnodes + 4; ++j)
          cssinv[j][i] = ipi[j];

       ipi.clear();
     }*/

    cssinv = Lapack_degv(css, nsnodes + 4);

    //***创建afs矩阵
    for (i = 0; i < nanodes; ++i)
    {
        afs[i][0] = 1.0;
        afs[i][1] = xao[i];
        afs[i][2] = yao[i];
        afs[i][3] = zao[i];
    }
    for (i = 0; i < nanodes; ++i)
    {
        for (j = 4; j < nsnodes + 4; ++j)
        {
            length = sqrt(kx * pow(xao[i] - xso[j - 4], 2) + ky * pow(yao[i] - yso[j - 4], 2) + kz * pow(zao[i] - zso[j - 4], 2));
            afs[i][j] = calculatecss(basisfun, length, sr);
        }
    }

    //**构造hmat矩阵，hmat = afs*cssinv
  ARI_OMP(parallel for schedule(static) private(temp,  j, k))
  for (i = 0; i < nanodes; ++i)
  {
      for (j = 0; j < nsnodes + 4; ++j)
      {
          temp = 0.0;
          for (k = 0; k < nsnodes + 4; ++k)
          {
              temp += afs[i][k] * cssinv[k][j];
          }
          hmat[i][j] = temp;
      }
  }

  //**将hmat分成abcd四个块矩阵，其中b和d是插值矩阵
  for (i = 0; i < nanodes; ++i)
  {
      for (j = 0; j < nsnodes; ++j)
      {
          hm[i][j] = hmat[i][j + 4];
      }
  }

#endif
  return hm;
}

std::vector<std::vector<double>> AeroStaticMethod::calculateHm_adv(std::vector<double> xso, std::vector<double> yso, std::vector<double> zso, std::vector<double> xao, std::vector<double> yao, std::vector<double> zao, int nanodes, int nsnodes)
{

    std::vector<std::vector<double>> hm;
#if defined(_EnableMKL_)

    int i, j, k, info;
    double kx = 1.0, ky = 1.0, kz = 1.0, length, temp;
    std::string basisfun = "WENDLAND C2";

    //***css矩阵
    double *css = new double[(nsnodes + 4) * (nsnodes + 4)];
    double *cssinv = new double[(nsnodes + 4) * (nsnodes + 4)];

    double *afs = new double[nanodes * (nsnodes + 4)];
    double *hmat = new double[nanodes * (nsnodes + 4)];

    lapack_int *ipiv = new lapack_int[nsnodes + 4];

    //***hm矩阵

    hm.resize(nanodes);
    for (int i = 0; i < nanodes; i++)
        hm[i].resize(nsnodes);

    //***css矩阵
    for (i = 0; i < 4; ++i)
    {
        for (j = 0; j < 4; ++j)
        {
            css[i * (nsnodes + 4) + j] = 0.0;
        }
    }
    for (j = 0; j < nsnodes; ++j)
    {
        css[j + 4] = 1.0;
        css[1 * (nsnodes + 4) + j + 4] = xso[j];
        css[2 * (nsnodes + 4) + j + 4] = yso[j];
        css[3 * (nsnodes + 4) + j + 4] = zso[j];
        css[(j + 4) * (nsnodes + 4) + 0] = 1.0;
        css[(j + 4) * (nsnodes + 4) + 1] = xso[j];
        css[(j + 4) * (nsnodes + 4) + 2] = yso[j];
        css[(j + 4) * (nsnodes + 4) + 3] = zso[j];
    }
    Scalar sr = data->GetFlowConfigure().GetStaticAero().CSDParameter.Radiu; //*支撑半径

    for (i = 0; i < nsnodes; ++i)
    {
        for (j = 0; j < nsnodes; ++j)
        {
            length = sqrt(kx * pow(xso[i] - xso[j], 2) + ky * pow(yso[i] - yso[j], 2) + kz * pow(zso[i] - zso[j], 2));
            css[(i + 4) * (nsnodes + 4) + j + 4] = calculatecss(basisfun, length, sr);
        }
    }

    //***
    for (i = 0; i < nsnodes + 4; ++i)
    {
        for (j = 0; j < nsnodes + 4; ++j)
        {
            cssinv[i * (nsnodes + 4) + j] = (i == j) ? 1.0 : 0.0;
        }
    }
    int n = nsnodes + 4;
    info = LAPACKE_dgesv(LAPACK_ROW_MAJOR, n, n, css, n, ipiv, cssinv, n);

    //***构造afs矩阵
    for (i = 0; i < nanodes; ++i)
    {

        afs[i * (nsnodes + 4)] = 1.0;
        afs[i * (nsnodes + 4) + 1] = xao[i];
        afs[i * (nsnodes + 4) + 2] = yao[i];
        afs[i * (nsnodes + 4) + 3] = zao[i];

        for (j = 0; j < 4; ++j)
            hmat[i * (nsnodes + 4) + j] = 0.0;
    }
    for (i = 0; i < nanodes; ++i)
    {
        for (j = 4; j < nsnodes + 4; ++j)
        {
            length = sqrt(kx * pow(xao[i] - xso[j - 4], 2) + ky * pow(yao[i] - yso[j - 4], 2) + kz * pow(zao[i] - zso[j - 4], 2));
            afs[i * (nsnodes + 4) + j] = calculatecss(basisfun, length, sr);
            hmat[i * (nsnodes + 4) + j] = 0.0;
        }
    }

    cblas_dgemm(CblasRowMajor, CblasNoTrans, CblasNoTrans, nanodes, nsnodes + 4, nsnodes + 4, 1.0, afs, nsnodes + 4, cssinv, nsnodes + 4, 0.0, hmat, nsnodes + 4);

    clock_t start = clock();
    //**将hmat分成abcd四个块矩阵，其中bd插值矩阵
    for (i = 0; i < nanodes; ++i)
    {
        for (j = 0; j < nsnodes; ++j)
        {
            int nn = i * (nsnodes + 4) + j + 4;
            hm[i][j] = hmat[nn];
        }
    }
    clock_t end = clock();
    double elapsed = double(end - start) / CLOCKS_PER_SEC;
    std::cout << "GetMPIRank : " << ToString(GetMPIRank()) << "  use time : " << elapsed << " second" << std::endl;
#endif
    return hm;
}

Scalar AeroStaticMethod::calculatecss(std::string bfun, double dis, double sra)
{
    double tmp;
    if (bfun.substr(0, 11) == "WENDLAND C2" || bfun.substr(0, 11) == "wendland c2")
    {
        if (dis < sra)
        {
            tmp = pow(1 - dis / sra, 4) * (4 * dis / sra + 1);
        }
        else
        {
            tmp = 0.0;
        }
    }
    else if (bfun.substr(0, 5) == "GAUSS" || bfun.substr(0, 5) == "gauss")
    {

        if (dis < sra)
        {
            tmp = exp(-dis / sra);
        }
        else
        {
            tmp = 0.0;
        }
    }
    else if (bfun.substr(0, 11) == "WENDLAND C0" || bfun.substr(0, 11) == "wendland c0")
    {
        if (dis < sra)
        {
            tmp = pow(1 - dis / sra, 2);
        }
        else
        {
            tmp = 0.0;
        }
    }
    else if (bfun.substr(0, 3) == "TPS" || bfun.substr(0, 3) == "tps")
    {
        tmp = pow(dis, 2) * log10(dis);
    }
    else
    {
        std::cerr << "no basic function name is founded! stop" << std::endl;
        exit(1);
    }
    return tmp;
}

void AeroStaticMethod::Calcaeroforce()
{
    //**从流场包data中提取出当前分区的流场变量p
    const ElementField<Scalar> &p = *data->GetField().pressure;

    for (int i = 0; i < v_aeroMeshData.size(); i++)
    {
        int ncfd = v_aeroMeshData[i].ncfd;
        v_aeroMeshData[i].v_force.clear();
        v_aeroMeshData[i].v_force.resize(ncfd);
        for (int j = 0; j < ncfd; j++)
            v_aeroMeshData[i].v_force[j] = Vector0;
    }

    for (int i = 0; i < v_wallboundaryID.size(); i++) // 循环当前分区的物面边界（部件）数目
    {
        const int &n_face = subMesh->GetBoundaryFaceSize(v_wallboundaryID[i]); // 当前分区下第v_wallboundaryID[i]个物面边界的面数目
        for (int j = 0; j < n_face; j++)
        {
            const int &faceID = subMesh->GetBoundaryFaceID(v_wallboundaryID[i], j); // 面编号
            const Face &face = subMesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();    // 第faceID个面所在的单元编号
            const int &neighID = face.GetNeighborID(); // 第faceID个面的邻居单元编号

            Scalar face_Pressure = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID) - 2 * pressure_ref); // 该面的压强 = （第ownerID个单元的压强 + 第neighID个单元的压强）/2

            if (data->GetFlowConfigure().GetPreprocess().dualMeshFlag)
            {
                Vector force_node = face_Pressure * face.GetArea() * face.GetNormal(); //**该面中第k个点的压力 = 该面的压强 * 第k个点的面积
                const int faceIDGlobal = subMesh->GetFaceGlobalID(faceID);
                auto range = map_data.equal_range(faceIDGlobal);
                for (auto it = range.first; it != range.second; ++it)
                {
                    std::vector<int> location = it->second;
                    Vector &node = v_aeroMeshData[location[0]].v_force[location[1]]; // 赋值v_aeroMeshDatap中的变量v_force
                    node.SetX(node.X() + force_node.X());
                    node.SetY(node.Y() + force_node.Y());
                    node.SetZ(node.Z() + force_node.Z());
                }
            }
            else
            {
                std::vector<Node> faceNodeList;
                for (int k = 0; k < face.GetNodeSize(); k++) // nodeID
                {
                    int tempID = face.GetNodeID(k);
                    faceNodeList.push_back(subMesh->GetNode(tempID)); //**组成该面的点的编号
                }
                const Node &faceCenter = face.GetCenter(); //**面心值
                std::vector<Vector> faceNodeArea;
                faceNodeArea = this->CalculateNodeArea(faceNodeList, faceCenter); //**计算各个点在该面中所占面积
                for (int k = 0; k < face.GetNodeSize(); k++)                      //
                {
                    int nodeGlobalID = subMesh->GetNodeGlobalID(face.GetNodeID(k));
                    Vector force_node = face_Pressure * faceNodeArea[k]; //**该面中第k个点的压力 = 该面的压强 * 第k个点的面积
                    auto range = map_data.equal_range(nodeGlobalID);
                    for (auto it = range.first; it != range.second; ++it)
                    {
                        std::vector<int> location = it->second;
                        Vector &node = v_aeroMeshData[location[0]].v_force[location[1]]; // 赋值v_aeroMeshDatap中的变量v_force
                        node.SetX(node.X() + force_node.X());
                        node.SetY(node.Y() + force_node.Y());
                        node.SetZ(node.Z() + force_node.Z());
                    }
                }
            }
        }
    }
    //*测试添加并行等待语句

    if (processorID != 0)
    {
        std::vector<std::vector<Node>> v_force_transfer;
        v_force_transfer.resize(v_aeroMeshData.size());
        for (int i = 0; i < v_aeroMeshData.size(); i++)
        {
            int n = v_aeroMeshData[i].ncfd;
            v_force_transfer[i].resize(n);
            for (int j = 0; j < n; j++) //   for (int j = 0; j < n; i++)
                v_force_transfer[i][j] = v_aeroMeshData[i].v_force[j];
        }

        mpi_world.send(0, 6, v_force_transfer);
    }
    else
    {

        for (int i = 1; i < npart; i++)
        {
            std::vector<std::vector<Node>> v_force_transfer;
            mpi_world.recv(i, 6, v_force_transfer);
            for (int j = 0; j < v_aeroMeshData.size(); j++)
            {
                for (int k = 0; k < v_force_transfer[j].size(); k++)
                {
                    v_aeroMeshData[j].v_force[k].SetX(v_aeroMeshData[j].v_force[k].X() + v_force_transfer[j][k].X());
                    v_aeroMeshData[j].v_force[k].SetY(v_aeroMeshData[j].v_force[k].Y() + v_force_transfer[j][k].Y());
                    v_aeroMeshData[j].v_force[k].SetZ(v_aeroMeshData[j].v_force[k].Z() + v_force_transfer[j][k].Z());
                }
            }
            v_force_transfer.clear();
        }
    }

    if (processorID == 0)
    {

        for (int i = 1; i < npart; i++)
            mpi_world.send(i, 7, v_aeroMeshData);
    }
    else
    {
        mpi_world.recv(0, 7, v_aeroMeshData);
    }
}

std::vector<Vector> AeroStaticMethod::CalculateNodeArea(std::vector<Node> faceNodeList, const Node &faceCenter)
{
    //**根据组成面的点列表以及面心，计算各点面积
    int nodesize = faceNodeList.size();
    std::vector<Node> faceNodeArea(nodesize, Vector0);

    for (int i = 0; i < nodesize; i++)
    {

        Node node1, node2;
        int ID1, ID2;
        if (i != nodesize - 1)
        {
            node1 = faceNodeList[i];
            node2 = faceNodeList[i + 1];
            ID1 = i;
            ID2 = i + 1;
        }
        else
        {
            node1 = faceNodeList[i];
            node2 = faceNodeList[0];
            ID1 = i;
            ID2 = 0;
        }

        Node edgeCenter = 0.5 * (node1 + node2);
        Vector halfEdge = 0.5 * (node2 - node1);

        Vector area;

        area = 0.5 * (halfEdge ^ (faceCenter - edgeCenter));

        faceNodeArea[ID1] += area;
        faceNodeArea[ID2] += area;
    }

    return faceNodeArea;
}

void AeroStaticMethod::SetV_wallboundaryID(std::vector<int> v_wallboundaryID_, std::multimap<int, std::vector<int>> map_data_, int ncsdt_)
{

    ncsdt = ncsdt_;
    int n = v_wallboundaryID_.size();
    v_wallboundaryID.resize(n);
    for (int i = 0; i < n; i++)
        v_wallboundaryID[i] = v_wallboundaryID_[i];
    for (auto it = map_data_.begin(); it != map_data_.end(); it++)
        map_data.insert(std::make_pair(it->first, it->second));
}

void AeroStaticMethod::CalcuateForceSolid()
{
    int n_boundary = v_solidMeshData.size();

    //**串行情况，0进程直接计算
    if (npart == 1)
    {
        for (int i = 0; i < n_boundary; i++)
        {
            Scalar faxtotal[2] = {0};
            Scalar faytotal[2] = {0};
            Scalar faztotal[2] = {0};

            int ncsd = v_solidMeshData[i].ncsd;
            v_solidMeshData[i].v_force.clear();
            v_solidMeshData[i].v_force.resize(ncsd);

            int id_aero = v_solidMeshData[i].id_aero; // 结构数据包对应气动数据包位置
            int ncfd = v_aeroMeshData[id_aero].ncfd;

            for (int j = 0; j < ncsd; j++)
                v_solidMeshData[i].v_force[j] = Vector0;

            // 气动力三方向总和
            for (int j = 0; j < ncfd; j++)
            {
                faxtotal[0] = faxtotal[0] + v_aeroMeshData[id_aero].v_force[j].X();
                faytotal[0] = faytotal[0] + v_aeroMeshData[id_aero].v_force[j].Y();
                faztotal[0] = faztotal[0] + v_aeroMeshData[id_aero].v_force[j].Z();
            }

            // 由hm矩阵及气动力计算结构力
            for (int j = 0; j < ncsd; j++)
            {
                for (int k = 0; k < ncfd; k++)
                {
                    Vector force = v_aeroMeshData[id_aero].v_force[k];
                    v_solidMeshData[i].v_force[j].SetX(v_solidMeshData[i].v_force[j].X() + hm_all[i][k][j] * force.X());
                    v_solidMeshData[i].v_force[j].SetY(v_solidMeshData[i].v_force[j].Y() + hm_all[i][k][j] * force.Y());
                    v_solidMeshData[i].v_force[j].SetZ(v_solidMeshData[i].v_force[j].Z() + hm_all[i][k][j] * force.Z());
                }
                faxtotal[1] = faxtotal[1] + v_solidMeshData[i].v_force[j].X();
                faytotal[1] = faytotal[1] + v_solidMeshData[i].v_force[j].Y();
                faztotal[1] = faztotal[1] + v_solidMeshData[i].v_force[j].Z(); // 结构力三方向总和
            }

            Print("\n part is " + v_aeroMeshData[id_aero].PartName + "  Fax,Fay,Faz = " + std::to_string((long double)faxtotal[0]) + "  " + std::to_string((long double)faytotal[0]) + "  " + std::to_string((long double)faztotal[0]));
            Print(" part is " + v_solidMeshData[i].PartName + "  Fsx,Fsy,Fsz = " + std::to_string((long double)faxtotal[1]) + "  " + std::to_string((long double)faytotal[1]) + "  " + std::to_string((long double)faztotal[1]));
        }
    }

    //**并行情况,将n_boundary个结构部件按顺序循环分发给npart个进程
    if (npart != 1)
    {
        if (processorID != 0)
        {
            for (int i = 0; i < n_boundary; i++)
            {

                Scalar faxtotal[2] = {0};
                Scalar faytotal[2] = {0};
                Scalar faztotal[2] = {0};

                int id = i % (npart - 1);
                if (processorID == id + 1)
                {

                    int ncsd = v_solidMeshData[i].ncsd;
                    v_solidMeshData[i].v_force.clear();
                    v_solidMeshData[i].v_force.resize(ncsd);

                    int id_aero = v_solidMeshData[i].id_aero; // 结构数据包对应气动数据包位置
                    int ncfd = v_aeroMeshData[id_aero].ncfd;

                    for (int j = 0; j < ncsd; j++)
                        v_solidMeshData[i].v_force[j] = Vector0;

                    // 气动力三方向总和
                    for (int j = 0; j < ncfd; j++)
                    {
                        faxtotal[0] = faxtotal[0] + v_aeroMeshData[id_aero].v_force[j].X();
                        faytotal[0] = faytotal[0] + v_aeroMeshData[id_aero].v_force[j].Y();
                        faztotal[0] = faztotal[0] + v_aeroMeshData[id_aero].v_force[j].Z();
                    }

                    // 由hm矩阵及气动力计算结构力
                    for (int j = 0; j < ncsd; j++)
                    {
                        for (int k = 0; k < ncfd; k++)
                        {
                            Vector force = v_aeroMeshData[id_aero].v_force[k];
                            v_solidMeshData[i].v_force[j].SetX(v_solidMeshData[i].v_force[j].X() + hm_all[i][k][j] * force.X());
                            v_solidMeshData[i].v_force[j].SetY(v_solidMeshData[i].v_force[j].Y() + hm_all[i][k][j] * force.Y());
                            v_solidMeshData[i].v_force[j].SetZ(v_solidMeshData[i].v_force[j].Z() + hm_all[i][k][j] * force.Z());
                        }
                        faxtotal[1] = faxtotal[1] + v_solidMeshData[i].v_force[j].X();
                        faytotal[1] = faytotal[1] + v_solidMeshData[i].v_force[j].Y();
                        faztotal[1] = faztotal[1] + v_solidMeshData[i].v_force[j].Z(); // 结构力三方向总和
                    }

                    Print("\n part is " + v_solidMeshData[i].PartName + "  fax,fay,faz = " + std::to_string((long double)faxtotal[0]) + "  " + std::to_string((long double)faytotal[0]) + "  " + std::to_string((long double)faztotal[0]));
                    Print(" part is " + v_solidMeshData[i].PartName + "  fsx,fsy,fsz = " + std::to_string((long double)faxtotal[1]) + "  " + std::to_string((long double)faytotal[1]) + "  " + std::to_string((long double)faztotal[1]));

                    mpi_world.send(0, i, v_solidMeshData[i].v_force); // 将计算完成的部件结构力发送给0进程
                }
            }
        }
        else
        {
            for (int i = 0; i < n_boundary; i++)
            {
                int id = i % (npart - 1);
                mpi_world.recv(id + 1, i, v_solidMeshData[i].v_force);
            }
        }

        if (processorID == 0)
        {
            for (int i = 1; i < npart; i++)
                mpi_world.send(i, 110, v_solidMeshData);
        }
        else
        {
            mpi_world.recv(0, 110, v_solidMeshData);
        }
    }
}

void AeroStaticMethod::WriteForceSolid()
{

    if (processorID == 0)
    {
        std::vector<std::string> forcedat;
        std::vector<int> location;
        std::fstream file;
        std::string line;
        file.open("bdf/FOR.DAT", std::fstream::in);
        while (std::getline(file, line))
        {
            forcedat.push_back(line);
            if (line.find("$ Nodal Forces of Load Set :") != std::string::npos)
                location.push_back(forcedat.size() - 1);
        }
        file.close(); // 读一次for.dat，将内容作为全局变量

        //*写for.dat前几行信息LOAD
        file.open("bdf/FOR.DAT", std::fstream::out);
        for (int i = 0; i < location[0]; i++)
        {
            file << forcedat[i] << std::endl;
        }

        double eps = 1e-6;
        double nxyz[3] = {0.0, 0.0, 1.0};
        std::string RwingDir = data->GetFlowConfigure().GetStaticAero().CSDParameter.RwingDir; //.GetStaticAero().staticaero.RwingDir;
        std::string FuseDir = data->GetFlowConfigure().GetStaticAero().CSDParameter.FuseDir;
        Eigen::Vector3d translation_vector(0, 0, 0); // 平移向量   /////////////////////位移不需要平移
        int index = 1;
        //*循环每个部件写力
        for (int partIdx = 0; partIdx < location.size() && partIdx < v_solidMeshData.size(); partIdx++)
        {
            file << forcedat[location[partIdx]] << std::endl;
            for (int j = 0; j < v_solidMeshData[partIdx].ncsd; j++)
            {
                Eigen::Vector3d force(v_solidMeshData[partIdx].v_force[j].X(), v_solidMeshData[partIdx].v_force[j].Y(), v_solidMeshData[partIdx].v_force[j].Z());
                Vector temp = rotate_and_translate_fem_coordinates(force, translation_vector, RwingDir, FuseDir, index);
                Node node = temp * data->GetFlowConfigure().GetStaticAero().CSDParameter.ForceScale;
                double fsum = sqrt(node.X() * node.X() + node.Y() * node.Y() + node.Z() * node.Z());

                if (fsum > eps)
                {
                    nxyz[0] = node.X() / fsum;
                    nxyz[1] = node.Y() / fsum;
                    nxyz[2] = node.Z() / fsum;
                }
                else
                {
                    fsum = eps;
                }

                std::ostringstream oss;
                oss << std::scientific;
                oss << std::setprecision(4);
                oss << fsum;
                std::string str = oss.str();
                oss.clear();

                std::string sfsum = "        ";
                // 安全地复制字符串，避免越界访问
                int copyLen = std::min(6, static_cast<int>(str.length()));
                for (int k = 0; k < copyLen; k++)
                    sfsum[k] = str[k];
                if (abs(fsum) >= 1)
                {
                    sfsum[6] = '+';
                }
                else
                {
                    sfsum[6] = '-';
                }
                sfsum[7] = str[str.size() - 1];

                std::ostringstream oss1;
                std::string str1;
                for (int nxyzIdx = 0; nxyzIdx < 3; nxyzIdx++)
                {
                    oss1 << std::fixed << std::setprecision(5) << std::setw(8) << nxyz[nxyzIdx]; // 保留小数点后5位，总宽度8位
                    str1 = oss1.str();
                }
                oss1.clear();

                std::string line1 = forcedat[location[partIdx] + j + 1].substr(0, 32);
                std::string line2 = sfsum;
                std::string line3 = str1;

                file << line1 << line2 << line3 << std::endl;
            }
        }
        file.close();
    }
}

void AeroStaticMethod::UseNastAndReadF06()
{
    //**0进程读取f06文件
    if (processorID == 0)
    {
        std::string stringTemp = data->GetFlowConfigure().GetStaticAero().CSDParameter.MeshName;
        std::string bdfName;
        std::string::size_type pos = stringTemp.rfind(".");
        if (pos != stringTemp.npos)
            bdfName = stringTemp.erase(pos, -1);

        // 获取当前路径
        char cwd[1024];
#if defined(_BasePlatformWinddows_)
        if (_getcwd(cwd, sizeof(cwd)) != NULL)
#else
        if (getcwd(cwd, sizeof(cwd)) != NULL)
#endif
        {
            std::cout << "\nCurrent path is : " << cwd << std::endl;
        }
        else
        {
            std::cerr << "Get current path error!" << std::endl;
            exit(1);
        }

        // 从当前路径切换到bdf下
        std::string bdfPath = std::string(cwd) + "/bdf";
#if defined(_BasePlatformWinddows_)
        if (_chdir(bdfPath.c_str()) == 0)
#else
        if (chdir(bdfPath.c_str()) == 0)
#endif
        {
            if (processorID == 0) std::cout << "Success, change into bdf directory" << std::endl;
        }
        else
        {
            if (processorID == 0) std::cerr << "Fail, change into bdf directory" << std::endl;
            exit(1);
        }

        // 在bdf文件夹下运行nastran
        std::string nastpath = data->GetFlowConfigure().GetStaticAero().CSDParameter.NastranPath + " " + bdfName + ".bdf";
        const char *nast2021 = nastpath.c_str(); //"/home/<USER>/MSC/MSC_Nastran/20121/bin/nast20121 m6.bdf"

        if (processorID == 0) std::cout << "nast2021 = " << nast2021 << std::endl;

        if (system(nast2021) != 0)
        {
            std::cerr << "run nastran error" << std::endl;
            exit(1);
        }
        else
        {
            Print("==success run nastran.==");
        }

        while (true)
        {
            std::string logFileName = bdfName + ".log"; // 名字后续更改
            std::ifstream infile(logFileName);

            std::string fileContent((std::istreambuf_iterator<char>(infile)),
                                    std::istreambuf_iterator<char>());
            infile.close();
            bool flag = fileContent.find("Nastran finished") != std::string::npos;
            if (flag)
            {
                if (processorID == 0) std::cout << "Nastran compute finished!" << std::endl;
                break;
            }
            else
            {
                if (processorID == 0) std::cout << "Nastran not finished! Wait 5 seconds..." << std::endl;
                // windows 系统 5秒要写成5000
                // Sleep(5000);
                // UNIX 系统
                // sleep(5);
                // 需要c++支持
                std::this_thread::sleep_for(std::chrono::seconds(5));
            }
        }

        // 删除nastran分析完成后的部分文件
        std::string baseFileName = bdfName;
        std::vector<std::string> suffixes = {
            ".DBALL",
            ".IFPDAT",
            ".MASTER",
            ".log",
            ".f04"
        };
        for (const auto& suffix : suffixes) {
            std::string fullFileName = baseFileName + suffix;
            try {
                if (remove(fullFileName.c_str()) !=0 ) {
                    Print("Failed to delete file: " + fullFileName);
                }
            } catch (const std::exception& e) {
                Print("Exception when deleting file " + fullFileName + ": " + e.what());
            }
        }

        // 运行完之后切换回上一级
        if (chdir(cwd) == 0)
        {
            std::cout << "Success, change into .." << cwd << std::endl;
        }
        else
        {
            std::cerr << "Fail, change into .." << cwd << std::endl;
            exit(1);
        }
        // 读f06文件文件中的所有结构点位移
        std::fstream infile;
        std::string fo6FileName = "bdf/" + bdfName + ".f06";
        infile.open(fo6FileName, std::fstream::in);
        int n_boundary = v_solidMeshData.size();

        std::vector<Node> v_dxyzt;
        std::vector<int> v_idt;
        std::string line;
        int j = 0;
        Node dxyz;

        v_dxyzt.resize(ncsdt); //**ncsdt结构点数目
        v_idt.resize(ncsdt, -1);

        bool spointFlag = false;
        while (std::getline(infile, line))
        {
            int nSpoint;
            if (line.find("SPOINT") != std::string::npos)
                nSpoint = std::stoi(line.substr(53, 17));

            if (line.find("D I S P L A C E M E N T   V E C T O R") != std::string::npos)
            {
                std::getline(infile, line);
                std::getline(infile, line);
                for (int i = 0; i < 50; i++)
                {
                    std::getline(infile, line);
                    if (line[20] == 'G')
                    {
                        v_idt[j] = std::stoi(line.substr(0, 14));
                        dxyz.SetX(std::stod(line.substr(26, 13)));
                        dxyz.SetY(std::stod(line.substr(41, 13)));
                        dxyz.SetZ(std::stod(line.substr(56, 13)));
                        v_dxyzt[j] = dxyz;
                        j++;
                    }
                    else if (line[20] == 'S')
                    {
                        if (!spointFlag)
                        {
                            j = j + nSpoint;
                            spointFlag = true;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }
        // 删除f06文件
        /* if (remove(fo6FileName.c_str()) != 0)
         {
           Print("\n failed to delete f06!");
         }
         else
         {
           Print("\n delete f06 successfully!");
         }*/
        // 从所有结构点位移中提取结构加载点位移
        for (int i = 0; i < n_boundary; i++)
        {
            int ncsd = v_solidMeshData[i].ncsd;
            v_solidMeshData[i].v_dxyz.resize(ncsd);
            for (int j = 0; j < ncsd; j++) //**ncsd:结构加载点数目
            {
                int index = v_solidMeshData[i].v_Index[j];
                v_solidMeshData[i].v_dxyz[j].SetX(v_dxyzt[index].X());
                v_solidMeshData[i].v_dxyz[j].SetY(v_dxyzt[index].Y());
                v_solidMeshData[i].v_dxyz[j].SetZ(v_dxyzt[index].Z());
            }
        }

        this->CoordTransfCSDToCFD();
        this->RBFSInSolid();

        for (int i = 1; i < npart; i++)
            mpi_world.send(i, 120, v_solidMeshData);
    }
    else
    {
        mpi_world.recv(0, 120, v_solidMeshData);
    }
}

void AeroStaticMethod::RBFSInSolid()
{
#ifdef _Supports_CXX11_
    std::unordered_map<int, int> id;
    std::unordered_map<int, int> Trans_id;
    ;
#else
    std::map<int, int> id;
    std::map<int, int> Trans_id;
    ;
#endif

    for (int i = 0; i < v_solidMeshData.size(); i++)
    {
        int ncsd = v_solidMeshData[i].ncsd;
        for (int j = 0; j < ncsd; j++)
        {
            int n = v_solidMeshData[i].v_RbfID[j];
            if (n < 10 && n > 0)
            {
                int temp = n * 1000 + n;
                id.insert(std::make_pair(temp, n));
                Trans_id.insert(std::make_pair(n, temp));
            }
            else if (n < 100 && n > 9)
            {
                int temp = n * 100 + n;
                id.insert(std::make_pair(temp, n));
                Trans_id.insert(std::make_pair(n, temp));
            }
        }
    }

    std::vector<int> map_master;
    map_master.reserve(id.size());
    for (auto it = id.begin(); it != id.end(); it++)
    {
        map_master.push_back(it->first);
    }
    std::vector<std::vector<Node>> v_nodeID1;
    std::vector<std::vector<Node>> v_deformID1;
    v_nodeID1.resize(id.size());
    v_deformID1.resize(id.size());
    for (int i = 0; i < id.size(); i++)
    {
        v_nodeID1[i].reserve(ncsdt);
        v_deformID1[i].reserve(ncsdt);
    }
    for (int i = 0; i < v_solidMeshData.size(); i++)
    {
        int ncsd = v_solidMeshData[i].ncsd;
        for (int j = 0; j < ncsd; j++)
        {
            int n_temp = v_solidMeshData[i].v_RbfID[j];
            auto it = id.find(n_temp);
            int location = -1;
            if (it != id.end())
            {
                for (int k = 0; k < id.size(); k++)
                    if (n_temp == map_master[k])
                        location = k;
                v_nodeID1[location].push_back(v_solidMeshData[i].v_xyz[j]);
                v_deformID1[location].push_back(v_solidMeshData[i].v_xyz[j] + v_solidMeshData[i].v_dxyz[j]);
            }
        }
    }

    double R_deform = data->GetFlowConfigure().GetStaticAero().CFDParameter.Radiu;
    std::vector<std::vector<Vector>> v_weight;
    v_weight.resize(id.size());
    for (int i = 0; i < id.size(); i++)
    {
        // 流固耦合专用RBF求解：不使用减点策略，确保数据传递精度
        // 原因：流固耦合RBF矩阵构造与动网格不同，且不能使用减点策略
        if (processorID == 0)
            Print("流固耦合部件 " + std::to_string(i) + " 节点数: " + std::to_string(v_nodeID1[i].size()) + "，使用静气弹专用RBF方法");

        // 使用静气弹专用的RBF求解方法，直接调用Lapack_degv
        this->SolveRBFWithStaticAeroelasticMethod(v_weight[i], v_nodeID1[i], v_deformID1[i]);
    }

    for (int i = 0; i < v_solidMeshData.size(); i++)
    {
        int ncsd = v_solidMeshData[i].ncsd;
        for (int j = 0; j < ncsd; j++)
        {
            int n_temp = v_solidMeshData[i].v_RbfID[j];
            auto it = Trans_id.find(n_temp);
            int location = -1;
            if (it != Trans_id.end())
            {
                int map_n = it->second;

                for (int k = 0; k < id.size(); k++)
                    if (map_n == map_master[k])
                        location = k;
                Node temp = v_solidMeshData[i].v_xyz[j];
                v_solidMeshData[i].v_dxyz[j] = this->RebuildCSDNode(v_weight[location], v_nodeID1[location], temp, R_deform);
            }
        }
    }
}

void AeroStaticMethod::CoordTransfCSDToCFD()
{
    std::string RwingDir = data->GetFlowConfigure().GetStaticAero().CSDParameter.RwingDir;
    std::string FuseDir = data->GetFlowConfigure().GetStaticAero().CSDParameter.FuseDir;
    Eigen::Vector3d translation_vector(0, 0, 0); // 平移向量   /////////////////////位移不需要平移
                                                 // std::fstream outfile;
    // outfile.open("csd_f06.dat", std::fstream::out);
    for (int i = 0; i < v_solidMeshData.size(); i++)
    {
        int ncsd = v_solidMeshData[i].ncsd;
        for (int j = 0; j < ncsd; j++)
        {
            if (v_solidMeshData[i].local_CD[j] != 0)
            {
                Eigen::Vector3d global_displacement;
                int index = v_solidMeshData[i].v_Index[j];
                global_displacement = local_to_global(v_solidMeshData[i].v_dxyz[j], v_solidMeshData[i].local_A[j], v_solidMeshData[i].local_B[j], v_solidMeshData[i].local_C[j]);
                Vector temp = rotate_and_translate_fem_coordinates(global_displacement, translation_vector, RwingDir, FuseDir, index);

                v_solidMeshData[i].v_dxyz[j].SetX(temp.X());
                v_solidMeshData[i].v_dxyz[j].SetY(temp.Y());
                v_solidMeshData[i].v_dxyz[j].SetZ(temp.Z());
            }

            // outfile<< v_solidMeshData[i].v_id[j] << " " << v_solidMeshData[i].v_xyz[j].X()*1000 + v_solidMeshData[i].v_dxyz[j].X() << " " << v_solidMeshData[i].v_xyz[j].Y()*1000 + v_solidMeshData[i].v_dxyz[j].Y() << " " <<v_solidMeshData[i].v_xyz[j].Z()*1000 + v_solidMeshData[i].v_dxyz[j].Z()<<"       " <<  v_solidMeshData[i].local_A[j].X()<<" "<< v_solidMeshData[i].local_A[j].Y()<<" "<<v_solidMeshData[i].local_A[j].Z()<<std::endl ;

            // 缩放
            v_solidMeshData[i].v_dxyz[j].SetX(v_solidMeshData[i].v_dxyz[j].X() * data->GetFlowConfigure().GetStaticAero().CSDParameter.scale.X());
            v_solidMeshData[i].v_dxyz[j].SetY(v_solidMeshData[i].v_dxyz[j].Y() * data->GetFlowConfigure().GetStaticAero().CSDParameter.scale.Y());
            v_solidMeshData[i].v_dxyz[j].SetZ(v_solidMeshData[i].v_dxyz[j].Z() * data->GetFlowConfigure().GetStaticAero().CSDParameter.scale.Z());
        }
    }
    //  outfile.close();
}

Eigen::Vector3d AeroStaticMethod::local_to_global(Vector &local_displacement_, Vector &A_, Vector &B_, Vector &C_)
{
    // 变量类型转换
    Eigen::Vector3d local_displacement(local_displacement_.X(), local_displacement_.Y(), local_displacement_.Z());
    Eigen::Vector3d A(A_.X(), A_.Y(), A_.Z());
    Eigen::Vector3d B(B_.X(), B_.Y(), B_.Z());
    Eigen::Vector3d C(C_.X(), C_.Y(), C_.Z());

    // 计算局部坐标系的基向量
    Eigen::Vector3d Z = (B - A).normalized();
    Eigen::Vector3d X = (C - A) - ((C - A).dot(Z)) * Z;
    X.normalize();

    Eigen::Vector3d Y = Z.cross(X);

    // 构建转换矩阵
    Eigen::Matrix3d transformation_matrix;
    transformation_matrix << X, Y, Z;

    // 将局部坐标转换为全局坐标
    Eigen::Vector3d global_displacement = transformation_matrix * local_displacement;

    return global_displacement;
}

Vector AeroStaticMethod::rotate_and_translate_fem_coordinates(Eigen::Vector3d &global_displacement, Eigen::Vector3d &translation_vector, std::string &RwingDir, std::string &FuseDir, int &index)
{
    Eigen::Vector3d Row1, Row2, Normal;

    // 计算Row2
    if (RwingDir == "X")
    {
        Row2 = Eigen::Vector3d(1, 0, 0);
    }
    else if (RwingDir == "Y")
    {
        Row2 = Eigen::Vector3d(0, 1, 0);
    }
    else if (RwingDir == "Z")
    {
        Row2 = Eigen::Vector3d(0, 0, 1);
    }
    else if (RwingDir == "-X")
    {
        Row2 = Eigen::Vector3d(-1, 0, 0);
    }
    else if (RwingDir == "-Y")
    {
        Row2 = Eigen::Vector3d(0, -1, 0);
    }
    else if (RwingDir == "-Z")
    {
        Row2 = Eigen::Vector3d(0, 0, -1);
    }
    else
    {
        std::cerr << "RwingDir输入错误" << std::endl;
    }

    // 计算Row1
    if (FuseDir == "X")
    {
        Row1 = Eigen::Vector3d(1, 0, 0);
    }
    else if (FuseDir == "Y")
    {
        Row1 = Eigen::Vector3d(0, 1, 0);
    }
    else if (FuseDir == "Z")
    {
        Row1 = Eigen::Vector3d(0, 0, 1);
    }
    else if (FuseDir == "-X")
    {
        Row1 = Eigen::Vector3d(-1, 0, 0);
    }
    else if (FuseDir == "-Y")
    {
        Row1 = Eigen::Vector3d(0, -1, 0);
    }
    else if (FuseDir == "-Z")
    {
        Row1 = Eigen::Vector3d(0, 0, -1);
    }
    else
    {
        std::cerr << "FuseDir输入错误" << std::endl;
    }

    // 计算Normal
    Normal = Row1.cross(Row2);

    // 组合成旋转矩阵
    Eigen::Matrix3d rotation_matrix;
    rotation_matrix.row(0) = Row1;
    rotation_matrix.row(1) = Row2;
    rotation_matrix.row(2) = Normal;

    // 旋转坐标
    Eigen::Vector3d rotated_coordinates = rotation_matrix * global_displacement;

    // 平移坐标
    Eigen::Vector3d translated_coordinates = rotated_coordinates + translation_vector;

    Vector displacement;
    displacement.SetX(translated_coordinates[0]);
    displacement.SetY(translated_coordinates[1]);
    displacement.SetZ(translated_coordinates[2]);

    return displacement;
}

void AeroStaticMethod::CalcuateAeroDeform()
{
    int n_boundary = v_solidMeshData.size();
    //**串行情况，0进程直接计算
    if (npart == 1)
    {
        for (int i = 0; i < n_boundary; i++)
        {
            int ncsd = v_solidMeshData[i].ncsd;
            int id_aero = v_solidMeshData[i].id_aero; // 结构数据包对应气动数据包位置

            int ncfd = v_aeroMeshData[id_aero].ncfd;
            v_aeroMeshData[id_aero].v_dxyz.clear();
            v_aeroMeshData[id_aero].v_dxyz.resize(ncfd);

            for (int j = 0; j < ncfd; j++)
                v_aeroMeshData[id_aero].v_dxyz[j] = Vector0;

            // 由hm矩阵及结构变形计算气动变形

            for (int j = 0; j < ncfd; j++)
            {
                for (int k = 0; k < ncsd; k++)
                {
                    Vector dxyz = v_solidMeshData[i].v_dxyz[k];
                    v_aeroMeshData[id_aero].v_dxyz[j].SetX(v_aeroMeshData[id_aero].v_dxyz[j].X() + hm_all[i][j][k] * dxyz.X());
                    v_aeroMeshData[id_aero].v_dxyz[j].SetY(v_aeroMeshData[id_aero].v_dxyz[j].Y() + hm_all[i][j][k] * dxyz.Y());
                    v_aeroMeshData[id_aero].v_dxyz[j].SetZ(v_aeroMeshData[id_aero].v_dxyz[j].Z() + hm_all[i][j][k] * dxyz.Z());
                }
            }

            // 检查气动变形结果
            if (processorID == 0) {
                double maxAeroDeform = 0.0;
                for (int j = 0; j < ncfd; j++) {
                    Vector dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                    double deformMag = sqrt(dxyz.X()*dxyz.X() + dxyz.Y()*dxyz.Y() + dxyz.Z()*dxyz.Z());
                    maxAeroDeform = Max(maxAeroDeform, deformMag);
                }
                Print("最大气动变形量: " + std::to_string(maxAeroDeform));
            }

            // Store previous step's deformation before calculating new one
            // 如果是一步求解的方式，这里要改
            std::vector<Vector> prev_dxyz;
            if (FileExists("WallTecplot_prev.plt"))
            {
                Print("WallTecplot_prev.plt is exist!\n");
                prev_dxyz = ReadPreviousDeformationFromWallDat(id_aero);
            }
            else
            {
                Print("WallTecplot_prev.plt is not exist!\n");
                prev_dxyz.resize(v_aeroMeshData[id_aero].ncfd, Vector0);
            }

            // Apply adaptive relaxation factor with convergence check
            double baseAlpha = data->GetFlowConfigure().GetStaticAero().relaxFactor; // Base relaxation factor
            double maxDeformChange = 0.0;

            // 计算自适应松弛因子
            double adaptiveAlpha = 1.0;
            if (abs(baseAlpha - 1.0) <= 0.01)
            {
                // 首先计算当前最大变形量变化
                for (int j = 0; j < ncfd; j++)
                {
                    Vector current_dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                    Vector deform_change = current_dxyz - prev_dxyz[j];
                    double change_magnitude = sqrt(deform_change & deform_change);
                    maxDeformChange = Max(maxDeformChange, change_magnitude);
                }
                adaptiveAlpha = CalculateAdaptiveRelaxationFactor(maxDeformChange, previousMaxDeformChange, baseAlpha);
            }

            // 应用松弛因子
            for (int j = 0; j < ncfd; j++)
            {
                Vector current_dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                Vector relaxed_dxyz = prev_dxyz[j] + adaptiveAlpha * (current_dxyz - prev_dxyz[j]);
                v_aeroMeshData[id_aero].v_dxyz[j] = relaxed_dxyz;
            }

            // 更新历史记录
            UpdateRelaxationHistory(maxDeformChange);

            // 输出收敛信息
            if (processorID == 0)
            {
                Print("Part " + v_aeroMeshData[id_aero].PartName + " max deform change: " + ToString(maxDeformChange));
                Print("Base relaxation factor: " + ToString(baseAlpha) + ", Adaptive factor: " + ToString(adaptiveAlpha));
                Print("Iteration count: " + ToString(relaxationIterationCount));
            }
        } //**

        this->AverageIntersectPartDxyz();

        // 计算串行模式下的全局最大变形量
        double globalMaxStructDeform = 0.0;
        double globalMaxAeroDeform = 0.0;

        for (int i = 0; i < n_boundary; i++)
        {
            int ncsd = v_solidMeshData[i].ncsd;
            int id_aero = v_solidMeshData[i].id_aero;
            int ncfd = v_aeroMeshData[id_aero].ncfd;

            // 计算结构最大变形量
            for (int k = 0; k < ncsd; k++) {
                Vector dxyz = v_solidMeshData[i].v_dxyz[k];
                double deformMag = sqrt(dxyz.X()*dxyz.X() + dxyz.Y()*dxyz.Y() + dxyz.Z()*dxyz.Z());
                globalMaxStructDeform = Max(globalMaxStructDeform, deformMag);
            }

            // 计算气动最大变形量
            for (int j = 0; j < ncfd; j++) {
                Vector dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                double deformMag = sqrt(dxyz.X()*dxyz.X() + dxyz.Y()*dxyz.Y() + dxyz.Z()*dxyz.Z());
                globalMaxAeroDeform = Max(globalMaxAeroDeform, deformMag);
            }
        }

        // 输出全局最大变形量（仅在串行模式下输出）
        if (processorID == 0 && npart == 1) {
            Print("=== 流固耦合变形统计 ===");
            Print("全局最大结构变形量: " + std::to_string(globalMaxStructDeform));
            Print("全局最大气动变形量: " + std::to_string(globalMaxAeroDeform));
            Print("========================");
        }

        for (int i = 0; i < n_boundary; i++)
        {
            int id_aero = v_solidMeshData[i].id_aero;
            int ncfd = v_aeroMeshData[id_aero].ncfd;
            v_aeroMeshData[id_aero].v_xyz.clear();
            v_aeroMeshData[id_aero].v_xyz.resize(ncfd);

            for (int j = 0; j < ncfd; j++)
            {
                Node xyz_o = v_aeroMeshData[id_aero].v_xyz_o[j];
                Node dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                v_aeroMeshData[id_aero].v_xyz[j].SetX(xyz_o.X() + dxyz.X());
                v_aeroMeshData[id_aero].v_xyz[j].SetY(xyz_o.Y() + dxyz.Y());
                v_aeroMeshData[id_aero].v_xyz[j].SetZ(xyz_o.Z() + dxyz.Z());
            }
        }
    }
    //**并行情况,将n_boundary个结构部件按顺序循环分发给npart个进程
    if (npart != 1)
    {
        // 声明局部最大变形量变量用于并行汇总
        double localMaxStructDeform = 0.0;
        double localMaxAeroDeform = 0.0;

        if (processorID != 0)
        {
            for (int i = 0; i < n_boundary; i++)
            {
                int id = i % (npart - 1);
                if (processorID == id + 1)
                {
                    int ncsd = v_solidMeshData[i].ncsd;
                    int id_aero = v_solidMeshData[i].id_aero; // 结构数据包对应气动数据包位置

                    int ncfd = v_aeroMeshData[id_aero].ncfd;
                    v_aeroMeshData[id_aero].v_dxyz.clear();
                    v_aeroMeshData[id_aero].v_dxyz.resize(ncfd);

                    for (int j = 0; j < ncfd; j++)
                        v_aeroMeshData[id_aero].v_dxyz[j] = Vector0;

                    // 计算结构最大变形量
                    double maxStructDeform = 0.0;
                    for (int k = 0; k < ncsd; k++) {
                        Vector dxyz = v_solidMeshData[i].v_dxyz[k];
                        double deformMag = sqrt(dxyz.X()*dxyz.X() + dxyz.Y()*dxyz.Y() + dxyz.Z()*dxyz.Z());
                        maxStructDeform = Max(maxStructDeform, deformMag);
                    }

                    // 由hm矩阵及结构变形计算气动变形
                    for (int j = 0; j < ncfd; j++)
                    {
                        for (int k = 0; k < ncsd; k++)
                        {
                            Vector dxyz = v_solidMeshData[i].v_dxyz[k];
                            v_aeroMeshData[id_aero].v_dxyz[j].SetX(v_aeroMeshData[id_aero].v_dxyz[j].X() + hm_all[i][j][k] * dxyz.X());
                            v_aeroMeshData[id_aero].v_dxyz[j].SetY(v_aeroMeshData[id_aero].v_dxyz[j].Y() + hm_all[i][j][k] * dxyz.Y());
                            v_aeroMeshData[id_aero].v_dxyz[j].SetZ(v_aeroMeshData[id_aero].v_dxyz[j].Z() + hm_all[i][j][k] * dxyz.Z());
                        }
                    }

                    // 计算气动最大变形量
                    double maxAeroDeform = 0.0;
                    for (int j = 0; j < ncfd; j++) {
                        Vector dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                        double deformMag = sqrt(dxyz.X()*dxyz.X() + dxyz.Y()*dxyz.Y() + dxyz.Z()*dxyz.Z());
                        maxAeroDeform = Max(maxAeroDeform, deformMag);
                    }

                    // 存储局部最大变形量用于后续全局汇总
                    localMaxStructDeform = Max(localMaxStructDeform, maxStructDeform);
                    localMaxAeroDeform = Max(localMaxAeroDeform, maxAeroDeform);

                    // Store previous step's deformation before calculating new one
                    // 如果是一步求解的方式，这里要改
                    std::vector<Vector> prev_dxyz;
                    if (FileExists("WallTecplot_prev.plt"))
                    {
                        Print("WallTecplot_prev.plt is exist!\n");
                        prev_dxyz = ReadPreviousDeformationFromWallDat(id_aero);
                    }
                    else
                    {
                        Print("WallTecplot_prev.plt is not exist!\n");
                        prev_dxyz.resize(v_aeroMeshData[id_aero].ncfd, Vector0);
                    }

                    // Apply adaptive relaxation factor with convergence check
                    double baseAlpha = data->GetFlowConfigure().GetStaticAero().relaxFactor; // Base relaxation factor
                    Print("Base relaxFactor is : " + ToString(baseAlpha));
                    double maxDeformChange = 0.0;

                    double adaptiveAlpha = 1.0;
                    if (abs(baseAlpha - 1.0) >= 0.01)
                    {
                        // 首先计算当前最大变形量变化
                        for (int j = 0; j < ncfd; j++)
                        {
                            Vector current_dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                            Vector deform_change = current_dxyz - prev_dxyz[j];
                            double change_magnitude = sqrt(deform_change & deform_change);
                            maxDeformChange = Max(maxDeformChange, change_magnitude);
                        }
                        // 计算自适应松弛因子
                        adaptiveAlpha = CalculateAdaptiveRelaxationFactor(maxDeformChange, previousMaxDeformChange, baseAlpha);
                    }

                    // 应用松弛因子
                    for (int j = 0; j < ncfd; j++)
                    {
                        Vector current_dxyz = v_aeroMeshData[id_aero].v_dxyz[j];
                        Vector relaxed_dxyz = prev_dxyz[j] + adaptiveAlpha * (current_dxyz - prev_dxyz[j]);
                        v_aeroMeshData[id_aero].v_dxyz[j] = relaxed_dxyz;
                    }

                    // 更新历史记录
                    UpdateRelaxationHistory(maxDeformChange);

                    Print("Part " + v_aeroMeshData[id_aero].PartName + " max deform change: " + ToString(maxDeformChange));
                    Print("Adaptive relaxation factor: " + ToString(adaptiveAlpha));

                    mpi_world.send(0, i, v_aeroMeshData[id_aero].v_dxyz); // 将计算完成的部件结构力发送给0进程
                }
            }

            // 发送局部最大变形量到0号进程
            mpi_world.send(0, 9999, localMaxStructDeform);
            mpi_world.send(0, 9998, localMaxAeroDeform);
        }
        else
        {

            for (int i = 0; i < n_boundary; i++) // 接收其他进程计算的气动变形量
            {
                int id = i % (npart - 1);
                int id_aero = v_solidMeshData[i].id_aero;
                mpi_world.recv(id + 1, i, v_aeroMeshData[id_aero].v_dxyz);
            }

            // 接收并汇总所有进程的最大变形量
            double globalMaxStructDeform = 0.0;
            double globalMaxAeroDeform = 0.0;

            for (int i = 1; i < npart; i++) {
                double recvMaxStructDeform, recvMaxAeroDeform;
                mpi_world.recv(i, 9999, recvMaxStructDeform);
                mpi_world.recv(i, 9998, recvMaxAeroDeform);
                globalMaxStructDeform = Max(globalMaxStructDeform, recvMaxStructDeform);
                globalMaxAeroDeform = Max(globalMaxAeroDeform, recvMaxAeroDeform);
            }

            // 输出全局最大变形量
            Print("=== 流固耦合变形统计 ===");
            Print("全局最大结构变形量: " + std::to_string(globalMaxStructDeform));
            Print("全局最大气动变形量: " + std::to_string(globalMaxAeroDeform));
            Print("========================");

            for (int i = 0; i < v_aeroMeshData.size(); i++) // 将多余的部件气动变形量设为0
            {
                if (v_aeroMeshData[i].v_dxyz.size() == 0)
                {
                    v_aeroMeshData[i].v_dxyz.resize(v_aeroMeshData[i].ncfd);
                    for (int j = 0; j < v_aeroMeshData[i].ncfd; j++)
                        v_aeroMeshData[i].v_dxyz[j] = Vector0;
                }
            }

            this->AverageIntersectPartDxyz(); // 部件交线处变形量平均
        }

        if (processorID == 0)
        {

            for (int i = 0; i < v_aeroMeshData.size(); i++)
            {
                int ncfd = v_aeroMeshData[i].ncfd;
                v_aeroMeshData[i].v_xyz.clear();
                v_aeroMeshData[i].v_xyz.resize(ncfd);

                for (int j = 0; j < ncfd; j++)
                {
                    Node xyz_o = v_aeroMeshData[i].v_xyz_o[j];
                    Node dxyz = v_aeroMeshData[i].v_dxyz[j];
                    v_aeroMeshData[i].v_xyz[j].SetX(xyz_o.X() + dxyz.X());
                    v_aeroMeshData[i].v_xyz[j].SetY(xyz_o.Y() + dxyz.Y());
                    v_aeroMeshData[i].v_xyz[j].SetZ(xyz_o.Z() + dxyz.Z());
                }
            }

            for (int i = 1; i < npart; i++)
                mpi_world.send(i, 130, v_aeroMeshData);
        }
        else
        {
            mpi_world.recv(0, 130, v_aeroMeshData);
        }
    }
}

void AeroStaticMethod::AverageIntersectPartDxyz()
{
    for (auto it = map_part.begin(); it != map_part.end(); it++)
    {
        // int globalID = it->first;
        auto map = it->second;
        Node temp = Vector0;
        for (auto it1 = map.begin(); it1 != map.end(); it1++)
        {
            int location1 = it1->first;
            int location2 = it1->second;
            temp.SetX(temp.X() + v_aeroMeshData[location1].v_dxyz[location2].X());
            temp.SetY(temp.Y() + v_aeroMeshData[location1].v_dxyz[location2].Y());
            temp.SetZ(temp.Z() + v_aeroMeshData[location1].v_dxyz[location2].Z());
        }

        temp.SetX(temp.X() / map.size());
        temp.SetY(temp.Y() / map.size());
        temp.SetZ(temp.Z() / map.size());

        for (auto it1 = map.begin(); it1 != map.end(); it1++)
        {
            int location1 = it1->first;
            int location2 = it1->second;
            v_aeroMeshData[location1].v_dxyz[location2] = temp;
        }
    }
}

void AeroStaticMethod::OutMaxDefom()
{
    // 输出maxDeform.dat文件
    if (processorID == 0)
    {
        std::fstream file;
        int idmax[2] = {-1, -1};
        Scalar maxDeform = 0.0;

        // 计算本次迭代最大变形量
        for (int i = 0; i < v_solidMeshData.size(); i++)
        {
            int id_aero = v_solidMeshData[i].id_aero;
            for (int j = 0; j < v_aeroMeshData[id_aero].ncfd; j++)
            {
                Vector vdistance = v_aeroMeshData[id_aero].v_dxyz[j];
                Scalar distance = sqrt(vdistance & vdistance);
                if (distance > maxDeform)
                {
                    maxDeform = distance;
                    idmax[0] = id_aero;
                    idmax[1] = j;
                }
            }
        }

        // 输出相关参数
        file.open("maxDeform.dat", std::fstream::out);
        file << "#max deformation:   "
             << "\n";
        file << maxDeform << "\n";
            file << "#max deformation Node Id:   "
                 << "\n";
            file << v_aeroMeshData[idmax[0]].v_id[idmax[1]] << "\n";
            file << "#max deformation Node:   "
                 << "\n";
        file << v_aeroMeshData[idmax[0]].v_xyz[idmax[1]].X() << " " << v_aeroMeshData[idmax[0]].v_xyz[idmax[1]].Y() << " " << v_aeroMeshData[idmax[0]].v_xyz[idmax[1]].Z() << "\n";
        file.close();
    }

    // 输出tecpolt格式的物面
    if (processorID == 0 && data->GetFlowConfigure().GetStaticAero().Output_Flag)
    {
        int n_aero = v_aeroMeshData.size();
        std::vector<int> v_wallboundaryIDAll;
        v_wallboundaryIDAll.resize(n_aero);

        int n = globalMesh->GetBoundarySize();

        for (int i = 0; i < n_aero; i++)
        {
            std::string name = v_aeroMeshData[i].PartName;
            for (int j = 0; j < n; j++)
            {
                std::string nametemp = globalMesh->GetBoundaryName(j);
                if (nametemp == name)
                    v_wallboundaryIDAll[i] = j;
            }
        }

        Post::Position postPosition = Post::Position::CELL_CENTER;
        Post::Tecplot *postPointer = new Post::Tecplot(globalMesh, false, postPosition);

        std::vector<std::vector<int>> nodeListVector;
        nodeListVector.resize(n_aero);

        std::vector<std::vector<Node>> nodeVector;
        nodeVector.resize(n_aero);
        std::vector<std::vector<int>> nodeID;
        nodeID.resize(n_aero);

        // 建立对偶网格边界面编号与原始网格节点编号映射关系
        if (data->GetFlowConfigure().GetPreprocess().dualMeshFlag)
        {
            std::vector<int> v_mapNodeID;

            for (int m = 0; m < v_wallboundaryIDAll.size(); ++m)
            {
                const int patchID = v_wallboundaryIDAll[m];

                // 原始网格边界面节点容器创建
                std::vector<bool> nodeFlag(globalMesh->GetNodeNumber(), false);
                for (int index = 0; index < globalMesh->GetBoundaryFaceSize(patchID); ++index)
                {
                    const int &faceID = globalMesh->GetBoundaryFaceID(patchID, index);
                    for (int i = 0; i < globalMesh->GetFace(faceID).GetNodeSize(); ++i)
                    {
                        const int &nodeID = globalMesh->GetFace(faceID).GetNodeID(i);
                        if (!nodeFlag[nodeID])
                        {
                            v_mapNodeID.push_back(nodeID);
                            nodeFlag[nodeID] = true;
                        }
                    }
                }
            }

            for (int i = 0; i < n_aero; i++)
            {
                nodeListVector[i] = postPointer->GetnodeListVector(v_wallboundaryIDAll[i]);
                for (int j = 0; j < nodeListVector[i].size(); j++)
                {
                    int id = nodeListVector[i][j];
                    for (int k = 0; k < v_aeroMeshData[i].ncfd; k++)
                    {
                        const int &faceID = v_aeroMeshData[i].v_id[k];
                        if (id == v_mapNodeID[faceID])
                        {
                            nodeVector[i].push_back(v_aeroMeshData[i].v_xyz[k]);
                            nodeID[i].push_back(id);
                        }
                    }
                }
            }
        }
        else
        {
            for (int i = 0; i < n_aero; i++)
            {
                nodeListVector[i] = postPointer->GetnodeListVector(v_wallboundaryIDAll[i]);
                for (int j = 0; j < nodeListVector[i].size(); j++)
                {
                    int id = nodeListVector[i][j];
                    for (int k = 0; k < v_aeroMeshData[i].ncfd; k++)
                    {
                        if (id == v_aeroMeshData[i].v_id[k])
                        {
                            nodeVector[i].push_back(v_aeroMeshData[i].v_xyz[k]);
                            nodeID[i].push_back(id);
                        }
                    }
                }
            }
        }

        std::string name = "WallTecplot";
        postPointer->SetCaseName(name);
        postPointer->WriteBoundaryFile_AeroDynamic(v_wallboundaryIDAll, nodeVector, nodeID);
        Print("output WallTecplot.plt");
    }
}

void AeroStaticMethod::ProcessMeshDeform()
{

#if defined(_EnableMKL_)
    std::vector<Vector> v_wallNode;
    std::vector<Vector> v_wallNode_deform;

    if (processorID == 0)
    {

        for (auto it = map_allWallNode.begin(); it != map_allWallNode.end(); it++)
        {

            auto temp = it->second;
            int locat1 = temp.begin()->first;
            int locat2 = temp.begin()->second;

            if (v_aeroMeshData[locat1].v_dxyz.size() != 0)
            {
                v_wallNode.push_back(v_aeroMeshData[locat1].v_xyz_o[locat2]);
                v_wallNode_deform.push_back(v_aeroMeshData[locat1].v_xyz[locat2]);
            }
            else
            {
                v_wallNode.push_back(v_aeroMeshData[locat1].v_xyz_o[locat2]);
                v_wallNode_deform.push_back(v_aeroMeshData[locat1].v_xyz_o[locat2]);
            }
        }

        for (int i = 1; i < npart; i++)
        {
            mpi_world.send(i, 140, v_wallNode);
            mpi_world.send(i, 150, v_wallNode_deform);
        }
    }
    else
    {
        mpi_world.recv(0, 140, v_wallNode);
        mpi_world.recv(0, 150, v_wallNode_deform);
    }
    mpi_world.barrier();

    // 使用标准动网格MeshDeform类替代静气弹专用方法
    double R_deform = data->GetFlowConfigure().GetStaticAero().CFDParameter.Radiu;
    MeshDeform* meshDeformer = new MeshDeform(v_wallNode, v_wallNode_deform, R_deform);

    // 配置简化的RBF求解器
    RBFSolverConfig config;
    config.solverType = RBFSolverType::TRADITIONAL_GAUSS;  // 使用自动选择策略
    config.enableMKL = true;
    config.enableParallel = true;  // 启用并行ScaLAPACK
    config.serialThreshold = 5000;  // 串行/分布式求解阈值
    config.maxNodes = 100000;       // 缩减时的最大节点数
    config.skipInterval = 5;       // 缩减时的跳跃间隔

    // 执行配置化的RBF求解
    meshDeformer->ProcessWithConfig(config);

    // 获取权重系数
    std::vector<Vector> v_weight = meshDeformer->GetV_weight();

    this->RebuildOriginalGlobalNode(v_weight, v_wallNode);

    // 清理内存
    delete meshDeformer;

    v_weight.clear();
    v_wallNode.clear();
    v_wallNode_deform.clear();

    mpi_world.barrier();

#endif
}

void AeroStaticMethod::ProcessMeshDeformWithOverset()
{
    if (processorID == 0)
        Print("开始重叠网格变形处理");

    // 检查是否启用重叠网格
    bool enableOverset = false;
#if defined(_EnableOverset_)
    enableOverset = true;
#endif

    if (!enableOverset)
    {
        Print("重叠网格未启用，使用传统网格变形方法");
        ProcessMeshDeform();
        return;
    }

    // 配置重叠网格变形参数
    OversetDeformConfig config;
    config.enableOversetDeform = true;
    config.updateOversetAssembly = true;
    config.preserveOverlapRegion = true;
    config.maxDeformIterations = 3;
    config.convergenceTolerance = 1e-6;
    config.enableQualityCheck = true;
    config.independentZoneDeform = true;
    config.oversetRBFRadius = data->GetFlowConfigure().GetStaticAero().CFDParameter.Radiu;
    config.maxRBFNodes = 8000;

    // 创建重叠网格变形器
    Package::FlowPackage *flowPackage = const_cast<Package::FlowPackage *>(data);
    OversetMeshDeform oversetDeformer(*flowPackage, config);

    // 执行重叠网格变形
    oversetDeformer.ProcessForStaticAeroelasticWithOverset(v_aeroMeshData);

    if (processorID == 0)
        Print("重叠网格变形处理完成");
}

void AeroStaticMethod::OutFinalCgns()
{
    std::vector<Node> v_globalNode = this->FindAndGatherGlobalNode();
    if (processorID == 0)
    {
        this->CopyFile();
        this->cgns2cgns(v_globalNode);
    }
}

void AeroStaticMethod::gettime()
{
    auto now = std::chrono::system_clock::now();
    std::time_t now_c = std::chrono::system_clock::to_time_t(now);
    struct tm now_tm = *std::localtime(&now_c);
    // if (GetMPIRank() == 0) std::cout << std::put_time(&now_tm, "%Y-%m-%d %H:%M:%S") << std::endl;
}

void AeroStaticMethod::OutSurfFile()
{

    // 输出surf文件
    if (processorID == 0)
    {
        // 获取surf文件名称
        const std::string deformname = data->GetFlowConfigure().GetStaticAero().CFDParameter.DeformMeshFile;
        size_t pos = deformname.find('.');
        if (pos == std::string::npos)
        {
            exit(1);
        }
        std::string name = deformname.substr(0, pos) + ".surf";

        // 获取按顺序排序的变形后的物面点键对值（id-node）
        std::map<int, std::vector<double>> map_wallNode_deform;
        std::vector<double> nodetemp(3);
        Vector sacle = data->GetFlowConfigure().GetMeshParameters().meshTransform.scale;
        for (auto it = map_allWallNode.begin(); it != map_allWallNode.end(); it++)
        {

            auto temp = it->second;
            int locat1 = temp.begin()->first;
            int locat2 = temp.begin()->second;

            Vector node;
            if (v_aeroMeshData[locat1].v_dxyz.size() != 0 && !v_aeroMeshData[locat1].v_IfSymFlag[locat2])
            {
                node = v_aeroMeshData[locat1].v_xyz[locat2];
            }
            else
            {
                node = v_aeroMeshData[locat1].v_xyz_o[locat2];
            }
            nodetemp[0] = node.X() / sacle.X();
            nodetemp[1] = node.Y() / sacle.Y();
            nodetemp[2] = node.Z() / sacle.Z();
            int id = v_aeroMeshData[locat1].v_id[locat2];
            map_wallNode_deform.insert(std::make_pair(id, nodetemp));
        }

        // 所有物面点总数
        int ncfd_all = map_wallNode_deform.size();

        std::cout << "ncfd_all =" << ncfd_all << std::endl;

        // 读surf内容，并存储
        std::vector<std::string> surfdat;
        std::fstream file;
        std::string line;
        std::vector<int> num(3);
        file.open(name, std::fstream::in);
        file >> num[0] >> num[1] >> num[2];
        file.close();
        file.open(name, std::fstream::in);
        while (std::getline(file, line))
        {
            surfdat.push_back(line);
        }
        file.close();

        // 更新surf文件
        file.open(name, std::fstream::out);
        file << num[0] << " " << num[1] << " " << num[2] << std::endl;
        std::vector<double> nodeout(3);
        for (const auto &pair : map_wallNode_deform)
        {
            int i = 0;
            int id = pair.first;
            for (const auto &item : pair.second)
            {
                nodeout[i] = item;
                i = i + 1;
            }
            file << nodeout[0] << " " << nodeout[1] << " " << nodeout[2] << " "
                 << "0"
                 << " "
                 << "0"
                 << "\n";
        }
        for (int i = ncfd_all + 1; i < surfdat.size(); i++)
            file << surfdat[i] << "\n";
        file.close();
    }
}

Node AeroStaticMethod::RebuildCSDNode(std::vector<Vector> v_weight, std::vector<Vector> v_wallNode, Node &node, double &R_deform)
{
    std::vector<Node> Delta;

    Node Delta_xyz = Vector0;
    Scalar yita = 0.0;
    std::fstream file;
    int n = v_wallNode.size();
    Delta.resize(n);

    std::vector<Scalar> fai;
    int j, k;

    const Node nodeTemp1 = node;
    fai.resize(n);
    for (j = 0; j < n; j++)
    {
        const Node nodeTemp2 = v_wallNode[j];

        yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2) + pow((nodeTemp2.Y() - nodeTemp1.Y()), 2) + pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R_deform;

        if (yita > 1.0)
        {
            fai[j] = 0.0;
        }
        else
        {
            fai[j] = pow((1 - yita), 4) * (4 * yita + 1);
        }
    }

    for (k = 0; k < n; k++)
    {
        Delta_xyz.SetX(Delta_xyz.X() + v_weight[k].X() * fai[k]);
        Delta_xyz.SetY(Delta_xyz.Y() + v_weight[k].Y() * fai[k]);
        Delta_xyz.SetZ(Delta_xyz.Z() + v_weight[k].Z() * fai[k]);
    }

    return Delta_xyz;
}

std::vector<Vector> AeroStaticMethod::RebuildNode(std::vector<Vector> v_weight, std::vector<Vector> v_wallNode, std::vector<Vector> v_Node)
{
    std::vector<Node> Delta;

    Node Delta_xyz;
    Scalar yita;
    // Scalar temp;
    std::fstream file;
    int n = v_Node.size();
    int n1 = v_wallNode.size();
    Delta.resize(n1);

    std::vector<Node> v_ReturnNode(n);

    std::vector<Scalar> fai;
    int j, k;

    double R_deform = data->GetFlowConfigure().GetStaticAero().CFDParameter.Radiu;
    // openmp
    for (int i = 0; i < n; i++)
    {
        Delta_xyz = Vector0;
        yita = 0.0;
        const Node nodeTemp1 = v_Node[i]; //*支撑半径;//*scale;
        Node node_deform = Vector0;
        fai.resize(n1);
        for (j = 0; j < v_wallNode.size(); j++)
        {
            const Node nodeTemp2 = v_wallNode[j];

            yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2) + pow((nodeTemp2.Y() - nodeTemp1.Y()), 2) + pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R_deform;

            if (yita > 1.0)
            {
                fai[j] = 0.0;
            }
            else
            {
                fai[j] = pow((1 - yita), 4) * (4 * yita + 1);
            }
        }

        for (k = 0; k < v_wallNode.size(); k++)
        {
            Delta_xyz.SetX(Delta_xyz.X() + v_weight[k].X() * fai[k]);
            Delta_xyz.SetY(Delta_xyz.Y() + v_weight[k].Y() * fai[k]);
            Delta_xyz.SetZ(Delta_xyz.Z() + v_weight[k].Z() * fai[k]);
        }

        node_deform.SetX(nodeTemp1.X() + Delta_xyz.X());
        node_deform.SetY(nodeTemp1.Y() + Delta_xyz.Y());
        node_deform.SetZ(nodeTemp1.Z() + Delta_xyz.Z());

        fai.clear();
        v_ReturnNode[i] = node_deform;
    }
    return v_ReturnNode;
}

void AeroStaticMethod::RebuildOriginalGlobalNode(std::vector<Vector> v_weight, std::vector<Vector> v_wallNode)
{

    std::vector<Node> v_Node;
    int number;
    const int n = globalMesh->GetNodeNumber();
    if (processorID == 0)
    {
        number = n / npart;
        int n_mod = n % npart;
        Print("number:" + ToString(number) + " n " + ToString(n) + " n_mod " + ToString(n_mod));
        v_Node.resize(number);
        for (int i = 0; i < number; i++)
            v_Node[i] = globalMesh->GetNode(i);

        for (int i = 1; i < npart; i++)
        {
            std::vector<Node> v_Node_temp;
            if (i != npart - 1)
            {
                v_Node_temp.resize(number);
                for (int j = number * i; j < number * (i + 1); j++)
                    v_Node_temp[j - number * i] = globalMesh->GetNode(j);
            }
            else
            {
                v_Node_temp.resize(n_mod + number);
                for (int j = number * i; j < n; j++)
                    v_Node_temp[j - number * i] = globalMesh->GetNode(j);
            }

            mpi_world.send(i, 7, v_Node_temp);
            v_Node_temp.clear();
        }
    }
    else
    {
        mpi_world.recv(0, 7, v_Node);
    }
    std::vector<Node> v_RebuildNode = this->RebuildNode(v_weight, v_wallNode, v_Node);
    if (processorID == 0)
    {
        std::vector<Node> v_globalNode;
        v_globalNode.resize(n);
        for (int i = 0; i < v_RebuildNode.size(); i++)
            v_globalNode[i] = v_RebuildNode[i];

        for (int i = 1; i < npart; i++)
        {
            std::vector<Node> v_tempdNode;
            mpi_world.recv(i, 8, v_tempdNode);
            for (int j = 0; j < v_tempdNode.size(); j++)
                v_globalNode[j + number * i] = v_tempdNode[j];
        }

        this->CopyFile();
        this->cgns2cgns(v_globalNode);
    }
    else
    {
        mpi_world.send(0, 8, v_RebuildNode);
    }
}

void AeroStaticMethod::UpdateSubMeshAndParallelBoundary()
{

    subMesh->CalculateCenterAndVolume();
    mpi_world.barrier();

#if defined(_BaseParallelMPI_)

    ElementField<Vector> elementCenter(subMesh, Vector0);
    for (int fineID = 0; fineID < subMesh->GetElementNumberReal(); fineID++)
        elementCenter.SetValue(fineID, subMesh->GetElement(fineID).GetCenter());
    elementCenter.SetGhostlValueParallel(); //
    elementCenter.SetGhostValueMultigrid(); //

    for (int i = 0; i < subMesh->GetElementNumberAll(); ++i)
    {
        const Vector e_center = elementCenter.GetValue(i);
        subMesh->SetElementCenter(i, e_center);
    }

    ElementField<double> elementVolume(subMesh, Scalar0);
    for (int fineID = 0; fineID < subMesh->GetElementNumberReal(); fineID++)
        elementVolume.SetValue(fineID, subMesh->GetElement(fineID).GetVolume());
    elementVolume.SetGhostlValueParallel(); //
    elementVolume.SetGhostValueMultigrid(); //

    for (int i = 0; i < subMesh->GetElementNumberAll(); ++i)
    {
        const Scalar e_Volume = elementVolume.GetValue(i);
        subMesh->SetElementVolume(i, e_Volume);
    }

    //
    const int boundarySize = subMesh->GetBoundarySize();
    for (int i = 0; i < boundarySize; i++)
    {
        const int faceSize = subMesh->GetBoundaryFaceSize(i);
        for (int j = 0; j < faceSize; j++)
        {
            const int &faceID = subMesh->GetBoundaryFaceID(i, j);
            const int &ownerID = subMesh->GetFace(faceID).GetOwnerID();
            const int &neighID = subMesh->GetFace(faceID).GetNeighborID();
            const Vector &faceNormal = subMesh->GetFace(faceID).GetNormal();

            Vector distance = subMesh->GetFace(faceID).GetCenter() - subMesh->GetElement(ownerID).GetCenter();
            distance = (distance & faceNormal) * faceNormal;
            Vector e_center = subMesh->GetElement(ownerID).GetCenter() + 2.0 * distance;
            subMesh->SetElementCenter(neighID, e_center);

            const Scalar &e_Volume = subMesh->GetElement(ownerID).GetVolume();
            subMesh->SetElementVolume(neighID, e_Volume);
        }
    }

    if (processorID == 0)
        Print("\n 并行边界 处理完成");

#endif
}

void AeroStaticMethod::BuildMapRelation()
{

    if (processorID == 0)
    {

        std::vector<int>::iterator it;
        int n_boundary = v_aeroMeshData.size();
        std::map<int, int> map0;

        for (int i = 0; i < n_boundary; i++)
        {
            int ncfd = v_aeroMeshData[i].ncfd;
            for (int j = 0; j < ncfd; j++)
            {
                int id = v_aeroMeshData[i].v_id[j];
                std::map<int, int> map0;
                map0.insert(std::make_pair(i, j));
                for (int k = i + 1; k < n_boundary; k++)
                {
                    it = std::find(v_aeroMeshData[k].v_id.begin(), v_aeroMeshData[k].v_id.end(), id);
                    if (it != v_aeroMeshData[k].v_id.end())
                    {
                        for (int kk = 0; kk < v_aeroMeshData[k].ncfd; kk++)
                        {
                            if (id == v_aeroMeshData[k].v_id[kk])
                                map0.insert(std::make_pair(k, kk));
                        }
                    }
                }
                if (map0.size() > 1)
                {
                    map_allWallNode.insert(std::make_pair(id, map0));
                    map_part.insert(std::make_pair(id, map0));
                }
                else
                {
                    map_allWallNode.insert(std::make_pair(id, map0));
                }

                map0.clear();
            }
        }
    }
}

void AeroStaticMethod::WriteLog(const std::string &message)
{
    std::ofstream logFile("fsi_run.log", std::ios::app);
    if (logFile.is_open())
    {
        auto t = std::time(nullptr);
        auto tm = *std::localtime(&t);
        char timeString[100];
        strftime(timeString, sizeof(timeString), "%Y-%m-%d %H:%M:%S", &tm);
        logFile << "[" << timeString << "]" << message << std::endl;
        logFile.close();
    }
    else
    {
        std::cerr << "Unable to open log file." << std::endl;
    }
};

int AeroStaticMethod::CopyFile()
{

    std::string SourceFile = data->GetFlowConfigure().GetStaticAero().CFDParameter.meshPath[0] + data->GetFlowConfigure().GetStaticAero().CFDParameter.fileName[0];
    std::string Newfile = data->GetFlowConfigure().GetStaticAero().CFDParameter.DeformMeshFile;
    std::ifstream in;
    std::ofstream out;
    try
    {
        in.open(SourceFile, std::ios::binary);
        if (in.fail())
        {
            std::cout << "Fail to open the source file;" << std::endl;
            in.close();
            out.close();
            return 0;
        }
        out.open(Newfile, std::ios::binary);
        if (out.fail())
        {
            std::cout << "Fail to open the new file;" << std::endl;
            in.close();
            out.close();
            return 0;
        }
        out << in.rdbuf();
        out.close();
        in.close();
        return 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Exception occurred during file copy: " << e.what() << std::endl;
        return 0; // 发生异常时返回0表示失败
    }
}

void AeroStaticMethod::cgns2cgns(std::vector<Node> v_globalNode)
{

    std::string input_fullfilename = data->GetFlowConfigure().GetStaticAero().CFDParameter.DeformMeshFile;
    int fileID, baseID = 1, cellDim;
    if (cg_open(input_fullfilename.c_str(), CG_MODE_MODIFY, &fileID))
        cg_error_exit();

    char basename[33];
    int physDim;
    if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim))
        cg_error_exit();

    cgsize_t isize[3][1];
    char zonename[33];
    int zoneID = 1;
    if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0]))
        cg_error_exit();
    int nodeSize = isize[0][0];
    std::fstream file;
    Scalar temp;

    cgsize_t irmin = 1, irmax = nodeSize;
    Scalar *coor = new Scalar[nodeSize];
    Scalar *coor_x = new Scalar[nodeSize];
    Scalar *coor_y = new Scalar[nodeSize];
    Scalar *coor_z = new Scalar[nodeSize];
    char coordname_x[33], coordname_y[33], coordname_z[33];
    DataType_t dataType;
    int coodID;

    if (cg_coord_info(fileID, baseID, zoneID, 1, &dataType, coordname_x))
        cg_error_exit();
    if (cg_coord_read(fileID, baseID, zoneID, coordname_x, dataType, &irmin, &irmax, coor))
        cg_error_exit();

        ARI_OMP(parallel for schedule(static))
        for (int j = 0; j < v_globalNode.size(); j++)
        {
            coor_x[j] = v_globalNode[j].X();
            coor_y[j] = v_globalNode[j].Y();
            if (physDim == 3)
            {
                coor_z[j] = v_globalNode[j].Z();
            }
        }
        if (cg_coord_write(fileID, baseID, zoneID, dataType, coordname_x, coor_x, &coodID))
            cg_error_exit();

        if (cg_coord_info(fileID, baseID, zoneID, 2, &dataType, coordname_y))
            cg_error_exit();
        if (cg_coord_read(fileID, baseID, zoneID, coordname_y, dataType, &irmin, &irmax, coor))
            cg_error_exit();
        if (cg_coord_write(fileID, baseID, zoneID, dataType, coordname_y, coor_y, &coodID))
            cg_error_exit();

        if (physDim == 3)
        {
            if (cg_coord_info(fileID, baseID, zoneID, 3, &dataType, coordname_z))
                cg_error_exit();
            if (cg_coord_read(fileID, baseID, zoneID, coordname_z, dataType, &irmin, &irmax, coor))
                cg_error_exit();
            if (cg_coord_write(fileID, baseID, zoneID, dataType, coordname_z, coor_z, &coodID))
                cg_error_exit();
        }

        if (cg_close(fileID))
            cg_error_exit();
}

std::vector<Node> AeroStaticMethod::FindAndGatherGlobalNode()
{
    int n = subMesh->GetNodeNumber();
    std::vector<Node> v_Nodetemp;
    std::vector<int> v_NodetempID;
    std::vector<int>::iterator it;
    std::vector<Node> v_globalNode0;
    std::vector<int> v_globalNodeID0;

    std::vector<Node> v_globalNode;

    int totalNodeSize = n, maxNodeIDGlobal = -1;
    for (int i = 0; i < n; i++)
        maxNodeIDGlobal = Max(maxNodeIDGlobal, subMesh->GetNodeGlobalID(i));

    int maxNodeIDGlobalAll = -1;
    int totalNodeSizeAll = 0;
    boost::mpi::all_reduce(mpi_world, maxNodeIDGlobal, maxNodeIDGlobalAll, boost::mpi::maximum<int>());
    boost::mpi::all_reduce(mpi_world, totalNodeSize, totalNodeSizeAll, std::plus<int>());
    maxNodeIDGlobal = maxNodeIDGlobalAll;
    totalNodeSize = totalNodeSizeAll;

    if (processorID == 0)
    {
        v_globalNode0.reserve(totalNodeSize);
        v_globalNodeID0.reserve(totalNodeSize);

        std::vector<bool> existFlag(maxNodeIDGlobal, false);
        for (int i = 0; i < n; i++)
        {
            const Node nodeTemp1 = subMesh->GetNode(i);
            const int ID = subMesh->GetNodeGlobalID(i);
            v_globalNode0.push_back(nodeTemp1);
            v_globalNodeID0.push_back(ID);
            existFlag[ID] = true;
        }

        for (int i = 1; i < npart; i++)
        {
            std::vector<Node> v1;
            std::vector<int> v2;
            mpi_world.recv(i, 77, v1);
            mpi_world.recv(i, 78, v2);
            for (int j = 0; j < v1.size(); j++)
            {
                Node node1 = v1[j];
                int id = v2[j];

                if (!existFlag[id])
                {
                    v_globalNode0.push_back(node1);
                    v_globalNodeID0.push_back(id);
                    existFlag[id] = true;
                }
            }
            v1.clear();
            v2.clear();
        }

        v_globalNode.resize(v_globalNode0.size());
        for (int i = 0; i < v_globalNode0.size(); i++)
        {
            v_globalNode[v_globalNodeID0[i]] = v_globalNode0[i];
        }

        for (int i = 1; i < npart; i++)
            mpi_world.send(i, 79, v_NodetempID);
    }
    else
    {

        v_Nodetemp.resize(n);
        v_NodetempID.resize(n);
        for (int i = 0; i < n; i++)
        {
            v_Nodetemp[i] = subMesh->GetNode(i);
            v_NodetempID[i] = subMesh->GetNodeGlobalID(i);
        }

        mpi_world.send(0, 77, v_Nodetemp);
        mpi_world.send(0, 78, v_NodetempID);

        mpi_world.recv(0, 79, v_globalNode);
    }

    MPIBarrier();

    return v_globalNode;
}

std::vector<std::vector<double>> AeroStaticMethod::Lapack_degv(std::vector<std::vector<double>> css, int n)
{
    std::vector<std::vector<double>> cssinv;
#if defined(_EnableMKL_)
    double *A = new double[n * n];
    double *A_inv = new double[n * n];
    lapack_int *ipiv = new lapack_int[n];
    int info;
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < n; j++)
            A[i * n + j] = css[i][j];
    }

    for (int i = 0; i < n; ++i)
    {
        for (int j = 0; j < n; ++j)
        {
            A_inv[i * n + j] = (i == j) ? 1.0 : 0.0;
        }
    }

    info = LAPACKE_dgesv(LAPACK_ROW_MAJOR, n, n, A, n, ipiv, A_inv, n);
    if (info != 0)
    {
        std::cerr << "LAPACK Error compute A_inv: " << std::endl;
    }

    cssinv.resize(n);
    for (int i = 0; i < n; i++)
        cssinv[i].resize(n);

    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < n; j++)
            cssinv[i][j] = A_inv[i * n + j];
    }
#endif
    return cssinv;
}

std::vector<Vector> AeroStaticMethod::ReadPreviousDeformationFromWallDat(int id_aero)
{
    std::vector<Vector> prev_deform;
    prev_deform.resize(v_aeroMeshData[id_aero].ncfd, Vector0);

    std::ifstream file("WallTecplot_prev.plt");
    if (!file.is_open())
    {
        return prev_deform;
    }

    std::string line;
    bool found_part = false;
    std::string part_name = v_aeroMeshData[id_aero].PartName;

    // 查找对应部件
    int num;
    while (std::getline(file, line))
    {
        if (line.find("zone t=\"" + part_name) != std::string::npos)
        {
            found_part = true;
            size_t pos_i = line.find("i=1");
            if (pos_i != std::string::npos)
            {
                pos_i += 2;
                size_t pos_j = line.find(", j=");
                if (pos_j != std::string::npos)
                {
                    std::string num_str = line.substr(pos_i, pos_j - pos_i);
                    num = std::stoi(num_str);
                }
            }
            break;
        }
    }

    if (!found_part)
    {
        file.close();
        Print("part_name not found\n");
        return prev_deform;
    }

    // 读取该部件的节点数据
    std::map<int, Vector> id_pos_map;
    double x, y, z, id;

    for (int i = 0; i < num; i++)
    {
        file >> x >> y >> z >> id;
        id_pos_map[id] = Vector(x, y, z);
    }

    // 根据v_aeroMeshData中的id顺序构建变形量
    for (int i = 0; i < v_aeroMeshData[id_aero].ncfd; i++)
    {
        int node_id = v_aeroMeshData[id_aero].v_id[i];
        if (id_pos_map.find(node_id) != id_pos_map.end())
        {
            Vector original = v_aeroMeshData[id_aero].v_xyz_o[i];
            Vector current = id_pos_map[node_id];
            prev_deform[i] = Vector(
                current.X() - original.X(),
                current.Y() - original.Y(),
                current.Z() - original.Z());
        }
    }

    file.close();
    return prev_deform;
}

bool AeroStaticMethod::FileExists(const std::string &wtfilename)
{
    std::ifstream file(wtfilename);
    return file.good();
}

// 计算自适应松弛因子
double AeroStaticMethod::CalculateAdaptiveRelaxationFactor(double currentMaxChange, double previousMaxChange, double baseAlpha)
{
    double adaptiveAlpha = baseAlpha;

    // 如果这是第一次迭代，使用基础松弛因子
    if (relaxationIterationCount == 0)
    {
        return adaptiveAlpha;
    }

    // 计算变形量变化趋势
    double changeRatio = 1.0;
    if (previousMaxChange > 1e-12)
    {
        changeRatio = currentMaxChange / previousMaxChange;
    }

    // 自适应调整策略
    if (changeRatio < 0.5)
    {
        // 收敛很快，可以增大松弛因子
        adaptiveAlpha = Min(1.0, baseAlpha * 1.2);
    }
    else if (changeRatio > 1.5)
    {
        // 发散或收敛慢，减小松弛因子
        adaptiveAlpha = Max(0.1, baseAlpha * 0.8);
    }
    else if (changeRatio > 0.95 && changeRatio < 1.05)
    {
        // 变化很小，可能接近收敛，保持当前松弛因子
        adaptiveAlpha = baseAlpha;
    }

    // 基于历史收敛性调整
    if (relaxationHistory.size() >= 3)
    {
        bool isOscillating = true;
        for (int i = relaxationHistory.size() - 3; i < relaxationHistory.size() - 1; i++)
        {
            if (relaxationHistory[i + 1] <= relaxationHistory[i])
            {
                isOscillating = false;
                break;
            }
        }

        if (isOscillating)
        {
            // 检测到振荡，减小松弛因子
            adaptiveAlpha = Max(0.1, adaptiveAlpha * 0.7);
        }
    }

    return adaptiveAlpha;
}

// 更新松弛因子历史记录
void AeroStaticMethod::UpdateRelaxationHistory(double maxChange)
{
    relaxationHistory.push_back(maxChange);

    // 保持历史记录在合理范围内
    if (relaxationHistory.size() > 50)
    {
        relaxationHistory.erase(relaxationHistory.begin());
    }

    previousMaxDeformChange = maxChange;
    relaxationIterationCount++;
}

// 静气弹专用的RBF求解方法，直接使用Lapack_degv
void AeroStaticMethod::SolveRBFWithStaticAeroelasticMethod(std::vector<Vector> &v_weight, std::vector<Vector> &v_NodeLocal, std::vector<Vector> &v_Node_deformLocal)
{
    int processorID = GetMPIRank();
    int n = v_NodeLocal.size();

    if (processorID == 0)
        Print("\n使用静气弹专用RBF求解方法");

    if (processorID == 0)
        Print("节点数: " + std::to_string(n) + ", 使用LAPACK直接求解");

    // 构建RBF矩阵
    std::vector<std::vector<double>> rbfMatrix(n, std::vector<double>(n));
    double R = data->GetFlowConfigure().GetStaticAero().CFDParameter.Radiu;

    // 填充RBF矩阵
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            double distance = sqrt(
                pow(v_NodeLocal[j].X() - v_NodeLocal[i].X(), 2) +
                pow(v_NodeLocal[j].Y() - v_NodeLocal[i].Y(), 2) +
                pow(v_NodeLocal[j].Z() - v_NodeLocal[i].Z(), 2)
            );
            double yita = distance / R;

            if (yita > 1.0) {
                rbfMatrix[i][j] = 0.0;
            } else {
                rbfMatrix[i][j] = pow((1 - yita), 4) * (4 * yita + 1.0);
            }
        }
    }

    // 构建右端项矩阵 (n x 3)
    std::vector<std::vector<double>> rhsMatrix(n, std::vector<double>(3));
    for (int i = 0; i < n; i++) {
        rhsMatrix[i][0] = v_Node_deformLocal[i].X() - v_NodeLocal[i].X(); // X方向变形
        rhsMatrix[i][1] = v_Node_deformLocal[i].Y() - v_NodeLocal[i].Y(); // Y方向变形
        rhsMatrix[i][2] = v_Node_deformLocal[i].Z() - v_NodeLocal[i].Z(); // Z方向变形
    }

    if (processorID == 0)
        Print("\t构建RBF矩阵完成，开始LAPACK求解");

    // 使用静气弹专用的Lapack_degv方法求解
    std::vector<std::vector<double>> rbfInverse = Lapack_degv(rbfMatrix, n);

    // 计算权重系数：weight = rbfInverse * rhs
    v_weight.resize(n);
    for (int i = 0; i < n; i++) {
        double wx = 0.0, wy = 0.0, wz = 0.0;
        for (int j = 0; j < n; j++) {
            wx += rbfInverse[i][j] * rhsMatrix[j][0];
            wy += rbfInverse[i][j] * rhsMatrix[j][1];
            wz += rbfInverse[i][j] * rhsMatrix[j][2];
        }
        v_weight[i].SetX(wx);
        v_weight[i].SetY(wy);
        v_weight[i].SetZ(wz);
    }

    if (processorID == 0)
        Print("\t静气弹RBF权重系数计算完成");
}
