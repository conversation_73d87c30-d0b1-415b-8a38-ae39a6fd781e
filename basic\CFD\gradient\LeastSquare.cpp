﻿#include "basic/CFD/gradient/LeastSquare.h"
#include <unordered_set>

namespace Gradient
{

LeastSquare::LeastSquare(Mesh *mesh_, const bool &LSVertexFlag_, const bool nodeCenter_)
    : GradientBase(mesh_, nodeCenter_), LSVertexFlag(LSVertexFlag_)
{
    if (LSVertexFlag) this->SearchNodeAdjacentElements();
    else              this->SearchFaceAdjacentElements();
    
    this->CalculateLSWeight();
}

LeastSquare::~LeastSquare()
{
}

void LeastSquare::CalculateScalar(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi)
{
    this->Calculate(phi, gradPhi);
}

void LeastSquare::CalculateVector(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi)
{
    this->Calculate(phi, gradPhi);
}

template void LeastSquare::Calculate(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void LeastSquare::Calculate(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void LeastSquare::Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    const Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    // 内部面循环，基于QR分解计算最小二乘梯度
    const int connectNumber = adjacentConnect.size();
    for (int index = 0; index < connectNumber; ++index)
    {
        // 获取单元编号
        const int &ownerID = adjacentConnect[index].first;
        const int &neighID = adjacentConnect[index].second;

        // 梯度计算
        const Type delta = phi.GetValue(neighID) - phi.GetValue(ownerID);
        gradPhi.AddValue(ownerID,  LSWeight[index].first * delta);
        gradPhi.AddValue(neighID, -LSWeight[index].second * delta);
    }
    
    // 边界面循环
    if(!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0, m = connectNumber; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                const int &neighID = mesh->GetFace(faceID).GetNeighborID();

                // 梯度计算
                const Type delta = 0.5 * (phi.GetValue(neighID) - phi.GetValue(ownerID)); // 采用面心值和单元值计算
                gradPhi.AddValue(ownerID,  LSWeight[m++].first * delta);
            }
        }
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

void LeastSquare::SearchFaceAdjacentElements()
{
    // 内部面循环
    const int innerNumber = mesh->GetInnerFaceNumberInDomain();
    adjacentConnect.resize(innerNumber);
    for (int index = 0; index < innerNumber; ++index)
    {
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        adjacentConnect[index].first = mesh->GetFace(faceID).GetOwnerID();
        adjacentConnect[index].second = mesh->GetFace(faceID).GetNeighborID();
    }
}

void LeastSquare::SearchNodeAdjacentElements()
{
	const int elementSize = mesh->GetElementNumberReal();
	const int nodeSize = mesh->GetNodeNumber();
	
	// step 1 统计点相邻的单元数量
	std::vector<int> count(nodeSize, 0);

	// step 1.1 遍历真实单元
	for (auto elemID = 0; elemID < elementSize; ++elemID)
	{
		const Element &elem = this->mesh->GetElement(elemID);
		for (int index = 0; index < elem.GetNodeSize(); ++index)
			count[elem.GetNodeID(index)]++;
	}

	// step 1.2 遍历并行边界虚单元
    for (int i = 0; i < mesh->GetGhostElementsParallel().size(); ++i)
    {
        const auto &ghostElements = mesh->GetGhostElementsParallel()[i];
        for (int j = 0; j < ghostElements.size(); ++j)
        {
            const int &faceID = ghostElements[j].GetID();
            const Face &face = mesh->GetFace(faceID);
		    for (int index1 = 0; index1 < face.GetNodeSize(); ++index1)
		    	count[face.GetNodeID(index1)]++;
        }
    }

	// step 2 生成点相邻单元编号列表
	std::vector<std::vector<int>> nodeElemID(nodeSize);
	for (auto i = 0; i < nodeSize; ++i) nodeElemID[i].reserve(count[i]);

	// step 2.1 遍历真实单元
	for (auto elemID = 0; elemID < elementSize; ++elemID)
	{
		const Element &elem = this->mesh->GetElement(elemID);
		for (int index = 0; index < elem.GetNodeSize(); ++index)
			nodeElemID[elem.GetNodeID(index)].push_back(elemID);
	}
    
	// step 2.2 遍历并行边界虚单元
    for (int i = 0; i < mesh->GetGhostElementsParallel().size(); ++i)
    {
        const auto &ghostElements = mesh->GetGhostElementsParallel()[i];
        for (int j = 0; j < ghostElements.size(); ++j)
        {
            const int &faceID = ghostElements[j].GetID();
            const Face &face = mesh->GetFace(faceID);
		    for (int index1 = 0; index1 < face.GetNodeSize(); ++index1)
			    nodeElemID[face.GetNodeID(index1)].push_back(face.GetNeighborID());
        }
    }

    std::vector<int>().swap(count);

	// 形成目标单元的点相邻单元列表并统计总相邻数量
    std::vector<std::unordered_set<int>> adjcentElemIDSet(elementSize);
    int adjcentConnectNum = 0;
	for (int elemID = 0; elemID < elementSize; elemID++)
	{
		const Element &elem = mesh->GetElement(elemID);
		const int nodeSize = elem.GetNodeSize();
		for (int i = 0; i < nodeSize; i++)
		{
			const int &nodeID = elem.GetNodeID(i);
			const std::vector<int> list = nodeElemID[nodeID];
			for (int j = 0; j < list.size(); j++)
            {
                if (list[j] > elemID) adjcentElemIDSet[elemID].insert(list[j]);
            }
		}
        adjcentConnectNum += adjcentElemIDSet[elemID].size();
    }
    std::vector<std::vector<int>>().swap(nodeElemID);

	// 形成单元与点相邻单元连接信息
    adjacentConnect.resize(adjcentConnectNum);
	for (int elemID = 0, index = 0; elemID < elementSize; elemID++)
	{
        const auto &setTemp = adjcentElemIDSet[elemID];
		for (auto it = setTemp.begin(); it != setTemp.end(); ++it)
		{
            adjacentConnect[index].first = elemID;
            adjacentConnect[index].second = *it;
            index++;
		}
    }
}

void LeastSquare::CalculateLSWeight()
{
    // 计算最小二乘矩阵
    std::vector<std::vector<Scalar>> mat;
    this->CalculateRMatrix(mat);
    
    // 大小统计
    int totalSize = adjacentConnect.size();
    if(!nodeCenter)
    {
        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        {
            totalSize += mesh->GetBoundaryFaceNumberInDomain(patchID);
        }
    }
    LSWeight.resize(totalSize);

    // 基于QR分解计算最小二乘梯度
    const int connectSize = adjacentConnect.size();
    for (int index = 0; index < connectSize; ++index)
    {
        // 获取单元编号
        const int &ownerID = adjacentConnect[index].first;
        const int &neighID = adjacentConnect[index].second;

        // 单元距离
        const Vector dist = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
        const Scalar dx = dist.X();
        const Scalar dy = dist.Y();
        const Scalar dz = dist.Z();
        const Scalar ww = 1.0 / (dist & dist);

        // R矩阵元素
        const Scalar &l_11 = mat[ownerID][I_11];
        const Scalar &l_12 = mat[ownerID][I_12];
        const Scalar &l_13 = mat[ownerID][I_13];
        const Scalar &l_22 = mat[ownerID][I_22];
        const Scalar &l_23 = mat[ownerID][I_23];
        const Scalar rl11 = 1.0 / l_11;
        const Scalar rl22 = 1.0 / l_22;
        const Scalar &rl33_sqr = mat[ownerID][I_33];
        const Scalar &r_11 = mat[neighID][I_11];
        const Scalar &r_12 = mat[neighID][I_12];
        const Scalar &r_13 = mat[neighID][I_13];
        const Scalar &r_22 = mat[neighID][I_22];
        const Scalar &r_23 = mat[neighID][I_23];
        const Scalar rr11 = 1.0 / r_11;
        const Scalar rr22 = 1.0 / r_22;
        const Scalar &rr33_sqr = mat[neighID][I_33];

        // owner单元QR分解计算系数
        const Scalar chi1l = (l_12 * l_23 * rl22 - l_13) * rl11;
        const Scalar chi2l = (dy - l_12 * rl11 * dx) * rl22 * rl22;
        const Scalar chi3l = (dz - l_23 * rl22 * dy + chi1l * dx) * rl33_sqr;
        const Scalar wxl = ww * (dx * rl11 * rl11 - chi2l * l_12 * rl11 + chi1l * chi3l);
        const Scalar wyl = ww * (chi2l - l_23 * rl22 * chi3l);
        const Scalar wzl = ww * chi3l;
        LSWeight[index].first = Vector(wxl, wyl, wzl);

        // neigh单元QR分解计算系数
        const Scalar chi1r = (r_12 * r_23 * rr22 - r_13) * rr11;
        const Scalar chi2r = (dy - r_12 * rr11 * dx) * rr22 * rr22;
        const Scalar chi3r = (dz - r_23 * rr22 * dy + chi1r * dx) * rr33_sqr;
        const Scalar wxr = -ww * (dx * rr11 * rr11 - chi2r * r_12 * rr11 + chi1r * chi3r);
        const Scalar wyr = -ww * (chi2r - r_23 * rr22 * chi3r);
        const Scalar wzr = -ww * chi3r;
        LSWeight[index].second = Vector(wxr, wyr, wzr);
    }

    // 边界面循环
    if(!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0, m = connectSize; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const int &ownerID = face.GetOwnerID();

                const Vector dist = face.GetCenter() - mesh->GetElement(ownerID).GetCenter();
                const Scalar dx = dist.X();
                const Scalar dy = dist.Y();
                const Scalar dz = dist.Z();
                const Scalar ww = 1.0 / (dist & dist);

                // R矩阵元素
                const Scalar &l_11 = mat[ownerID][I_11];
                const Scalar &l_12 = mat[ownerID][I_12];
                const Scalar &l_13 = mat[ownerID][I_13];
                const Scalar &l_22 = mat[ownerID][I_22];
                const Scalar &l_23 = mat[ownerID][I_23];
                const Scalar &rl33_sqr = mat[ownerID][I_33];
                const Scalar rl11 = 1.0 / l_11;
                const Scalar rl22 = 1.0 / l_22;

                // QR分解计算系数
                const Scalar chi1l = (l_12 * l_23 * rl22 - l_13) * rl11;
                const Scalar chi2l = (dy - l_12 * rl11 * dx) * rl22 * rl22;
                const Scalar chi3l = (dz - l_23 * rl22 * dy + chi1l * dx) * rl33_sqr;
                const Scalar wxl = ww * (dx * rl11 * rl11 - chi2l * l_12 * rl11 + chi1l * chi3l);
                const Scalar wyl = ww * (chi2l - l_23 * rl22 * chi3l);
                const Scalar wzl = ww * chi3l;
                LSWeight[m++].first = Vector(wxl, wyl, wzl);
            }
        }
    }
}

void LeastSquare::CalculateRMatrix(std::vector<std::vector<Scalar>> &mat)
{
    // 数据初始化
    std::vector<std::vector<Scalar>> sum(mesh->GetElementNumberAll());
    mat.resize(mesh->GetElementNumberAll());
    for (int elemID = 0; elemID < sum.size(); elemID++)
    {
        sum[elemID].resize(6, Scalar0);
        mat[elemID].resize(6, Scalar0);
    }

    // 内部面循环
    const int connectSize = adjacentConnect.size();
    for (int index = 0; index < connectSize; ++index)
    {
        // 获取单元编号
        const int &ownerID = adjacentConnect[index].first;
        const int &neighID = adjacentConnect[index].second;

        const Vector dist = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
        const Vector tmp = dist * 1.0 / (dist & dist);
        const Scalar wdxx = tmp.X() * dist.X(), wdyy = tmp.Y() * dist.Y(), wdzz = tmp.Z() * dist.Z();
        const Scalar wdxy = tmp.X() * dist.Y(), wdxz = tmp.X() * dist.Z(), wdyz = tmp.Y() * dist.Z();
        sum[ownerID][I_11] += wdxx; sum[neighID][I_11] += wdxx;
        sum[ownerID][I_22] += wdyy; sum[neighID][I_22] += wdyy;
        sum[ownerID][I_33] += wdzz; sum[neighID][I_33] += wdzz;
        sum[ownerID][I_12] += wdxy; sum[neighID][I_12] += wdxy;
        sum[ownerID][I_13] += wdxz; sum[neighID][I_13] += wdxz;
        sum[ownerID][I_23] += wdyz; sum[neighID][I_23] += wdyz;
    }

    // 边界面循环
    if(!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const int &ownerID = face.GetOwnerID();

                const Vector dist = face.GetCenter() - mesh->GetElement(ownerID).GetCenter();
                const Vector tmp = 1.0 / (dist & dist) * dist;
                const Scalar wdxx = tmp.X() * dist.X(), wdyy = tmp.Y() * dist.Y(), wdzz = tmp.Z() * dist.Z();
                const Scalar wdxy = tmp.X() * dist.Y(), wdxz = tmp.X() * dist.Z(), wdyz = tmp.Y() * dist.Z();
                sum[ownerID][I_11] += wdxx; sum[ownerID][I_22] += wdyy; sum[ownerID][I_33] += wdzz;
                sum[ownerID][I_12] += wdxy; sum[ownerID][I_13] += wdxz; sum[ownerID][I_23] += wdyz;
            }
        }
    }
    if(mesh->GetMeshDimension() == Mesh::MeshDim::md2D)
    {
        for (int elemID = 0; elemID < sum.size(); elemID++)
            sum[elemID][I_33] = INF;
    }

    // 计算R矩阵
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elemID = mesh->GetElementIDInDomain(index);
        
        mat[elemID][I_11] = sqrt(sum[elemID][I_11]); 
        Scalar rr11 = 1.0 / mat[elemID][I_11];
        mat[elemID][I_12] = sum[elemID][I_12] * rr11;
        mat[elemID][I_13] = sum[elemID][I_13] * rr11;
        mat[elemID][I_22] = sqrt(sum[elemID][I_22] - (mat[elemID][I_12] * mat[elemID][I_12]));
        Scalar rr22 = 1.0 / mat[elemID][I_22];
        mat[elemID][I_23] = (sum[elemID][I_23] * rr22) - sum[elemID][I_13] * mat[elemID][I_12] * rr11 * rr22;
        mat[elemID][I_33] = 1.0 / (sum[elemID][I_33] - (mat[elemID][I_13] * mat[elemID][I_13]) - (mat[elemID][I_23] * mat[elemID][I_23]));
    }
}

} // namespace FieldManipulation