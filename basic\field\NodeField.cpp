﻿#include "basic/field/NodeField.h"
#include "basic/common/InterpolationTools.h"

template NodeField<Int>::<PERSON><PERSON><PERSON><PERSON>(const NodeField<Int> &field, const std::string name);
template NodeField<Scalar>::Node<PERSON><PERSON>(const NodeField<Scalar> &field, const std::string name);
template NodeField<Vector>::Node<PERSON>ield(const NodeField<Vector> &field, const std::string name);
template NodeField<Tensor>::Node<PERSON>ield(const NodeField<Tensor> &field, const std::string name);
template NodeField<Matrix>::NodeField(const NodeField<Matrix> &field, const std::string name);
template<class Type>
NodeField<Type>::NodeField(const NodeField<Type> &field, const std::string name)
:BaseField<Type>(field.p_blockMesh, name)
{
    this->Initialize(field.v_value);
}

//Create node Field
template void NodeField<Int>::Create();
template void NodeField<Scalar>::Create();
template void NodeField<Vector>::Create();
template void NodeField<Tensor>::Create();
template void NodeField<Matrix>::Create();
template<class Type>
void NodeField<Type>::Create()
{
    if (false == BaseField<Type>::Existence())
    {
        this->v_value.resize(this->p_blockMesh->GetNodeNumber());
        this->fs_status = BaseField<Type>::fsCreated;
    }
}

//Initialize with a fixed value
template void NodeField<Int>::Initialize(Int initalValue);
template void NodeField<Scalar>::Initialize(Scalar initalValue);
template void NodeField<Vector>::Initialize(Vector initalValue);
template void NodeField<Tensor>::Initialize(Tensor initalValue);
template void NodeField<Matrix>::Initialize(Matrix initalValue);
template<class Type>
void NodeField<Type>::Initialize(Type initalValue)
{
    this->Create();
    
    for (int i = 0; i < (int)this->v_value.size(); ++i)
    {
        BaseField<Type>::SetValue(i, initalValue);
    }
    this->fs_status = BaseField<Type>::fsAssigned;
}

//Initialize with a zero value
template<> void NodeField<Int>::Initialize(){ this->Initialize(0); }
template<> void NodeField<Scalar>::Initialize(){ this->Initialize(Scalar0); }
template<> void NodeField<Vector>::Initialize(){ this->Initialize(Vector0); }
template<> void NodeField<Tensor>::Initialize(){ this->Initialize(Tensor0); }
template<> void NodeField<Matrix>::Initialize(){ this->Initialize(Matrix(0, 0)); }

//Initialize with a list of values
template void NodeField<Int>::Initialize(const std::vector<Int>& valueList);
template void NodeField<Scalar>::Initialize(const std::vector<Scalar>& valueList);
template void NodeField<Vector>::Initialize(const std::vector<Vector>& valueList);
template void NodeField<Tensor>::Initialize(const std::vector<Tensor>& valueList);
template<class Type>
void NodeField<Type>::Initialize(const std::vector<Type>& valueList)
{
    this->Create();
    
    if (BaseField<Type>::v_value.size() != valueList.size())
    {
        FatalError("Cannot initialize field, Numbers of field points and given valueList are not consistent");
    }
    for (int i = 0; i < (int)BaseField<Type>::v_value.size(); ++i)
    {
        BaseField<Type>::SetValue(i, valueList[i]);
    }
    this->fs_status = BaseField<Type>::fsAssigned;
}

//Initialize with a given function (for 3D case)
template void NodeField<Int>::Initialize(Int(*udf)(Scalar, Scalar, Scalar));
template void NodeField<Scalar>::Initialize(Scalar(*udf)(Scalar, Scalar, Scalar));
template void NodeField<Vector>::Initialize(Vector(*udf)(Scalar, Scalar, Scalar));
template void NodeField<Tensor>::Initialize(Tensor(*udf)(Scalar, Scalar, Scalar));
template<class Type>
void NodeField<Type>::Initialize(Type(*udf)(Scalar, Scalar, Scalar))
{
    this->Create();
    
    for (int i = 0; i < (int)this->v_value.size(); ++i)
    {
        const Node& xyz = this->p_blockMesh->GetNode(i);
        BaseField<Type>::SetValue(i, udf(xyz.X(), xyz.Y(), xyz.Z()));
    }
    this->fs_status = BaseField<Type>::fsAssigned;
}

// "="overload
template NodeField<Int>& NodeField<Int>::operator = (const NodeField<Int>& rhs);
template NodeField<Scalar>& NodeField<Scalar>::operator = (const NodeField<Scalar>& rhs);
template NodeField<Vector>& NodeField<Vector>::operator = (const NodeField<Vector>& rhs);
template NodeField<Tensor>& NodeField<Tensor>::operator = (const NodeField<Tensor>& rhs);
template NodeField<Matrix>& NodeField<Matrix>::operator = (const NodeField<Matrix>& rhs);
template<class Type>
NodeField<Type>& NodeField<Type>::operator = (const NodeField<Type>& rhs)
{
    this->p_blockMesh = rhs.p_blockMesh;
    this->Initialize(rhs.v_value);
    this->fs_status = BaseField<Type>::fsAssigned;
    return *this;
}

// "="overload
template NodeField<Int>& NodeField<Int>::operator = (const Int& rhs);
template NodeField<Vector>& NodeField<Vector>::operator = (const Vector& rhs);
template NodeField<Tensor>& NodeField<Tensor>::operator = (const Tensor& rhs);
template NodeField<Matrix>& NodeField<Matrix>::operator = (const Matrix& rhs);
template<class Type>
NodeField<Type>& NodeField<Type>::operator = (const Type& rhs)
{
    this->Initialize(rhs);
    this->fs_status = BaseField<Type>::fsAssigned;
    return *this;
}

template<>
int NodeField<Scalar>::CheckAndLimit(const Scalar &minValue, const Scalar &maxValue)
{
    if (minValue > maxValue)
        FatalError("In NodeField<Scalar>::CheckAndLimit, minimum value is greater than the maximum one");

    if (false == this->Assignment())
        FatalError("NodeField<Scalar>::CheckAndLimit, value list is not given");

    std::string stringTemp = " of processor " + ToString(GetMPIRank());

    for (int i = 0; i < this->p_blockMesh->GetNodeNumber(); ++i)
    {
        const Scalar &phiC = this->GetValue(i);

        if (std::isinf(phiC))
        {
            FatalError(this->st_name + " at node " + ToString(i) + stringTemp + " is inf!");
            return i;
        }
        else if (std::isnan(phiC))
        {
            FatalError(this->st_name + " at node " + ToString(i) + stringTemp + " is nan!");
            return i;
        }
        else if (phiC < minValue)
        {
            Print(this->st_name + " at node " + ToString(i) + stringTemp + " is beyond minValue!");
            this->SetValue(i, minValue);
            return i;
        }
        else if (phiC > maxValue)
        {
            Print(this->st_name + " at node " + ToString(i) + stringTemp + " is beyond maxValue!");
            this->SetValue(i, maxValue);
            return i;
        }
        else
        {
        }
    }

    return -1;
}

template<>
int NodeField<Vector>::CheckAndLimit(const Scalar &minValue, const Scalar &maxValue)
{
    if (minValue > maxValue)
        FatalError("In NodeField<Vector>::CheckAndLimit, minimum value is greater than the maximum one");

    if (false == this->Assignment())
        FatalError("NodeField<Vector>::CheckAndLimit, value list is not given");

    std::string stringTemp = " of processor " + ToString(GetMPIRank());

    for (int i = 0; i < this->p_blockMesh->GetNodeNumber(); ++i)
    {
        const Vector &phiC = this->GetValue(i);

        if (std::isinf(phiC.X()) || std::isinf(phiC.Y()) || std::isinf(phiC.Z()))
        {
            FatalError(this->st_name + " at node " + ToString(i) + stringTemp + " is inf!");
            return i;
        }
        else if (std::isnan(phiC.X()) || std::isnan(phiC.Y()) || std::isnan(phiC.Z()))
        {
            FatalError(this->st_name + " at node " + ToString(i) + stringTemp + " is nan!");
            return i;
        }
        else if (phiC.X() < minValue || phiC.Y() < minValue || phiC.Z() < minValue)
        {
            Print(this->st_name + " at node " + ToString(i) + stringTemp + " is beyond minValue!");
            this->SetValue(i, Vector(Max(minValue, phiC.X()), Max(minValue, phiC.Y()), Max(minValue, phiC.Z())));
            return i;
        }
        else if (phiC.X() > maxValue || phiC.Y() > maxValue || phiC.Z() > maxValue)
        {
            Print(this->st_name + " at node " + ToString(i) + stringTemp + " is beyond maxValue!");
            this->SetValue(i, Vector(Min(maxValue, phiC.X()), Min(maxValue, phiC.Y()), Min(maxValue, phiC.Z())));
            return i;
        }
        else
        {
        }
    }

    return -1;
}

template<>
int NodeField<Tensor>::CheckAndLimit(const Scalar &minValue, const Scalar &maxValue)
{
    FatalError("NodeField<Tensor>::CheckAndLimit, CheckAndLimit is not supported");
    return -1;
}

template void NodeField<Int>::ReadFile(const std::string fileName, const bool binary);
template void NodeField<Scalar>::ReadFile(const std::string fileName, const bool binary);
template void NodeField<Vector>::ReadFile(const std::string fileName, const bool binary);
template void NodeField<Tensor>::ReadFile(const std::string fileName, const bool binary);
template<class Type>
void NodeField<Type>::ReadFile(const std::string fileName, const bool binary)
{
    std::fstream file;
    if(binary) file.open(fileName, std::ios::in | std::ios::binary);
    else       file.open(fileName, std::ios::in);
    if (!file) FatalError("File is not existed: " + fileName);
    IO::Read(file, this->v_value, binary);
    file.close();
    return;
}

template void NodeField<Int>::WriteFile(const std::string fileName, const bool binary);
template void NodeField<Scalar>::WriteFile(const std::string fileName, const bool binary);
template void NodeField<Vector>::WriteFile(const std::string fileName, const bool binary);
template void NodeField<Tensor>::WriteFile(const std::string fileName, const bool binary);
template<class Type>
void NodeField<Type>::WriteFile(const std::string fileName, const bool binary)
{
    std::fstream file;
    if(binary) file.open(fileName, std::ios::out | std::ios::binary);
    else       file.open(fileName, std::ios::out);
    IO::Write(file, this->v_value, binary);
    file.close();
    return;
}