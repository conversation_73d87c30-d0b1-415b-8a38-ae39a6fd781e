//  rational_constant.hpp  ---------------------------------------------------------------//
//  Copyright 2011 <PERSON> J<PERSON> Botet Escriba
//  Distributed under the Boost Software License, Version 1.0.
//  See http://www.boost.org/LICENSE_1_0.txt


#ifndef BOOST_RATIO_MPL_RATIONAL_CONSTANT_HPP
#define BOOST_RATIO_MPL_RATIONAL_CONSTANT_HPP

#include <boost/ratio/mpl/rational_c_tag.hpp>
#include <boost/ratio/mpl/numeric_cast.hpp>
#include <boost/ratio/mpl/arithmetic.hpp>
#include <boost/ratio/mpl/comparison.hpp>

#endif  // BOOST_RATIO_HPP
