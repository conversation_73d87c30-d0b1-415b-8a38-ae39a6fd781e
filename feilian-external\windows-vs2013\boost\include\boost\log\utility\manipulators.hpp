/*
 *          Copyright <PERSON><PERSON> 2007 - 2015.
 * Distributed under the Boost Software License, Version 1.0.
 *    (See accompanying file LICENSE_1_0.txt or copy at
 *          http://www.boost.org/LICENSE_1_0.txt)
 */
/*!
 * \file   manipulators.hpp
 * \author <PERSON><PERSON>
 * \date   06.11.2012
 *
 * This header includes all manipulators.
 */

#ifndef BOOST_LOG_UTILITY_MANIPULATORS_HPP_INCLUDED_
#define BOOST_LOG_UTILITY_MANIPULATORS_HPP_INCLUDED_

#include <boost/log/detail/config.hpp>

#include <boost/log/utility/manipulators/add_value.hpp>
#include <boost/log/utility/manipulators/to_log.hpp>
#include <boost/log/utility/manipulators/dump.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_LOG_UTILITY_MANIPULATORS_HPP_INCLUDED_
