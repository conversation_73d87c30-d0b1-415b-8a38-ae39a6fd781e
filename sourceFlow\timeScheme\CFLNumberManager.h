﻿# ifndef _sourceFlow_timeScheme_CFLNumberManager_
# define _sourceFlow_timeScheme_CFLNumberManager_

#include "sourceFlow/package/FlowPackage.h"

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 计算CFL数的类
 * 
 */
class CFLNumberManager
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    CFLNumberManager(Package::FlowPackage &flowPackage_);

    /**
     * @brief 析构函数
     * 
     */
    ~CFLNumberManager();
    
    /**
     * @brief 计算CFL数
     * 
     */
    void Calculate();

private:

    /**
     * @brief 计算质量残值
     * 
     */
    Scalar CalculateMonitorMassResidual();

    /**
     * @brief 计算deltaQ/Q,作为自动CFL第二个收敛判据
     * 
     */
    Scalar CalculateDeltaQDivideQ();

private:
	const Configure::Flow::FlowConfigure &flowConfigure; ///< 流场参数
    Package::FlowPackage &flowPackage; ///< 流场包
    Mesh *mesh; ///< 当前网格
    const Scalar currentLevel; ///< 当前网格层级
    Scalar &CFL; ///< CFL数

    // 变CFL数相关参数
    Scalar coarseRatio; ///< 粗网格与细网格CFL数比值
    int CFLType; ///< CFL数是否变化标识，0为常数，1为线性增长CFL数，2为自动CFL数
    int CFLGrowthStep; ///< CFL数增长步数
    Scalar CFLGrowthRatio; ///< CFL数增长率
    Scalar AutoCFLMax; ///< CFL数最大值
    Scalar AutoCFLMin; ///< CFL数最小值
    int currrentCFLStep; ///< 当前CFL数变化步数
    int startStep; ///< 起始步
    
    Scalar monitorResidualCurrent;
    Scalar monitorResidualOld;
    Scalar initial_residual_0;
    Scalar monitorMassResidual;///<定义质量残值判断
    Scalar DeltaQ_Divide_Q;

    Scalar epsilon;
    Scalar delta1;
    Scalar delta2;
    Scalar CFL0;
    Scalar CFLFactor;

    ElementField<Scalar> *rho; ///< 密度
    ElementField<Scalar> *rho0; ///< 上一步密度
    ElementField<Scalar> *residualMass; ///< 质量残值
    
    int nElementGlobal; ///< 总单元数
};

} // namespace Flow
} // namespace Time

#endif
