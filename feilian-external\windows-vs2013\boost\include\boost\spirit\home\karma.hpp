//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
//
//  Distributed under the Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(SPIRIT_KARMA_CORE_MARCH_06_2007_0833PM)
#define SPIRIT_KARMA_CORE_MARCH_06_2007_0833PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/char.hpp>
#include <boost/spirit/home/<USER>/string.hpp>
#include <boost/spirit/home/<USER>/numeric.hpp>
#include <boost/spirit/home/<USER>/operator.hpp>
#include <boost/spirit/home/<USER>/nonterminal.hpp>
#include <boost/spirit/home/<USER>/action.hpp>
#include <boost/spirit/home/<USER>/directive.hpp>
#include <boost/spirit/home/<USER>/auxiliary.hpp>
#include <boost/spirit/home/<USER>/binary.hpp>
#include <boost/spirit/home/<USER>/generate.hpp>
#include <boost/spirit/home/<USER>/generate_attr.hpp>
#include <boost/spirit/home/<USER>/generator.hpp>
#include <boost/spirit/home/<USER>/delimit_out.hpp>
#include <boost/spirit/home/<USER>/what.hpp>
#include <boost/spirit/home/<USER>/stream.hpp>
#include <boost/spirit/home/<USER>/auto.hpp>
#include <boost/spirit/home/<USER>/format.hpp>

#endif
