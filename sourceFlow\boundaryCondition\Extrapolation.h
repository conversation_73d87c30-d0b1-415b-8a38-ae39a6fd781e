﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Extrapolation.h
//! <AUTHOR>
//! @brief 插值边界条件类,物面场信息由内场直接赋值得到
//! @date  2021-4-20
//
//------------------------------修改日志----------------------------------------
//
// 2021-04-20 李艳亮
// 说明：新建
// 
//------------------------------------------------------------------------------
#ifndef  _sourceFlow_boundaryCondition_Extrapolation_
#define  _sourceFlow_boundaryCondition_Extrapolation_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 外插边界条件类
 * 
 */
class Extrapolation : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，创建外插边界条件对象
     * 
     * @param[in] boundaryPatchID 边界编号
     * @param[in, out] data 包含各类场的数据包
     */
    Extrapolation(const int &boundaryPatchID, Package::FlowPackage &data);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 更新边界条件
     * 
     */
    void UpdateBoundaryCondition();
    
};

} // namespace Flow
} // namespace Boundary

#endif
