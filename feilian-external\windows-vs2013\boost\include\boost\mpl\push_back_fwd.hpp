
#ifndef BOOST_MPL_PUSH_BACK_FWD_HPP_INCLUDED
#define BOOST_MPL_PUSH_BACK_FWD_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

namespace boost { namespace mpl {

template< typename Tag > struct push_back_impl;
template< typename Sequence, typename T > struct push_back;

}}

#endif // BOOST_MPL_PUSH_BACK_FWD_HPP_INCLUDED
