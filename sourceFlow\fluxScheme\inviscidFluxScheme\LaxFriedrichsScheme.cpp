﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/LaxFriedrichsScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

LaxFriedrichsScheme::LaxFriedrichsScheme(Package::FlowPackage &data,
                                         Limiter::Limiter *limiter,
                                         Flux::Flow::Precondition::Precondition *precondition)
    : UpwindScheme(data, limiter, precondition)
{    
}

NSFaceFlux LaxFriedrichsScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    // 得到面法向和面积大小
    const Face &face = mesh->GetFace(faceID);
    const Vector &faceNormal = face.GetNormal();
    const Scalar &faceArea = face.GetArea();    

	// 谱半径计算
    const int ownerID = mesh->GetFace(faceID).GetOwnerID();
    const int neighID = mesh->GetFace(faceID).GetNeighborID();
	const Scalar lambdaLeft = fabs(U.GetValue(ownerID) & faceNormal) + A.<PERSON>(ownerID);
	const Scalar lambdaRight = fabs(U.GetValue(neighID) & faceNormal) + A.GetValue(neighID);
	const Scalar lambdaFace = 0.5 * (lambdaLeft + lambdaRight);

    // 计算左右面守恒量
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;
    const Vector rhoULeft = rhoLeft * ULeft;
    const Vector rhoURight = rhoRight * URight;
#if defined(_EnableMultiSpecies_)
    const std::vector<Scalar> &massFractionLeft = faceValue.massFractionLeft;
    const std::vector<Scalar> &massFractionRight = faceValue.massFractionRight;
	const Scalar rhoELeft = material.CalculateTotalEnergy(rhoLeft, pLeft, ULeft, massFractionLeft);
	const Scalar rhoERight = material.CalculateTotalEnergy(rhoRight, pRight, URight, massFractionRight);
#else
	const Scalar rhoELeft = material.CalculateTotalEnergy(rhoLeft, pLeft, ULeft);
	const Scalar rhoERight = material.CalculateTotalEnergy(rhoRight, pRight, URight);
#endif

    // 计算面通量
    const Scalar halfArea = 0.5 * faceArea;
    faceFlux.massFlux = halfArea * ((rhoRight + rhoLeft) - lambdaFace * (rhoRight - rhoLeft));
    faceFlux.momentumFlux = halfArea * ((rhoURight + rhoULeft) - lambdaFace * (rhoURight - rhoULeft));
    faceFlux.energyFlux = halfArea * ((rhoERight + rhoELeft) - lambdaFace * (rhoERight - rhoELeft));
	
    return faceFlux;
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux