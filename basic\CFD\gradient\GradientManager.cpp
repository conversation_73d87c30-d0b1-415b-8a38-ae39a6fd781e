﻿#include "basic/CFD/gradient/GradientManager.h"

namespace Gradient
{

GradientManager::GradientManager(Mesh *mesh_, const FieldManipulation::GradientScheme method_, const bool nodeCenter_)
{
    if ( method_ == FieldManipulation::GradientScheme::GREEN_GAUSS
      || method_ == FieldManipulation::GradientScheme::GREEN_GAUSS_M1
      || method_ == FieldManipulation::GradientScheme::GREEN_GAUSS_M2
      || method_ == FieldManipulation::GradientScheme::GREEN_GAUSS_M3 )
    {
        gradient = new GreenGauss(mesh_, method_, nodeCenter_);
    }
    else if (method_ == FieldManipulation::GradientScheme::LEAST_SQUARE ||
             method_ == FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX)
    {
        const bool LSVertexFlag = (!nodeCenter_ && method_ == FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX);
        gradient = new LeastSquare(mesh_, LSVertexFlag, nodeCenter_);
    }
    else
    {
        gradient = new GreenGauss(mesh_, method_, nodeCenter_);
    }
}

GradientManager::~GradientManager()
{

}

template<> void GradientManager::Calculate(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi)
{
    gradient->CalculateScalar(phi, gradPhi);
}

template<> void GradientManager::Calculate(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi)
{
    gradient->CalculateVector(phi, gradPhi);
}

} // namespace FieldManipulation