//---------------------------------------------------------------------------//
// Copyright (c) 2013-2014 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_UTILITY_HPP
#define BOOST_COMPUTE_UTILITY_HPP

#include <boost/compute/utility/dim.hpp>
#include <boost/compute/utility/extents.hpp>
#include <boost/compute/utility/invoke.hpp>
#include <boost/compute/utility/program_cache.hpp>
#include <boost/compute/utility/source.hpp>
#include <boost/compute/utility/wait_list.hpp>

#endif // BOOST_COMPUTE_UTILITY_HPP
