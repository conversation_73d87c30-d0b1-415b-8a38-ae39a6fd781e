#include <iostream>
#include <vector>
#include <chrono>  // for high_resolution_clock
#include <onnxruntime_cxx_api.h>

class mlTurbModel 
{
public:
    mlTurbModel(const std::string& model_path, int IntraOpNumThreads, int InterOpNumThreads, int numFeatures_);

    ~mlTurbModel();

    std::vector<float> predictor(std::vector<float>& input_data);

private:
    Ort::Env env_;
    Ort::Session session_;
    int numFeatures;
};
