﻿#ifndef _basic_geometry_Geometry_
#define _basic_geometry_Geometry_

#include "basic/geometry/CylinderWall.h"
#include "basic/geometry/PlaneWall.h"
#include "basic/geometry/Line.h"

/**
 * @brief 几何命名空间
 * 
 */
namespace Geometry
{

/**
 * @brief 几何类
 * 
 */
class Geometry
{
public:
    /**
     * @brief 默认构造函数
     * 
     */
    Geometry();

    /**
     * @brief 添加已有平面
     * 
     * @param[in] wall 输入平面
     */
    void AddPlaneWall(const PlaneWall &wall);
    
    /**
     * @brief 添加新建平面
     * 
     * @param[in] nodeList 端点列表
     * @param[in] prop_type_ 属性编号
     * @param[in] t_vel 平移速度
     * @param[in] r_vel 旋转速度
     * @param[in] r_line 旋转轴
     * @param[in] both_ 是否为双面
     * 
     */
    void AddPlaneWall(const std::vector<Vector> &nodeList,
                       const int &propType = 0,
                       const Vector &t_vel = Vector0, const Scalar &r_vel = Scalar0,
                       const Line &r_line = Line(), const bool &both = false);
    
    /**
     * @brief 新增柱面
     * 
     * @param[in] cylinder 输入柱面
     */
    void AddCylinder(const CylinderWall &cylinder);
    
    /**
     * @brief 获得平面数量
     * 
     * @return const int 平面数量
     */
    const int GetPlaneWallNumber()const {return this->pWall.size();}
    
    /**
     * @brief 获得几何中的指定平面
     * 
     * @param[in] n 平面编号
     * @return const PlaneWall& 平面
     */
    const PlaneWall &GetWall(const int &n)const { return this->pWall[n]; }
    
    /**
     * @brief 获得点到面法向单位矢量、点到面的距离、壁面属性、投影点坐标等
     * 
     * @param[in] pos 位置
     * @param[in] diam 直径
     * @param[in] wallID 壁面编号
     * @param[out] nv 点到面法向单位矢量
     * @param[out] dist 法向距离
     * @param[out] vel 接触点速度
     * @param[out] pt 壁面属性
     */
    void GetPointToWallInfo(const Vector &pos, const Scalar &diam, const int& wallID, Vector& nv, Scalar& dist, Vector& vel, int& pt);
    
    /**
     * @brief 壁面反向
     * 
     * @param n 壁面编号
     */
    void ReverseWall(const int &n) {this->pWall[n].Reverse();}

    /**
     * @brief 设置几何维度
     * 
     * @param dimension_ 维度：2（2D）、3（3D）
     */
    void SetDimension(const int &dimension_) { this->dimension = dimension_; }

    /**
     * @brief 获取几何维度
     * 
     * @return const int& 维度：2（2D）、3（3D）
     */
    const int &GetDimension()const { return this->dimension; }

    /**
     * @brief 以Tecplot格式输出
     * 
     * @param file 输出流
     * @param sol_time 时间
     * @param strnd_id 编号
     */
    void OutputTecplot(std::fstream &file, const Scalar& sol_time, const int& strnd_id);

	/**
	* @brief 获取面编号起始值
	*
	* @param wallID0 面编号起始值
	*/
	const int GetWallBaseID()const { return wallID0; }

public:
    int dimension; ///< 几何维度
    std::vector<PlaneWall> pWall; ///< 壁面容器

	// 壁面起始编号，应远大于颗粒编号
	const int wallID0;

#if defined(_BaseParallelMPI_)
public:
	template<class Archive>
	void serialize(Archive& ar, const unsigned int version)
	{
		//当前类成员序列化
		ar& dimension;
		ar& pWall;
		ar& wallID0;
	}
#endif

};

} // namespace Geometry

#endif