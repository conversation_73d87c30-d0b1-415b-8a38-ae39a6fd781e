# 摄动场数据文件格式说明

## ASCII格式 (.dat)

### 文件结构
```
# 摄动场数据文件 - 模态1弯曲模态
# 节点ID  位移X  位移Y  位移Z
0  0.0000  0.0000  0.0000
1  0.0001  0.0000  0.0000
2  0.0002  0.0001  0.0000
3  0.0003  0.0002  0.0000
...
```

### 格式说明
- 以 `#` 或 `!` 开头的行为注释行，会被忽略
- 每行包含4个数值：节点ID、X方向位移、Y方向位移、Z方向位移
- 节点ID必须从0开始，连续递增
- 位移单位与网格单位保持一致
- 节点数量必须与网格节点数量完全匹配

### 示例文件内容
```
# 机翼第一阶弯曲模态摄动场
# 模态频率: 15.8 Hz
# 节点ID  位移X(m)  位移Y(m)  位移Z(m)
0  0.000000  0.000000  0.000000
1  0.000100  0.000000  0.000000
2  0.000200  0.000010  0.000000
3  0.000300  0.000020  0.000000
4  0.000400  0.000030  0.000000
```

## 二进制格式 (.bin)

### 文件结构
```
[4字节] 节点数量 (int)
[12字节] 节点0位移 (3个double: dx, dy, dz)
[12字节] 节点1位移 (3个double: dx, dy, dz)
...
[12字节] 节点N位移 (3个double: dx, dy, dz)
```

### 格式说明
- 文件开头4字节存储节点数量（int类型）
- 随后每个节点占用12字节（3个double类型的位移分量）
- 节点顺序必须与网格节点顺序一致
- 使用小端字节序

## 数据要求

### 物理约束
1. **边界条件一致性**：固定边界处的摄动位移应为零
2. **连续性**：相邻节点的位移应保持合理的连续性
3. **幅值合理性**：位移幅值应在合理范围内（通常小于特征长度的10%）

### 数值要求
1. **精度**：建议使用双精度浮点数
2. **单位**：与网格坐标单位保持一致
3. **归一化**：摄动场通常应进行适当的归一化处理

## 验证检查

前处理程序会自动进行以下验证：

1. **文件完整性检查**
   - 文件是否存在且可读
   - 文件格式是否正确
   - 节点数量是否匹配

2. **数据合理性检查**
   - 位移幅值是否在合理范围内
   - 是否存在异常的大值或NaN值
   - 边界条件是否满足

3. **一致性检查**
   - 所有模态的节点数量是否一致
   - 数据格式是否统一

## 生成建议

### 从结构分析软件导出
1. **NASTRAN**: 使用PUNCH文件导出模态位移
2. **ANSYS**: 使用APDL命令导出节点位移
3. **Abaqus**: 使用ODB文件提取模态结果

### 数据处理
1. 确保节点编号与CFD网格一致
2. 进行适当的单位转换
3. 应用边界条件约束
4. 进行模态归一化

## 注意事项

1. **网格一致性**：摄动场数据必须与CFD网格完全对应
2. **模态正交性**：多个模态之间应保持正交性
3. **文件命名**：建议使用描述性的文件名
4. **备份**：重要的摄动场数据应进行备份
5. **版本控制**：对摄动场数据进行版本管理
