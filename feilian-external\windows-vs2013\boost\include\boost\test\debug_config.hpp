//  (C) Copyright Genna<PERSON><PERSON> Roz<PERSON> 2001.
//  Distributed under the Boost Software License, Version 1.0.
//  (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/test for the library home page.
//
//! @file
//! @brief user's config for Boost.Test debugging support
//!
//! This file is intended to be edited by end user to specify varios macros, which configure debugger interface
//! Alterntively you can set these parameters in your own sources/makefiles
// ***************************************************************************

#ifndef BOOST_TEST_DEBUG_CONFIG_HPP_112006GER
#define BOOST_TEST_DEBUG_CONFIG_HPP_112006GER

// ';' separated list of supported debuggers
// #define BOOST_TEST_DBG_LIST gdb;dbx

// maximum size of /proc/pid/stat file
// #define BOOST_TEST_STAT_LINE_MAX

#endif
