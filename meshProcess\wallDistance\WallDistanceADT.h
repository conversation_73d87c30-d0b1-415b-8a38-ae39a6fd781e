﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceADT.h
//! <AUTHOR>
//! @brief 计算壁面距离ADT方法
//! @date  2022-11-07
//
//------------------------------修改日志----------------------------------------
// 2022-11-7 李艳亮、乔龙
// 说明：规范化
//
// 2021-05-12 尹强
// 说明：添加ADT搜索计算壁面距离的方法
//
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_WallDistanceADT_
#define _meshProcess_wallDistance_WallDistanceADT_

#include "meshProcess/wallDistance/WallDistanceBase.h"
#include "meshProcess/wallDistance/ADT_utilities.h"

/**
 * @brief 采用ADT计算近壁面距离类
 * 
 */
class WallDistanceADT : public WallDistanceBase
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in, out] mesh 网格指针
	* @param[in] wallBoundaryFace_ 物面面元容器（包含面元信息和构成面的点坐标）
	*/
    WallDistanceADT(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_);
    
	/**
	* @brief 析构函数
	*
	*/
    ~WallDistanceADT();

	/**
	* @brief 计算壁面距离
	*
	*/
    void Calculate();

private:
	/**
	* @brief 建立ADT树结构
	*
	* @param[in] ndim 网格维度，2或3
	*/
    void BuildUnstructuredSurfaceADT(const int &ndim);
    
private:
    typedef std::pair<std::size_t, int> FaceID; ///< 重命名
	typedef DataStruct_AdtNode<FaceID, Scalar> WallFaceNode; ///< 重命名
    typedef DataStruct_AdtTree<FaceID, Scalar> WallFaceTree;  ///< 重命名
    
    WallFaceTree* wallFaceTree; ///< 壁面ADT树的指针
};

#include "meshProcess/wallDistance/ADT_utilities.hxx"

#endif
