/*=============================================================================
    Boost.Wave: A Standard compliant C++ preprocessor library

    This is the current version of the Wave library

    http://www.boost.org/

    Copyright (c) 2001-2012 <PERSON><PERSON><PERSON>. Distributed under the Boost
    Software License, Version 1.0. (See accompanying file
    LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/

#if !defined(WAVE_VERSION_H_9D79ABDB_AC54_4C0A_89B1_F70A2DCFE21E_INCLUDED)
#define WAVE_VERSION_H_9D79ABDB_AC54_4C0A_89B1_F70A2DCFE21E_INCLUDED

//  BOOST_WAVE_VERSION & 0x0000FF is the sub-minor version
//  BOOST_WAVE_VERSION & 0x00FF00 is the minor version
//  BOOST_WAVE_VERSION & 0xFF0000 is the major version
#define BOOST_WAVE_VERSION                 0x020302

//  The following defines contain the same information as above
#define BOOST_WAVE_VERSION_MAJOR           2
#define BOOST_WAVE_VERSION_MINOR           3
#define BOOST_WAVE_VERSION_SUBMINOR        2

#endif // !defined(WAVE_VERSION_H_9D79ABDB_AC54_4C0A_89B1_F70A2DCFE21E_INCLUDED)
