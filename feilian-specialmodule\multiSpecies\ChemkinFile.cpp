﻿#include "feilian-specialmodule/multiSpecies/ChemkinFile.h"

namespace MultiSpecies
{
ChemkinFile::ChemkinFile(const std::string &fileName) : R_(8.314462)
{
	ReadFile(fileName);
	reactionSize = reactionList.size();
}

void ChemkinFile::Initialize(const std::string &fileName)
{
	ReadFile(fileName);
}

void ChemkinFile::ReadFile(const std::string &fileName)
{
	std::ifstream file;
	file.open(fileName, std::ios::in);
	if (!file) std::cout << "File is not existed: " << "mechanism.inp";

	// step1:元素信息
	std::string s;	
	GetStringFromKeyWords(file, elementList, "ELEMENTS", "END");

	// step2:组分信息	 
	GetStringFromKeyWords(file, speciesList, "SPECIES", "END");
	speciesSize = speciesList.size();
	speciesDataList.resize(speciesSize);
	GetMolarMass(speciesList, speciesDataList);

	// step3:NASA多项式信息
	GetStringFromKeyWordsThermo(file, thermoDat, "THERMO ALL", "END");
	ReadThermoDat(thermoDat, speciesDataList);

	// step4:输运参数信息
	GetStringFromKeyWordsTrans(file, transDat, "TRANS ALL", "END");
	ReadTransDat(transDat, speciesDataList);

	// step5:化学反应信息
	int count = GetReactionsNumber(file);
	reactionList.resize(count);
	for (int index = 1; index < count + 1; ++index)
	{
		std::vector<std::string> info; //存放某个反应的全部信息
		GetReactionString(file, index, info);
		GetReaction(info, reactionList[index - 1]);
		int test = 1;
	}
	int test = 1;
}

void ChemkinFile::ReadThermoDat(const std::vector<std::string> &info, std::vector<PureSpeciesStruct> &pureSpecies)
{
	for (int k = 0; k < speciesSize; k++)
	{
		pureSpecies[k].nasaPolynomialLow.resize(7);
		pureSpecies[k].nasaPolynomialHigh.resize(7);

		for (int i = 0; i < info.size(); i++)
		{
			std::string name = speciesList[k];
			if (info[i] == speciesList[k])
			{
				for (int j = 0; j < 7; j++)
				{
					pureSpecies[k].nasaPolynomialHigh[j] = std::stod(info[i + j + 1]);
					pureSpecies[k].nasaPolynomialLow[j] = std::stod(info[i + j + 8]);
				}
			}
		}
	}
}

void ChemkinFile::ReadTransDat(const std::vector<std::string> &info, std::vector<PureSpeciesStruct> &pureSpecies)
{
	for (int k = 0; k < speciesSize; k++)
	{
		for (int i = 0; i < info.size(); i++)
		{
			std::string name = speciesList[k];
			if (info[i] == speciesList[k])
			{
				pureSpecies[k].wellDepth = std::stod(info[i + 2]);
				pureSpecies[k].diameter = std::stod(info[i + 3]);
				pureSpecies[k].polarizability = std::stod(info[i + 5]);
				pureSpecies[k].rotationalRelaxation = std::stod(info[i + 6]);
			}
		}
	}
}

void ChemkinFile::GetMolarMass(const std::vector<std::string> &info, std::vector<PureSpeciesStruct> &pureSpecies)
{
	for (int k = 0; k < speciesSize; k++)
	{
		pureSpecies[k].molarMass = Scalar0;
		Scalar &molarMassTemp = pureSpecies[k].molarMass;
		Scalar &gasConstant = pureSpecies[k].gasConstant;

		const std::string name = speciesList[k];
		pureSpecies[k].speciesName = name;
		std::vector<int> elementPosition;
		elementPosition.resize(name.size());

		for (int j = 0; j < name.size(); j++)
		{
			const char elementTemp = name[j];
			int findElement = GetElementMolarMass(elementTemp);
			if (findElement)
			{
				elementPosition[j] = findElement;
			}
			else
			{
				elementPosition[j] = -1;
			}
		}

		if (elementPosition[name.size() - 1] > 0)
			molarMassTemp = molarMassTemp + elementPosition[name.size() - 1];

		for (int j = 0; j < name.size() - 1; j++)
		{
			if (elementPosition[j] > 0)
			{
				if (elementPosition[j + 1] > 0)
				{
					molarMassTemp = molarMassTemp + elementPosition[j];
				}
				else
				{
					int length = 0;
					for (int ii = 0; ii < name.size() - j - 1; ii++)
					{
						if (elementPosition[j + 1 + ii] < 0)
						{
							length = length - elementPosition[j + 1 + ii];
							break;
						}
					}
					int elementCount = stoi(name.substr(j + 1, length));
					molarMassTemp = molarMassTemp + elementPosition[j] * elementCount;
				}
			}
		}
		molarMassTemp = molarMassTemp / 1000.0;
		gasConstant = R_ / molarMassTemp;
	}
}

const int ChemkinFile::GetElementMolarMass(const char &elementName)
{
	switch (elementName)
	{
	case 72:
		return 1;
		break;
	case 67:
		return 12;
		break;
	case 79:
		return 16;
		break;
	case 78:
		return 14;
		break;
	default:
		return 0;
		break;
	}
}

void ChemkinFile::GetReaction(const std::vector<std::string> &info, ReactionStruct &r)
{
	// step0: 反应类型是否为可逆
	std::string leftFlag = "<", rightFlag = ">";
	if (info[0].find("<") == std::string::npos)
	{
		leftFlag = "=";
		r.reverse = false;
	}
	else
	{
		r.reverse = true;
	}

	// step1: 获取arrhenius并删除数据，仅保留反应信息
	int pos = info[0].find(' ');
	const std::string reaction = info[0].substr(0, pos);
	std::istringstream stringStream(info[0].substr(pos, info[0].length() - reaction.length()));
	r.arrhenius.resize(3);
	stringStream >> r.arrhenius[0] >> r.arrhenius[1] >> r.arrhenius[2];

	// step2: 获取left反应物信息
	pos = reaction.find(leftFlag);
	std::string s = reaction.substr(0, pos);
	GetSpeciesandCoeff(s, r, true);

	// step3: 获取right生成物信息
	pos = reaction.find(rightFlag);
	s = reaction.substr(pos + 1, reaction.length() - pos);
	GetSpeciesandCoeff(s, r, false);

	// step4: 判定是否有字符串(+M)，有则需要读入附加信息LOW和TROE
	int posM = -1;
	if (reaction.find("(+M)") != std::string::npos)
	{
		GetMAddCoeff(info[1], info[2], r);
		posM = 3; //M信息位于序列3行
	}

	// step5: 判定是否有字符串M，有则需要读入附加信息thirdBody
	if (reaction.find('M') != std::string::npos)
	{
		if (posM < 0) posM = 1;//M信息位于序列1行
		GetMCoeff(info[posM], r);
	}

	if (r.reverse || info.size() == 1)
	{
		const int coeffSize = r.left.size();
		for (int i = 0; i < coeffSize; i++)
			r.Order.push_back(r.left[i]);
	}
	else
	{
		for (int k = 0; k < info.size() - 1; k++)
		{
			GetOrder(info[k + 1], r);
		}
	}
}

void ChemkinFile::GetOrder(const std::string &sOrder, ReactionStruct &r)
{
	std::string coeff = sOrder.substr(5);
	int pos = coeff.find(" ");
	std::string speciesName = coeff.substr(1, pos-1);
	Scalar order = std::stod(coeff.substr(pos, coeff.length() - 2));

	for (int k = 0; k < speciesSize; k++)
	{
		if (speciesName == speciesList[k])
		{
			std::pair<int, Scalar> p(k, order);
			r.Order.push_back(p);
		}
	}
}

void ChemkinFile::GetSpeciesandCoeff(std::string reaction, ReactionStruct &r, const bool &left)
{
	Scalar c;
	std::string name;	
	int pos = reaction.find('(');
	if (pos != std::string::npos) reaction.erase(pos);
	reaction += "+";

	while (true)
	{
		pos = reaction.find('+');
		if (pos == std::string::npos) break;
		std::string sub = reaction.substr(0, pos); //获得某个组分及其系数，如 3H2O
		SplitString(sub, name, c);
		if (name == "M" || name == "M)") break;

		for (int i = 0; i < speciesList.size(); ++i)
		{
			if (speciesList[i] == name)
			{
				std::pair<int, Scalar> p(i, c); //组分在列表中的序列号及系数
				if (left) r.left.push_back(p);
				else      r.right.push_back(p);

				reaction = reaction.substr(pos + 1, reaction.length() - pos - 1);
				break;
			}
		}
	}
}

void ChemkinFile::GetMAddCoeff(const std::string &sLow, const std::string &sTROE, ReactionStruct &r)
{
	std::string s;
	int pos;

	pos = sLow.find('//');
	s = sLow.substr(pos + 1, sLow.length() - pos);
	pos = s.find('//');
	std::istringstream stringStream1(s.substr(0, pos));
	r.low.resize(3);
	stringStream1 >> r.low[0] >> r.low[1] >> r.low[2];

	pos = sTROE.find('//');
	s = sTROE.substr(pos + 1, sTROE.length() - pos);
	pos = s.find('//');
	std::istringstream stringStream2(s.substr(0, pos));
	r.troe.resize(4);
	stringStream2 >> r.troe[0] >> r.troe[1] >> r.troe[2] >> r.troe[3];
}

void ChemkinFile::GetMCoeff(std::string ss, ReactionStruct &r)
{
	std::string name;
	Scalar c;
	while (true)
	{
		int pos = ss.find('//');
		if (pos == std::string::npos) break;
		ss[pos] = ' '; //修改为空格
		pos = ss.find('//');
		std::istringstream stringStream(ss.substr(0, pos));
		stringStream >> name >> c;

		for (int i = 0; i < speciesList.size(); ++i)
		{
			if (speciesList[i] == name)
			{
				std::pair<int, Scalar> p(i, c); //组分在列表中的序列号及参数值				
				r.thirdBody.push_back(p);

				ss = ss.substr(pos + 1, ss.length() - pos);
				break;
			}
		}
	}
}

void ChemkinFile::GetStringFromKeyWords(std::ifstream &file, std::vector<std::string> &info, const std::string &start, const std::string &end)
{
	std::string s;
	info.resize(0);
	file.clear();
	file.seekg(0);

	while (!file.eof())
	{
		std::getline(file, s);
		if (s == start) break;
	}

	while (!file.eof())
	{
		file >> s;
		if (s == end) break;
		info.push_back(s);
	}
}

void ChemkinFile::GetStringFromKeyWordsThermo(std::ifstream &file, std::vector<std::string> &info, const std::string &start, const std::string &end)
{
	std::string s;
	info.resize(0);
	file.clear();
	file.seekg(0);

	while (!file.eof())
	{
		std::getline(file, s);
		if (s == start) break;
	}

	std::vector<std::vector<Scalar>> nasaPoly;
	for (int k = 0; k < speciesSize; ++k)
	{
		nasaPoly.push_back(std::vector<Scalar>());
		for (int i = 0; i < 5; ++i)
		{
			nasaPoly[k].push_back(Scalar0);
		}
	}
	
	int speciesIndex;
	bool findSpecies = false;
	int nasaNum = 0;

	while (!file.eof())
	{
		file >> s;
		if (s == end) break;

		for (int k = 0; k < speciesSize; ++k)
		{
			if (s == speciesList[k])
			{
				speciesIndex = k;
				findSpecies = true;
				info.push_back(s);
			}
		}

		if (findSpecies)
		{
			const int lengthTemp = s.length();
			if (lengthTemp >= 14)
			{
				std::string ssub;
				const int numPara = lengthTemp / 15;
				const int checkFirst = lengthTemp % 15;

				if (!checkFirst)
				{
					for (int i = 0; i < numPara; i++)
					{
						ssub = s.substr(i * 15, 15);
						info.push_back(ssub);
					}
					nasaNum = nasaNum + numPara;
				}
				else
				{
					ssub = s.substr(0, 14);
					info.push_back(ssub);

					for (int i = 0; i < numPara; i++)
					{
						ssub = s.substr(14 + i * 15, 15);
						info.push_back(ssub);
					}
					nasaNum = nasaNum + numPara + 1;
				}
			}

			if (nasaNum == 14)
			{
				findSpecies = false;
				nasaNum = 0;
			}
		}
		
	}
}

void ChemkinFile::GetStringFromKeyWordsTrans(std::ifstream &file, std::vector<std::string> &info, const std::string &start, const std::string &end)
{
	std::string s;
	info.resize(0);
	file.clear();
	file.seekg(0);

	while (!file.eof())
	{
		std::getline(file, s);
		if (s == start) break;
	}

	int speciesIndex;
	bool findSpecies = false;
	int counter = 0;
	while (!file.eof())
	{
		file >> s;
		if (s == end) break;

		for (int k = 0; k < speciesSize; ++k)
		{
			if (s == speciesList[k])
			{
				speciesIndex = k;
				findSpecies = true;
			}
		}

		if (findSpecies)
		{
			info.push_back(s);
			counter++;
			if (counter == 7)
			{
				findSpecies = false;
				counter = 0;
			}
		}
	}
}

int ChemkinFile::GetReactionsNumber(std::ifstream &file)
{
	std::string s;
	file.clear();
	file.seekg(0);
	int count = 0;
	int n;
	while (!file.eof())
	{
		std::getline(file, s);
		if (s.substr(0, 2) == "!R")
		{
			std::istringstream stringStream(s.substr(2, 10));
			stringStream >> n;
			if (n > count) count = n;
		}
	}

	return count;
}

void ChemkinFile::GetReactionString(std::ifstream &file, const int &index, std::vector<std::string> &info)
{
	std::string s;
	file.clear();
	file.seekg(0);
	while (!file.eof())
	{
		std::getline(file, s);
		if (s == "REACTIONS") break;
	}

	bool flagFind = false;
	while (!file.eof())
	{
		std::getline(file, s);
		if (s == "!R" + ToString(index))
		{
			flagFind = true;
			continue;
		}

		if (flagFind && (s == "!R" + ToString(index + 1) || s == "END")) break;

		if (flagFind) info.push_back(s);
	}
}

void ChemkinFile::SplitString(const std::string s, std::string &name, Scalar &n)
{
	n = 1.0; //没有系数，默认为1
	name = "";
	const int length = s.length();
	for (int i = 0; i < length; ++i)
	{
		if (s[i]=='C' || s[i]=='O' || s[i]=='N' || s[i]=='H' || s[i]=='M')
		{
			name = s.substr(i, length - i);
			std::string temp = s.substr(0, i);
			if (temp.length() > 0)
			{
				std::istringstream stringStream(temp);
				stringStream >> n;
			}
			return;
		}
		//if (s[i] == '0' ||
		//	s[i] == '1' ||
		//	s[i] == '2' ||
		//	s[i] == '3' ||
		//	s[i] == '4' ||
		//	s[i] == '5' ||
		//	s[i] == '6' ||
		//	s[i] == '7' ||
		//	s[i] == '8' ||
		//	s[i] == '9') continue;
		//else
		//{
		//	name = s.substr(i, length - i);
		//	std::string temp = s.substr(0, i);
		//	if (temp.length() > 0)
		//	{
		//		std::istringstream stringStream(temp);
		//		stringStream >> n;
		//	}
		//	return;
		//}
	}
	return;
}

} //namespace Turbulence
