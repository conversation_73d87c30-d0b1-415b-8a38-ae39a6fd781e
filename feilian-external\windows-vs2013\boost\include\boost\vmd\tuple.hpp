
//  (C) Copyright <PERSON> 2015
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).

#if !defined(BOOST_VMD_TUPLE_HPP)
#define BOOST_VMD_TUPLE_HPP

#include <boost/vmd/detail/setup.hpp>

#if BOOST_PP_VARIADICS

#include <boost/vmd/tuple/is_vmd_tuple.hpp>
#include <boost/vmd/tuple/pop_back.hpp>
#include <boost/vmd/tuple/pop_front.hpp>
#include <boost/vmd/tuple/push_back.hpp>
#include <boost/vmd/tuple/push_front.hpp>
#include <boost/vmd/tuple/remove.hpp>
#include <boost/vmd/tuple/size.hpp>
#include <boost/vmd/tuple/to_array.hpp>
#include <boost/vmd/tuple/to_list.hpp>
#include <boost/vmd/tuple/to_seq.hpp>

#endif /* BOOST_PP_VARIADICS */
#endif /* BOOST_VMD_TUPLE_HPP */
