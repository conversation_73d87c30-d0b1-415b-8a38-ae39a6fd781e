      SUMMARY OF THE HDF5 CONFIGURATION
      =================================

General Information:
-------------------
                   HDF5 Version: 1.8.19
                  Configured on: 2017-06-15
                  Configured by: Visual Studio 12 2013 Win64
                 Configure mode: CMAKE 3.8.0
                    Host system: Windows-6.1.7601
              Uname information: Windows
                       Byte sex: little-endian
                      Libraries: 
             Installation point: C:/Program

Compiling Options:
------------------
               Compilation Mode: RelWithDebInfo
                     C Compiler: C:/Program Files (x86)/Microsoft Visual Studio 12.0/VC/bin/x86_amd64/cl.exe
                         CFLAGS: /DWIN32 /D_WINDOWS  /W3
                      H5_CFLAGS: 
                      AM_CFLAGS: 
                       CPPFLAGS: 
                    H5_CPPFLAGS: 
                    AM_CPPFLAGS: 
               Shared C Library: YES
               Static C Library: YES
  Statically Linked Executables: OFF
                        LDFLAGS: /machine:x64
                     AM_LDFLAGS: 
                Extra libraries: zlib-static;szip-static
                       Archiver: 
                         Ranlib: 
              Debugged Packages: 
                    API Tracing: OFF

Languages:
----------
                        Fortran: OFF
               Fortran Compiler: 
          Fortran 2003 Compiler: ON
                  Fortran Flags: 
               H5 Fortran Flags: 
               AM Fortran Flags: 
         Shared Fortran Library: YES
         Static Fortran Library: YES

                            C++: ON
                   C++ Compiler: C:/Program Files (x86)/Microsoft Visual Studio 12.0/VC/bin/x86_amd64/cl.exe
                      C++ Flags: /DWIN32 /D_WINDOWS /GR /EHsc /W3
                   H5 C++ Flags: 
                   AM C++ Flags: 
             Shared C++ Library: YES
             Static C++ Library: YES

Features:
---------
                  Parallel HDF5: OFF
             High Level library: ON
                   Threadsafety: OFF
            Default API Mapping: v18
 With Deprecated Public Symbols: ON
         I/O filters (external):  DEFLATE DECODE ENCODE
                            MPE: 
                     Direct VFD: 
                        dmalloc: 
Clear file buffers before write: ON
           Using memory checker: OFF
         Function Stack Tracing: OFF
      Strict File Format Checks: OFF
   Optimization Instrumentation: 
