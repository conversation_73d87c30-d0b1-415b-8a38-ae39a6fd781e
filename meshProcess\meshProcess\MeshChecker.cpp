﻿#include "meshProcess/meshProcess/MeshChecker.h"

MeshChecker::MeshChecker(Mesh *mesh_, const int &level_, const bool &dualFlag_)
    : mesh(mesh_), level(level_), dualFlag(dualFlag_), maxID(10), angleLimit(15.0)
{
}

MeshChecker::~MeshChecker()
{}

void MeshChecker::Check()
{
    Print("\n开始网格质量检查： Level = " + ToString(level));

    if (level == 0) this->CheckRange();

    this->CheckArea();

    this->CheckVolume();

    this->CheckDistanceAndNorm();

    if (level == 0) this->CheckBoundary();

    Print("结束网格质量检查： Level = " + ToString(level));

}

void MeshChecker::CheckRange()
{
    Vector minRange, maxRange;
    if (dualFlag)
    {
        minRange = mesh->GetElement(0).GetCenter(), maxRange = mesh->GetElement(0).GetCenter();
        for (int elementID = 0; elementID < mesh->GetElementNumberReal(); ++elementID)
        {
            const Vector &center = mesh->GetElement(elementID).GetCenter();
            minRange = Min(center, minRange);
            maxRange = Max(center, maxRange);
        }
    }
    else
    {
        minRange = mesh->GetNode(0), maxRange = mesh->GetNode(0);
        for (int nodeID = 0; nodeID < mesh->GetNodeNumber(); ++nodeID)
        {
            minRange = Min(mesh->GetNode(nodeID), minRange);
            maxRange = Max(mesh->GetNode(nodeID), maxRange);
        }
    }
    
    Scalar minVolume = mesh->GetElement(0).GetVolume(), maxVolume = minVolume;
    for (int elementID = 0; elementID < mesh->GetElementNumberReal(); ++elementID)
    {
        const Scalar &volume = mesh->GetElement(elementID).GetVolume();
        minVolume = Min(volume, minVolume);
        maxVolume = Max(volume, maxVolume);
    }

    Scalar minArea = mesh->GetFace(0).GetArea(), maxArea = minArea;
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        const Scalar &area = mesh->GetFace(faceID).GetArea();
        minArea = Min(area, minArea);
        maxArea = Max(area, maxArea);
    }

    std::ostringstream stringStream;
    stringStream << "\n网格基本信息：\n";
    stringStream << std::setw(18) << "坐标范围: "
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << minRange.X()
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << maxRange.X() << "\n" << std::string(14, ' ')
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << minRange.Y()
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << maxRange.Y() << "\n" << std::string(14, ' ')
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << minRange.Z()
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << maxRange.Z() << "\n";
                 
    stringStream << std::setw(18) << "体积范围: "
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << minVolume
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << maxVolume << "\n";
    stringStream << std::setw(18) << "面积范围: "
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << minArea
                 << std::setprecision(6) << std::fixed << std::setw(14) << std::scientific << maxArea << "\n";
    
    Print(stringStream.str());
}

void MeshChecker::CheckVolume()
{
    const int &elementNumber = mesh->GetElementNumberReal();

    int countVolume = 0; //负体积计数器
    std::vector<int> ID(maxID); //负体积单元号列表
    std::vector<Scalar> volList(maxID, INF); ////负体积值最大的值列表

    for (int elementID = 0; elementID < elementNumber; ++elementID)
    {
        const Scalar &vol = mesh->GetElement(elementID).GetVolume();
        if (vol <= Scalar0)
        {
            InsertList(false, elementID, vol, countVolume, ID, volList);
        }
    }

    if (countVolume == 0)
    {
        Print("\t检查项：单元体积， 无问题...");
        return;
    }

    Print("\t检查项：单元体积...");

    const int exampleID = Min(countVolume, maxID);

    std::ostringstream stringStream;
    stringStream
        << std::string(70, '-') << "\n"
        << " 负体积单元数量： " << countVolume
        << " (" << std::setprecision(3) << (Scalar)countVolume / elementNumber * 100.0 << "%)，"
        << "前 " << exampleID << " 个单元信息如下：\n"
        << std::string(70, '-') << "\n"
        << "|   ID |  elementID |       volume |                  element center |\n"
        << std::string(70, '-') << "\n";
    for (int i = 0; i < exampleID; ++i)
    {
        stringStream
            << "| " << std::setw(4) << i + 1 << " | "
            << std::setw(10) << ID[i] << " | "
            << std::setprecision(6) << std::fixed << std::setw(12) << std::scientific << volList[i] << " | "
            << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetElement(ID[i]).GetCenter().X() << ", "
            << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetElement(ID[i]).GetCenter().Y() << ", "
            << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetElement(ID[i]).GetCenter().Z() << " |\n";
    }
    stringStream << std::string(70, '-') << "\n";

    FatalError(stringStream.str());
}

void MeshChecker::CheckArea()
{
    const int &faceNumber = mesh->GetFaceNumber();

    int countArea = 0; //非正面积计数器
    std::vector<int> ID(maxID); //非正面积面号列表
    std::vector<Scalar> areaList(maxID, INF); ////负面积值最大的值列表

    for (int faceID = 0; faceID < faceNumber; ++faceID)
    {
        const Scalar area = mesh->GetFace(faceID).GetArea();
        if (area < SMALL)
        {
            InsertList(false, faceID, area, countArea, ID, areaList);
        }
    }

    if (countArea == 0)
    {
        Print("\t检查项：单元面积， 无问题...");
    }
    else
    {
        Print("\t检查项：单元面积...");

        const int exampleID = Min(countArea, maxID);

        std::ostringstream stringStream;
        stringStream
            << std::string(70, '-') << "\n"
            << " 非正面积数量：" << countArea
            << " (" << std::setprecision(3) << (Scalar)countArea / faceNumber * 100.0 << "%)，"
            << "前 " << exampleID << " 个面信息如下：\n"
            << std::string(70, '-') << "\n"
            << "|   ID |     faceID |         area |                     face center |\n"
            << std::string(70, '-') << "\n";
        for (int i = 0; i < exampleID; ++i)
        {
            stringStream
                << "| " << std::setw(4) << i + 1 << " | "
                << std::setw(10) << ID[i] << " | "
                << std::setprecision(6) << std::fixed << std::setw(12) << std::scientific << areaList[i] << " | "
                << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(ID[i]).GetCenter().X() << ", "
                << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(ID[i]).GetCenter().Y() << ", "
                << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(ID[i]).GetCenter().Z() << " |\n";
        }
        stringStream << std::string(70, '-') << "\n";

        FatalError(stringStream.str());

    }
}

void MeshChecker::CheckDistanceAndNorm()
{
    const int &faceNumber = mesh->GetFaceNumber();
    int countDistance = 0; //体心距离过小的计数器
    std::vector<int> distanceID(maxID); //体心距离过小的面号列表
    std::vector<Scalar> distanceList(maxID, INF); //体心距离最小的前10个值

    int countNorm = 0; //体心连线与面法矢夹角超过angleLimit的计数器
    std::vector<int> normID(maxID); //体心连线与面法矢夹角超过angleLimit的最差的前10个面号列表
    std::vector<Scalar> angleList(maxID, 0.0); //体心连线与面法矢夹角最差的前10个角度
    
    //统计信息
    for (int faceID = 0; faceID < faceNumber; ++faceID)
    {
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Vector &faceNorm = face.GetNormal();

        Vector L;
        if (neighID >= 0) L = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
        else
        {
            if (dualFlag) L = faceNorm; // 对偶网格边界不检查
            else          L= face.GetCenter() - mesh->GetElement(ownerID).GetCenter();
        }
        
        Scalar angleTemp = acos(L.GetNormal() & faceNorm) * 180 / PI;

        Scalar distance = L.Mag();
        if (distance < SMALL)
        {
            InsertList(false, faceID, distance, countDistance, distanceID, distanceList);
        }

        if (angleTemp > angleLimit)
        {            
            InsertList(true, faceID, angleTemp, countNorm, normID, angleList);
        }
    }

    if (level == 0) // 粗网格不检查体心连线与面法矢夹角
    {
        // 体心连线与面法矢夹角超过angleLimit的信息打印
        if (countNorm == 0)
        {
            Print("\t检查项：体心连线与面法矢夹角， 无问题...");
        }
        else
        {
            Print("\t检查项：体心连线与面法矢夹角...");
    
            const int exampleID = Min(countNorm, maxID);
    
            std::ostringstream stringStream;
            stringStream
                << std::string(70, '-') << "\n"
                << " 夹角大于 " << angleLimit << " 度的数量：" << countNorm
                << " (" << std::setprecision(3) << (Scalar)countNorm / faceNumber * 100.0 << "%)，"
                << "前 " << exampleID << " 个面信息如下：\n"
                << std::string(70, '-') << "\n"
                << "|   ID |     faceID |        angle |                     face center |\n"
                << std::string(70, '-') << "\n";
            for (int i = 0; i < exampleID; ++i)
            {
                stringStream
                    << "| " << std::setw(4) << i + 1 << " | "
                    << std::setw(10) << normID[i] << " | "
                    << std::setprecision(6) << std::fixed << std::setw(12) << angleList[i] << " | "
                    << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(normID[i]).GetCenter().X() << ", "
                    << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(normID[i]).GetCenter().Y() << ", "
                    << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(normID[i]).GetCenter().Z() << " |\n";
            }
            stringStream << std::string(70, '-') << "\n";
    
            WarningContinue(stringStream.str());
        }
    }
    
    // 体心距离信息打印
    if (countDistance == 0)
    {
        Print("\t检查项：体心距离， 无问题...");
    }
    else
    {
        Print("\t检查项：体心距离...");
    
        const int exampleID = Min(countDistance, maxID);
    
        std::ostringstream stringStream;
        stringStream
            << std::string(70, '-') << "\n"
            << " 体心距离过小的数量" << countDistance
            << " (" << std::setprecision(3) << (Scalar)countDistance / faceNumber * 100.0 << "%)，"
            << "前 " << exampleID << " 个面信息如下：\n"
            << std::string(70, '-') << "\n"
            << "|   ID |     faceID |     distance |                     face center |\n"
            << std::string(70, '-') << "\n";
        for (int i = 0; i < exampleID; ++i)
        {
            stringStream
                << "| " << std::setw(4) << i + 1 << " | "
                << std::setw(10) << distanceID[i] << " | "
                << std::setprecision(6) << std::fixed << std::setw(12) << std::scientific << distanceList[i] << " | "
                << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(distanceID[i]).GetCenter().X() << ", "
                << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(distanceID[i]).GetCenter().Y() << ", "
                << std::setprecision(6) << std::fixed << std::setw(9) << mesh->GetFace(distanceID[i]).GetCenter().Z() << " |\n";
        }
        stringStream << std::string(70, '-') << "\n";
        
        FatalError(stringStream.str());
    }
}

void MeshChecker::CheckBoundary()
{
    std::vector<int> patchIDList; //边界存在面法矢向内的边界编号列表
    std::vector<int> countList; //边界存在面法矢向内的面数量

    if (!dualFlag) //对偶网格不检查
    {
        countList.resize(mesh->GetBoundarySize());
        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        {
            bool findFlag = false;
            for (int index = 0; index < mesh->GetBoundaryFaceSize(patchID); ++index)
            {
                const int &faceID = mesh->GetBoundaryFaceID(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const Vector &faceNormal = face.GetNormal();
                const Vector &faceCenter = face.GetCenter();
                const Vector &elementCenter = mesh->GetElement(face.GetOwnerID()).GetCenter();

                if (((faceCenter - elementCenter) & faceNormal) < SMALL)
                {
                    countList[patchID]++;
                    if (!findFlag) findFlag = true;
                }
            }
            if (findFlag) patchIDList.push_back(patchID);
        }
    }    

    // 边界面法矢检查信息打印
    if (patchIDList.size() == 0)
    {
        Print("\t检查项：边界面法矢方向， 无问题...");
    }
    else
    {
        std::ostringstream stringStream;

        stringStream << "\t检查项：边界面法矢方向...\n"
            << "\t\t存在面法矢方向向内的边界编号列表如下：\n";

        for (int i = 0; i < patchIDList.size(); ++i)
        {
            const int &patchID = patchIDList[i];
            const int &faceSize = mesh->GetBoundaryFaceSize(patchID);
            stringStream << "\t\t#" << patchID << " : " << countList[patchID]
                << " (" << std::setprecision(3)
                << (Scalar)countList[patchID] / faceSize * 100.0 << "%)\n";
        }

        WarningContinue(stringStream.str());
    }
}

void MeshChecker::InsertList(const bool &big, const int &ID, const Scalar &value, int &count,
                             std::vector<int> &IDList, std::vector<Scalar> &valueList)
{    
    int sign = 1;
    if (!big) sign = -1;

    for (int pos = 0; pos < maxID; ++pos)
    {
        if (value * sign > valueList[pos] * sign)
        {
            for (int m = Min(count, maxID) - 2; m >= pos; --m)
            {
                valueList[m + 1] = valueList[m];
                IDList[m + 1] = IDList[m];
            }
            valueList[pos] = value;
            IDList[pos] = ID;
            break;
        }
    }
    ++count;    
}