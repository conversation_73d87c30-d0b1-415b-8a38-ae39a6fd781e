﻿#include "sourceFlow/postprocessor/ForceNondimensionalize.h"

ForceNondimensionalize::ForceNondimensionalize(Configure::Flow::FlowConfigure &flowConfigure_)
	:
	flowConfigure(flowConfigure_)
{
	resultPath = flowConfigure.GetControl().resultSavePath;
	if (resultPath.find_last_of("/") != resultPath.length() - 1) resultPath = resultPath + "/";

	resultName = flowConfigure.GetCaseName();
	globalBoundaryName = flowConfigure.GetGlobalBoundaryNameList();

	// 确定全局网格每个边界是否为物面，true为物面
	globalWallFlag.resize(globalBoundaryName.size());
	for (int i = 0; i < globalWallFlag.size(); i++)
		globalWallFlag[i] = flowConfigure.JudgeWallGlobal(i);

	dimension = flowConfigure.GetMeshParameters().dimension;
	
	lengthReference = flowConfigure.GetForceNondimensionalized().lengthRef;
	SReference = flowConfigure.GetForceNondimensionalized().SRef;
	momentCenter = flowConfigure.GetForceNondimensionalized().momentCenter;

	alpha = flowConfigure.GetForceNondimensionalized().alpha * PI / 180;
	beta = flowConfigure.GetForceNondimensionalized().beta * PI / 180;
	densityReference = flowConfigure.GetForceNondimensionalized().density;
	velocityReference = flowConfigure.GetForceNondimensionalized().velocityMag;
	pressureReference = flowConfigure.GetForceNondimensionalized().pressure;
	boundaryIDList = flowConfigure.GetForceNondimensionalized().boundaryIDList;

	if (SReference.X() == 0 || SReference.Y() == 0 || SReference.Z() == 0) FatalError("参数文件后处理参考值SRef参数设置错误！");
	if (!boundaryIDList.empty())
	{
		if (boundaryIDList.size() > globalBoundaryName.size()) FatalError("参数文件后处理参考值boundaryIDList参数设置错误！");
		for (int i = 0; i < boundaryIDList.size(); i++)
		{
			if (boundaryIDList[i] > globalBoundaryName.size()) FatalError("参数文件后处理参考值boundaryIDList参数中有无效边界ID！");
			if (!globalWallFlag[boundaryIDList[i]]) FatalError("参数文件后处理参考值boundaryIDList参数中有非物面边界条件！");
		}
	}
	else
	{
		for (int i = 0; i < globalWallFlag.size(); i++)
		{
			if (globalWallFlag[i]) boundaryIDList.push_back(i);
		}
	}

	// 转换矩阵
	matrix.Resize(3, 3);
	if (dimension == 3)
	{
		matrix.SetValue(0, 0,  cos(alpha) * cos(-beta));
		matrix.SetValue(0, 1,               sin(-beta));
		matrix.SetValue(0, 2,  sin(alpha) * cos(-beta));
		matrix.SetValue(1, 0, -cos(alpha) * sin(-beta));
		matrix.SetValue(1, 1,               cos(-beta));
		matrix.SetValue(1, 2, -sin(alpha) * sin(-beta));
		matrix.SetValue(2, 0, -sin(alpha)             );
		matrix.SetValue(2, 1,  0.0                    );
		matrix.SetValue(2, 2,  cos(alpha)             );
	}
	else
	{
		matrix.Resize(3, 3);
		matrix.SetValue(0, 0, cos(alpha));
		matrix.SetValue(0, 1, sin(alpha));
		matrix.SetValue(0, 2, 0.0);
		matrix.SetValue(1, 0, -sin(alpha));
		matrix.SetValue(1, 1, cos(alpha));
		matrix.SetValue(1, 2, 0.0);
		matrix.SetValue(2, 0, 0.0);
		matrix.SetValue(2, 1, 0.0);
		matrix.SetValue(2, 2, 0.0);
	}
}

ForceNondimensionalize::~ForceNondimensionalize()
{

}

void ForceNondimensionalize::ProcessForce()
{
	this->ReadDimensionalForce();
	this->Nondimensionalize();
	this->WriteNondimensionalForce();
}

void ForceNondimensionalize::ReadDimensionalForce()
{
	//两层容器，第一层为边界ID，第二层为有量纲力
	std::vector<std::vector<Vector>> forceDimensional;
	// 确定无量纲力容器大小
	forceDimensional.resize(globalBoundaryName.size());
	for (int i = 0; i < forceDimensional.size(); i++) forceDimensional[i].resize(AREA_SUM + 1);

	for (int i = 0; i < forceDimensional.size(); i++)
	{
		for (int j = FORCE_PRESSURE; j <= AREA_SUM; j++)
		{
			forceDimensional[i][j] = Vector0;
		}
	}

	std::fstream readForceFile;
	readForceFile.open(resultPath + resultName + "_force" + ".dat", std::ios::in);

	std::string lineContent;
	int line = 0;
	int iter0 = 0;
	bool newIter = false;
	getline(readForceFile, lineContent);
	while (getline(readForceFile, lineContent))
	{
		line++;
		int iter;
		int boundaryID;
		std::istringstream iss(lineContent);
		iss >> iter >> boundaryID;
		if (iter0 != iter)
		{
			iter0 = iter;
			newIter = true;
		}
		else
		{
			newIter = false;
		}

		for (int i = FORCE_PRESSURE; i <= AREA_SUM; i++)
		{
			Scalar x, y, z;
			iss >> x >> y >> z;
			forceDimensional[boundaryID][i] = Vector(x, y, z);
		}

		if (newIter)
		{
			forceDimensionalAll.push_back(forceDimensional);
		}
	}
	readForceFile.close();
}

void ForceNondimensionalize::Nondimensionalize()
{
	int totalSteps = forceDimensionalAll.size();
	forceNonDimensionalAll = forceDimensionalAll;
	Scalar dynamicPressure = 0.5 * densityReference * velocityReference * velocityReference;

	for (int i = 0; i < totalSteps; i++)
	{
		for (int j = 0; j < globalBoundaryName.size(); j++)
		{
			if (!globalWallFlag[j]) continue;
			forceNonDimensionalAll[i][j][FORCE_PRESSURE] = (forceDimensionalAll[i][j][FORCE_PRESSURE] - pressureReference * forceDimensionalAll[i][j][AREA_SUM]) / (dynamicPressure * SReference);
			forceNonDimensionalAll[i][j][FORCE_VISCOUS] = forceDimensionalAll[i][j][FORCE_VISCOUS] / (dynamicPressure * SReference);

			for (int k = FORCE_PRESSURE; k <= FORCE_VISCOUS; k++)
			{

				// 体轴系转风轴系
				Matrix forceBody(3, 1);
				forceBody.SetValue(0, 0, forceNonDimensionalAll[i][j][k].X());
				forceBody.SetValue(1, 0, forceNonDimensionalAll[i][j][k].Y());
				forceBody.SetValue(2, 0, forceNonDimensionalAll[i][j][k].Z());

				Matrix forceWind = matrix * forceBody;

				forceNonDimensionalAll[i][j][k].SetX(forceWind.GetValue(0, 0));
				forceNonDimensionalAll[i][j][k].SetY(forceWind.GetValue(1, 0));
				forceNonDimensionalAll[i][j][k].SetZ(forceWind.GetValue(2, 0));
			}

			forceNonDimensionalAll[i][j][MOMENT_PRESSURE].SetX((forceDimensionalAll[i][j][MOMENT_PRESSURE] - (momentCenter ^ forceDimensionalAll[i][j][FORCE_PRESSURE])).X() / (dynamicPressure * SReference.X() * lengthReference.X()));
			forceNonDimensionalAll[i][j][MOMENT_PRESSURE].SetY((forceDimensionalAll[i][j][MOMENT_PRESSURE] - (momentCenter ^ forceDimensionalAll[i][j][FORCE_PRESSURE])).Y() / (dynamicPressure * SReference.Y() * lengthReference.Y()));
			forceNonDimensionalAll[i][j][MOMENT_PRESSURE].SetZ((forceDimensionalAll[i][j][MOMENT_PRESSURE] - (momentCenter ^ forceDimensionalAll[i][j][FORCE_PRESSURE])).Z() / (dynamicPressure * SReference.Z() * lengthReference.Z()));

			forceNonDimensionalAll[i][j][MOMENT_VISCOUS].SetX((forceDimensionalAll[i][j][MOMENT_VISCOUS] - (momentCenter ^ forceDimensionalAll[i][j][FORCE_VISCOUS])).X() / (dynamicPressure * SReference.X() * lengthReference.X()));
			forceNonDimensionalAll[i][j][MOMENT_VISCOUS].SetY((forceDimensionalAll[i][j][MOMENT_VISCOUS] - (momentCenter ^ forceDimensionalAll[i][j][FORCE_VISCOUS])).Y() / (dynamicPressure * SReference.Y() * lengthReference.Y()));
			forceNonDimensionalAll[i][j][MOMENT_VISCOUS].SetZ((forceDimensionalAll[i][j][MOMENT_VISCOUS] - (momentCenter ^ forceDimensionalAll[i][j][FORCE_VISCOUS])).Z() / (dynamicPressure * SReference.Z() * lengthReference.Z()));
			
			if (dimension == 2)
			{
				forceNonDimensionalAll[i][j][FORCE_PRESSURE].SetZ(0.0);
				forceNonDimensionalAll[i][j][FORCE_VISCOUS].SetZ(0.0);

				forceNonDimensionalAll[i][j][MOMENT_PRESSURE].SetX(0.0);
				forceNonDimensionalAll[i][j][MOMENT_PRESSURE].SetY(0.0);
				
				forceNonDimensionalAll[i][j][MOMENT_VISCOUS].SetX(0.0);
				forceNonDimensionalAll[i][j][MOMENT_VISCOUS].SetY(0.0);
			}
			
		}
	}

	//累加
	forceNonDimensionalSum.resize(totalSteps);
	for (int i = 0; i < forceNonDimensionalSum.size(); i++) forceNonDimensionalSum[i].resize(6);

	for (int i = 0; i < forceNonDimensionalSum.size(); i++)
	{
		for (int j = 0; j < boundaryIDList.size(); j++)
		{
			forceNonDimensionalSum[i][0] += forceNonDimensionalAll[i][boundaryIDList[j]][FORCE_PRESSURE] + forceNonDimensionalAll[i][boundaryIDList[j]][FORCE_VISCOUS];
			forceNonDimensionalSum[i][1] += forceNonDimensionalAll[i][boundaryIDList[j]][MOMENT_PRESSURE] + forceNonDimensionalAll[i][boundaryIDList[j]][MOMENT_VISCOUS];

			forceNonDimensionalSum[i][2] += forceNonDimensionalAll[i][boundaryIDList[j]][FORCE_PRESSURE];
			forceNonDimensionalSum[i][3] += forceNonDimensionalAll[i][boundaryIDList[j]][FORCE_VISCOUS];

			forceNonDimensionalSum[i][4] += forceNonDimensionalAll[i][boundaryIDList[j]][MOMENT_PRESSURE];
			forceNonDimensionalSum[i][5] += forceNonDimensionalAll[i][boundaryIDList[j]][MOMENT_VISCOUS];
		}
	}
}


void ForceNondimensionalize::WriteNondimensionalForce()
{
	std::fstream outputForceFile;
	outputForceFile.open(resultPath + resultName + "_force_nondimensional" + ".dat", std::ios::out);

	std::ostringstream stringStream;

	// 参考力输出/
	stringStream.str("");
	stringStream << "# Reference values: \n";
	stringStream << std::setw(24) << "#        alpha (deg)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << alpha * 180 / PI << "\n";
	stringStream << std::setw(24) << "#         beta (deg)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << beta * 180 / PI << "\n";
	stringStream << std::setw(24) << "#   density (kg/m^3)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << densityReference << "\n";
	stringStream << std::setw(24) << "#     velocity (m/s)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << velocityReference << "\n";
	stringStream << std::setw(24) << "#      pressure (Pa)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << pressureReference << "\n";
	stringStream << std::setw(24) << "#         length (m)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << lengthReference.X()
																<< std::setiosflags(std::ios_base::scientific) << std::setw(15) << lengthReference.Y()
																<< std::setiosflags(std::ios_base::scientific) << std::setw(15) << lengthReference.Z() << "\n";
	stringStream << std::setw(24) << "#         area (m^2)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << SReference.X()
																<< std::setiosflags(std::ios_base::scientific) << std::setw(15) << SReference.Y()
																<< std::setiosflags(std::ios_base::scientific) << std::setw(15) << SReference.Z() << "\n";
	stringStream << std::setw(24) << "#  moment center (m)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << momentCenter.X()
															    << std::setiosflags(std::ios_base::scientific) << std::setw(15) << momentCenter.Y()
																<< std::setiosflags(std::ios_base::scientific) << std::setw(15) << momentCenter.Z();
	Print(stringStream.str() + "\n"); //输出值info文件
	outputForceFile << stringStream.str() << std::endl;

	stringStream.str("");
	stringStream
		<< "# Selected Boundary(BoundaryName(BoundaryID)): ";
	for (int i = 0; i < boundaryIDList.size(); i++)
	{
		stringStream
			<< globalBoundaryName[boundaryIDList[i]] + "(" + ToString(boundaryIDList[i])  + ")\t";
	}
	Print(stringStream.str());
	outputForceFile << stringStream.str() << std::endl;

	stringStream.str("");
	stringStream
		<< std::setw(15) << "Cx"
		<< std::setw(15) << "Cy"
		<< std::setw(15) << "Cz"
		<< std::setw(15) << "Cmx"
		<< std::setw(15) << "Cmy"
		<< std::setw(15) << "Cmz"
		<< std::setw(15) << "Cx_p"
		<< std::setw(15) << "Cy_p"
		<< std::setw(15) << "Cz_p"
		<< std::setw(15) << "Cx_v"
		<< std::setw(15) << "Cy_v"
		<< std::setw(15) << "Cz_v"
		<< std::setw(15) << "Cmx_p"
		<< std::setw(15) << "Cmy_p"
		<< std::setw(15) << "Cmz_p"
		<< std::setw(15) << "Cmx_v"
		<< std::setw(15) << "Cmy_v"
		<< std::setw(15) << "Cmz_v";

	outputForceFile << "variables = iter" << stringStream.str() << std::endl;

	int totalSteps = forceNonDimensionalSum.size();
	for (int i = 0; i < totalSteps; i++)
	{
		stringStream.str("");
		stringStream
			<< std::setw(16) << i + 1
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][0].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][0].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][0].Z()

			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][1].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][1].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][1].Z()

			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][2].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][2].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][2].Z()

			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][3].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][3].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][3].Z()

			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][4].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][4].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][4].Z()

			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][5].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][5].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalSum[i][5].Z();

		outputForceFile << stringStream.str() << std::endl;

	}
	outputForceFile.close();

	//输出值info文件
	stringStream.str("");
	stringStream
		<< "\nNondimesionalized Force and Moment(Last Step): \n";
	Print(stringStream.str());

	for (int i = 0; i < boundaryIDList.size(); i++)
	{
		stringStream.str("");
		stringStream
			<< globalBoundaryName[boundaryIDList[i]] + "(" + ToString(boundaryIDList[i]) + "):\n"

			<< std::setw(26) << "X"
			<< std::setw(15) << "Y"
			<< std::setw(15) << "Z" << "\n"

			<< std::setw(17) << "C_Sum       :"
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_PRESSURE].X() + forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_VISCOUS].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_PRESSURE].Y() + forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_VISCOUS].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_PRESSURE].Z() + forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_VISCOUS].Z() << "\n"

			<< std::setw(17) << "C_Pressure  :"
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_PRESSURE].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_PRESSURE].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_PRESSURE].Z() << "\n"

			<< std::setw(17) << "C_Viscous   :"
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_VISCOUS].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_VISCOUS].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][FORCE_VISCOUS].Z() << "\n"

			<< std::setw(17) << "Cm_Sum      :"
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_PRESSURE].X() + forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_VISCOUS].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_PRESSURE].Y() + forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_VISCOUS].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_PRESSURE].Z() + forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_VISCOUS].Z() << "\n"

			<< std::setw(17) << "Cm_Pressure :"
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_PRESSURE].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_PRESSURE].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_PRESSURE].Z() << "\n"

			<< std::setw(17) << "Cm_Viscous  :"
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_VISCOUS].X()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_VISCOUS].Y()
			<< std::setw(15) << std::scientific << forceNonDimensionalAll[totalSteps - 1][boundaryIDList[i]][MOMENT_VISCOUS].Z() << "\n";
		
		Print(stringStream.str());

	}

	stringStream.str("");
	stringStream
		<< "Sum: \n"

		<< std::setw(26) << "X"
		<< std::setw(15) << "Y"
		<< std::setw(15) << "Z" << "\n"

		<< std::setw(17) << "C_Sum       :"
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][0].X()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][0].Y()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][0].Z() << "\n"

		<< std::setw(17) << "C_Pressure  :"
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][2].X()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][2].Y()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][2].Z() << "\n"

		<< std::setw(17) << "C_Viscous   :"
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][3].X()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][3].Y()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][3].Z() << "\n"

		<< std::setw(17) << "Cm_Sum      :"
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][1].X()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][1].Y()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][1].Z() << "\n"

		<< std::setw(17) << "Cm_Pressure :"
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][4].X()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][4].Y()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][4].Z() << "\n"

		<< std::setw(17) << "Cm_Viscous  :"
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][5].X()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][5].Y()
		<< std::setw(15) << std::scientific << forceNonDimensionalSum[totalSteps - 1][5].Z() << "\n";
	Print(stringStream.str());



}


