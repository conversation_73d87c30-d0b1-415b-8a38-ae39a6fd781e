﻿#include "meshProcess/meshConverter/CgnsMeshBase.h"

CgnsMeshBase::CgnsMeshBase(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_)
    :MeshConverter(MshFileName, meshDimension_, mesh_)
{
}

CgnsMeshBase::~CgnsMeshBase()
{
}

int CgnsMeshBase::ObtainZoneType()
{
    //打开文件
    int fileID;
    if (cg_open(fullFileName.c_str(), CG_MODE_READ, &fileID)) return 1;
    
    // 检查网格类型
    ZoneType_t zoneType;
    if (cg_zone_type(fileID, 1, 1, &zoneType)) return 1;
    
    if (zoneType != Structured && zoneType != Unstructured)
    {
        FatalError("CgnsMeshBase::ObtainZoneType: the zoneType must be Structured or Unstructured!");
        return 1;
    }
    
    return (int)zoneType;
}

void CgnsMeshBase::CheckFaceDirection()
{
    if (mesh->GetMeshDimension() == Mesh::MeshDim::md2D)
    {
	    int wrongSize = 0;
	    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
	    {
	    	const Face &face = mesh->GetFace(faceID);
	    	const int &ownerID = face.GetOwnerID();
	    	const int &neighID = face.GetNeighborID();
	    	const Vector &faceNorm = face.GetNormal();

	    	Vector L;
	    	if (neighID >= 0) L = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
	    	else
	    	{
	    		L = face.GetCenter() - mesh->GetElement(ownerID).GetCenter();
	    	}

	    	const Scalar angleTemp = acos(L.GetNormal() & faceNorm) * 180 / PI;
	    	if (angleTemp > 89) wrongSize++;
	    }

	    if (wrongSize > 0.5 * mesh->GetFaceNumber())
        {
            Print("Check Face Direction ...");
            mesh->CheckFaceDirection();
        }
    }
    
    for (int i = 0; i < mesh->v_face.size(); i++)
    {
        const int ownerID0 = mesh->v_face[i].n_owner;
        const int neighID0 = mesh->v_face[i].n_neighbor;

        if (neighID0 < 0) continue;

        if (ownerID0 > neighID0)
        {
            mesh->v_face[i].n_owner = neighID0;
            mesh->v_face[i].n_neighbor = ownerID0;
			mesh->v_face[i].normal = -mesh->v_face[i].normal;
            mesh->v_face[i].ReverseNodeID();
        }
    }
}
