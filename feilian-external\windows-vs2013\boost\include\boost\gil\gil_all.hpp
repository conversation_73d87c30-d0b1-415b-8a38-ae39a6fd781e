/*
    Copyright 2005-2007 Adobe Systems Incorporated
   
    Use, modification and distribution are subject to the Boost Software License,
    Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt).

    See http://opensource.adobe.com/gil for most recent version including documentation.
*/

/*************************************************************************************************/

#ifndef GIL_ALL_HPP
#define GIL_ALL_HPP

////////////////////////////////////////////////////////////////////////////////////////
/// \file               
/// \brief  Includes all GIL files for convenience
/// \author <PERSON><PERSON><PERSON> and <PERSON><PERSON> \n
///         Adobe Systems Incorporated
///
///
////////////////////////////////////////////////////////////////////////////////////////

#include "gil_config.hpp"
#include "channel_algorithm.hpp"
#include "algorithm.hpp"
#include "pixel.hpp"
#include "packed_pixel.hpp"
#include "planar_pixel_reference.hpp"
#include "planar_pixel_iterator.hpp"
#include "pixel_iterator_adaptor.hpp"
#include "step_iterator.hpp"
#include "iterator_from_2d.hpp"
#include "image.hpp"
#include "image_view_factory.hpp"
#include "typedefs.hpp"
#include "metafunctions.hpp"
#include "color_convert.hpp"
#include "device_n.hpp"
#include "virtual_locator.hpp"
#include "bit_aligned_pixel_iterator.hpp"
// Uncomment this line to help in porting your code from an older version of GIL
//#include "deprecated.hpp"

#endif
