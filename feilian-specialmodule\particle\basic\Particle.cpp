﻿#include "feilian-specialmodule/particle/basic/Particle.h"

namespace Particle
{
Particle::Particle()
{
    this->ID = -1;
    this->flag = ParticleFlag::NO_INSERTED;
    this->position = Vector0;
    this->diameter = Scalar0;
    this->angularPosition  = Vector0;
    this->angularVelocity  = Vector0;
    this->linearVelocity   = Vector0;
    this->force = Vector0;
    this->torque = Vector0;
    this->linearAcceleration = Vector0;
    this->angularAcceleration = Vector0;
	this->contactPWIndex = -1;
}

Scalar Overlap(const Particle &particle1, const Particle &particle2)
{
    const Vector &pos1 = particle1.position;
    const Vector &pos2 = particle2.position;
    const Scalar &diam1 = particle1.diameter;
    const Scalar &diam2 = particle2.diameter;
    return 0.5 * (diam1 + diam2) - (pos1 - pos2).Mag();
}

} // namespace Particle