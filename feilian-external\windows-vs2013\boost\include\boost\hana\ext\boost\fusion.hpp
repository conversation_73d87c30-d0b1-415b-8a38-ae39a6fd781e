/*!
@file
Includes all the adaptors for the Boost.Fusion library.

@copyright <PERSON> 2013-2016
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_HANA_EXT_BOOST_FUSION_HPP
#define BOOST_HANA_EXT_BOOST_FUSION_HPP

//! @ingroup group-ext
//! @defgroup group-ext-fusion Boost.Fusion adapters
//! Adapters for Boost.Fusion containers.

#include <boost/hana/ext/boost/fusion/deque.hpp>
#include <boost/hana/ext/boost/fusion/list.hpp>
#include <boost/hana/ext/boost/fusion/tuple.hpp>
#include <boost/hana/ext/boost/fusion/vector.hpp>

#endif // !BOOST_HANA_EXT_BOOST_FUSION_HPP
