﻿#ifndef _particle_backgroundGrid_BackgroundGrid_
#define _particle_backgroundGrid_BackgroundGrid_

#include "basic/geometry/Geometry.h"
#include "basic/mesh/SubMesh.h"
#include "feilian-specialmodule/particle/backgroundGrid/CartesianGrid.h"

#include "meshProcess/wallDistance/KDT_utilities.h"
#include "meshProcess/wallDistance/KDT_utilities.hxx"

typedef DataStruct_KdtNode<std::vector<Scalar>, int> KdtNode;
typedef DataStruct_KdtTree<std::vector<Scalar>, int> KdtTree;

class BackgroundGrid
{
public:
    /**
     * @brief 构造函数
     * 
     * @param localMesh_ 当地子网格
     */
    explicit BackgroundGrid(SubMesh *localMesh_);

    /**
     * @brief 析构函数
     * 
     */
    ~BackgroundGrid();

    /**
     * @brief 获取指定坐标点所在的单元编号
     * 
     * @param[in] pos 坐标点
     * @param[in] firstFind 首次搜索标识
     * @param[in] lastID 上次搜索标识
     * @return std::vector<int> 单元编号
     */
	int GetAdjElementID(const Vector &pos, const bool &firstFind = true, const int &lastID = -1, const Vector &vel = Vector0);

	/**
	* @brief 获取指定坐标点所在的单元编号
	*
	* @param[in] pos 坐标点
	* @return int 单元编号列表
	*/
	int GetAdjElementID_KDT(const Vector &pos);

	/**
	* @brief 获取指定坐标点所在的单元编号
	*
	* @param[in] pos 坐标点
	* @return int 单元编号列表
	*/
	int GetAdjElementID_Cartesian(const Vector &pos);

	/**
	* @brief 获取指定坐标点所在的单元编号
	*
	* @param[in] pos 坐标点
    * @param[in] lastID 上次搜索标识
	* @return int 单元编号列表
	*/
	int GetAdjElementID_Nearest(const Vector &pos, const int &lastID, const Vector &vel);

	///< 获取指定位置所在进程号
	int GetAdjProcess(const Vector &pos);

	///< 获取运动到边界的最小时间
	Scalar GetMinTime(const Vector &pos, const Vector &vel, const int &adjElemID);

private:

    /**
     * @brief 创建KDT树
     * 
     */
	void CreateElementKdtTree();

	/**
	* @brief 创建相邻信息
	*
	*/
	void CreatAdjInfo();

    /**
     * @brief 判断坐标点是否在单元内
     * 
     * @param[in] pos 坐标点
     * @param[in] elem 单元
     * @return true 
     * @return false 
     */
    bool NodeInElem(const Vector &pos, const Element &elem);

private:
    Geometry::Geometry *geometry;
    SubMesh *localMesh;
    CartesianGrid *backGrid;

    KdtTree *kdtree;
	std::vector<Scalar> spaceMin;
	std::vector<Scalar> spaceMax;

    std::vector<int> adjProcessorID;

    int elementSize; ///< 全局网格单元数量

    /// 相邻单元编号列表
    std::vector<std::vector<int>> adjElemIDList;

	/// 相邻单元方向矢量
	std::vector<std::vector<Vector>> adjElemDistantList;

	/// 相邻面编号列表
	std::vector<std::vector<int>> adjFaceIDList;

    /// 全局网格体心坐标
    std::vector<Vector> elementCenterTotal;

    /// 二维标识
    bool dim2;
};

#endif