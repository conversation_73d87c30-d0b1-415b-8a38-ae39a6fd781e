﻿#ifndef _particle_configure_ParticleConfigureMacroXX_
#define _particle_configure_ParticleConfigureMacroXX_

#include "feilian-specialmodule/particle/configure/ParticleConfigureMacro.h"

#include <vector>
#include <map>
#include <string>
#include <algorithm>

/**
 * @brief 参数命名空间
 * 
 */
namespace Configure
{

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

const std::map<std::string, WallContactType> wallContactTypeMap =
{
	{ "WCT_PLANE", WallContactType::WCT_PLANE },
	{ "WCT_GRANULAR", WallContactType::WCT_GRANULAR }
};

const std::map<WallContactType, std::string> wallContactTypeReverseMap =
{
	{ WallContactType::WCT_PLANE, "WCT_PLANE" },
	{ WallContactType::WCT_GRANULAR, "WCT_GRANULAR" }
};

const std::map<std::string, ContactForceType> contactForceTypeMap =
{
    { "LINEAR_NOLIMITED", ContactForceType::LINEAR_NOLIMITED },
    { "LINEAR_LIMITED", ContactForceType::LINEAR_LIMITED },
    { "NONLINEAR_NOLIMITED", ContactForceType::NONLINEAR_NOLIMITED },
    { "NONLINEAR_LIMITED", ContactForceType::NONLINEAR_LIMITED}
};

const std::map<ContactForceType, std::string> contactForceTypeReverseMap =
{
    { ContactForceType::LINEAR_NOLIMITED, "LINEAR_NOLIMITED" },
    { ContactForceType::LINEAR_LIMITED, "LINEAR_LIMITED" },
    { ContactForceType::NONLINEAR_NOLIMITED, "NONLINEAR_NOLIMITED" },
    { ContactForceType::NONLINEAR_LIMITED, "NONLINEAR_LIMITED"}
};

const std::map<std::string, ContactTorqueType> contactTorqueTypeMap =
{
	{ "CTT_NONE", ContactTorqueType::CTT_NONE },
    { "CONSTANT", ContactTorqueType::CONSTANT },
    { "VARIABLE", ContactTorqueType::VARIABLE }
};

const std::map<ContactTorqueType, std::string> contactTorqueTypeReverseMap =
{
	{ ContactTorqueType::CTT_NONE, "CTT_NONE" },
    { ContactTorqueType::CONSTANT, "CONSTANT" },
    { ContactTorqueType::VARIABLE, "VARIABLE" }
};

const std::map<std::string, ContactSearchMethod> contactSearchMethodMap =
{
    { "NBS", ContactSearchMethod::NBS },
    { "NBS_Munjiza", ContactSearchMethod::NBS_Munjiza },
    { "NBS_Hrchl", ContactSearchMethod::NBS_Hrchl }
};

const std::map<ContactSearchMethod, std::string> contactSearchMethodReverseMap =
{
    { ContactSearchMethod::NBS, "NBS" },
    { ContactSearchMethod::NBS_Munjiza, "NBS_Munjiza" },
    { ContactSearchMethod::NBS_Hrchl, "NBS_Hrchl" }
};

const std::map<std::string, MotionIntegrationScheme> motionIntegrationSchemeMap =
{
    { "PIM_FE",     MotionIntegrationScheme::PIM_FE     },
    { "PIM_ME",     MotionIntegrationScheme::PIM_ME     },
    { "PIM_AB2",    MotionIntegrationScheme::PIM_AB2    },
    { "PIM_AB3",    MotionIntegrationScheme::PIM_AB3    },
    { "PIM_AB4",    MotionIntegrationScheme::PIM_AB4    },
    { "PIM_AB5",    MotionIntegrationScheme::PIM_AB5    },
    { "PIM_AB2AM3", MotionIntegrationScheme::PIM_AB2AM3 },
    { "PIM_AB3AM4", MotionIntegrationScheme::PIM_AB3AM4 },
    { "PIM_AB4AM5", MotionIntegrationScheme::PIM_AB4AM5 }
};

const std::map<MotionIntegrationScheme, std::string> motionIntegrationSchemeReverseMap =
{
    { MotionIntegrationScheme::PIM_FE,      "PIM_FE"     },
    { MotionIntegrationScheme::PIM_ME,      "PIM_ME"     },
    { MotionIntegrationScheme::PIM_AB2,     "PIM_AB2"    },
    { MotionIntegrationScheme::PIM_AB3,     "PIM_AB3"    },
    { MotionIntegrationScheme::PIM_AB4,     "PIM_AB4"    },
    { MotionIntegrationScheme::PIM_AB5,     "PIM_AB5"    },
    { MotionIntegrationScheme::PIM_AB2AM3,  "PIM_AB2AM3" },
    { MotionIntegrationScheme::PIM_AB3AM4,  "PIM_AB3AM4" },
    { MotionIntegrationScheme::PIM_AB4AM5,  "PIM_AB4AM5" }
};

const std::map<std::string, InsertionMethod> insertionMethodMap =
{
    { "FILE", InsertionMethod::FILE },
    { "BOX", InsertionMethod::BOX }
};

const std::map<InsertionMethod, std::string> insertionMethodReverseMap =
{
    {InsertionMethod::FILE, "FILE" },
    {InsertionMethod::BOX, "BOX" }
};

const std::map<std::string, InsertionShape> insertionShapeMap =
{
    { "PLANE", InsertionShape::PLANE },
    { "ZONE", InsertionShape::ZONE }
};

const std::map<InsertionShape, std::string> insertionShapeReverseMap =
{
    {InsertionShape::PLANE, "PLANE" },
    {InsertionShape::ZONE, "ZONE" }
};

const std::map<std::string, DistributionType> distributionTypeMap =
{
    { "UNIFORM", DistributionType::UNIFORM },
    { "NORMAL", DistributionType::NORMAL },
    { "LOG_NORMAL", DistributionType::LOG_NORMAL }
};

const std::map<DistributionType, std::string> distributionTypeReverseMap =
{
    {DistributionType::UNIFORM, "UNIFORM" },
    {DistributionType::NORMAL, "NORMAL" },
    {DistributionType::LOG_NORMAL, "LOG_NORMAL" }
};

const std::map<std::string, FileType> fileTypeMap =
{
    { "VTK", FileType::VTK },
    { "TECPLOT", FileType::TECPLOT }
};

const std::map<FileType, std::string> fileTypeReverseMap =
{
    {FileType::VTK, "VTK" },
    {FileType::TECPLOT, "TECPLOT" }
};

const std::map<std::string, InterpolationMethod> interpolationMethodMap =
{
	{ "INTERPOLATION_CONSTANT", InterpolationMethod::INTERPOLATION_CONSTANT },
	{ "TAYLOR_1ST", InterpolationMethod::TAYLOR_1ST },
	{ "IDW", InterpolationMethod::IDW },
	{ "LINEAR", InterpolationMethod::LINEAR },
	{ "TRILINEAR", InterpolationMethod::TRILINEAR },
	{ "LEAST_SQUARE", InterpolationMethod::LEAST_SQUARE },
	{ "HAMONIC", InterpolationMethod::HAMONIC }
};

const std::map<InterpolationMethod, std::string> interpolationMethodReverseMap =
{
	{ InterpolationMethod::INTERPOLATION_CONSTANT, "INTERPOLATION_CONSTANT" },
	{ InterpolationMethod::TAYLOR_1ST, "TAYLOR_1ST" },
	{ InterpolationMethod::IDW, "IDW" },
	{ InterpolationMethod::LINEAR, "LINEAR" },
	{ InterpolationMethod::TRILINEAR, "TRILINEAR" },
	{ InterpolationMethod::LEAST_SQUARE, "LEAST_SQUARE" },
	{ InterpolationMethod::HAMONIC, "HAMONIC" }
};

} // namespace Particle

} // namespace Configure

#endif