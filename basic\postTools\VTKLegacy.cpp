﻿#include  "basic/postTools/VTKLegacy.h"

namespace Post
{

VTKLegacy::VTKLegacy(Mesh *mesh_, const bool dualFlag_, const Position outputPosition_, const bool exportInteriorFlag_, const std::string caseName_)
	: BasePost(mesh_, false, outputPosition_, exportInteriorFlag_, caseName_), binary(true)
{
}

void VTKLegacy::WriteFile()
{
    if (caseName.size() == 0) FatalError("VTKLegacy::WriteFile: case name is missing!");

    // 打开文件
    std::fstream file;
    if (binary) file.open(caseName + ".vtk", std::ios::out | std::ios::binary);
    else        file.open(caseName + ".vtk", std::ios::out);

    // 写文件
    this->WriteTitle(file);
    this->WriteVolume(file);
    this->WriteFieldData(file);

    std::size_t pos = caseName.rfind("_");
    if (pos == caseName.npos) Print("\nVTK文件路径: " + caseName);
    else                      Print("\nVTK文件路径: " + caseName.substr(0, pos));

    file.close();

    return;
}

void VTKLegacy::WriteTitle(std::fstream& file)
{
    // 写VTK文件标题
    file << "# vtk DataFile Version 2.0" << std::endl;
    file << "Unstructured Grid VTK" << std::endl;
    if (binary) file << "BINARY" << std::endl;
    else        file << "ASCII" << std::endl;
    file << "DATASET UNSTRUCTURED_GRID" << std::endl;
}

void VTKLegacy::WriteVolume(std::fstream& file)
{
    const int nodeNumber = mesh->GetNodeNumber();
    const int cellNumber = mesh->GetElementNumberReal();

    //写网格点坐标
    file << "POINTS " << nodeNumber << " double" << std::endl;
    for (int i = 0; i < nodeNumber; i++) this->WriteVector(file, mesh->GetNode(i), binary);

    //写单元的拓扑信息
    int nCount = cellNumber;
    for (int i = 0; i < cellNumber; i++) nCount += mesh->GetElement(i).GetNodeSize();
    file << "CELLS " << cellNumber << " " << nCount << std::endl;
    for (int i = 0; i < cellNumber; i++)
    {
        const Element &element = mesh->GetElement(i);
        const int nodeSize = element.GetNodeSize();
        this->Write(file, nodeSize, binary);
        for (int j = 0; j < nodeSize; ++j) this->Write(file, element.GetNodeID(j), binary);
    }

    //写单元的类型
    file << "CELL_TYPES " << cellNumber << std::endl;
    for (int i = 0; i < cellNumber; i++)
    {
        const int cellType = this->GetVTKCellType(mesh->GetElement(i).GetElemShapeType());
        this->Write(file, cellType, binary);
    }
}

void VTKLegacy::WriteBoundary(std::fstream& file)
{
    
}

void VTKLegacy::WriteFieldData(std::fstream& file)
{
    int fieldSize = mesh->GetElementNumberReal();
    if (!outputAtCenter) fieldSize = mesh->GetNodeNumber();

    if (outputAtCenter) file << "CELL_DATA " << fieldSize << std::endl;
    else                file << "POINT_DATA " << fieldSize << std::endl;
    
    //写出所有的标量场
    for (size_t i = 0; i < scalarField.size(); i++)
    {
        const std::string fieldName = GetScalarFieldName(scalarField[i]);
        file << "SCALARS " << fieldName << " " << "double " << "1" << std::endl;
        file << "LOOKUP_TABLE " << "default" << std::endl;

        for (size_t j = 0; j < fieldSize; j++)
            this->Write(file, this->GetScalarFieldValue(scalarField[i], j), binary);
        file << std::endl;
    }

    //写出所有的矢量场
    for (size_t i = 0; i < vectorField.size(); i++)
    {
        const std::string fieldName = GetVectorFieldName(vectorField[i]);
        file << "VECTORS " << fieldName << " " << "double" << std::endl;

        for (size_t j = 0; j < fieldSize; j++)
            this->WriteVector(file, this->GetVectorFieldValue(vectorField[i], j), binary);
        file << std::endl;
    }
}

int VTKLegacy::GetVTKCellType(const Element::ElemShapeType &elemShapeType)
{
    switch (elemShapeType)
    {
    case Element::ElemShapeType::estTriangular:
        return 5;
    case Element::ElemShapeType::estQuadrilateral:
        return 9;
    case Element::ElemShapeType::estTetrahedral:
        return 10;
    case Element::ElemShapeType::estHexahedral:
        return 12;
    case Element::ElemShapeType::estWedge:
        return 13;
    case Element::ElemShapeType::estPyramid:
        return 14;
    default:
        FatalError("VTKLegacy::GetVTKCellType: element shape isnot supported");
    }
    
    return 0;
}

template void VTKLegacy::Write(std::fstream& file, const int& var, const bool &binary);
template void VTKLegacy::Write(std::fstream& file, const Scalar& var, const bool &binary);
template<class Type>
void VTKLegacy::Write(std::fstream& file, const Type& var, const bool &binary)
{
    if (binary)
    {
        Type var1 = var;
        this->SwapEnd(var1);
        file.write((char *)&var1, sizeof(var1));
    }
    else
    {
        file << var << std::endl;
    }
}

void VTKLegacy::WriteVector(std::fstream& file, const Vector& var, const bool &binary)
{
    if (binary)
    {
        Scalar varX = var.X();
        Scalar varY = var.Y();
        Scalar varZ = var.Z();
        this->SwapEnd(varX);
        this->SwapEnd(varY);
        this->SwapEnd(varZ);
        file.write((char *)&varX, sizeof(varX));
        file.write((char *)&varY, sizeof(varY));
        file.write((char *)&varZ, sizeof(varZ));
    }
    else
    {
        file << var.X() << " " << var.Y() << " " << var.Z() << std::endl;
    }
}

template<class Type>
void VTKLegacy::SwapEnd(Type& value)
{
    char* valueArray = reinterpret_cast<char*>(&value);
    for (long i = 0; i < static_cast<long>(sizeof(value) / 2); i++)
        std::swap(valueArray[sizeof(value) - 1 - i], valueArray[i]);
}

} //namespace post