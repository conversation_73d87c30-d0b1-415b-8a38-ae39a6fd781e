 #pragma once
#include "ThirdPartyHeadersBegin.h"
#include <vector>
#include <boost/shared_ptr.hpp>
#include <boost/unordered_map.hpp>
#include "ThirdPartyHeadersEnd.h"
#include "AltTecUtil.h"
#include "basicTypes.h"
#include "IJK.h"
namespace tecplot { namespace ___3933 { class FESubzonePartitionerInterface; class ___37; class PartitionTecUtilDecorator; class ___1881; class ___1350; class ItemSetIterator; class ZoneInfoCache { public: ZoneInfoCache( ___37*               ___36, ___1844 const&                maxIJKSubzoneSize, ___2090::ItemOffset_t feSubzoneSize); boost::shared_ptr<___1881> getIJKZoneInfo(___4636 zone); boost::shared_ptr<___1881> getIJKZonePartitionInfo( ___4636 zone, ___4636 ___2977); boost::shared_ptr<___1350> getFEZoneInfo(___4636 zone); boost::shared_ptr<___1350> getFEZonePartitionInfo( ___4636 zone, ___4636 ___2977, PartitionTecUtilDecorator& partitionTecUtilDecorator); void remove(___4636 zone); void clear(); void replaceDataSource(___37* ___36); private: ___37*                       ___2337; ___1844 const                         m_maxIJKSubzoneSize; ___2090::ItemOffset_t const   m_feSubzoneSize; boost::unordered_map<___4636, boost::shared_ptr<___1881> >                                     m_ijkZoneInfos; boost::unordered_map<___4636, boost::unordered_map<___4636, boost::shared_ptr<___1881> > > m_ijkZonePartitionInfos; boost::unordered_map<___4636, boost::shared_ptr<___1350> >                                      m_feZoneInfos; boost::unordered_map<___4636, boost::unordered_map<___4636, boost::shared_ptr<___1350> > >  m_feZonePartitionInfos; template<typename ZoneInfoType> void storeForSharedZones( boost::unordered_map<___4636, boost::shared_ptr<ZoneInfoType> >& zoneInfos, boost::shared_ptr<ZoneInfoType>                zoneInfo, ___4636                                    zone); template <typename ZoneInfoType> void storeForSharedPartitions( boost::unordered_map<___4636, boost::unordered_map<___4636, boost::shared_ptr<ZoneInfoType> > >& zoneInfos, boost::shared_ptr<ZoneInfoType> zoneInfo, ___4636 zone, ___4636 ___2977); }; }}
