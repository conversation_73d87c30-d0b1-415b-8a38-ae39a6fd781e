﻿#include "sourceFlow/flowSolver/InnerLoop.h"

InnerLoop::InnerLoop(SubMesh *subMesh_,
                     std::vector<Package::FlowPackage *> &flowPackageVector_,
                     std::vector<Time::Flow::FlowTimeManager *> &timeSchemeVector_,
                     FlowResultsProcess *resultProcess_)
        :flowPackage(flowPackageVector_[0]), resultProcess(resultProcess_),
        multigridSolver(subMesh_, flowPackageVector_, timeSchemeVector_), multiGridFlag(true)
{
}

InnerLoop::~InnerLoop()
{
}

void InnerLoop::CloseMultigrid()
{
    multigridSolver.CloseMultigrid();
    multiGridFlag = false;
}

void InnerLoop::Solve()
{
    // 内循环总迭代步数及保存间隔
    const auto &flowConfigure = flowPackage->GetFlowConfigure();
    const int &innerLoopSteps = flowConfigure.GetControl().innerLoop.steps;
    const int &innerInterval = flowConfigure.GetControl().innerLoop.interval;

    // 获取非定常标识
    const bool &unsteady = flowPackage->GetUnsteadyStatus().unsteadyFlag;
	const bool &dualTime = flowPackage->GetUnsteadyStatus().dualTime;

	// 多重网格求解器初始化
	multigridSolver.Initialize(0, resultProcess, multiGridFlag);
    CheckStatus(2301);

	int convergenceFlag;

    // 流场迭代求解内循环
    for (int innerStep = 0; innerStep < innerLoopSteps; ++innerStep)
    {
        // 多重网格求解
		multigridSolver.Solve(innerStep);
        CheckStatus(2302);

        // 获取内循环收敛标识
		if (!unsteady || dualTime)
		{
			const Scalar &criteria = flowConfigure.GetControl().innerLoop.criteria;
			convergenceFlag = resultProcess->CheckConvergence(criteria);
            CheckStatus(2303);
		}
        
        if (!unsteady)
        {
            // 定常内循环物理场输出
            const int &currentStep = resultProcess->GetCurrentStep();
            if (innerStep + 1 == innerLoopSteps || convergenceFlag == 1)
            {
                // 最后一步输出
                resultProcess->SaveFinalResults();
                CheckStatus(2304);
                break;
            }
            else if (innerInterval > 0 && currentStep % innerInterval == 0)
            {
                // 中间结果保存
                resultProcess->SaveIntervalResults();
                CheckStatus(2305);
            }
        }
		else if (dualTime)
		{
            // 非定常内循环跳出
            if (convergenceFlag == 1) break;
		}
    }
}
