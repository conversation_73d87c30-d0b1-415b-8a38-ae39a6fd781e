﻿#include "basic/CFD/linearSystemSolver/BlockSparseMatrixSelf.h"

BlockSparseMatrixSelf::BlockSparseMatrixSelf(Mesh *mesh_, const int nVariable_)
	: BlockSparseMatrix(mesh_, nVariable_)
{
}

BlockSparseMatrixSelf::~BlockSparseMatrixSelf()
{
}

void BlockSparseMatrixSelf::Initialize()
{
	nVariable2 = nVariable * nVariable;	
	rowSize = mesh->GetElementNumberInDomain();
	const int faceNumber = mesh->GetFaceNumber();

	//行数据关系建立
	rowPtr.resize(rowSize + 1);
	col_ind.reserve(rowSize + 2 * faceNumber);
	
	for (int row = 0; row < rowSize; ++row)
	{
		rowPtr[row] = col_ind.size();
		std::set<int> neighbors;
		neighbors.insert(row);
		
		const auto &element = mesh->GetElement(row);
		for (int iNeigh = 0; iNeigh < element.GetFaceSize(); ++iNeigh)
		{
			const int &faceID = element.GetFaceID(iNeigh);
			const Face &face = mesh->GetFace(faceID);
			const int &ownerID = face.GetOwnerID();
			if (!mesh->JudgeRealElement(ownerID)) continue;//跳过被挖洞的面
			const int &neighberID = face.GetNeighborID();
			if (mesh->JudgeBoundaryFace(faceID)) continue; //只能跳过边界面，不能跳过并行面			

			if (ownerID == row) neighbors.insert(neighberID);
			else                neighbors.insert(ownerID);
		}		
		col_ind.insert(col_ind.end(), neighbors.begin(), neighbors.end());
	}
	blockSize = col_ind.size();
	rowPtr[rowSize] = blockSize;	
	
	//对角阵数据关系建立
	diagPtr.resize(rowSize);
	for (int row = 0; row < rowSize; ++row)
	{
		for (int k = rowPtr[row]; k < rowPtr[row + 1]; ++k)
			if (col_ind[k] == row)
			{
				diagPtr[row] = k;
				break;
			}
	}
	
	//面两侧相邻关系建立
	faceIDPtr.resize(mesh->GetFaceNumber(), { -1, -1 });
	for (int index = 0; index < mesh->GetInnerFaceNumberInDomain(); ++index)
	{
		const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		int k = rowPtr[ownerID];
		while (col_ind[k] != neighID) ++k;
		faceIDPtr[faceID].first = k;

		if (neighID >= rowSize) //考虑并行虚单元
		{
			faceIDPtr[faceID].second = -1;
		}
		else
		{
			k = rowPtr[neighID];
			while (col_ind[k] != ownerID) ++k;
			faceIDPtr[faceID].second = k;
		}		
	}
	
	// 开辟矩阵的存储空间
	value.resize(blockSize * nVariable2);
}

void BlockSparseMatrixSelf::SetValZero() 
{
	for (int index = 0; index < blockSize * nVariable2; ++index)
		value[index] = 0.0;
}

void BlockSparseMatrixSelf::SetVal2Diag(const int &row, const Scalar &val) 
{
	const int index = diagPtr[row] * nVariable2;

	for (int i = 0; i < nVariable2; ++i)
		value[index + i] = 0.0;

	for (int i = 0; i < nVariable; ++i)
		value[index + i*(nVariable + 1)] = val;
}

void BlockSparseMatrixSelf::AddVal2Diag(const int &row, const Scalar &val) 
{
	const int index = diagPtr[row] * nVariable2;

	for (int i = 0; i < nVariable; ++i)
		value[index + i*(nVariable + 1)] += val;
}

void BlockSparseMatrixSelf::AddBlock2Diag(const int &row, const Matrix &val) 
{
	Scalar *pValue = &value[diagPtr[row] * nVariable2];
	for (int i = 0, offset = 0; i < nVariable; ++i)
	{
		for (int j = 0; j < nVariable; ++j)
			pValue[offset++] += val.GetValue(i, j);
	}
}

void BlockSparseMatrixSelf::UpdateBlocks(const int &faceID, const int &elemI, const int &elemJ, const Matrix &valI, const Matrix &valJ)
{
	Scalar *vii = &value[diagPtr[elemI] * nVariable2];
	Scalar *vij = &value[faceIDPtr[faceID].first * nVariable2];
	Scalar *vji = nullptr;
	Scalar *vjj = nullptr;
	if (faceIDPtr[faceID].second >= 0) //不是虚单元
	{
		vji = &value[faceIDPtr[faceID].second * nVariable2];
		vjj = &value[diagPtr[elemJ] * nVariable2];
	}

	for (int i = 0, offset = 0; i < nVariable; ++i)
	{
		for (int j = 0; j < nVariable; ++j)
		{
			vii[offset] += valI.GetValue(i, j);
			vij[offset] += valJ.GetValue(i, j);
			if (vji != nullptr) vji[offset] -= valI.GetValue(i, j);
			if (vjj != nullptr) vjj[offset] -= valJ.GetValue(i, j);
			++offset;
		}
	}
}

void BlockSparseMatrixSelf::DeleteValsRowi(const int &row, const int &index0)
{
	for (int index = rowPtr[row]; index < rowPtr[row + 1]; index++)
	{
		for (int iVar = 0; iVar < nVariable; iVar++)
			value[index * nVariable2 + index0 * nVariable + iVar] = 0.0; // Delete row values in the block
		if (col_ind[index] == row)
			value[index * nVariable2 + index0 * nVariable + index0] = 1.0; // Set 1 to the diagonal element
	}
}

void BlockSparseMatrixSelf::MatrixVectorProduct(const std::vector<Scalar> &vec, std::vector<Scalar> &prod)const
{
	const Scalar *const matrix_ptr = &(value[0]);
	const Scalar *const vector_ptr = &(vec[0]);
	for (int row = 0; row < rowSize; ++row)
	{
		Scalar *prod_ptr = &(prod[0]) + row * nVariable; // 向量块的起始位置
		for (int i = 0; i < nVariable; ++i) prod_ptr[i] = 0.0;

		for (int index = rowPtr[row]; index < rowPtr[row + 1]; ++index)
		{
			const Scalar *const vector0 = vector_ptr + col_ind[index] * nVariable; // 向量块的起始位置
			const Scalar *const matrix_row = matrix_ptr + index * nVariable2;	   // 矩阵块的起始位置
			for (int i = 0; i < nVariable; i++)
			{
				Scalar sum = 0;
				const Scalar *matrix0 = matrix_row + i * nVariable;
				for (int j = 0; j < nVariable; j++) sum += matrix0[j] * vector0[j];
				prod_ptr[i] += sum;
			}
		}
	}
}

void BlockSparseMatrixSelf::MatrixInverse(Scalar *matrix, Scalar *inverse)const
{	
	for (int i = 0; i < nVariable; ++i)
		for (int j = 0; j < nVariable; ++j)
			inverse[i*nVariable + j] = Scalar(i == j);

	//Transform system in Upper Matrix
	for (int i = 1; i < nVariable; ++i)
		for (int j = 0; j < i; ++j)
		{
			Scalar weight = matrix[i*nVariable + j] / matrix[j*nVariable + j];

			for (int k = j; k < nVariable; ++k)
				matrix[i*nVariable + k] -= weight * matrix[j*nVariable + k];

			for (int k = 0; k <= j; ++k)
				inverse[i*nVariable + k] -= weight * inverse[j*nVariable + k];
		}	

	//backwards substitution
	for (int i = nVariable; i > 0;)
	{
		i--;
		for (int j = i + 1; j < nVariable; j++)
			for (int k = 0; k < nVariable; k++)
				inverse[i*nVariable + k] -= matrix[i*nVariable + j] * inverse[j*nVariable + k];

		for (int k = 0; k < nVariable; k++)
			inverse[i*nVariable + k] /= matrix[i*nVariable + i];
	}
}

Scalar *BlockSparseMatrixSelf::GetBlock(const int &row, const int &col)
{
	for (int index = rowPtr[row]; index < rowPtr[row + 1]; ++index)
	{
		if (col_ind[index] == col)
		{
			return &(value[index * nVariable2]);
		}
	}

    return nullptr;
}

void BlockSparseMatrixSelf::SetBlock(const int &row, const int &col, Scalar *val)
{
	for (int index = rowPtr[row]; index < rowPtr[row + 1]; ++index)
	{
		if (col_ind[index] == col)
		{
			// 将val数组中的值复制到矩阵块中
			Scalar *p = &(value[index * nVariable2]);
			for (int i = 0; i < nVariable2; ++i) p[i] = val[i];
		}
	}
}