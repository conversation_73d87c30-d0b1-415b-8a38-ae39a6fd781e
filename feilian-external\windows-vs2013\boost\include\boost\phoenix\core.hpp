/*=============================================================================
 Copyright (c) 2001-2010 <PERSON>
 
 Distributed under the Boost Software License, Version 1.0. (See accompanying 
 file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 ==============================================================================*/
#ifndef BOOST_PHOENIX_CORE_HPP
#define BOOST_PHOENIX_CORE_HPP

#include <boost/phoenix/version.hpp>
#include <boost/phoenix/core/limits.hpp>
#include <boost/phoenix/core/actor.hpp>
#include <boost/phoenix/core/debug.hpp>
#include <boost/phoenix/core/is_actor.hpp>
#include <boost/phoenix/core/argument.hpp>
#include <boost/phoenix/core/value.hpp>
#include <boost/phoenix/core/reference.hpp>
#include <boost/phoenix/core/nothing.hpp>
#include <boost/phoenix/core/function_equal.hpp>
#include <boost/phoenix/core/visit_each.hpp>
#include <boost/phoenix/core/v2_eval.hpp>
#include <boost/phoenix/scope/local_variable.hpp> // to fix 5824
#include <boost/proto/generate.hpp> // attempt to fix problems in intel 14.0.1

#endif
