// Boost.Signals library

// Copyright <PERSON> 2001-2003. Use, modification and
// distribution is subject to the Boost Software License, Version
// 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

// For more information, see http://www.boost.org

#ifndef BOOST_SIGNALS_SIGNAL0_HEADER
#define BOOST_SIGNALS_SIGNAL0_HEADER

#define BOOST_SIGNALS_NUM_ARGS 0
#define BOOST_SIGNALS_TEMPLATE_PARMS
#define BOOST_SIGNALS_TEMPLATE_ARGS
#define BOOST_SIGNALS_PARMS
#define BOOST_SIGNALS_ARGS
#define BOOST_SIGNALS_BOUND_ARGS
#define BOOST_SIGNALS_ARGS_AS_MEMBERS
#define BOOST_SIGNALS_COPY_PARMS
#define BOOST_SIGNALS_INIT_ARGS
#define BOOST_SIGNALS_ARG_TYPES

#include <boost/signals/signal_template.hpp>

#undef BOOST_SIGNALS_ARG_TYPES
#undef BOOST_SIGNALS_INIT_ARGS
#undef BOOST_SIGNALS_COPY_PARMS
#undef BOOST_SIGNALS_ARGS_AS_MEMBERS
#undef BOOST_SIGNALS_BOUND_ARGS
#undef BOOST_SIGNALS_ARGS
#undef BOOST_SIGNALS_PARMS
#undef BOOST_SIGNALS_TEMPLATE_ARGS
#undef BOOST_SIGNALS_TEMPLATE_PARMS
#undef BOOST_SIGNALS_NUM_ARGS

#endif // BOOST_SIGNALS_SIGNAL0_HEADER
