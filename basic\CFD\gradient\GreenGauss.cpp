﻿#include "basic/CFD/gradient/GreenGauss.h"

namespace Gradient
{

GreenGauss::GreenGauss(Mesh *mesh_, const FieldManipulation::GradientScheme &method_, const bool nodeCenter_)
    : GradientBase(mesh_, nodeCenter_), method(method_)
{
}

GreenGauss::~GreenGauss()
{
}

void GreenGauss::CalculateScalar(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi)
{
    this->Calculate(phi, gradPhi);
}

void GreenGauss::CalculateVector(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi)
{
    this->Calculate(phi, gradPhi);
}

template void GreenGauss::Calculate(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void GreenGauss::Calculate(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void GreenGauss::Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{   
    switch (method)
    {
        case FieldManipulation::GradientScheme::GREEN_GAUSS:
            this->GreenGaussOrigin(phi, gradPhi);
            break;
        case FieldManipulation::GradientScheme::GREEN_GAUSS_M1:
            this->GreenGaussModified1(phi, gradPhi);
            break;
        case FieldManipulation::GradientScheme::GREEN_GAUSS_M2:
            this->GreenGaussModified2(phi, gradPhi);
            break;
        case FieldManipulation::GradientScheme::GREEN_GAUSS_M3:
            this->GreenGaussModified3(phi, gradPhi);
            break;
        default:
            this->GreenGaussOrigin(phi, gradPhi);
            break;
    }
}

template void GreenGauss::GreenGaussOrigin(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void GreenGauss::GreenGaussOrigin(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void GreenGauss::GreenGaussOrigin(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    // 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
		    // 得到面相关信息
		    const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();
            const Vector faceArea = face.GetArea() * face.GetNormal();

            //计算面心通量并累加（系数0.5放在最后处理）
            const TypeGradient phiFlux = faceArea * (phi.GetValue(ownerID) + phi.GetValue(nodeCenter ? ownerID : neighID));
            gradPhi.AddValue(ownerID, phiFlux);
        }
    }
    
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Vector faceArea = face.GetArea() * face.GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 0.5 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void GreenGauss::GreenGaussModified1(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void GreenGauss::GreenGaussModified1(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void GreenGauss::GreenGaussModified1(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    ElementField<TypeGradient> gradPhiTemp(mesh);    
    gradPhiTemp.Initialize();

    //第一次迭代
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算两心连线与面相交处的物理量（即f'）
        Type phiCross;
        if (mesh->JudgeBoundaryFace(faceID))
        {
            phiCross = 0.5 * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        }
        else
        {
            Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            Vector e = distance.GetNormal();
            Vector cross = ((faceCenter & faceNormal) / (e & faceNormal)) * e;
            Scalar ownerWeight = (mesh->GetElement(neighID).GetCenter() - cross).Mag() / (mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter()).Mag();
            Scalar neighWeight = 1.0 - ownerWeight;
            phiCross = phi.GetValue(ownerID) * ownerWeight + phi.GetValue(neighID) * neighWeight;
        }
        
        //计算面心通量并累加
        const TypeGradient phiFlux = faceArea * phiCross;
        gradPhiTemp.AddValue(ownerID, phiFlux);
        gradPhiTemp.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhiTemp.MultiplyValue(elementID, coefficient);
    }        

    gradPhiTemp.SetGhostlValueParallel();
    gradPhiTemp.SetGhostlValueOverset();
    gradPhiTemp.SetGhostlValueBoundary();

    //第二次迭代
    gradPhi.Initialize();
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心处的物理量（即f）
        Type phiFace;
        if (mesh->JudgeBoundaryFace(faceID))
        {
            phiFace = 0.5 * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        }
        else
        {
            Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            Vector e = distance.GetNormal();
            Vector cross = ((faceCenter & faceNormal) / (e & faceNormal)) * e;
            Scalar ownerWeight = (mesh->GetElement(neighID).GetCenter() - cross).Mag() / (mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter()).Mag();
            Scalar neighWeight = 1.0 - ownerWeight;
            Type phiCross = phi.GetValue(ownerID) * ownerWeight + phi.GetValue(neighID) * neighWeight;
            phiFace = phiCross
                    + ownerWeight * Dot(faceCenter - mesh->GetElement(ownerID).GetCenter(), gradPhiTemp.GetValue(ownerID))
                    + neighWeight * Dot(faceCenter - mesh->GetElement(neighID).GetCenter(), gradPhiTemp.GetValue(neighID));
        }

        //计算面心通量并累加
        const TypeGradient phiFlux = faceArea * phiFace;
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void GreenGauss::GreenGaussModified2(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void GreenGauss::GreenGaussModified2(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void GreenGauss::GreenGaussModified2(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    GreenGaussOrigin(phi, gradPhi);

    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();
    
    //第二次迭代
    for (int step = 0; step < 1; ++step)
    {
        //梯度置零
        ElementField<TypeGradient> gradPhiTemp(mesh);
        gradPhiTemp.Initialize();

        for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
        {
            // 得到面相关信息
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();
            const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
            const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

            //计算面心处的物理量（即f）
            Type phiFace;
            if (mesh->JudgeBoundaryFace(faceID))
            {
                //0.5在单元循环中乘
                phiFace = phi.GetValue(ownerID) + phi.GetValue(neighID);
            }
            else
            {
                //0.5在单元循环中乘
                Type phiCross = phi.GetValue(ownerID) + phi.GetValue(neighID);
                Vector temp1 = faceCenter - 0.5 * (mesh->GetElement(ownerID).GetCenter() + mesh->GetElement(neighID).GetCenter());
                TypeGradient temp2 = gradPhi.GetValue(ownerID) + gradPhi.GetValue(neighID);
                phiFace = phiCross + Dot(temp1, temp2);
            }

            //计算面心通量并累加
            const TypeGradient phiFlux = faceArea * phiFace;
            gradPhiTemp.AddValue(ownerID, phiFlux);
            gradPhiTemp.AddValue(neighID, -phiFlux);
        }

        //除以体积
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            const Scalar coefficient = 0.5 / mesh->GetElement(elementID).GetVolume();
            gradPhiTemp.MultiplyValue(elementID, coefficient);
        }

        gradPhiTemp.SetGhostlValueParallel();
        gradPhiTemp.SetGhostlValueOverset();
        gradPhiTemp.SetGhostlValueBoundary();
        
        gradPhi = gradPhiTemp;
    }

    return;
}

template void GreenGauss::GreenGaussModified3(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void GreenGauss::GreenGaussModified3(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void GreenGauss::GreenGaussModified3(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    //面循环
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector &ownerCenter = mesh->GetElement(ownerID).GetCenter();
        const Vector &neighCenter = mesh->GetElement(neighID).GetCenter();
        const Vector distLeft = ownerCenter - faceCenter;
        const Vector distRight = neighCenter - faceCenter;
        const Scalar distMagLeft = distLeft.Mag();
        const Scalar distMagRight = distRight.Mag();
        const Scalar weightLeft = distMagRight / Max(distMagLeft + distMagRight, SMALL);
        const Scalar weightRight = 1.0 - weightLeft;

        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (weightLeft * phi.GetValue(ownerID) + weightRight * phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

} // namespace FieldManipulation