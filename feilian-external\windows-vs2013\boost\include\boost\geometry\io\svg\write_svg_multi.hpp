// Boost.Geometry (aka GGL, Generic Geometry Library)

// Copyright (c) 2009-2012 Barend Gehrels, Amsterdam, the Netherlands.

// This file was modified by Oracle on 2016.
// Modifications copyright (c) 2016, Oracle and/or its affiliates.

// Contributed and/or modified by <PERSON>, on behalf of Oracle

// Parts of Boost.Geometry are redesigned from <PERSON><PERSON>dan's Geographic Library
// (geolib/GGL), copyright (c) 1995-2010 Geodan, Amsterdam, the Netherlands.

// Use, modification and distribution is subject to the Boost Software License,
// Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_GEOMETRY_IO_SVG_WRITE_SVG_MULTI_HPP
#define BOOST_GEOMETRY_IO_SVG_WRITE_SVG_MULTI_HPP


// THIS FILE WAS LEFT HERE FOR BACKWARD COMPATIBILITY


#include <boost/geometry/io/svg/write.hpp>


#endif // BOOST_GEOMETRY_IO_SVG_WRITE_SVG_MULTI_HPP
