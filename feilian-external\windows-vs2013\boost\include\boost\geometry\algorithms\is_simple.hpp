// Boost.Geometry (aka GGL, Generic Geometry Library)

// Copyright (c) 2014, Oracle and/or its affiliates.

// Contributed and/or modified by <PERSON><PERSON><PERSON>, on behalf of Oracle

// Licensed under the Boost Software License version 1.0.
// http://www.boost.org/users/license.html

#ifndef BOOST_GEOMETRY_ALGORITHMS_IS_SIMPLE_HPP
#define BOOST_GEOMETRY_ALGORITHMS_IS_SIMPLE_HPP

#include <boost/geometry/algorithms/detail/is_simple/interface.hpp>
#include <boost/geometry/algorithms/detail/is_simple/implementation.hpp>

#endif // BOOST_GEOMETRY_ALGORITHMS_IS_SIMPLE_HPP
