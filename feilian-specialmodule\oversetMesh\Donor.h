﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Donor.h
//! <AUTHOR>
//! @brief 贡献单元类
//! @date 2024-03-20
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_Donor_
#define _specialModule_oversetMesh_Donor_

#include "basic/common/ConfigUtility.h"

class Donor
{
public:
  /**
   * @brief default constructor
   *
   */
  Donor(){};

  /**
   * @brief 构造贡献单元
   *
   * @param acceptorID_
   */
  Donor(const int &acceptorID_,
        const int &acceptorProcID_,
        const Vector &acceptorCenter_,
        const std::vector<int> &donorID_,
        const std::vector<Scalar> &donorWeights_)
      : acceptorID(acceptorID_),
        acceptorProcID(acceptorProcID_),
        acceptorCenter(acceptorCenter_),
        donorIDlist(donorID_),
        donorWeights(donorWeights_){};

  /**
   * @brief Destroy the Donor object
   *
   */
  ~Donor(){};

  std::vector<int> &GetDonorIDlist() { return this->donorIDlist; }
  std::vector<Scalar> &GetDonorWeights() { return this->donorWeights; }

  const std::vector<int> &GetDonorIDlist() const { return this->donorIDlist; }
  const std::vector<Scalar> &GetDonorWeights() const { return this->donorWeights; }
  const int &GetAcceptorID() const { return this->acceptorID; }
  const int &GetAcceptorProcID() const { return this->acceptorProcID; }
  const Vector &GetAcceptorCenter() const { return this->acceptorCenter; }

private:
  // 贡献单元信息
  std::vector<int> donorIDlist;     // 贡献单元编号列表，第一个元素是插值单元中心所在的单元,称为“中央贡献单元CentralDonor”
  std::vector<Scalar> donorWeights; // 根据插值方法计算的贡献单元插值权重，与donorIDlist中的单元编号一一对应

  // 插值单元信息
  int acceptorID;
  int acceptorProcID;
  Vector acceptorCenter;
};

#endif