//  memory.hpp  --------------------------------------------------------------//

//  Copyright 2010 <PERSON> J<PERSON> Botet Escriba
//  Copyright 2015 <PERSON><PERSON>

//  Distributed under the Boost Software License, Version 1.0.
//  See http://www.boost.org/LICENSE_1_0.txt


#ifndef BOOST_DETAIL_WINAPI_MEMORY_HPP
#define BOOST_DETAIL_WINAPI_MEMORY_HPP

#include <boost/detail/winapi/heap_memory.hpp>
#include <boost/detail/winapi/local_memory.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_DETAIL_WINAPI_MEMORY_HPP
