﻿#include "sourceFlow/boundaryCondition/Extrapolation.h"

namespace Boundary
{
namespace Flow
{

Extrapolation::Extrapolation(const int &boundaryPatchID, Package::FlowPackage &data)
    :
    ExternalBoundary(boundaryPatchID, data)
{        
}

void Extrapolation::Initialize()
{
    this->UpdateBoundaryCondition();
}

void Extrapolation::UpdateBoundaryCondition()
{
    // 边界值来自相邻单元（相邻单元T满足状态方程，直接取用即可）
    this->BoundFromElement(rho);
    this->BoundFromElement(U);
    this->BoundFromElement(p);
    this->BoundFromElement(T);

    return;
}

}// namespace Flow
}// namespace Boundary