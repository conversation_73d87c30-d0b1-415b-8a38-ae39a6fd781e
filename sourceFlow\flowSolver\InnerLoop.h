﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file InnerLoop.h
//! <AUTHOR>
//! @brief 流场解算器类.
//! @date 2021-03-30
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释，并对函数参数名称及参数顺序进行调整
//
// 2021-03-13 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_flowSolver_InnerLoop_
#define _sourceFlow_flowSolver_InnerLoop_

#include "sourceFlow/flowSolver/MultigridSolver.h"

/**
 * @brief 流场计算内循环类
 * 
 */
class InnerLoop
{
public:
    /**
     * @brief 流场解算器构造函数
     * 根据设置参数文件，建立基于当地网格的特定流体流动解算器
     * 
     * @param[in] subMesh_ 当地网格，含细网格和所有粗网格
     * @param[in, out] flowPackageVector_ 流场包对象容器
     * @param[in, out] timeSchemeVector_ 时间推进对象容器
     * @param[in, out] resultProcess_ 计算结果监控及IO对象
     */
    InnerLoop(SubMesh *subMesh_, 
              std::vector<Package::FlowPackage *> &flowPackageVector_,
              std::vector<Time::Flow::FlowTimeManager *> &timeSchemeVector_,
              FlowResultsProcess *resultProcess_);

    /**
     * @brief 析构函数
     * 释放流场解算器构造函数中通过new建立的数据
     * 
     */
    ~InnerLoop();

    /**
     * @brief 流场解算器求解函数
     * 
     */
    void Solve();

    void CloseMultigrid();

private:
    /// 细网格物理场包
    Package::FlowPackage *flowPackage;

    /// 多重网格求解器对象
    MultigridSolver multigridSolver;

    /// 依据输入参数创建流场计算结果输出对象
    FlowResultsProcess *resultProcess; 

    bool multiGridFlag;
};
#endif
