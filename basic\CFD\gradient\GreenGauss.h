﻿#ifndef _basic_CFD_gradient_GreenGauss_
#define _basic_CFD_gradient_GreenGauss_

#include "basic/field/ElementField.h"
#include "basic/configure/Configure.h"
#include "basic/configure/ConfigureMacro.h"
#include "basic/CFD/gradient/GradientBase.h"

/**
 * @brief 梯度计算命名空间
 * 
 */
namespace Gradient
{

/**
 * @brief Green-Gauss梯度计算类
 * 
 */
class GreenGauss : public GradientBase
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] method_ 梯度计算方法
     * @param[in] nodeCenter_ 格点标识
     */
    GreenGauss( Mesh *mesh_,
                const FieldManipulation::GradientScheme &method_ = FieldManipulation::GradientScheme::GREEN_GAUSS,
                const bool nodeCenter_ = false );

    /**
     * @brief 析构函数
     * 
     */
    ~GreenGauss();
    
    /**
     * @brief 标量场梯度计算函数
     * 
     * @param[in] phi 标量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    void CalculateScalar(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);

    /**
     * @brief 矢量场梯度计算函数
     * 
     * @param[in] phi 矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    void CalculateVector(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);

    /**
     * @brief 梯度计算函数
     * 
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template<class Type, class TypeGradient>
    void Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

private:
    /**
     * @brief 梯度计算函数
     * 
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template<class Type, class TypeGradient>
    void GreenGaussOrigin(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGaussModified1(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGaussModified2(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGaussModified3(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

private:
    const FieldManipulation::GradientScheme method; ///< 梯度计算方法

};

}
#endif