﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshChecker.h
//! <AUTHOR>
//! @brief 网格质量检查类
//! @date 2023-01-11
//
//------------------------------修改日志----------------------------------------
// 2023-01-11 李艳亮、乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _meshProcess_MeshChecker_
#define _meshProcess_MeshChecker_

#include "basic/configure/Configure.h"
#include "basic/mesh/Mesh.h"

/// 网格质量检查类
class MeshChecker
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 网格指针
     * @param[in] level_ 网格层次
     * @param[in] dualFlag_ 是否是对偶后的网格
     */
    MeshChecker(Mesh *mesh_, const int &level_ = 0, const bool &dualFlag_ = false);

    /**
     * @brief 析构函数
     * 
     */
    ~MeshChecker();

    /**
     * @brief 网格质量检查
     * 
     */
    void Check();

private:
    /**
     * @brief 检查网格坐标范围
     * 
     */
    void CheckRange();

    /**
     * @brief 检查体积
     * 
     */
    void CheckVolume();

    /**
    * @brief 检查面积
    *
    */
    void CheckArea();

    /**
    * @brief 检查体心距离、体心连线与面法矢夹角
    *
    */
    void CheckDistanceAndNorm();

    /**
    * @brief 检查边界面法向是否向外
    *
    */
    void CheckBoundary();

    /**
    * @brief 构造函数
    *    
    * @param[in] big true为判定>号, false为判定<号
    * @param[in] ID 待插入的编号
    * @param[in] value 待插入的值
    * @param[in,out] count 列表中已经存在的元素数量
    * @param[in,out] IDList 待插入的编号列表
    * @param[in,out] valueList 待插入的值列表
    */
    void InsertList(const bool &big, const int &ID, const Scalar &value, int &count,
                    std::vector<int> &IDList, std::vector<Scalar> &valueList);

private:
    Mesh *mesh; ///< 网格指针
    const int &level; ///< 细网格标识
    const bool &dualFlag; ///< 是否是对偶网格（ture为对偶网格）
    const int maxID; ///< 最大信息打印个数
    const Scalar angleLimit; ///< 体心连线与面法矢的夹角上限值(度)
};

#endif 