﻿#include "basic/CFD/linearSystemSolver/LinearSystemSolverSelf.h"

LinearSystemSolverSelf::LinearSystemSolverSelf(Mesh *mesh_, const int nVariable_)
	:
	LinearSystemSolver(mesh_, nVariable_),
	rowSize(mesh_->GetElementNumberInDomain()),
	vectorSize(mesh_->GetElementNumberInDomain() * nVariable_)
{
	P = new PreconditionMatrix(rowSize, nVariable_);
}

LinearSystemSolverSelf::~LinearSystemSolverSelf()
{
	if (P != nullptr) {delete P; P = nullptr;}
}

void LinearSystemSolverSelf::Initialize(const Time::Scheme &scheme,
                                        const Configure::Flow::ExactJacobianStruct &paras,
										BlockSparseMatrix *A_,
										LinearSystemVector *b_,
										LinearSystemVector *x_)
{
    CSRMatrix = (BlockSparseMatrixSelf *)A_;
    resVector = (LinearSystemVectorSelf *)b_;
    solVector = (LinearSystemVectorSelf *)x_;
	
	if (scheme == Time::Scheme::GMRES)
	{
		// 预处理矩阵初始化
		P->Initialize(paras.preconditionerType);

		// 子空间大小设定为最大步数
		maxIter = paras.maxIterStep;
		linearError = paras.linearError; //线性求解器迭代精度

		// 如果采用重启型GMRES，则子空间大小采用重启步数
		restared_gmres_flag = paras.restartFlag;
		if (restared_gmres_flag) maxIter = paras.restartStep;

		W.resize(maxIter + 1);
		Z.resize(maxIter + 1);
		H.resize(maxIter + 1);

		g.resize(maxIter + 1);
		sn.resize(maxIter + 1);
		cs.resize(maxIter + 1);
		y.resize(maxIter);

		for (int i = 0; i < H.size(); ++i)
		{
			W[i].resize(rowSize * nVariable);
			H[i].resize(maxIter);
			Z[i].resize(mesh->GetElementNumberAll() * nVariable);
		}

		Z0.resize(mesh->GetElementNumberAll() * nVariable);
	}
}

void LinearSystemSolverSelf::Solve()
{
	std::vector<Scalar> &b = resVector->GetVector();
	std::vector<Scalar> &x = solVector->GetVector();

	P->Build(*CSRMatrix);
	int IterLinSol = 0;
	Scalar residual = 0.0, norm0 = 0.0;
	if(restared_gmres_flag)
	{
		norm0 = Norm(b);
		while (IterLinSol < maxIter)
		{
			IterLinSol += GmresSolver(*CSRMatrix, b, x, residual);
			if ( residual <= linearError*norm0 ) break;
		}
	}
	else
	{
		GmresSolver(*CSRMatrix, b, x, residual);
	}
}

int LinearSystemSolverSelf::GmresSolver(const BlockSparseMatrixSelf &A, const std::vector<Scalar> &b, std::vector<Scalar> &x, Scalar &residual)
{
	const Scalar eps = 2.2204460492503131e-016;

	// step0: 准备	
	for (int i = 0; i < g.size(); ++i) g[i] = 0.0;

	//step1:
	Scalar norm0 = Norm(b);
	SetGhostlValueParallel(x);  //x全为0,不用并发
	A.MatrixVectorProduct(x, W[0]);
	Subtract(W[0], b);
	Scalar beta = Norm(W[0]);
	if ((beta < linearError*norm0) || (beta < eps))
	{
		residual = beta;
		return 0;
	}
	
	//step2:
	Divide(W[0], -beta);
	g[0] = beta;
	norm0 = beta;

	//step3:
	int i = 0;
	for (i = 0; i < maxIter; ++i)
	{
		if (beta < linearError*norm0) break;

		Z0 = W[i];
		SetGhostlValueParallel(Z0);
		P->MatrixVectorProduct(Z0, Z[i]);
		SetGhostlValueParallel(Z[i]);
		A.MatrixVectorProduct(Z[i], W[i + 1]);
		ModGramSchmidt(i);

		for (int k = 0; k < i; ++k) ApplyGivens(sn[k], cs[k], H[k][i], H[k + 1][i]);
		GenerateGivens(H[i][i], H[i + 1][i], sn[i], cs[i]);
		ApplyGivens(sn[i], cs[i], g[i], g[i + 1]);

		beta = fabs(g[i + 1]);
	}

	SolveReduced(i);
	for (int k = 0; k < i; k++)  Plus_AX(x, y[k], Z[k]);
	residual = beta/norm0;
	return i;
}

void LinearSystemSolverSelf::ModGramSchmidt(const int &i)
{
	const Scalar reorth = 0.98;

	Scalar nrm = Dot(W[i + 1], W[i + 1]);
	Scalar thr = nrm * reorth;
	
	for (int k = 0; k < i + 1; ++k) 
	{
		Scalar prod = Dot(W[i + 1], W[k]);
		Scalar &H_ki = H[k][i];
		H_ki = prod;
		Plus_AX(W[i + 1], -prod, W[k]); // w[i+1] += -prod * w[k]		

		if (prod * prod > thr) 
		{
			prod = Dot(W[i + 1], W[k]);
			H_ki += prod;
			Plus_AX(W[i + 1], -prod, W[k]);
		}

		nrm -= H_ki * H_ki;
		if (nrm < 0.0) nrm = 0.0;
		thr = nrm * reorth;
	}

	nrm = Norm(W[i + 1]);
	H[i + 1][i] = nrm;
	Divide(W[i + 1], nrm);
}

void LinearSystemSolverSelf::ApplyGivens(const Scalar &s, const Scalar &c, Scalar &h1, Scalar &h2)
{
	Scalar temp = c*h1 + s*h2;
	h2 = c*h2 - s*h1;
	h1 = temp;
}

void LinearSystemSolverSelf::GenerateGivens(Scalar &dx, Scalar &dy, Scalar &s, Scalar &c)
{
	if ((dx == 0.0) && (dy == 0.0))
	{
		c = 1.0;
		s = 0.0;
	}
	else if (fabs(dy) > fabs(dx))
	{
		Scalar tmp = dx / dy;
		dx = sqrt(1.0 + tmp*tmp);
		s = Sign(1.0 / dx, dy);
		c = tmp*s;
	}
	else if (fabs(dy) <= fabs(dx))
	{
		Scalar tmp = dy / dx;
		dy = sqrt(1.0 + tmp*tmp);
		c = Sign(1.0 / dy, dx);
		s = tmp*c;
	}
	else 
	{
		dx = 0.0;
		dy = 0.0;
		c = 1.0;
		s = 0.0;
	}
	dx = fabs(dx*dy);
	dy = 0.0;
}

void LinearSystemSolverSelf::SolveReduced(const int &n)
{
	for (int i = 0; i < n; i++) y[i] = g[i];
	
	for (int i = n - 1; i >= 0; i--)
	{
		y[i] /= H[i][i];
		for (int j = i - 1; j >= 0; j--) y[j] -= H[j][i] * y[i];		
	}
}

void LinearSystemSolverSelf::SetGhostlValueParallel(std::vector<Scalar> &b)
{
	const auto &vv_ghostElement = mesh->GetGhostElementsParallel();
	const int &ghostBoundarySize = vv_ghostElement.size();

	if (ghostBoundarySize == 0) return;

#if defined(_BaseParallelMPI_)
	namespace mpi = boost::mpi;
#endif

	if (GetMPISize() == 1)
	{
		for (int i = 0; i < ghostBoundarySize; ++i)
		{
			for (int j = 0; j < vv_ghostElement[i].size(); ++j)
			{
				const int &faceID = vv_ghostElement[i][j].GetID();
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();
				
				const int indexOwner = ownerID * nVariable;
				const int indexNeigh = neighID * nVariable;
				for (int k = 0; k < nVariable; ++k) b[indexNeigh + i] = b[indexOwner + i];
			}
		}
	}
	else
	{
#if defined(_BaseParallelMPI_)

		std::vector<mpi::request> sendRequests(ghostBoundarySize);
		std::vector<mpi::request> recvRequests(ghostBoundarySize);

		std::vector<std::vector<Scalar>> vv_recvList(ghostBoundarySize);
		for (int i = 0; i < ghostBoundarySize; ++i)
		{
			const std::pair<int, int> &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
			recvRequests[i] = MPI::mpiWorld.irecv(procPair.second, procPair.second, vv_recvList[i]);
		}

		for (int i = 0; i < ghostBoundarySize; ++i)
		{
			const int ghostElementSize = vv_ghostElement[i].size();
			std::vector<Scalar> v_sendList;
			v_sendList.reserve(nVariable * ghostElementSize);

			for (int j = 0; j < ghostElementSize; ++j)
			{
				const int &faceID = vv_ghostElement[i][j].GetID();
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();			

				const int index = ownerID * nVariable;
				for (int k = 0; k < nVariable; ++k) v_sendList.push_back(b[index + k]);
			}
			const std::pair<int, int> &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
			sendRequests[i] = MPI::mpiWorld.isend(procPair.second, procPair.first, v_sendList);
		}

		MPIWaitAll(recvRequests);
		MPIWaitAll(sendRequests);

		for (int i = 0; i < ghostBoundarySize; ++i)
		{
			for (int j = 0; j < (int)vv_ghostElement[i].size(); ++j)
			{
				const int &faceID = vv_ghostElement[i][j].GetID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();

				const int index = j * nVariable;
				const int indexB = neighID * nVariable;
				for (int k = 0; k < nVariable; ++k) b[indexB + k] = vv_recvList[i][index + k];
			}
		}
#endif
	}
}
