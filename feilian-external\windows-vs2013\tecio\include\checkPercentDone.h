 #pragma once
#include "AltTecUtil.h"
#include "basicTypes.h"
namespace tecplot { namespace ___3933 { enum  SzPltWriteOperation_e { SzPltWriteOperation_DetermineSubzones, SzPltWriteOperation_WriteConnectivity, SzPltWriteOperation_CalcSubzoneMinMax, SzPltWriteOperation_WriteFieldData, END_SzPltWriteOperation_e, SzPltWriteOperation_Invalid = ___329 }; ___372 ___485( SzPltWriteOperation_e szPltWriteOperation, ___37& ___36, ___4352 ___4336, ___4636 zone); }}
