﻿#include "basic/mesh/BaseMesh.h"
#include "basic/common/GeometryTools.h"
#include <stdlib.h>

BaseMesh::BaseMesh()
	:
	n_elemNum(0),
	n_faceNum(0),
	n_nodeNum(0),
	md_meshDim(mdNoType),
	est_shapeType(Element::estNoType),    
	zoneID(0),
	n_elemNum_all(0)
{
}

BaseMesh::BaseMesh(const std::string& MshFileName)
	:st_fileName(MshFileName),
	n_elemNum(0),
	n_faceNum(0),
	n_nodeNum(0),
	md_meshDim(mdNoType),
    est_shapeType(Element::estNoType),     
	zoneID(0),
	n_elemNum_all(0)
{
}

//Search the IDs of elements in the same element zone sharing faces with a element whose ID is given
std::vector<int> BaseMesh::SearchElementNeighbor(int EID) const
{
	if (EID<0 || EID >= this->n_elemNum)
	{
		FatalError("element ID out of range in BaseMesh::SearchElementNeighbor!");
	}
	int zoneID = v_elem[EID].n_ElementZoneID;
	std::vector<int> neighborList;
	for (int i = 0; i < v_elem[EID].GetFaceSize(); i++)
		//visiting all faces bounding the element
	{
		int faceID = v_elem[EID].v_faceID[i];
		std::pair<int, int> oAndN = { this->v_face[faceID].n_owner, this->v_face[faceID].n_neighbor };
		if ((-1 != oAndN.first) && (EID != oAndN.first))
		{
			if (zoneID == v_elem[oAndN.first].n_ElementZoneID)
			{
				neighborList.push_back(oAndN.first);
			}
		}
		if ((-1 != oAndN.second) && (EID != oAndN.second))
		{
			if (zoneID == v_elem[oAndN.second].n_ElementZoneID)
			{
				neighborList.push_back(oAndN.second);
			}
		}
	}
	if (0 == (int)neighborList.size())
	{
		std::stringstream ss;
		ss << "element " << EID << " is found to be isolated by BaseMesh::SearchElementNeighbor";
		WarningContinue(ss.str());
	}
	return neighborList;
}

std::vector<int> BaseMesh::SearchNearbyElements(int EID, Scalar dis) const
{
	if (EID<0 || EID >= this->n_elemNum)
	{
		FatalError("element ID out of range in BaseMesh::SearchElementNeighbor!");
	}
	std::map<int, Scalar> elemAndDis;
	std::vector<int> v_front;
	std::vector<int> v_directNb = this->SearchElementNeighbor(EID);
	for (int i = 0; i < (int)v_directNb.size(); i++)
	{
		int thisID = v_directNb[i];
		Scalar disTemp = (this->v_elem[thisID].center - this->v_elem[EID].center).Mag();
		if (disTemp < dis)
		{
			elemAndDis.insert(std::pair<int, Scalar>(thisID, disTemp));
			v_front.push_back(thisID);
		}
	}
	while (!v_front.empty())
	{
		std::vector<int> v_newFront;
		for (int i = 0; i < (int)v_front.size(); i++)
		{
			std::vector<int> nbIDs = this->SearchElementNeighbor(v_front[i]);
			for (int j = 0; j < (int)nbIDs.size(); j++)
			{
				int thisID = nbIDs[j];
				if (thisID == EID) continue;
				if (elemAndDis.find(nbIDs[j]) == elemAndDis.end())
				{
					Scalar disTemp = (this->v_elem[thisID].center - this->v_elem[EID].center).Mag();
					if (disTemp < dis)
					{
						elemAndDis.insert(std::pair<int, Scalar>(thisID, disTemp));
						v_newFront.push_back(thisID);
					}
				}
			}
		}
		v_front = GetNonRepeatedList(v_newFront);
	}
	std::vector<int> v_result(elemAndDis.size());
	std::map<int, Scalar>::iterator it = elemAndDis.begin();
	int i = 0;
	while (it != elemAndDis.end())
	{
		v_result[i] = it->first;
		i++;
		it++;
	}
	return v_result;
}

std::vector<int> BaseMesh::SearchNearbyElements(int EID, int layerNum) const
{
	if (EID<0 || EID >= this->n_elemNum)
	{
		FatalError("element ID out of range in BaseMesh::SearchElementNeighbor(int,int)!");
	}
	if (layerNum < 1)
	{
		FatalError("layer number should be at least 1 in BaseMesh::SearchElementNeighbor(int,int)!");
	}
	if (1 == layerNum)
	{
		std::vector<int> nbElemIDList;
        for (int i = 0; i < this->v_elem[EID].GetFaceSize(); i++)
        {
            const int &faceID = this->v_elem[EID].v_faceID[i];
            const int &ownerID = this->v_face[faceID].n_owner;
            const int &neighID = this->v_face[faceID].n_neighbor;
            if(neighID == -1) continue;
            int adjacentID = neighID;
            if(neighID == EID) adjacentID = ownerID;
            nbElemIDList.push_back(adjacentID);
        }
	}
	else
	{
		Scalar gridScale = this->GetCharacteristicLength();
		return SearchNearbyElements(EID, (Scalar)layerNum*gridScale);
	}

	return std::vector<int>();
}

//calculate to get the characteristic length of this BaseMesh::
Scalar BaseMesh::GetCharacteristicLength() const
{
	Scalar averageVol = 0.0;
	for (int i = 0; i < (int)this->v_elem.size(); i++)
	{
		averageVol += this->v_elem[i].volume;
	}
	averageVol = averageVol / (Scalar)this->n_elemNum;
	Scalar gridScale = 0.0;
	if (this->md_meshDim == BaseMesh::md2D)
	{
		gridScale = sqrt(averageVol);
	}
	else if (this->md_meshDim == BaseMesh::md3D)
	{
		gridScale = pow(averageVol, 1.0 / 3.0);
	}
	return gridScale;
}


/**
 * @brief Translate the entire mesh with a given vector
 * 
 * @param transVector given Vector
 */
void BaseMesh::Translate(Vector transVector)
{
	for (int i = 0; i < this->n_nodeNum; i++)
	{
		this->v_node[i] = this->v_node[i] + transVector;
	}
	
    for (int i = 0; i < this->n_elemNum; i++)
    {
        this->v_elem[i].center = this->v_elem[i].center + transVector;
    }

    for (int i = 0; i < this->n_faceNum; i++)
    {
        this->v_face[i].center = this->v_face[i].center + transVector;
    }
}


/**
 * @brief Scaling of the entrire grid
 * 
 * @param factor scaling factor
 */
void BaseMesh::Scale(Scalar factor)
{
	this->Scale(Vector(factor, factor, factor));
}

/**
 * @brief Scaling of the entrire grid
 * 
 * @param factor scaling factor
 */
void BaseMesh::Scale(Vector factor)
{
	for (int i = 0; i < this->n_nodeNum; i++)
	{
        Vector &value = this->v_node[i];
		value.SetX(value.X() *  factor.X());
        value.SetY(value.Y() *  factor.Y());
        value.SetZ(value.Z() *  factor.Z());
	}
	
    for (int i = 0; i < this->n_elemNum; i++)
    {
        Vector &value = this->v_elem[i].center;
		value.SetX(value.X() *  factor.X());
        value.SetY(value.Y() *  factor.Y());
        value.SetZ(value.Z() *  factor.Z());

        this->v_elem[i].volume *= factor.X();
        this->v_elem[i].volume *= factor.Y();
        if(this->md_meshDim == BaseMesh::md3D)
            this->v_elem[i].volume *= factor.Z();
    }

    for (int i = 0; i < this->n_faceNum; i++)
    {
        Vector &value = this->v_face[i].center;
		value.SetX(value.X() *  factor.X());
        value.SetY(value.Y() *  factor.Y());
        value.SetZ(value.Z() *  factor.Z());
        
        Vector faceArea = this->v_face[i].areaMag * this->v_face[i].normal;
		faceArea.SetX(faceArea.X() *  factor.Y() * factor.Z());
        faceArea.SetY(faceArea.Y() *  factor.Z() * factor.X());
        faceArea.SetZ(faceArea.Z() *  factor.X() * factor.Y());

        this->v_face[i].areaMag = faceArea.Mag();
        this->v_face[i].normal = faceArea / this->v_face[i].areaMag;
    }
}


/**
 * @brief Rotate the entire mesh with a given center,
 *  an axis and a rotating angle
 * 
 * @param AxisPoint given center
 * @param axis given axis
 * @param theeta giving rotating anlge
 */
void BaseMesh::Rotate(
	Vector AxisPoint,
	Vector axis,
	Scalar theeta
	)
{
	for (int i = 0; i < this->n_nodeNum; i++)
	{
		this->v_node[i] = RotatePoint(this->v_node[i], AxisPoint, axis, theeta);
	}
    
    for (int i = 0; i < this->n_elemNum; i++)
    {
        this->v_elem[i].center = RotatePoint(this->v_elem[i].center, AxisPoint, axis, theeta);
    }

    for (int i = 0; i < this->n_faceNum; i++)
    {
        this->v_face[i].center = RotatePoint(this->v_face[i].center, AxisPoint, axis, theeta);
    }
}

void BaseMesh::ClearMesh()
{
    this->zoneID = 0;
    this->est_shapeType = Element::ElemShapeType::estNoType;
    this->n_elemNum = 0;
    this->n_faceNum = 0;
    this->n_nodeNum = 0;
    this->n_elemNum_all = 0;

    this->md_meshDim = MeshDim::mdNoType;
    this->st_fileName = "";
    this->st_meshName = "";

	std::vector<Element>().swap(this->v_elem);
	std::vector<Face>().swap(this->v_face);
	std::vector<Node>().swap(this->v_node);

    std::vector<std::string>().swap(v_boundaryName);
    std::vector<std::vector<int>>().swap(vv_boundaryFaceID);
}

void BaseMesh::PrintMeshInfomation()
{
    int boundaryFaceNumber=0;
    for (int i = 0; i < this->GetBoundarySize(); i++)
        boundaryFaceNumber += this->GetBoundaryFaceSize(i);    

	std::ostringstream stringStream;
	stringStream
		<< std::string(70, '-') << "\n mesh infomation: \n" << std::string(70, '-') << "\n"
		<< "|       1 |           total element number |" << std::setw(24) << this->n_elemNum << " |\n"
		<< "|       2 |              total node number |" << std::setw(24) << this->n_nodeNum << " |\n"
		<< "|       3 |              total face number |" << std::setw(24) << this->n_faceNum << " |\n"
		<< "|       4 |           boundary face number |" << std::setw(24) << boundaryFaceNumber << " |\n"
		<< std::string(70, '-');
	Print(stringStream.str());
}

void BaseMesh::CalculateCenterAndVolume(const bool boundaryFlag)
{
	if (boundaryFlag)
	{
		for (int patchID = 0; patchID < vv_boundaryFaceID.size(); patchID++)
		{
			for (int index = 0; index < vv_boundaryFaceID[patchID].size(); index++)
			{
				const int &faceID = vv_boundaryFaceID[patchID][index];
				this->v_face[faceID].CalculateCenterAndArea(this->v_node);
			}
		}
		return;
	}

	//Step 1: Get centers and areas for faces;
    ARI_OMP(parallel for schedule(static))
	for (int i = 0; i < this->n_faceNum; i++)
		this->v_face[i].CalculateCenterAndArea(this->v_node);

	//Step 2: Get centers and volumes for elements;
	if (this->md_meshDim == md2D)
    {
        ARI_OMP(parallel for schedule(static))
		for (int i = 0; i < this->n_elemNum; i++)
			this->v_elem[i].CalculateCenterAndVolume(this->v_node);
	}
	else if (this->md_meshDim == md3D)
    {
        ARI_OMP(parallel for schedule(static))
		for (int i = 0; i < this->n_elemNum; i++)
			this->v_elem[i].CalculateCenterAndVolume(this->v_face);
	}
}

void BaseMesh::CheckFaceDirection()
{
	for (int i = 0; i < this->n_faceNum; i++)
	{
	    const int &ownerID = this->v_face[i].n_owner;
        
		Vector ownerSidePoint = this->v_elem[ownerID].center;
		Vector faceCenterPoint = this->v_face[i].center;
        Vector faceNormal = v_face[i].normal;
        
        // 由于多边形存在不共面可能性，会引起面法矢方向判断错误
        // 对多边形面的ownerCenter和neighborCenter进行修改
        if(this->v_face[i].GetNodeSize() > 3)
        {
            // 取前三个点
            const std::vector<int> &triNodeID = v_face[i].v_nodeID;

            //对于多面体网格可能三点共线
            //const Vector &thirdNode = this->v_node[triNodeID[2]]; 
            const Vector &thirdNode = faceCenterPoint;

            // 按照前三个点顺序计算修正面法矢
            faceNormal = (this->v_node[triNodeID[1]] - this->v_node[triNodeID[0]])
                        ^ (thirdNode - this->v_node[triNodeID[1]]);
			
            // 查找共边（triNodeID[0]--triNodeID[1]）的面
            int faceIDFlag = -1;
            for (int index = 0; index < v_elem[ownerID].GetFaceSize(); index++)
            {
                const int &faceID = v_elem[ownerID].v_faceID[index];
                if (faceID == i) continue;
                int count = 0;
                for (int k = 0; k < v_face[faceID].GetNodeSize(); k++)
                {
                    const int &nodeID = v_face[faceID].v_nodeID[k];
                    if (nodeID == triNodeID[0] || nodeID == triNodeID[1]) count++;
                }
                if (count == 2) { faceIDFlag = faceID; break; }
                
            } 

            // 查找该面的另一个点（任意点），对于六面体有两个可选点，对于金字塔仅有一个点
            int nodeIDFlag = -1;
            for (int k = 0; k < v_face[faceIDFlag].GetNodeSize(); k++)
            {
                const int &nodeID = v_face[faceIDFlag].v_nodeID[k];
                if (nodeID != triNodeID[0] && nodeID != triNodeID[1])
                {
                    nodeIDFlag = nodeID; break;
                }
            }

            // 将该点作为owner单元内部点
            ownerSidePoint = v_node[nodeIDFlag];

            // 采用前三个点构成三角形的面心为修正面心点
            faceCenterPoint = (v_node[triNodeID[0]] + v_node[triNodeID[1]] + v_node[triNodeID[2]]) / 3.0;
        }

		if ((faceNormal & (faceCenterPoint - ownerSidePoint)) < 0.0)
		{
			v_face[i].normal = -v_face[i].normal;
            v_face[i].ReverseNodeID();
		}
	}
}

void BaseMesh::UpdateElementFaceID()
{
	for (int elemID = 0; elemID < this->n_elemNum; ++elemID)
	{
		this->v_elem[elemID].v_faceID.clear();
		if ( GetMeshDimension() == MeshDim::md2D )
			this->v_elem[elemID].center.SetZ(Scalar0);
	}

    for (int faceID = 0; faceID < this->n_faceNum; ++faceID)
    {
        const int &ownerID = this->v_face[faceID].n_owner;
        const int &neighID = this->v_face[faceID].n_neighbor;
        this->v_elem[ownerID].v_faceID.push_back(faceID);
        if(neighID >= 0)this->v_elem[neighID].v_faceID.push_back(faceID);
		
		if ( GetMeshDimension() == MeshDim::md2D )
		{
			this->v_face[faceID].center.SetZ(Scalar0);
			this->v_face[faceID].normal.SetZ(Scalar0);
		}
    }
}

void BaseMesh::PopulateBoundaryNodeID(const int &patchID, std::vector<int> &boundaryNodeIDList)
{
	int localNodeID = 0;
	const int faceSize = vv_boundaryFaceID[patchID].size();
    boundaryNodeIDList.reserve(2 * faceSize);

#ifdef _Supports_CXX11_
	std::unordered_map<int, int> localNodeMap; localNodeMap.reserve(2 * faceSize);
#else
	std::map<int, int> localNodeMap;
#endif


	for (int index = 0; index < faceSize; index++)
	{
		const int &faceID = vv_boundaryFaceID[patchID][index];
        const Face &face = this->v_face[faceID];
		const int numNode = face.v_nodeID.size();
		for (int k = 0; k < numNode; k++)
		{
			const int &nodeID = face.v_nodeID[k];
			if (localNodeMap.find(nodeID) == localNodeMap.end())
			{
				localNodeMap.insert(std::make_pair(nodeID, localNodeID++));
				boundaryNodeIDList.push_back(nodeID);
			}
		}
	}
}

void BaseMesh::PopulateBoundaryFaceNodeID(const int &patchID, std::vector<int> &v_boundaryNodeID, std::vector<std::vector<int>> &vv_boundaryFaceNodeID)
{
    const int faceSize = this->GetBoundaryFaceSize(patchID);
    v_boundaryNodeID.reserve(faceSize);
    vv_boundaryFaceNodeID.resize(faceSize);
    std::vector<int> nodeFlag(this->n_nodeNum, -1);
    for (int index = 0, nodeIDLocal = 0; index < faceSize; ++index)
    {
        const int &faceID = this->GetBoundaryFaceID(patchID, index);
        const int numNode = this->v_face[faceID].v_nodeID.size();
        vv_boundaryFaceNodeID[index].reserve(numNode);
        for (int i = 0; i < numNode; ++i)
        {
            const int &nodeID = this->v_face[faceID].v_nodeID[i];
            if (nodeFlag[nodeID] == -1)
            {
                vv_boundaryFaceNodeID[index].push_back(nodeIDLocal++);
                v_boundaryNodeID.push_back(nodeID);
                nodeFlag[nodeID] = v_boundaryNodeID.size() - 1;
            }
            else
            {
                vv_boundaryFaceNodeID[index].push_back(nodeFlag[nodeID]);
            }
        }
    }
}
