﻿#include "basic/geometry/Geometry.h"

namespace Geometry
{

bool PlaneWall::CreateWall(const std::vector<Vector> &nodes_)
{
    this->nodes.clear();
    this->lines.clear();

    nodes = nodes_;

    if (nodes.size() == 2)
    {
        // 平面单位法向矢量
	    this->normal = ((nodes[1]-nodes[0]) ^ Vector(0, 0, 1)).GetNormal();

        // 平面到坐标原点的距离
        this->distance = - (this->normal & nodes[0]);

        // 平面边界线
        this->lines.push_back(Line(nodes[0], nodes[1]));
    }
    else if (nodes.size() == 3)
    {
        // 平面单位法向矢量
	    this->normal = ((nodes[1]-nodes[0]) ^ (nodes[2]-nodes[0])).GetNormal();

        // 平面到坐标原点的距离
        this->distance = - (this->normal & nodes[0]);

        // 平面边界线
        this->lines.reserve(3);
        this->lines.push_back(Line(nodes[0], nodes[1]));
        this->lines.push_back(Line(nodes[1], nodes[2]));
        this->lines.push_back(Line(nodes[2], nodes[0]));
    }
    else if (nodes.size() == 4)
    {
        // 平面单位法向矢量
	    this->normal = ((nodes[1]-nodes[0]) ^ (nodes[2]-nodes[0])).GetNormal();

        // 平面到坐标原点的距离
        this->distance = - (this->normal & nodes[0]);

	    // 检查第四个点是否在平面上checking whether the fourth point lies on the plane!
        if( fabs( (this->normal & nodes[3]) + this->distance ) > 0.00001 ) return false;

        // 平面边界线
        this->lines.reserve(4);
        this->lines.push_back(Line(nodes[0], nodes[1]));
        this->lines.push_back(Line(nodes[1], nodes[2]));
        this->lines.push_back(Line(nodes[2], nodes[3]));
        this->lines.push_back(Line(nodes[3], nodes[0]));
    }
    else
    {
        return false;
    }
    
    return true;
}

void PlaneWall::SetWallProperty(const int &wallID_, const int &propType_, const bool &both_)
{
    this->wallID = wallID_;
    this->propType = propType_;
    this->bothSide = both_;
}

int PlaneWall::IsInContact(const Vector &pos, const Scalar &diam) const
{
    // 半径
    const Scalar rad = 0.5 * diam;
    
    // 点到面的法向距离和投影点坐标
    const Scalar distNormal = this->ObtainNormalDistance(pos);

    // 判断是否重叠
    Scalar ovrlp;
    if( this->bothSide)   ovrlp = rad - abs(distNormal);
    else                  ovrlp = rad - distNormal;

	if (!bothSide && distNormal < 0.0)
	{
		if (ovrlp > 0) return -1;
		else           return 0;
	}

    if( ovrlp > 0.0)
    {
        // 检查接触点是否在边界面上
        if( this->IsInPlane( this->ObtainProjectionPoint(pos) ) ) return 1;

        // 检查颗粒是否与边或者交点接触
        Vector nv, cp; Scalar dist;
        if( this->IsOnLines(pos, diam, nv, cp, dist) ) return 1;
    }
    
    return 0;
}

bool PlaneWall::IsInPlane(const Vector &p) const
{
    if ( fabs(normal & (p - nodes[0])) > SMALL) return false;

    if (nodes.size() == 2)
    {
        if ( (lines[0].GetVector() & (p - nodes[0])) < 0 ) return false;
        if ( (lines[0].GetVector() & (p - nodes[1])) > 0 ) return false;
    }
    else if (nodes.size() == 3)
    {
        if ( (( lines[0].GetVector() ^ (p - nodes[0]) ) & normal) < 0 ) return false;
        if ( (( lines[1].GetVector() ^ (p - nodes[1]) ) & normal) < 0 ) return false;
        if ( (( lines[2].GetVector() ^ (p - nodes[2]) ) & normal) < 0 ) return false;
    }
    else if (nodes.size() == 4)
    {
        if ( (( lines[0].GetVector() ^ (p - nodes[0]) ) & normal) < 0 ) return false;
        if ( (( lines[1].GetVector() ^ (p - nodes[1]) ) & normal) < 0 ) return false;
        if ( (( lines[2].GetVector() ^ (p - nodes[2]) ) & normal) < 0 ) return false;
        if ( (( lines[3].GetVector() ^ (p - nodes[3]) ) & normal) < 0 ) return false;
    }

    return true;
}

bool PlaneWall::IsOnLines(const Vector &pos, const Scalar &diam, Vector &nv, Vector &cp, Scalar &dist) const
{
    for (int i = 0; i < lines.size(); i++)
    {
        const Scalar length = lines[i].GetLength();
        const Scalar r = 0.5 * diam;
        const Scalar t = lines[i].GetProject(pos);

        if( t>= 0.0 && t<= 1.0 ) cp = lines[i].GetPoint(t);
        else if( t >= (-r/length)  && t <0.0 ) cp = lines[i].GetPoint(0.0);
        else if( t> 1.0  && t>= (1.0+r/length) ) cp = lines[i].GetPoint(1.0);
        else return false;

        dist = cp & pos;

        if( (r - dist) > 0 )
        {
            nv = (pos - cp).GetNormal();
            return true;
        }
    }
    
    return false;
}

Scalar PlaneWall::ObtainNormalDistance(const Vector &p)const
{
    return (this->normal & p) + this->distance;
}

Vector PlaneWall::ObtainProjectionPoint(const Vector &p)const
{
    return - ( (this->normal & p)+ this->distance ) * this->normal + p;
}

void PlaneWall::Reverse()
{
    std::vector<Vector> nodeTemp = nodes;
    std::reverse(nodeTemp.begin(), nodeTemp.end());
    this->CreateWall(nodeTemp);
}

void PlaneWall::SetMovingInfo(const Vector &t_vel , const Scalar &r_vel, const Line &r_line)
{
    if (t_vel.Mag() > 0.00000001) this->moving = true;
    else                          this->moving = false;

    if (fabs(r_vel) > 0.00000001) this->rotating = true;
    else                          this->rotating = false;

    this->translationVelocity = t_vel;
    this->rotationVelocity = r_vel;
    this->rotationLine = r_line;
}

const Vector PlaneWall::GetVelocity(const Vector& pOld)
{
    Vector w_vel = Vector0;
    
    // 平移速度
    if (this->moving) w_vel = this->translationVelocity;
    
    // 旋转速度
    if (this->rotating)
    {
        const Scalar d_dt_wallVel = 0.0000001;
        const Scalar d_teta = this->rotationVelocity * d_dt_wallVel;
        
        // 旋转轴单位矢量
        const Vector nv = this->rotationLine.GetVector() / this->rotationLine.GetLength();

        // 旋转矢量
        const Vector vt = (pOld - this->rotationLine.GetPoint0()) - ((pOld - this->rotationLine.GetPoint0()) & nv) * this->rotationLine.GetVector();

        // Rodrigues' rotation formula
        Vector pNew = vt * cos(d_teta) + (nv ^ vt) * sin(d_teta) + nv * ( nv & vt) * (1- cos(d_teta)); 

        w_vel = w_vel + (pOld - pNew) / d_dt_wallVel;
    }

    return w_vel;
}

void PlaneWall::GetPointToWallInfo(const Vector &pos, const Scalar &diam, Vector &nv, Scalar &dist, Vector &vel, int &pt)
{
    // 接触点
    Vector cnt_p = this->ObtainProjectionPoint(pos);
    
    // 接触点在平面内
    if (this->IsInPlane(cnt_p))
    {
        // 空间点到平面距离
        dist = this->ObtainNormalDistance(pos);

        if (this->bothSide)
        {
            // 双面，根据距离判断法向矢量是否需要调整方向
            nv = dist > 0 ? this->GetNormal() : -this->GetNormal();
            dist = fabs(dist);
        }
        else
        {
            nv = this->GetNormal();
        }
    }
    else
    {
        this->IsOnLines(pos, diam, nv, cnt_p, dist);
    }

    // 壁面属性
    pt = this->propType;

    // 接触点速度
    vel = Vector0;
    if ( this->moving || this->rotating) vel = this->GetVelocity(cnt_p);
}

} // namespace Geometry