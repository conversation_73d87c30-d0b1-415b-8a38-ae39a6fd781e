﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file flowForceNondimensionalize.cpp
//! <AUTHOR>
//! @brief 力与力矩无量纲化无量纲化处理
//! @date 2024-12-01
//
//------------------------------修改日志----------------------------------------
// 2024-12-01 吴奥奇
//     说明：创建与编写
//------------------------------------------------------------------------------

#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/postprocessor/ForceNondimensionalize.h"

int main(int argc, char **argv)
{
	if (argc <= 1)
	{
		FatalError("命令行的参数错误");
		return -1;
	}

	// 输出软件相关信息
	SetInfoFile("AriCFD_ForceNondimensionalize.info");
    PrintTitleInfo(" ARI-ForceNondimensionalize ");
	PrintSystemTime();

	// 定义参数文件对象
	Configure::Flow::FlowConfigure flowConfigure;

	// 读入参数文件
	flowConfigure.ReadCaseXml(argv[1]);

	// 打印计算参数
	//flowConfigure.PrintInformation();

	// 设置后处理的线程数
	flowConfigure.SetOpenMPThread();

	ForceNondimensionalize forceNondimensionalize(flowConfigure);
	CheckStatus(3000);

	forceNondimensionalize.ProcessForce();


	return 0;
}