//---------------------------------------------------------------------------//
// Copyright (c) 2013-2014 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_MEMORY_HPP
#define BOOST_COMPUTE_MEMORY_HPP

/// \file
///
/// Meta-header to include all Boost.Compute memory headers.

#include <boost/compute/memory/local_buffer.hpp>
#include <boost/compute/memory/svm_ptr.hpp>

#endif // BOOST_COMPUTE_MEMORY_HPP
