﻿#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/material/Materials.h"

#include<fstream>

namespace Configure
{
namespace Flow
{
FlowConfigure::FlowConfigure()
{
    fluxScheme.resize(2);
    fluxScheme[1].reconstructOrder = Flux::ReconstructionOrder::FIRST;
    fluxScheme[1].turbulenceOrder = Flux::ReconstructionOrder::FIRST;
    fluxScheme[1].limiter = Flux::Flow::Limiter::NONE_LIMITER;
}

FlowConfigure::~FlowConfigure()
{
}

void FlowConfigure::ReadCaseXml(const std::string &fileName)
{
    // 读取基本控制参数
	ReadBasicCaseXml(fileName);

    // 参数根节点
	PropertyTree ptree(fileName, &mapPosValue);

    // 流场参数根节点
    PropertyTree flowNode;
    if (ptree.GetChildTree("flow", flowNode))
    {
        // 读取流场控制参数
        this->ReadMaterial(flowNode);
        this->ReadFlowReference(flowNode);
		this->ReadModel(flowNode);
        this->ReadFluxScheme(flowNode);
        this->ReadTimeScheme(flowNode);
        this->ReadAcceleration(flowNode);
		this->ReadControl(flowNode);
		this->ReadMRF(flowNode);
		this->ReadMotion(flowNode);
		this->ReadOverset(flowNode);
		this->ReadMonitor(flowNode);
        this->ReadPostprocess(flowNode);
		this->ReadForceNondimensionalized(flowNode);
    }

    // 检查是否存在aerostatic配置节点
    if (ptree.HasChildTree("AeroStatic"))
    {
        this->ReadAeroStatic(ptree);
    }

    // 检查是否存在flutter配置节点
    if (ptree.HasChildTree("flutter"))
    {
        this->ReadFlutter(ptree);
    }

    // 更新来流参数
    this->UpdateFlowReference();

    // 检查参数是否合理
    this->Configure::InspectConfigure();
    this->InspectConfigure();

    MPIBarrier();
}

void FlowConfigure::ReadAeroStatic(PropertyTree &ptree)
{
    PropertyTree AerostaticNode;
    ptree.GetChildTree("AeroStatic", AerostaticNode);
    ReadNodeValue(AerostaticNode, std::string("staticAEFlag"), staticaero.staticAEFlag);
    if (!staticaero.staticAEFlag) return;

    //<MESH_DEFORM
    PropertyTree CFDParameterNode;
    AerostaticNode.GetChildTree("MESH_DEFORM", CFDParameterNode);
    std::string stringTemp;

    ReadNodeValue(CFDParameterNode, std::string("ReMesh_Flag"), staticaero.CFDParameter.ReMesh_Flag);
    ReadNodeValue(CFDParameterNode, std::string("meshNumber"), staticaero.CFDParameter.meshNumber);
    for (int i = 0; i < staticaero.CFDParameter.meshNumber; i++)
    {
        // 读取网格文件全路径，转换"\"为"/""，并提取文件夹、文件名和文件类型
        if (i == 0)
            stringTemp = ReadNodeValue(CFDParameterNode, std::string("InitialMeshFile"), stringTemp, false);
        else
            stringTemp = ReadNodeValue(CFDParameterNode, std::string("InitialMeshFile" + ToString(i)), stringTemp, false);
        while (stringTemp.rfind("\\") != stringTemp.npos)
            stringTemp.replace(stringTemp.rfind("\\"), 1, "/");
        staticaero.CFDParameter.meshPath.push_back(ObtainMeshPath(stringTemp));
        staticaero.CFDParameter.fileName.push_back(ObtainFileName(stringTemp));
        staticaero.CFDParameter.meshType.push_back(ObtainMeshType(stringTemp));
    }

    ReadNodeValue(CFDParameterNode, std::string("InitialMeshScale"), staticaero.CFDParameter.scale);
    ReadNodeValue(CFDParameterNode, std::string("DeformMeshFile"), staticaero.CFDParameter.DeformMeshFile, false);
    ReadNodeValue(CFDParameterNode, std::string("Mesh_RBF_SR"), staticaero.CFDParameter.Radiu);

    auto meshTransform = meshStruct.meshTransform;
    meshTransform.scale = staticaero.CFDParameter.scale;
    meshTransform.scaleFlag = (meshTransform.scale - Vector(1.0, 1.0, 1.0)).Mag() > SMALL;
    staticaero.CFDParameter.meshTransform = meshTransform;

    //<Fem
    PropertyTree CSDParameterNode(AerostaticNode, "FEM");
    ReadNodeValue(CSDParameterNode, std::string("MeshFile"), staticaero.CSDParameter.MeshName, false);
    ReadNodeValue(CSDParameterNode, std::string("MeshScale"), staticaero.CSDParameter.scale);
    ReadNodeValue(CSDParameterNode, std::string("ForceScale"), staticaero.CSDParameter.ForceScale);
    ReadNodeValue(CSDParameterNode, std::string("FSI_RBF_SR"), staticaero.CSDParameter.Radiu);
    ReadNodeValue(CSDParameterNode, std::string("NastranPath"), staticaero.CSDParameter.NastranPath, false);
    ReadNodeValue(CSDParameterNode, std::string("RwingDir"), staticaero.CSDParameter.RwingDir);
    ReadNodeValue(CSDParameterNode, std::string("FuseDir"), staticaero.CSDParameter.FuseDir);
    ReadNodeValue(CSDParameterNode, std::string("FEMtransfer"), staticaero.CSDParameter.FEMtransfer);

    //<CONVERGENCE_CRITERIA
    PropertyTree CriteriaParameterNode(AerostaticNode, "CONVERGENCE_CRITERIA");
    ReadNodeValue(CriteriaParameterNode, std::string("OneStepSolver_Flag"), staticaero.OneStepSolver_Flag);
    ReadNodeValue(CriteriaParameterNode, std::string("FSI_ITMAX"), staticaero.FSI_ITMAX);
    ReadNodeValue(CriteriaParameterNode, std::string("relaxFactor"), staticaero.relaxFactor);
    ReadNodeValue(CriteriaParameterNode, std::string("Delta_Deform"), staticaero.Delta_Deform);
    ReadNodeValue(CriteriaParameterNode, std::string("Output_Flag"), staticaero.Output_Flag);
}

void FlowConfigure::ReadMaterial(PropertyTree &flowNode)
{
    PropertyTree materialNode;
    if (!flowNode.GetChildTree("material", materialNode)) return;
	ReadNodeValue(materialNode, std::string("density"), material.density, materialTypeMap, materialTypeReverseMap);
	ReadNodeValue(materialNode, std::string("viscosity"), material.viscosity, viscosityTypeMap, viscosityTypeReverseMap);
	ReadNodeValue(materialNode, std::string("cpType"), material.cpType, cpTypeMap, cpTypeReverseMap);
}

void FlowConfigure::ReadFlowReference(PropertyTree &flowNode)
{
    PropertyTree referenceNode;
    if (!flowNode.GetChildTree("reference", referenceNode)) return;

	ReadNodeValue(referenceNode, std::string("type"), reference.type, referenceTypeMap, referenceTypeReverseMap);

    if(reference.type == ReferenceType::MACH_REYNOLDS)
    {
        PropertyTree machReynoldsNode;
        if (referenceNode.GetChildTree("machReynolds", machReynoldsNode))
        {
            ReadNodeValue(machReynoldsNode, std::string("alpha"), reference.alpha);
            ReadNodeValue(machReynoldsNode, std::string("beta"), reference.beta);
            ReadNodeValue(machReynoldsNode, std::string("mach"), reference.mach);
            ReadNodeValue(machReynoldsNode, std::string("Reynolds"), reference.Reynolds);
        }
    }
    else if(reference.type == ReferenceType::VELOCITY_PRESSURE)
    {
        PropertyTree velocityPressureNode;
        if (referenceNode.GetChildTree("velocityPressure", velocityPressureNode))
        {
            ReadNodeValue(velocityPressureNode, std::string("velocity"), reference.velocity);
            ReadNodeValue(velocityPressureNode, std::string("pressure"), reference.staticPressure);
        }
    }
    else if(reference.type == ReferenceType::MACH_DENSITY)
    {
        PropertyTree machDensityNode;
        if (referenceNode.GetChildTree("machDensity", machDensityNode))
        {
            ReadNodeValue(machDensityNode, std::string("alpha"), reference.alpha);
            ReadNodeValue(machDensityNode, std::string("beta"), reference.beta);
            ReadNodeValue(machDensityNode, std::string("mach"), reference.mach);
            ReadNodeValue(machDensityNode, std::string("density"), reference.density);
        }
	}
	else if (reference.type == ReferenceType::MACH_ALTITUDE)
	{
		PropertyTree machAltitudeNode;
		if (referenceNode.GetChildTree("machAltitude", machAltitudeNode))
		{
			ReadNodeValue(machAltitudeNode, std::string("alpha"), reference.alpha);
			ReadNodeValue(machAltitudeNode, std::string("beta"), reference.beta);
			ReadNodeValue(machAltitudeNode, std::string("mach"), reference.mach);
			ReadNodeValue(machAltitudeNode, std::string("altitude"), reference.altitude);
		}
	}

    ReadNodeValue(referenceNode, std::string("temperature"), reference.staticTemperature);
    ReadNodeValue(referenceNode, std::string("turbulentIntensity"), reference.turbulentIntensity);
    ReadNodeValue(referenceNode, std::string("turbulentViscosityRatio"), reference.turbulentViscosityRatio);
}

void FlowConfigure::ReadModel(PropertyTree &flowNode)
{
    PropertyTree modelNode;
    if (!flowNode.GetChildTree("model", modelNode)) return;

	ReadNodeValue(modelNode, std::string("type"), model.type, turbulenceModelMap, turbulenceModelReverseMap);
	ReadNodeValue(modelNode, std::string("wallFunction"), model.wallFunctionMethod, wallFunctionMap, wallFunctionReverseMap);
    ReadNodeValue(modelNode, std::string("crossDiffusionFlag"), model.crossDiffusionFlag);
    ReadNodeValue(modelNode, std::string("PrandtlLaminar"), model.PrandtlLaminar);
    ReadNodeValue(modelNode, std::string("PrandtlTurbulent"), model.PrandtlTurbulent);
    ReadNodeValue(modelNode, std::string("DESconstant"), model.DESconstant);
    ReadNodeValue(modelNode, std::string("Cdt"), model.Cdt);
    ReadNodeValue(modelNode, std::string("filterVolume"), model.filterVolume);
    ReadNodeValue(modelNode, std::string("modelPath"), model.modelPath, false);
    ReadNodeValue(modelNode, std::string("IntraOpNumThreads"), model.IntraOpNumThreads);
    ReadNodeValue(modelNode, std::string("InterOpNumThreads"), model.InterOpNumThreads);
    ReadNodeValue(modelNode, std::string("predictInterval"), model.predictInterval);
    ReadNodeValue(modelNode, std::string("trainFlag"), model.trainFlag);
    ReadNodeValue(modelNode, std::string("updateTurbOnly"), model.updateTurbOnly);
}

void FlowConfigure::ReadFluxScheme(PropertyTree &flowNode)
{
    PropertyTree fluxSchemeNode;
    if (!flowNode.GetChildTree("fluxScheme", fluxSchemeNode)) return;

	ReadNodeValue(fluxSchemeNode, std::string("order"), std::vector<Flux::ReconstructionOrder *>{ &(fluxScheme[0].reconstructOrder), &(fluxScheme[1].reconstructOrder)}, reconstructOrderMap, reconstructOrderReverseMap);
	ReadNodeValue(fluxSchemeNode, std::string("turbulenceOrder"), std::vector<Flux::ReconstructionOrder *>{&(fluxScheme[0].turbulenceOrder), &(fluxScheme[1].turbulenceOrder)}, reconstructOrderMap, reconstructOrderReverseMap);
	ReadNodeValue(fluxSchemeNode, std::string("limiter"), std::vector<Flux::Flow::Limiter::Scheme *>{&(fluxScheme[0].limiter), &(fluxScheme[1].limiter)}, limiterMap, limiterReverseMap);
	ReadNodeValue(fluxSchemeNode, std::string("gradient"), std::vector<FieldManipulation::GradientScheme *>{&(fluxScheme[0].gradient), &(fluxScheme[1].gradient)}, gradientMap, gradientReverseMap);
	ReadNodeValue(fluxSchemeNode, std::string("inviscid"), std::vector<Flux::Flow::Inviscid::Scheme *>{&(fluxScheme[0].inviscid), &(fluxScheme[1].inviscid)}, inviscidMap, inviscidReverseMap);
	ReadNodeValue(fluxSchemeNode, std::string("viscous"), std::vector<Flux::Flow::Viscous::Scheme *>{&(fluxScheme[0].viscous), &(fluxScheme[1].viscous)}, viscousSchemeMap, viscousSchemeReverseMap);
	ReadNodeValue(fluxSchemeNode, std::string("source"), std::vector<Flux::Flow::Source::Scheme *>{&(fluxScheme[0].source), &(fluxScheme[1].source)}, sourceMap, sourceReverseMap);
}

void FlowConfigure::ReadTimeScheme(PropertyTree &flowNode)
{
    PropertyTree timeSchemeNode;
    if (!flowNode.GetChildTree("timeScheme", timeSchemeNode)) return;

	ReadNodeValue(timeSchemeNode, std::string("outerLoopType"), timeScheme.outerLoopType, unsteadyTypeMap, unsteadyTypeReverseMap);
	ReadNodeValue(timeSchemeNode, std::string("innnerLoopType"), timeScheme.innnerLoopType, timeSchemeMap, timeSchemeReverseMap);

    if(timeScheme.innnerLoopType == Time::Scheme::RUNGE_KUTTA)
    {
        PropertyTree rungeKuttaNode;
        if (timeSchemeNode.GetChildTree("RungeKutta", rungeKuttaNode))
        {
            auto &RungeKutta = timeScheme.RungeKutta;

            ReadNodeValue(rungeKuttaNode, std::string("stages"), RungeKutta.stages);
			ReadNodeValue(rungeKuttaNode, std::string("type"), RungeKutta.type, RKTypeMap, RKTypeReverseMap);

	        std::vector<Scalar> coefficients0;
	        coefficients0 = ReadNodeValue(rungeKuttaNode, std::string("coefficients"), coefficients0);
	        RungeKutta.coefficients = coefficients0;
	        if (coefficients0.size() > RungeKutta.stages) RungeKutta.coefficients.resize(RungeKutta.stages);
        }
    }
    else if(timeScheme.innnerLoopType == Time::Scheme::LUSGS || timeScheme.innnerLoopType == Time::Scheme::DPLUR)
    {
        PropertyTree lusgsNode;
        if (timeSchemeNode.GetChildTree("LUSGS", lusgsNode))
        {
            auto &LUSGS = timeScheme.LUSGS;
            ReadNodeValue(lusgsNode, std::string("underRelax"), LUSGS.underRelax);
        }
    }
    else if(timeScheme.innnerLoopType > Time::Scheme::ExactJacobian)
    {
        PropertyTree exactJacobianNode;
        if (timeSchemeNode.GetChildTree("ExactJacobian", exactJacobianNode))
        {
            auto &exactJacobian = timeScheme.exactJacobian;
			ReadNodeValue(exactJacobianNode, std::string("solverType"), exactJacobian.solverType, linearSolverTypeMap, linearSolverTypeReverseMap);
			ReadNodeValue(exactJacobianNode, std::string("preconditionerType"), exactJacobian.preconditionerType, preconditionerTypeMap, preconditionerTypeReverseMap);
            ReadNodeValue(exactJacobianNode, std::string("linearError"), exactJacobian.linearError);
	        ReadNodeValue(exactJacobianNode, std::string("maxIterStep"), exactJacobian.maxIterStep);
            ReadNodeValue(exactJacobianNode, std::string("restartFlag"), exactJacobian.restartFlag);
            if(exactJacobian.restartFlag) ReadNodeValue(exactJacobianNode, std::string("restartStep"), exactJacobian.restartStep);
        }
    }

    PropertyTree CFLNode;
    if (timeSchemeNode.GetChildTree("CFL", CFLNode))
    {
        auto &CFL = timeScheme.CFL;
        ReadNodeValue(CFLNode, std::string("value"), CFL.value);
        ReadNodeValue(CFLNode, std::string("coarseMeshRatio"), CFL.coarseMeshRatio);
        ReadNodeValue(CFLNode, std::string("variableFlag"), CFL.variableFlag);
        if (CFL.variableFlag)
        {
            ReadNodeValue(CFLNode, std::string("max"), CFL.max);
            ReadNodeValue(CFLNode, std::string("min"), CFL.min);
            ReadNodeValue(CFLNode, std::string("growthRatio"), CFL.growthRatio);
            ReadNodeValue(CFLNode, std::string("growthStep"), CFL.growthStep);
        }
    }

    if (timeScheme.outerLoopType != Time::UnsteadyType::STEADY)
		ReadNodeValue(timeSchemeNode, std::string("unsteadyTimeOrder"), timeScheme.unsteadyTimeOrder, unsteadyOrderMap, unsteadyOrderReverseMap);

}

void FlowConfigure::ReadAcceleration(PropertyTree &flowNode)
{
    PropertyTree accelerationNode;
    if (!flowNode.GetChildTree("acceleration", accelerationNode)) return;

    PropertyTree multigridSolverNode;
    if (accelerationNode.GetChildTree("multigridSolver", multigridSolverNode))
    {
        auto &multigridSolver = acceleration.multigridSolver;

        ReadNodeValue(multigridSolverNode, std::string("level"), multigridSolver.level);
        if (multigridSolver.level > 1)
        {
			ReadNodeValue(multigridSolverNode, std::string("type"), multigridSolver.type, multigridTypeMap, multigridTypeReverseMap);
			ReadNodeValue(multigridSolverNode, std::string("restrictionOperator"), multigridSolver.restrictionOperator, multigridTransferOperatorMap, multigridTransferOperatorReverseMap);
			ReadNodeValue(multigridSolverNode, std::string("prolongationOperator"), multigridSolver.prolongationOperator, multigridTransferOperatorMap, multigridTransferOperatorReverseMap);
            ReadNodeValue(multigridSolverNode, std::string("coarseMeshTurbulenceFlag"), multigridSolver.coarseMeshTurbulenceFlag);
        }
    }

	ReadNodeValue(accelerationNode, std::string("residualSmooth"), acceleration.residualSmooth, smoothMap, smoothReverseMap);
    ReadNodeValue(accelerationNode, std::string("preconditionFlag"), acceleration.preconditionFlag);
    ReadNodeValue(accelerationNode, std::string("entropyFixFlag"), acceleration.entropyFixFlag);
}

void FlowConfigure::ReadControl(PropertyTree &flowNode)
{
    PropertyTree controlNode;
    if (!flowNode.GetChildTree("control", controlNode)) return;

    PropertyTree initializationNode;
    if (controlNode.GetChildTree("initialization", initializationNode))
    {
        auto &initialization = control.initialization;

		ReadNodeValue(initializationNode, std::string("type"), initialization.type, initializationMap, initializationReverseMap);

        if (initialization.type == Initialization::Type::REFERENCE)
        {
        }
        else if (initialization.type == Initialization::Type::RESTART)
        {
            ReadNodeValue(initializationNode, std::string("restartStep"), initialization.restartStep);
        }
        else if (initialization.type == Initialization::Type::FILE)
        {
            std::string stringTemp = initialization.initialFilePath;
            ReadNodeValue(initializationNode, std::string("initialFilePath"), stringTemp, false);
            if (stringTemp.rfind("/") != stringTemp.length() - 1) stringTemp += "/";
            // initialization.initialFilePath = ObtainAbsolutePath(stringTemp);
            initialization.initialFilePath = stringTemp;
        }
        ReadNodeValue(initializationNode, std::string("fullMultigridSteps"), initialization.fullMultigridSteps);
        if (initialization.fullMultigridSteps > 0)
            ReadNodeValue(initializationNode, std::string("fullMultigridCriteria"), initialization.fullMultigridCriteria);

		ReadNodeValue(initializationNode, std::string("patchNumber"), initialization.patchNumber);
		for (int i = 0; i < initialization.patchNumber; i++)
		{
			PropertyTree patchNode;
			if (!initializationNode.GetChildTree("patch" + ToString(i), patchNode)) break;

			initialization.patches.push_back(PatchStruct());
			PatchStruct &patch = initialization.patches[i];
			ReadNodeValue(patchNode, std::string("shape"), patch.shape, true);
			if (patch.shape == "RECTANGLE")
			{
				PropertyTree rectangleNode;
				if (!patchNode.GetChildTree("rectangle", rectangleNode)) break;

				patch.rectangle = new RectangleStruct;
				ReadNodeValue(rectangleNode, std::string("min"), patch.rectangle->min);
				ReadNodeValue(rectangleNode, std::string("max"), patch.rectangle->max);
			}
			else if (patch.shape == "SPHERE")
			{
				PropertyTree sphereNode;
				if (!patchNode.GetChildTree("sphere", sphereNode)) break;

				patch.sphere = new SphereStruct;
				ReadNodeValue(sphereNode, std::string("center"), patch.sphere->center);
				ReadNodeValue(sphereNode, std::string("radius"), patch.sphere->radius);
			}
			else
			{
				break;
			}

			ReadNodeValue(patchNode, std::string("variableNumber"), patch.variableNumber);
			patch.variables.resize(patch.variableNumber);
			for (int j = 0; j < patch.variableNumber; j++)
			{
				PropertyTree variableNode;
				if (!patchNode.GetChildTree("variable" + ToString(j), variableNode)) break;
				ReadNodeValue(variableNode, std::string("name"), patch.variables[j].first);
				ReadNodeValue(variableNode, std::string("value"), patch.variables[j].second);
			}
		}
    }

    PropertyTree outerLoopNode;
    if (controlNode.GetChildTree("outerLoop", outerLoopNode))
    {
        auto &outerLoop = control.outerLoop;
        ReadNodeValue(outerLoopNode, std::string("steps"), outerLoop.steps);
        ReadNodeValue(outerLoopNode, std::string("interval"), outerLoop.interval);
        ReadNodeValue(outerLoopNode, std::string("timeStep"), outerLoop.timeStep);
        ReadNodeValue(outerLoopNode, std::string("totalTime"), outerLoop.totalTime);
	    ReadNodeValue(outerLoopNode, std::string("averagedStep"), outerLoop.averagedStep);
	    ReadNodeValue(outerLoopNode, std::string("resetAverage"), outerLoop.resetAverage);

		//如果续算且不重置时均物理量，则强制从第一步开始计算时均值
		if (control.initialization.type == Initialization::Type::RESTART && !outerLoop.resetAverage) outerLoop.averagedStep = 1;
    }

    PropertyTree innerLoopNode;
    if (controlNode.GetChildTree("innerLoop", innerLoopNode))
    {
        auto &innerLoop = control.innerLoop;
        ReadNodeValue(innerLoopNode, std::string("steps"), innerLoop.steps);
        ReadNodeValue(innerLoopNode, std::string("interval"), innerLoop.interval);
        ReadNodeValue(innerLoopNode, std::string("monitorInterval"), innerLoop.monitorInterval);
        ReadNodeValue(innerLoopNode, std::string("criteria"), innerLoop.criteria);
    }

    ReadNodeValue(controlNode, std::string("resultSavePath"), control.resultSavePath, false);
    if (control.resultSavePath.find_last_of("/") != control.resultSavePath.length() - 1) control.resultSavePath = control.resultSavePath + "/";
}

void FlowConfigure::ReadMRF(PropertyTree &flowNode)
{
	PropertyTree MRFNode;
	mrf.MRFFlag = false;
	if (!flowNode.GetChildTree("MRF", MRFNode)) return;
	mrf.MRFFlag = MRFNode.ReadBool("MRFFlag", false);
    if (!mrf.MRFFlag) return;

	mrf.MRFName = MRFNode.ReadString("MRFName", false, "MRFa");
	mrf.MRForigin = MRFNode.ReadVector("MRForigin", { 0.0, 0.0, 0.0 });
	mrf.MRFaxis = MRFNode.ReadVector("MRFaxis", { 0.0, 0.0, 1.0 });
	mrf.MRFomega = MRFNode.ReadScalar("MRFomega", 0.0);
}

void FlowConfigure::ReadOverset(PropertyTree &flowNode)
{
	PropertyTree oversetNode;
	if (!flowNode.GetChildTree("overset", oversetNode)) return;

	ReadNodeValue(oversetNode, std::string("interpolationType"), overset.interpolationType, oversetMap, oversetReverseMap);
}

void FlowConfigure::ReadMotion(PropertyTree &flowNode)
{
	PropertyTree motionNode;
	if (!flowNode.GetChildTree("motion", motionNode)) return;

	int motionNumber = 0;
	ReadNodeValue(motionNode, std::string("motionNumber"), motionNumber);
    this->enableMotion = motionNumber > 0; // 参数文件中存在运动节点时，即将启动标识改为true

	motion.resize(motionNumber);
	for (int i = 0; i < motionNumber; i++)
	{
		std::string motionName = "motion" + ToString(i);
		PropertyTree subMotionNode;
		if (!motionNode.GetChildTree(motionName, subMotionNode)) return;

		ReadNodeValue(subMotionNode, std::string("motionType"), motion[i].motionType, motionMap, motionReverseMap);

		if (motion[i].motionType == MotionType::Type::TRANSLATION_ROTATION)
		{
			ReadNodeValue(subMotionNode, std::string("velocity"), motion[i].translationRotation.velocity);
			ReadNodeValue(subMotionNode, std::string("axisOrigin"), motion[i].translationRotation.axisOrigin);
			ReadNodeValue(subMotionNode, std::string("axisDirection"), motion[i].translationRotation.axisDirection);
			ReadNodeValue(subMotionNode, std::string("rotationRate"), motion[i].translationRotation.rotationRate);

		}
		if (motion[i].motionType == MotionType::Type::XDOF)
		{
			ReadNodeValue(subMotionNode, std::string("mass"), motion[i].xdof.mass);
			ReadNodeValue(subMotionNode, std::string("releaseTime"), motion[i].xdof.releaseTime);
			ReadNodeValue(subMotionNode, std::string("CenterOfMass"), motion[i].xdof.CenterOfMass);
			ReadNodeValue(subMotionNode, std::string("degree"), motion[i].xdof.degree);
			ReadNodeValue(subMotionNode, std::string("MomentOfInertiaxx"), motion[i].xdof.MomentOfInertia[0]);
			ReadNodeValue(subMotionNode, std::string("MomentOfInertiayy"), motion[i].xdof.MomentOfInertia[1]);
			ReadNodeValue(subMotionNode, std::string("MomentOfInertiazz"), motion[i].xdof.MomentOfInertia[2]);
			ReadNodeValue(subMotionNode, std::string("MomentOfInertiaxy"), motion[i].xdof.MomentOfInertia[3]);
			ReadNodeValue(subMotionNode, std::string("MomentOfInertiaxz"), motion[i].xdof.MomentOfInertia[4]);
			ReadNodeValue(subMotionNode, std::string("MomentOfInertiayz"), motion[i].xdof.MomentOfInertia[5]);
			ReadNodeValue(subMotionNode, std::string("initialVelocity"), motion[i].xdof.initialVelocity);
		}
		if (motion[i].motionType == MotionType::Type::MORPHING)
		{
			Print("to be continued...... ");
		}
	}
}

void FlowConfigure::ReadMonitor(PropertyTree &flowNode)
{
    PropertyTree monitorNode;
    if (!flowNode.GetChildTree("monitor", monitorNode)) return;

    PropertyTree residualsNode;
    if (monitorNode.GetChildTree("residuals", residualsNode))
    {
        auto &residuals = monitor.residuals;
        ReadNodeValue(residualsNode, std::string("massFlag"), residuals.massFlag);
        ReadNodeValue(residualsNode, std::string("momentumFlag"), residuals.momentumFlag);
        ReadNodeValue(residualsNode, std::string("energyFlag"), residuals.energyFlag);
        ReadNodeValue(residualsNode, std::string("turbulenceFlag"), residuals.turbulenceFlag);
        ReadNodeValue(residualsNode, std::string("maxMassFlag"), residuals.maxMassFlag);
        ReadNodeValue(residualsNode, std::string("maxTurbulenceFlag"), residuals.maxTurbulenceFlag);
        ReadNodeValue(residualsNode, std::string("log10Flag"), residuals.log10Flag);
        ReadNodeValue(residualsNode, std::string("normalizedFlag"), residuals.normalizedFlag);
    }

    PropertyTree forcesNode;
    if (monitorNode.GetChildTree("forces", forcesNode))
    {
        auto &forces = monitor.forces;
        ReadNodeValue(forcesNode, std::string("ClFlag"), forces.ClFlag);
        ReadNodeValue(forcesNode, std::string("CdFlag"), forces.CdFlag);
        ReadNodeValue(forcesNode, std::string("CmFlag"), forces.CmFlag);

		std::vector<int> boundaryIDList0;
		boundaryIDList0 = ReadNodeValue(forcesNode, std::string("boundaryIDList"), boundaryIDList0);
		forces.boundaryIDList = boundaryIDList0;
    }
}

void FlowConfigure::ReadPostprocess(PropertyTree &flowNode)
{
    PropertyTree postprocessNode;
    if (!flowNode.GetChildTree("postprocess", postprocessNode)) return;

	ReadNodeValue(postprocessNode, std::string("resultType"), postprocess.resultType, resultTypeMap, resultTypeReverseMap);
	ReadNodeValue(postprocessNode, std::string("resultPosition"), postprocess.resultPosition, resultPositionMap, resultPositionReverseMap);
	ReadNodeValue(postprocessNode, std::string("exportInteriorFlag"), postprocess.exportInteriorFlag);
	ReadNodeValue(postprocessNode, std::string("exportValueName"), postprocess.exportValueName);
	ReadNodeValue(postprocessNode, std::string("exportFeaturesFlag"), postprocess.exportFeaturesFlag);
	ReadNodeValue(postprocessNode, std::string("exportMutFlag"), postprocess.exportMutFlag);
}

void FlowConfigure::ReadForceNondimensionalized(PropertyTree &flowNode)
{

	PropertyTree forceNondimensionalizedNode;
	if (flowNode.GetChildTree("forceNondimensionalized", forceNondimensionalizedNode))
	{
		PropertyTree referenceNode;
		if (forceNondimensionalizedNode.GetChildTree("reference", referenceNode))
		{
			ReadNodeValue(referenceNode, std::string("FND_LRef"), forceNondimensionalized.lengthRef);
			ReadNodeValue(referenceNode, std::string("FND_SRef"), forceNondimensionalized.SRef);
			ReadNodeValue(referenceNode, std::string("FND_MomentCenter"), forceNondimensionalized.momentCenter);

			ReadNodeValue(referenceNode, std::string("FND_alpha"), forceNondimensionalized.alpha);
			ReadNodeValue(referenceNode, std::string("FND_beta"), forceNondimensionalized.beta);
			ReadNodeValue(referenceNode, std::string("FND_pressure"), forceNondimensionalized.pressure);
			ReadNodeValue(referenceNode, std::string("FND_density"), forceNondimensionalized.density);
			ReadNodeValue(referenceNode, std::string("FND_velocityMag"), forceNondimensionalized.velocityMag);
		}
		ReadNodeValue(forceNondimensionalizedNode, std::string("FND_boundaryIDList"), forceNondimensionalized.boundaryIDList);
	}
}

void FlowConfigure::UpdateFlowReference()
{
	Material::Flow::Materials materialTemp(material.density, material.viscosity, material.cpType);

#if defined(_EnableMultiSpecies_)
    if (material.density != Material::Flow::DensityType::IDEAL_GAS)
    {
		const int &speciesSize = materialTemp.GetSpeciesSize();
		model.multiSpecies.massFractionP.resize(speciesSize, Scalar0);
		model.multiSpecies.massFractionR.resize(speciesSize, Scalar0);

		for (int reactantID = 0; reactantID < model.multiSpecies.reactants.size(); reactantID++)
		{
			int pos = model.multiSpecies.reactants[reactantID].find(":");
			std::string reactantName = model.multiSpecies.reactants[reactantID].substr(0, pos);
			Scalar reactantMassFraction = std::stod(model.multiSpecies.reactants[reactantID].substr(pos + 1, model.multiSpecies.reactants[reactantID].length() - pos));

			for (int speciesID = 0; speciesID < speciesSize; speciesID++)
			{
				if (reactantName == materialTemp.GetPureSpeciesData(speciesID).speciesName)
					model.multiSpecies.massFractionR[speciesID] = reactantMassFraction;
			}
		}

		for (int productionID = 0; productionID < model.multiSpecies.productions.size(); productionID++)
		{
			int pos = model.multiSpecies.productions[productionID].find(":");
			std::string productionName = model.multiSpecies.productions[productionID].substr(0, pos);
			Scalar prductionMassFraction = std::stod(model.multiSpecies.productions[productionID].substr(pos + 1, model.multiSpecies.productions[productionID].length() - pos));

			for (int speciesID = 0; speciesID < speciesSize; speciesID++)
			{
				if (productionName == materialTemp.GetPureSpeciesData(speciesID).speciesName)
					model.multiSpecies.massFractionP[speciesID] = prductionMassFraction;
			}
		}
    }
#endif

	if (reference.type == ReferenceType::MACH_ALTITUDE)
	{
		reference.staticTemperature = materialTemp.GetTemperature(reference.altitude);
		reference.staticPressure = materialTemp.GetPressure(reference.altitude);
	}
    reference.muLaminar = materialTemp.Mu(reference.staticTemperature, model.multiSpecies.massFractionR);
    reference.sound = materialTemp.GetSoundSpeed(reference.staticTemperature, model.multiSpecies.massFractionR);

	if (reference.type == ReferenceType::VELOCITY_PRESSURE)
	{
		if (meshStruct.dimension == Mesh::MeshDim::md2D)
		{
			reference.alpha = atan(reference.velocity.Y() / reference.velocity.X());
			reference.beta = Scalar0;
		}
		else
		{
			reference.alpha = atan(reference.velocity.Z() / reference.velocity.X());
			reference.beta = asin(-reference.velocity.Y() / reference.velocity.Mag());
		}
		reference.alpha *= (180.0 / PI);
		reference.beta *= (180.0 / PI);

		reference.mach = reference.velocity.Mag() / reference.sound;
		reference.density = materialTemp.GetDensity(reference.staticPressure, reference.staticTemperature, model.multiSpecies.massFractionR);
		reference.Reynolds = reference.density * meshStruct.reference.cRef * reference.velocity.Mag() / reference.muLaminar;
	}
    else
	{
		const Scalar alpha = reference.alpha * PI / 180.0;
		const Scalar beta = reference.beta * PI / 180.0;
		if (meshStruct.dimension == Mesh::MeshDim::md2D)
		{
			reference.velocity = reference.sound * reference.mach * Vector(cos(alpha), sin(alpha), 0.0);
		}
		else
		{
			const Scalar Umag = reference.sound * reference.mach;
			reference.velocity.SetX(Umag * cos(-beta) * cos(alpha));
			reference.velocity.SetY(Umag * sin(-beta)             );
			reference.velocity.SetZ(Umag * cos(-beta) * sin(alpha));
		}

		if (reference.type == ReferenceType::MACH_REYNOLDS)
		{
			reference.density = reference.Reynolds / (meshStruct.reference.cRef * reference.velocity.Mag()) * reference.muLaminar;
			reference.staticPressure = materialTemp.R(model.multiSpecies.massFractionR) * reference.density * reference.staticTemperature;
		}
		else if (reference.type == ReferenceType::MACH_DENSITY)
		{
			reference.staticPressure = materialTemp.R(model.multiSpecies.massFractionR) * reference.density * reference.staticTemperature;
			reference.Reynolds = reference.density * meshStruct.reference.cRef * reference.velocity.Mag() / reference.muLaminar;
		}
		else if (reference.type == ReferenceType::MACH_ALTITUDE)
		{
			reference.density = materialTemp.GetDensity(reference.staticPressure, reference.staticTemperature, model.multiSpecies.massFractionR);
			reference.Reynolds = reference.density * meshStruct.reference.cRef * reference.velocity.Mag() / reference.muLaminar;
		}
    }

    reference.R = materialTemp.R();
    reference.gamma = materialTemp.GAMMA();
    const Scalar gammaDivGamma1 = reference.gamma / (reference.gamma - 1.0);
    const Scalar coefficient = 1.0 + 0.5 * (reference.gamma - 1.0) * reference.mach * reference.mach;
    reference.totalPressure = reference.staticPressure * pow(coefficient, gammaDivGamma1);
    reference.totalTemperature = reference.staticTemperature * coefficient;
}

void FlowConfigure::InspectConfigure()
{
    // 湍流计算需要壁面距离
    if (model.type == Turbulence::Model::INVISCID || model.type == Turbulence::Model::LAMINAR)
    {
        preprocess.wallDistanceMethod = Turbulence::WallDistance::NONE_WALL_DISTANCE;
    }
    else if (preprocess.wallDistanceMethod == Turbulence::WallDistance::NONE_WALL_DISTANCE)
    {
        preprocess.wallDistanceMethod = Turbulence::WallDistance::ADT;
    }
    std::string stringTemp = wallDistanceReverseMap.find(preprocess.wallDistanceMethod)->second;
    mapPosValue.push_back(std::make_pair("preprocess.wallDistanceMethod", Parameter(Parameter::STRING, stringTemp)));

    // 目前源项通量格式必须为NONE
    fluxScheme[0].source = Flux::Flow::Source::Scheme::NONE_SOURCE;
    fluxScheme[1].source = Flux::Flow::Source::Scheme::NONE_SOURCE;
    stringTemp = " " + sourceReverseMap.find(fluxScheme[0].source)->second + " " + sourceReverseMap.find(fluxScheme[1].source)->second + " ";
    mapPosValue.push_back(std::make_pair("flow.fluxScheme.source", Parameter(Parameter::STRING_LIST, stringTemp)));

    // 网格为2维时，侧滑角为0
    if (meshStruct.dimension == Mesh::MeshDim::md2D) reference.beta = 0;

    // 湍流模型为无粘时，粘性项通量格式为NONE
    // 湍流模型不是无粘时，粘性项通量格式不能为NONE
    if (model.type == Turbulence::Model::INVISCID)
    {
        fluxScheme[0].viscous = Flux::Flow::Viscous::Scheme::NONE_VISCOUS;
        fluxScheme[1].viscous = Flux::Flow::Viscous::Scheme::NONE_VISCOUS;
    }
    else if (model.type != Turbulence::Model::INVISCID
        && (fluxScheme[0].viscous == Flux::Flow::Viscous::Scheme::NONE_VISCOUS
        ||  fluxScheme[1].viscous == Flux::Flow::Viscous::Scheme::NONE_VISCOUS))
    {
        fluxScheme[0].viscous = Flux::Flow::Viscous::Scheme::CENTRAL_DISTANCE;
        fluxScheme[1].viscous = Flux::Flow::Viscous::Scheme::CENTRAL_DISTANCE;
    }
    stringTemp = " " + viscousSchemeReverseMap.find(fluxScheme[0].viscous)->second + " " + viscousSchemeReverseMap.find(fluxScheme[1].viscous)->second + " ";
    mapPosValue.push_back(std::make_pair("flow.fluxScheme.viscous", Parameter(Parameter::STRING_LIST, stringTemp)));

    if(fluxScheme[0].inviscid == Flux::Flow::Inviscid::Scheme::CENTRAL)
    {
        fluxScheme[1].inviscid = Flux::Flow::Inviscid::Scheme::CENTRAL;
    }
    else if(fluxScheme[1].inviscid == Flux::Flow::Inviscid::Scheme::CENTRAL)
    {
        fluxScheme[1].inviscid = Flux::Flow::Inviscid::Scheme::VANLEER;
    }
    stringTemp = " " + inviscidReverseMap.find(fluxScheme[0].inviscid)->second + " " + inviscidReverseMap.find(fluxScheme[1].inviscid)->second + " ";
    mapPosValue.push_back(std::make_pair("flow.fluxScheme.inviscid", Parameter(Parameter::STRING_LIST, stringTemp)));

    if (acceleration.multigridSolver.level > preprocess.multigrid.totalLevel)
    {
        acceleration.multigridSolver.level = preprocess.multigrid.totalLevel;
        mapPosValue.push_back(std::make_pair("flow.acceleration.multigridSolver.level", Parameter(Parameter::INT, ToString(acceleration.multigridSolver.level))));
    }

    if (  timeScheme.innnerLoopType == Time::Scheme::RUNGE_KUTTA)
        preprocess.renumberMethod = Preprocessor::RenumberType::NONE_RENUMBER;

    if (  timeScheme.innnerLoopType != Time::Scheme::RUNGE_KUTTA
       && preprocess.renumberMethod == Preprocessor::RenumberType::NONE_RENUMBER)
        preprocess.renumberMethod = Preprocessor::RenumberType::RCM;

    stringTemp = renumberTypeReverseMap.find(preprocess.renumberMethod)->second;
    mapPosValue.push_back(std::make_pair("preprocess.renumberMethod", Parameter(Parameter::STRING, stringTemp)));

    if (timeScheme.outerLoopType == Time::UnsteadyType::GLOBAL_TIME_STEP)
    {
        control.innerLoop.steps = 1;
        control.innerLoop.interval = 0;
        mapPosValue.push_back(std::make_pair("flow.control.innerLoop.steps", Parameter(Parameter::INT, ToString(control.innerLoop.steps))));
        mapPosValue.push_back(std::make_pair("flow.control.innerLoop.interval", Parameter(Parameter::INT, ToString(control.innerLoop.interval))));
    }

    if (timeScheme.outerLoopType == Time::UnsteadyType::STEADY)
    {
        control.outerLoop.steps = 1;
        control.outerLoop.interval = 0;
        mapPosValue.push_back(std::make_pair("flow.control.outerLoop.steps", Parameter(Parameter::INT, ToString(control.outerLoop.steps))));
        mapPosValue.push_back(std::make_pair("flow.control.outerLoop.interval", Parameter(Parameter::INT, ToString(control.outerLoop.interval))));
    }

    // 多重网格层数为2时，多重网格格式为V
    auto &multigridSolver = acceleration.multigridSolver;
    if(multigridSolver.level == 1)
    {
        multigridSolver.type = MultigridType::Type::NONE_MULTIGRID;
    }
    else if (multigridSolver.level == 2 && multigridSolver.type != MultigridType::Type::V)
    {
        multigridSolver.type = MultigridType::Type::V;
    }
    else if (multigridSolver.level == 3 && multigridSolver.type == MultigridType::Type::NONE_MULTIGRID)
    {
        multigridSolver.type = MultigridType::Type::V;
    }
    stringTemp = multigridTypeReverseMap.find(multigridSolver.type)->second;
    mapPosValue.push_back(std::make_pair("flow.acceleration.multigridSolver.type", Parameter(Parameter::STRING, stringTemp)));

    if (model.type != Turbulence::Model::INVISCID && model.type != Turbulence::Model::LAMINAR)
    {
        if (reference.turbulentViscosityRatio < SMALL)
        {
            if (model.type == Turbulence::Model::SPALART_ALLMARAS)
            {
                reference.turbulentViscosityRatio = 1.0;
            }
            else
            {
                reference.turbulentViscosityRatio = 0.01;
            }
            mapPosValue.push_back(std::make_pair("flow.reference.turbulentViscosityRatio", Parameter(Parameter::DOUBLE, ToString(reference.turbulentViscosityRatio))));
        }

        if (reference.turbulentIntensity < SMALL)
        {
            reference.turbulentIntensity = 0.001;
            mapPosValue.push_back(std::make_pair("flow.reference.turbulentIntensity", Parameter(Parameter::DOUBLE, ToString(reference.turbulentIntensity))));
        }
    }

	auto &initialization = control.initialization;
	if (initialization.type == Initialization::Type::RESTART)
	{
		const std::string &resultPath = this->GetControl().resultSavePath;
		const std::string restartStep = ToString(initialization.restartStep);
		const std::string resFileString = resultPath + caseName + "_res" + ".dat";
		const std::string folderPath = resultPath + restartStep + "/";
		const std::string rhoString = folderPath + "RHO_zone0.Field";
		const std::string uString = folderPath + "U_zone0.Field";
		const std::string pString = folderPath + "P_zone0.Field";

		std::ifstream resFile(resFileString.c_str());
		std::ifstream rhoFile(rhoString.c_str());
		std::ifstream uFile(uString.c_str());
		std::ifstream pFile(pString.c_str());
		if (!resFile.good() || !rhoFile.good() || !uFile.good() || !pFile.good())
		{
			FatalError("Lack of results for restart!");
		}
	}
}

void FlowConfigure::PrintReferenceValues()
{
    // 参考力输出
    outString.str("");
    outString << "\nReference values: \n";
    outString << std::setw(26) << "     specific heat ratio (1)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.gamma << "\n";
    outString << std::setw(26) << "        air gas constant (J/(kg K)) :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.R << "\n";
    outString << std::setw(26) << "                   alpha (deg)      :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.alpha << "\n";
    outString << std::setw(26) << "                    beta (deg)      :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.beta << "\n";
    outString << std::setw(26) << "                 density (kg/m^3)   :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.density << "\n";
    outString << std::setw(26) << "      velocity magnitude (m/s)      :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.velocity.Mag() << "\n";
    outString << std::setw(26) << "         velocity vector (m/s)      :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.velocity.X()
                                                                          << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.velocity.Y()
                                                                          << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.velocity.Z() << "\n";
    outString << std::setw(26) << "         static pressure (Pa)       :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.staticPressure << "\n";
    outString << std::setw(26) << "          total pressure (Pa)       :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.totalPressure << "\n";
    outString << std::setw(26) << "      static temperature (K)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.staticTemperature << "\n";
    outString << std::setw(26) << "       total temperature (K)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.totalTemperature << "\n";
    outString << std::setw(26) << "                altitude (m)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.altitude << "\n";
    outString << std::setw(26) << "             sound speed (m/s)      :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.sound << "\n";
    outString << std::setw(26) << "             Mach number (1)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.mach << "\n";
    outString << std::setw(26) << " Reynolds number (chord) (1)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.Reynolds << "\n";
    outString << std::setw(26) << "               viscosity (kg/(m s)) :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << reference.muLaminar << "\n";
    outString << std::setw(26) << "                    area (m^2)      :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << meshStruct.reference.SRef << "\n";
    outString << std::setw(26) << "            chord length (m)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << meshStruct.reference.cRef << "\n";
    outString << std::setw(26) << "             span length (m)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << meshStruct.reference.bRef << "\n";
    outString << std::setw(26) << "           moment center (m)        :" << std::setiosflags(std::ios_base::scientific) << std::setw(15) << meshStruct.reference.cmRef.X()
                                                                          << std::setiosflags(std::ios_base::scientific) << std::setw(15) << meshStruct.reference.cmRef.Y()
                                                                          << std::setiosflags(std::ios_base::scientific) << std::setw(15) << meshStruct.reference.cmRef.Z();
    Print(outString.str());
}

void FlowConfigure::ReadFlutter(PropertyTree &ptree)
{
    PropertyTree flutterNode;
    if (ptree.GetChildTree("flutter", flutterNode))
    {
        ReadNodeValue(flutterNode, std::string("flutterFlag"), flutter.flutterFlag);

        if (flutter.flutterFlag)
        {
            ReadNodeValue(flutterNode, std::string("numModes"), flutter.numModes);

            // 读取模态参数
            flutter.modalMasses.resize(flutter.numModes);
            flutter.modalFrequencies.resize(flutter.numModes);
            flutter.modalDamping.resize(flutter.numModes);
            flutter.perturbationFiles.resize(flutter.numModes);

            for (int i = 0; i < flutter.numModes; i++)
            {
                PropertyTree modeNode;
                if (flutterNode.GetChildTree("mode" + ToString(i), modeNode))
                {
                    ReadNodeValue(modeNode, std::string("mass"), flutter.modalMasses[i]);
                    ReadNodeValue(modeNode, std::string("frequency"), flutter.modalFrequencies[i]);
                    ReadNodeValue(modeNode, std::string("damping"), flutter.modalDamping[i]);
                    ReadNodeValue(modeNode, std::string("perturbationFile"), flutter.perturbationFiles[i]);
                }
            }

            // 读取耦合界面边界条件编号
            PropertyTree couplingNode;
            if (flutterNode.GetChildTree("coupling", couplingNode))
            {
                int numBoundaries;
                ReadNodeValue(couplingNode, std::string("numBoundaries"), numBoundaries);
                flutter.couplingBoundaryIDs.resize(numBoundaries);

                for (int i = 0; i < numBoundaries; i++)
                {
                    ReadNodeValue(couplingNode, std::string("boundaryID" + ToString(i)), flutter.couplingBoundaryIDs[i]);
                }
            }
        }
    }
}

}
}
